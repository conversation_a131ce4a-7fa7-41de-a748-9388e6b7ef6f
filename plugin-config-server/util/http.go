package util

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-plugin/plugin-config-server/model"
	"git.woa.com/dialogue-platform/bot-plugin/plugin-config-server/util/config"
	pb "git.woa.com/dialogue-platform/lke_proto/pb-protocol/plugin_config_server"
	secapi "git.woa.com/sec-api/go/scurl"
	sse "github.com/tmaxmax/go-sse"
)

// CallApi 调用api接口，返回结果
func CallApi(ctx context.Context, url, method string, header, query, body map[string]any) ([]byte, error) {
	var req *http.Request
	newURL, err := addQueryToURL(url, query)
	if err != nil {
		return nil, err
	}
	switch method {
	case "GET":
		req, err = http.NewRequest(method, newURL, nil)
		if err != nil {
			return nil, err
		}
	case "POST":
		postBodyBytes, _ := json.Marshal(body)
		req, err = http.NewRequest(method, newURL, bytes.NewReader(postBodyBytes))
		if err != nil {
			return nil, err
		}
	default:
		return nil, model.ErrParams
	}
	// 添加header。用户没设置的Content-Type，才加上默认的
	noContentType := true
	for headKey, headValue := range header {
		if strings.EqualFold(headKey, "Content-Type") {
			noContentType = false
		}
		req.Header.Set(headKey, ToJsonStringNotNull(headValue))
	}
	if method == "POST" && noContentType {
		req.Header.Set("Content-Type", "application/json")
	}
	options := []secapi.SecOptions{
		secapi.WithConfTimeout(time.Duration(config.GetMainConfig().ApiConfig.Timeout) * time.Second),
		secapi.WithUnsafeDomain(config.GetMainConfig().ApiConfig.WhiteUrls),
		secapi.WithAllowPorts(config.GetMainConfig().ApiConfig.Ports),
	}
	log.InfoContextf(ctx, "callApi url:%v, method:%+v, header:%+v, query:%+v, body:%+v", newURL, method, header, query, body)
	httpClient := secapi.NewSafeClient(options...)
	var res []byte
	rsp, err := httpClient.Do(req)
	if err != nil {
		return nil, err
	}
	defer func() { _ = rsp.Body.Close() }()
	if rsp.StatusCode < 200 || rsp.StatusCode > 299 {
		return nil, fmt.Errorf(`return code "%v" is not 2XX`, rsp.StatusCode)
	}
	res, err = io.ReadAll(rsp.Body)
	if err != nil {
		return nil, err
	}
	return res, nil
}

func addQueryToURL(reqURL string, queryParams map[string]any) (string, error) {
	parsed, err := url.Parse(reqURL)
	if err != nil {
		return "", err
	}

	values := parsed.Query()
	for key, value := range queryParams {
		values.Add(key, ToJsonStringNotNull(value))
	}
	newURL := url.URL{
		Scheme:      parsed.Scheme,
		Opaque:      parsed.Opaque,
		User:        parsed.User,
		Host:        parsed.Host,
		Path:        parsed.Path,
		RawPath:     parsed.RawPath,
		ForceQuery:  parsed.ForceQuery,
		RawQuery:    values.Encode(),
		Fragment:    parsed.Fragment,
		RawFragment: parsed.RawFragment,
	}
	return newURL.String(), nil
}

// CallStreamApi 调用流式api接口，返回结果，仅支持 sse 协议
func CallStreamApi(ctx context.Context, url, method string, header, query, body map[string]any,
	outputs []*pb.ResponseParam) ([]byte, error) {
	var req *http.Request
	newURL, err := addQueryToURL(url, query)
	if err != nil {
		return nil, err
	}
	switch method {
	case "GET":
		req, err = http.NewRequest(method, newURL, nil)
		if err != nil {
			return nil, err
		}
	case "POST":
		postBodyBytes, _ := json.Marshal(body)
		req, err = http.NewRequest(method, newURL, bytes.NewReader(postBodyBytes))
		if err != nil {
			return nil, err
		}
	default:
		return nil, model.ErrParams
	}
	// 添加header。用户没设置的Content-Type，才加上默认的
	noContentType := true
	for headKey, headValue := range header {
		if strings.EqualFold(headKey, "Content-Type") {
			noContentType = false
		}
		req.Header.Set(headKey, ToJsonStringNotNull(headValue))
	}
	if method == "POST" && noContentType {
		req.Header.Set("Content-Type", "application/json")
	}
	options := []secapi.SecOptions{
		secapi.WithConfTimeout(time.Duration(config.GetMainConfig().ApiConfig.Timeout) * time.Second),
		secapi.WithUnsafeDomain(config.GetMainConfig().ApiConfig.WhiteUrls),
		secapi.WithAllowPorts(config.GetMainConfig().ApiConfig.Ports),
	}
	log.InfoContextf(ctx, "callStreamApi url:%v, method:%+v, header:%+v, query:%+v, body:%+v",
		newURL, method, header, query, body)
	httpClient := secapi.NewSafeClient(options...)
	res := map[string]interface{}{}
	rsp, err := httpClient.Do(req)
	if err != nil {
		return nil, err
	}
	defer func() { _ = rsp.Body.Close() }()
	if rsp.StatusCode < 200 || rsp.StatusCode > 299 {
		return nil, fmt.Errorf(`return code "%v" is not 2XX`, rsp.StatusCode)
	}
	for ev, err := range sse.Read(rsp.Body, &sse.ReadConfig{
		MaxEventSize: 10 * 1024 * 1024, // 10M buffer
	}) {
		if err != nil {
			return nil, fmt.Errorf("sse.Read error: %v", err)
		}
		log.InfoContextf(ctx, "sse read: %s", string(ev.Data))
		newv, v := map[string]interface{}{}, map[string]interface{}{}
		if err := json.Unmarshal([]byte(ev.Data), &v); err == nil {
			// data 可以反序列化
			newv["event"] = ev.Type
			newv["data"] = v
		} else {
			// data 无法反序列化
			newv["event"] = ev.Type
			newv["data"] = ev.Data
		}
		mergeData(res, newv, outputs)
		log.InfoContextf(ctx, "res after merge: %v", ToJsonString(res))
	}
	return json.Marshal(res)
}

func mergeData(mapRes, mapEvent map[string]interface{}, outputs []*pb.ResponseParam) {
	if mapRes == nil || mapEvent == nil {
		return
	}
	for _, output := range outputs {
		map1 := mapRes[output.GetName()]
		map2 := mapEvent[output.GetName()]
		if output.Type == pb.TypeEnum_STRING && output.AnalysisMethod == pb.AnalysisMethodTypeEnum_INCREMENT.String() {
			// 只有 string 才能增量
			if v1, ok1 := map1.(string); ok1 {
				if v2, ok2 := map2.(string); ok2 {
					mapRes[output.GetName()] = v1 + v2
				}
			} else {
				if v2, ok2 := map2.(string); ok2 {
					mapRes[output.GetName()] = v2
				}
			}
		} else if output.Type != pb.TypeEnum_OBJECT && output.Type != pb.TypeEnum_ARRAY_OBJECT {
			// 非 object，直接覆盖更新
			if map2 != nil {
				mapRes[output.GetName()] = map2
			}
		}
		if output.Type == pb.TypeEnum_OBJECT && len(output.GetSubParams()) > 0 {
			subMap1, ok1 := map1.(map[string]interface{})
			subMap2, ok2 := map2.(map[string]interface{})
			if !ok1 && ok2 {
				// 以前不存在直接赋值
				mapRes[output.GetName()] = subMap2
			} else if ok1 && ok2 {
				mergeData(subMap1, subMap2, output.GetSubParams())
			}
		}
		if output.Type == pb.TypeEnum_ARRAY_OBJECT && len(output.GetSubParams()) > 0 {
			arr1, okarr1 := map1.([]interface{})
			arr2, okarr2 := map2.([]interface{})
			if okarr1 && okarr2 {
				// 输出比原来更长，arr1 接上多余的输出
				if len(arr1) < len(arr2) {
					arr1 = append(arr1, arr2[len(arr1):]...)
				}
				// arr2 一定是短的
				for i := range len(arr2) {
					subMap1, ok1 := arr1[i].(map[string]interface{})
					subMap2, ok2 := arr2[i].(map[string]interface{})
					if !ok1 && ok2 {
						// 以前不存在直接赋值
						arr1[i] = arr2[i]
					} else if ok1 && ok2 {
						mergeData(subMap1, subMap2, output.GetSubParams())
					}
				}
			}
		}
	}
}
