package model

import (
	"encoding/json"
	"time"

	"git.woa.com/dialogue-platform/bot-plugin/plugin-config-server/util/config"
	pb "git.woa.com/dialogue-platform/lke_proto/pb-protocol/plugin_config_server"
)

var ToolTableName = "t_tool"

// ToolInfo 工具信息，db结构
type ToolInfo struct {
	Id             int64        `db:"id"`
	ToolId         string       `db:"f_tool_id"`
	PluginId       string       `db:"f_plugin_id"`
	Name           string       `db:"f_name"`
	Desc           string       `db:"f_desc"`
	BuiltinDesc    string       `db:"f_builtin_desc"`
	MetaInfo       string       `db:"f_meta_info" json:"-"`
	Example        string       `db:"f_example"`
	Status         int32        `db:"f_status"`
	Module         string       `db:"f_module"`
	FinancePolicy  int32        `db:"f_finance_policy"`
	IsDeleted      int32        `db:"f_is_deleted"`
	Uin            string       `db:"f_uin"`
	SubUin         string       `db:"f_sub_uin"`
	CreateTime     time.Time    `db:"f_create_time"`
	UpdateTime     time.Time    `db:"f_update_time"`
	PluginMetaInfo              // db中不存在该字段，在redis缓存中才保存
	ToolMeta       ToolMetaInfo // db中不存在该字段，由MetaInfo反序列化而来
}

// ToolMetaInfo 工具元信息
type ToolMetaInfo struct {
	Url     string
	Path    string
	Method  string
	Header  []*pb.RequestParam
	Query   []*pb.RequestParam
	Body    []*pb.RequestParam
	Inputs  []*pb.RequestParam // 由header、query、body合并而来
	Outputs []*pb.ResponseParam
	Code    string
	CodeUrl string // 代码地址
	// 工具是否是流式返回
	CallingMethod pb.CallingMethodTypeEnum
}

// ConvertToPbTool 转换为pb结构
func (t *ToolInfo) ConvertToPbTool(needDetail bool) (*pb.ToolInfo, error) {
	if t == nil {
		return nil, nil
	}
	// if t.MetaInfo != "" {
	// 	if err := json.Unmarshal([]byte(t.MetaInfo), &t.ToolMeta); err != nil {
	// 		return nil, err
	// 	}
	// }
	res := &pb.ToolInfo{
		ToolId:   t.ToolId,
		PluginId: t.PluginId,
		Name:     t.Name,
		Desc:     t.Desc,
		Url:      t.ToolMeta.Url,
		Path:     t.ToolMeta.Path,
		Method:   t.ToolMeta.Method,
		Header:   t.ToolMeta.Header,
		Query:    t.ToolMeta.Query,
		Body:     t.ToolMeta.Body,
		Inputs:   t.ToolMeta.Inputs,
		Outputs:  AdaptOutputsDefaultValue(t.ToolMeta.Outputs),
		Code:     t.ToolMeta.Code,
		// 是否是流式回复
		CallingMethod: t.ToolMeta.CallingMethod.String(),
		// 是否绑定知识库
		IsBindingKnowledge: t.isBindKnowledge(),
	}
	if t.Example != "" {
		var toolExample pb.ToolExample
		if err := json.Unmarshal([]byte(t.Example), &toolExample); err != nil {
			return nil, err
		}
		res.Example = &toolExample
	}
	if !needDetail {
		res.Url = ""
		res.Path = ""
		res.Code = ""
	}
	return res, nil
}

// isBindKnowledge 判断工具是否绑定知识库
func (t *ToolInfo) isBindKnowledge() bool {
	if t == nil {
		return false
	}
	var isBindKnowledge bool
	if t.ToolId == config.GetMainConfig().KnowledgeQATool.ToolId {
		isBindKnowledge = true
	}
	return isBindKnowledge
}

// GetToolMetaFromPbTool 从pb结构中获取ToolMetaInfo
func GetToolMetaFromPbTool(tool *pb.ToolInfo) ToolMetaInfo {
	if tool == nil {
		return ToolMetaInfo{}
	}
	return ToolMetaInfo{
		Url:     tool.GetUrl(),
		Path:    tool.GetPath(),
		Method:  tool.GetMethod(),
		Header:  tool.GetHeader(),
		Query:   tool.GetQuery(),
		Body:    tool.GetBody(),
		Inputs:  tool.GetInputs(),
		Outputs: AdaptOutputsDefaultValue(tool.GetOutputs()),
		Code:    tool.GetCode(),
		// 是否是流式回复
		CallingMethod: pb.CallingMethodTypeEnum(
			pb.CallingMethodTypeEnum_value[tool.GetCallingMethod()]),
	}
}

// AdaptOutputsDefaultValue ...
func AdaptOutputsDefaultValue(outputs []*pb.ResponseParam) []*pb.ResponseParam {
	for _, output := range outputs {
		if output.AnalysisMethod == "" {
			// 默认值为覆盖
			output.AnalysisMethod = pb.AnalysisMethodTypeEnum_COVER.String()
		}
		AdaptOutputsDefaultValue(output.GetSubParams())
	}
	return outputs
}

func (tm *ToolMetaInfo) ToString() string {
	newTm := *tm
	newTm.Code = ""
	res, _ := json.Marshal(newTm)
	return string(res)
}
