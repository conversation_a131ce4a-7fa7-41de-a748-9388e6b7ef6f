package model

// EnvType 环境标识
type EnvType string

const (
	// SandboxEnv 沙箱环境
	SandboxEnv EnvType = "Sandbox"
	// ProductEnv 发布环境
	ProductEnv EnvType = "Product"
)

type ParamShowType int32

const (
	ShowNormal ParamShowType = 0 // 正常展示
	ShowHidden ParamShowType = 1 // 不展示，官方内置插件配置不展示的参数
)

const (
	PluginDB     = "mysql.db_llm_plugin"
	PluginProdDB = "mysql.db_llm_prod_plugin"
	PluginRedis  = "redis.plugin-config-server"
	// RpcClientAccessManager AccessManager Client
	RpcClientAccessManager = "trpc.SmartService.AccessManagerServer.AccessManager"

	PluginStatusSuccess = 1 // 插件状态：成功
	PluginStatusFailure = 2 // 插件状态：不可用

	ToolStatusSuccess = 1 // 工具状态：成功

	FinancePolicyFree = 0 // 计费策略：免费

	DetailSwitchOn  = true  // 需要详情
	DetailSwitchOff = false // 不需要详情

	SceneSandbox = 1 // 场景：沙箱

	DefaultPageSize = 10 // 默认分页大小
	DefaultPageNum  = 1  // 默认分页页码

	StorageTypeCos = "cos" // 存储类型：COS

	// ReleaseStatusUnPublished 发布状态 未发布
	ReleaseStatusUnPublished = "UNPUBLISHED"
	// ReleaseStatusPublishing 发布状态 发布中
	ReleaseStatusPublishing = "PUBLISHING"
	// ReleaseStatusPublished 发布状态 已发布
	ReleaseStatusPublished = "PUBLISHED"
	// ReleaseStatusFail 发布状态 发布失败
	ReleaseStatusFail = "FAIL"

	// ActionInsert 执行动作 新增
	ActionInsert = "INSERT"
	// ActionUpdate 执行动作 更新
	ActionUpdate = "UPDATE"
	// ActionDelete 执行动作 删除
	ActionDelete = "DELETE"

	QueryActionInsert = "1"
	QueryActionUpdate = "2"
	QueryActionDelete = "3"

	// RedisKeyAppToolFormat 应用工具配置的redis key
	RedisKeyAppToolFormat = "PLUGIN_CONFIG:APP_TOOL:ENV:%s:AppID:%s"
	// RedisKeyAppPluginFormat 应用插件配置的redis key
	RedisKeyAppPluginFormat = "PLUGIN_CONFIG:APP_PLUGIN:ENV:%s:AppID:%s"
	// RedisKeyMCPSyncTask MCP工具同步定时任务的redis key
	RedisKeyMCPSyncTask = "PLUGIN_CONFIG:SYNC_TASK"
	// RedisKeyMCPSyncFail 记录MCP工具同步失败次数的redis key
	RedisKeyMCPSyncFail = "PLUGIN_CONFIG:SYNC_FAIL"
)

var EnumToAction = map[string]int{
	ActionInsert: 1,
	ActionUpdate: 2,
	ActionDelete: 3,
}

var EnumActionDescription = map[string]string{
	ActionInsert: "新增",
	ActionUpdate: "修改",
	ActionDelete: "删除",
}

var PublishActionDesc = map[string]string{
	QueryActionInsert: "INSERT",
	// ActionUpdate 执行动作 更新
	QueryActionUpdate: "UPDATE",
	// ActionDelete 执行动作 删除
	QueryActionDelete: "DELETE",
}

const (
	AppPatternStandard       = "standard"        // 标准模式
	AppPatternAgent          = "agent"           // agent模式
	AppPatternSingleWorkflow = "single_workflow" // 单工作流模式
)
