package model

import (
	"fmt"
	"sort"

	"git.code.oa.com/trpc-go/trpc-go/log"
	errorsRecover "git.woa.com/dialogue-platform/common/v3/errors"
	"git.woa.com/dialogue-platform/go-comm/utils"
	pb "git.woa.com/dialogue-platform/lke_proto/pb-protocol/plugin_config_server"
	"github.com/getkin/kin-openapi/openapi3"
)

// MCPServerInfo MCP服务器信息
type MCPServerInfo struct {
	Name    string `json:"name"`
	Version string `json:"version"`
}

// MCPToolInfo 工具信息
type MCPToolInfo struct {
	Name         string              `json:"name"`
	Description  string              `json:"description,omitempty"`
	InputSchema  MCPToolInputSchema  `json:"inputSchema"`
	OutputSchema MCPToolOutputSchema `json:"-"`
}

func (m *MCPToolInfo) ToToolInfo() (ToolInfo, error) {
	defer errorsRecover.PanicHandler()
	toolInfo := ToolInfo{}
	toolInfo.Name = m.Name
	toolInfo.Desc = m.Description
	inputs, err := m.InputSchema.ToRequestParam()
	if err != nil {
		return toolInfo, err
	}
	outputs := m.OutputSchema.ToResponseParam()
	toolInfo.ToolMeta = ToolMetaInfo{
		Inputs:  inputs,
		Outputs: outputs,
	}
	return toolInfo, nil
}

type MCPToolInputSchema openapi3.Schema

func (s *MCPToolInputSchema) judgeType(schemaType *openapi3.Types) (string, error) {
	if schemaType == nil {
		log.Warnf("MCPToolInputSchema|judgeType|schemaType is nil, type: %+v", schemaType)
		return "", fmt.Errorf("MCPToolInputSchema|judgeType|schemaType is nil")
	}
	types := *schemaType
	if len(types) == 0 {
		log.Warnf("MCPToolInputSchema|judgeType|types is empty, type: %+v", schemaType)
		return "", fmt.Errorf("MCPToolInputSchema|judgeType|types is empty")
	}
	if len(types) == 1 {
		return types[0], nil
	}
	// 如果是多元素的数组，取第一个非空元素，如果全为空，仍取第一个返回，告警处理
	for i := 0; i < len(types); i++ {
		if !s.isTypeNull(types[i]) {
			return types[i], nil
		}
	}
	return types[0], nil
}

func (s *MCPToolInputSchema) isTypeNull(schemaType string) bool {
	switch schemaType {
	case "string", "number", "integer", "boolean", "array", "object":
		return false
	default:
		return true
	}
}

func (s *MCPToolInputSchema) ToRequestParam() ([]*pb.RequestParam, error) {
	defer errorsRecover.PanicHandler()
	// MCP工具输入参数以object类型为根节点
	requiredParams := make(map[string]bool)
	for _, required := range s.Required {
		requiredParams[required] = true
	}
	params := make([]*pb.RequestParam, 0)
	propertyNames := make([]string, 0, len(s.Properties))
	for propertyName := range s.Properties {
		propertyNames = append(propertyNames, propertyName)
	}
	sort.Strings(propertyNames)
	for _, propertyName := range propertyNames {
		property := s.Properties[propertyName]
		isRequired := requiredParams[propertyName]
		if property.Value == nil {
			log.Warnf("MCPToolInputSchema|ToRequestParam|property.value is nil, skip property: %s", propertyName)
			continue
		}
		var param *pb.RequestParam
		var err error
		if len(property.Value.OneOf) > 0 {
			param, err = s.toOneOfParam(propertyName, property.Value)
		} else if len(property.Value.AnyOf) > 0 {
			param, err = s.toAnyOfParam(propertyName, property.Value)
		} else {
			param, err = s.toParamType(propertyName, property.Value)
		}
		if err != nil {
			log.Warnf("MCPToolInputSchema|ToRequestParam|convert failed, property:%s, value:%+v", propertyName, property.Value)
			return nil, err
		}
		if param == nil {
			log.Warnf("MCPToolInputSchema|ToRequestParam|param is nil, skip property: %s", propertyName)
			continue
		}
		param.Name = propertyName
		param.Desc = property.Value.Description
		param.IsRequired = isRequired
		if property.Value.Default != nil &&
			(param.Type == pb.TypeEnum_STRING || param.Type == pb.TypeEnum_INT ||
				param.Type == pb.TypeEnum_FLOAT || param.Type == pb.TypeEnum_BOOL) {
			// 默认值只支持string、number、integer、boolean类型，有默认值则填充
			param.DefaultValue = fmt.Sprintf("%v", property.Value.Default)
		}
		params = append(params, param)
	}
	return params, nil
}

// toParamType 转换参数类型，如果是object和array类型则递归处理子参数
// 参数名称、描述、是否必填等信息在外部填充
func (s *MCPToolInputSchema) toParamType(propertyName string, pValue *openapi3.Schema) (*pb.RequestParam, error) {
	propertyType, err := s.judgeType(pValue.Type)
	if err != nil {
		// 字段如果为空类型，跳过该字段，不返回失败
		log.Warnf("MCPToolInputSchema|ToRequestParam|skip property: %s, err:%s",
			propertyName, err.Error())
		return nil, nil
	}
	// 解析参数
	var param *pb.RequestParam
	switch propertyType {
	case "string":
		param, err = s.toStringParam()
	case "number":
		param, err = s.toFloatParam()
	case "integer":
		param, err = s.toIntegerParam()
	case "boolean":
		param, err = s.toBoolParam()
	case "array":
		param, err = s.toArrayParam(pValue)
	case "object":
		param, err = s.toObjectParam(pValue)
	case "null":
		param, err = s.toNullParam()
	default:
		// 字段如果为空类型，跳过该字段，不返回失败
		log.Warnf("MCPToolInputSchema|ToRequestParam|skip property: %s, type:",
			propertyName, propertyType)
		return nil, nil
	}
	if err != nil {
		return nil, err
	}
	return param, nil
}

func (s *MCPToolInputSchema) toOneOfParam(propertyName string, pValue *openapi3.Schema) (*pb.RequestParam, error) {
	param := &pb.RequestParam{
		Type: pb.TypeEnum_UNSPECIFIED,
	}
	for _, schema := range pValue.OneOf {
		if schema == nil || schema.Value == nil {
			log.Warnf("MCPToolInputSchema|toOneOfParam|schema.value is nil, skip schema: %s, schema:%+v", propertyName, schema)
			continue
		}
		paramType, err := s.toParamType(propertyName, schema.Value)
		if err != nil {
			log.Warnf("MCPToolInputSchema|toOneOfParam|convert failed, property:%s, value:%+v", propertyName, schema)
			return nil, err
		}
		param.OneOf = append(param.OneOf, paramType)
	}
	if len(param.OneOf) == 0 {
		log.Warnf("MCPToolInputSchema|toOneOfParam|param.OneOf invalid, property: %s, value:%+v", propertyName, pValue)
		return nil, fmt.Errorf("OneOf invalid, property: %s", propertyName)
	}
	return param, nil
}

func (s *MCPToolInputSchema) toAnyOfParam(propertyName string, pValue *openapi3.Schema) (*pb.RequestParam, error) {
	param := &pb.RequestParam{
		Type: pb.TypeEnum_UNSPECIFIED,
	}
	for _, schema := range pValue.AnyOf {
		if schema == nil || schema.Value == nil {
			log.Warnf("MCPToolInputSchema|toAnyOfParam|schema.value is nil, skip schema: %s, schema:%+v", propertyName, schema)
			continue
		}
		paramType, err := s.toParamType(propertyName, schema.Value)
		if err != nil {
			log.Warnf("MCPToolInputSchema|toAnyOfParam|convert failed, property:%s, value:%+v", propertyName, schema.Value)
			return nil, err
		}
		param.AnyOf = append(param.AnyOf, paramType)
	}
	if len(param.AnyOf) == 0 {
		log.Warnf("MCPToolInputSchema|toAnyOfParam|param.AnyOf invalid, property: %s, value:%+v", propertyName, pValue)
		return nil, fmt.Errorf("AnyOf invalid, property: %s", propertyName)
	}
	return param, nil
}

func (s *MCPToolInputSchema) toStringParam() (*pb.RequestParam, error) {
	param := &pb.RequestParam{
		Type: pb.TypeEnum_STRING,
	}
	return param, nil
}

func (s *MCPToolInputSchema) toFloatParam() (*pb.RequestParam, error) {
	param := &pb.RequestParam{
		Type: pb.TypeEnum_FLOAT,
	}
	return param, nil
}

func (s *MCPToolInputSchema) toIntegerParam() (*pb.RequestParam, error) {
	param := &pb.RequestParam{
		Type: pb.TypeEnum_INT,
	}
	return param, nil
}

func (s *MCPToolInputSchema) toBoolParam() (*pb.RequestParam, error) {
	param := &pb.RequestParam{
		Type: pb.TypeEnum_BOOL,
	}
	return param, nil
}

func (s *MCPToolInputSchema) toArrayParam(p *openapi3.Schema) (*pb.RequestParam, error) {
	param := &pb.RequestParam{}
	if p.Items == nil || p.Items.Value == nil {
		return nil, fmt.Errorf("array type must have items field")
	}
	itemsType, err := s.judgeType(p.Items.Value.Type)
	if err != nil {
		return nil, err
	}
	switch itemsType {
	case "string":
		param.Type = pb.TypeEnum_ARRAY_STRING
	case "number":
		param.Type = pb.TypeEnum_ARRAY_FLOAT
	case "integer":
		param.Type = pb.TypeEnum_ARRAY_INT
	case "boolean":
		param.Type = pb.TypeEnum_ARRAY_BOOL
	case "object":
		param.Type = pb.TypeEnum_ARRAY_OBJECT
		subInputSchema := MCPToolInputSchema(*p.Items.Value)
		param.SubParams, err = subInputSchema.ToRequestParam()
		if err != nil {
			log.Warnf("MCPToolInputSchema|toArrayParam|object array data marshal failed, "+
				"origin: %s, schema: %s", utils.ToJsonString(p.Items), utils.ToJsonString(s))
			return nil, err
		}
	default:
		log.Warnf("MCPToolInputSchema|toArrayParam|array data type is not supported, "+
			"origin: %s, schema: %s", itemsType, utils.ToJsonString(s))
		return nil, fmt.Errorf("invalid array type: %v", p.Items.Value.Type)
	}
	return param, nil
}

func (s *MCPToolInputSchema) toObjectParam(p *openapi3.Schema) (*pb.RequestParam, error) {
	subInputSchema := MCPToolInputSchema(*p)
	subParams, err := subInputSchema.ToRequestParam() // 递归处理
	if err != nil {
		return nil, err
	}
	return &pb.RequestParam{
		Type:      pb.TypeEnum_OBJECT,
		SubParams: subParams,
	}, nil
}

func (s *MCPToolInputSchema) toNullParam() (*pb.RequestParam, error) {
	param := &pb.RequestParam{
		Type: pb.TypeEnum_NULL,
	}
	return param, nil
}

// MCPToolOutputSchema MCP工具调用输出，包含JsonRPC错误信息和工具调用结果
type MCPToolOutputSchema struct {
	Result MCPToolCallResult `json:"result"`
	Error  MCPJsonRPCError   `json:"error"`
}

func (s *MCPToolOutputSchema) ToResponseParam() []*pb.ResponseParam {
	defer errorsRecover.PanicHandler()
	params := make([]*pb.ResponseParam, 0)
	params = append(params, &pb.ResponseParam{
		Name:      "result",
		Type:      pb.TypeEnum_OBJECT,
		SubParams: s.Result.ToResponseParam(),
	})
	params = append(params, &pb.ResponseParam{
		Name:      "error",
		Type:      pb.TypeEnum_OBJECT,
		SubParams: s.Error.ToResponseParam(),
	})
	return params
}

// MCPToolCallResult MCP工具调用结果
type MCPToolCallResult struct {
	Content []MCPToolCallResultContent `json:"content"`
	IsError bool                       `json:"isError,omitempty"`
}

func (r *MCPToolCallResult) ToResponseParam() []*pb.ResponseParam {
	contentItem := &MCPToolCallResultContent{}
	params := make([]*pb.ResponseParam, 0)
	params = append(params, &pb.ResponseParam{
		Name:      "content",
		Type:      pb.TypeEnum_ARRAY_OBJECT,
		SubParams: contentItem.ToResponseParam(),
	})
	params = append(params, &pb.ResponseParam{
		Name: "isError",
		Type: pb.TypeEnum_BOOL,
	})
	return params
}

// MCPToolCallResultContent MCP工具调用结果内容，已铺平，基于2025-03-26协议版本
type MCPToolCallResultContent struct {
	Type string `json:"type"`
	/*************************************************************
	 适用于text类型的字段
	*************************************************************/
	// 消息类型的文本
	Text string `json:"text,omitempty"`
	/*************************************************************
	 适用于image或audio类型的字段
	*************************************************************/
	// Base64编码的图像或音频数据
	Data string `json:"data,omitempty"`
	// 图像或音频的 MIME 类型
	MIMEType string `json:"mimeType,omitempty"`
	/*************************************************************
	 适用于resource类型的字段，暂不支持，这里没有展开
	*************************************************************/
	Resource MCPToolCallResultResourceContent `json:"resource,omitempty"`
}

func (c *MCPToolCallResultContent) ToResponseParam() []*pb.ResponseParam {
	params := make([]*pb.ResponseParam, 0)
	params = append(params, &pb.ResponseParam{
		Name: "type",
		Type: pb.TypeEnum_STRING,
	})
	params = append(params, &pb.ResponseParam{
		Name: "text",
		Type: pb.TypeEnum_STRING,
	})
	params = append(params, &pb.ResponseParam{
		Name: "data",
		Type: pb.TypeEnum_STRING,
	})
	params = append(params, &pb.ResponseParam{
		Name: "mimeType",
		Type: pb.TypeEnum_STRING,
	})
	params = append(params, &pb.ResponseParam{
		Name:      "resource",
		Type:      pb.TypeEnum_OBJECT,
		SubParams: c.Resource.ToResponseParam(),
	})
	return params
}

type MCPToolCallResultResourceContent struct {
	// 资源对应的 URI
	URI string `json:"uri"`
	// 资源的 MIME 类型
	MIMEType string `json:"mimeType,omitempty"`
	// 资源对应的文本（不是二进制数据）
	Text string `json:"text,omitempty"`
	// 资源对应的 Base64 编码的二进制数据
	Blob string `json:"blob,omitempty"`
}

func (c *MCPToolCallResultResourceContent) ToResponseParam() []*pb.ResponseParam {
	params := make([]*pb.ResponseParam, 0)
	params = append(params, &pb.ResponseParam{
		Name: "uri",
		Type: pb.TypeEnum_STRING,
	})
	params = append(params, &pb.ResponseParam{
		Name: "mimeType",
		Type: pb.TypeEnum_STRING,
	})
	params = append(params, &pb.ResponseParam{
		Name: "text",
		Type: pb.TypeEnum_STRING,
	})
	params = append(params, &pb.ResponseParam{
		Name: "blob",
		Type: pb.TypeEnum_STRING,
	})
	return params
}

// MCPJsonRPCError MCP协议JsonRPC错误信息
type MCPJsonRPCError struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
}

func (e MCPJsonRPCError) ToResponseParam() []*pb.ResponseParam {
	params := make([]*pb.ResponseParam, 0)
	params = append(params, &pb.ResponseParam{
		Name: "code",
		Type: pb.TypeEnum_INT,
	})
	params = append(params, &pb.ResponseParam{
		Name: "message",
		Type: pb.TypeEnum_STRING,
	})
	return params
}
