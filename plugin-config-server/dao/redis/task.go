package redisdao

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"time"

	"git.code.oa.com/trpc-go/trpc-database/goredis"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-plugin/plugin-config-server/model"
	"github.com/go-redis/redis/v8"
)

type RedisDelayTaskDao interface {
	// AddTask 添加延时任务
	AddTask(ctx context.Context, key string, val interface{}, delay time.Duration) error
	// RemoveTasks 移除延时任务
	RemoveTasks(ctx context.Context, key string, vals []interface{}) error
	// GetReadyTasks 获取已到期的任务
	GetReadyTasks(ctx context.Context, key string, batchSize int64) ([]string, error)
	// GetTaskDelay 获取任务对应的延时
	GetTaskDelay(ctx context.Context, key string, val interface{}) (int64, bool, error)

	// UpdateFailCount 更新失败次数
	UpdateFailCount(ctx context.Context, pluginID string, failCount int) error
	// GetFailCount 获取失败次数，如果不存在，则返回0，如果存在，则返回失败次数。
	GetFailCount(ctx context.Context, pluginID string) (int, error)
	// DeleteFailCount 删除该插件的失败次数记录
	DeleteFailCount(ctx context.Context, pluginID string) error
}

// dao 实现
type redisDelayTaskDao struct {
	redisCli redis.UniversalClient
}

// NewDelayTask 创建一个 redisDelayTaskDao 实例
func NewDelayTask() RedisDelayTaskDao {
	var err error
	if redisDaoSingleton == nil {
		redisDaoSingleton, err = goredis.New(model.PluginRedis, nil)
		if err != nil {
			log.Errorf("init redis error:%+v", err)
			return nil
		}
	}
	return &redisDelayTaskDao{
		redisCli: redisDaoSingleton,
	}
}

// AddTask 添加延时任务
func (d *redisDelayTaskDao) AddTask(ctx context.Context, key string, val interface{}, delay time.Duration) error {
	// 计算执行时间戳(当前时间+延迟时间)
	executeTime := time.Now().Add(delay).Unix()
	// 添加到有序集合
	return d.redisCli.ZAdd(ctx, key, &redis.Z{
		Score:  float64(executeTime),
		Member: val,
	}).Err()
}

// RemoveTasks 移除延时任务
func (d *redisDelayTaskDao) RemoveTasks(ctx context.Context, key string, vals []interface{}) error {
	if _, err := d.redisCli.ZRem(ctx, key, vals).Result(); err != nil {
		return err
	}
	return nil
}

// GetReadyTasks 获取已到期的任务
func (d *redisDelayTaskDao) GetReadyTasks(ctx context.Context, key string, batchSize int64) ([]string, error) {
	// 获取当前时间之前的所有任务
	tasks, err := d.redisCli.ZRangeByScore(ctx, key, &redis.ZRangeBy{
		Min:    "0",
		Max:    strconv.FormatInt(time.Now().Unix(), 10),
		Offset: 0,
		Count:  batchSize,
	}).Result()
	if err != nil {
		return nil, err
	}
	// 如果没有任务直接返回
	if len(tasks) == 0 {
		return nil, nil
	}
	return tasks, nil
}

// GetTaskDelay 获取任务对应的延时
func (d *redisDelayTaskDao) GetTaskDelay(ctx context.Context, key string, val interface{}) (int64, bool, error) {
	delay, err := d.redisCli.ZScore(ctx, key, fmt.Sprintf("%v", val)).Result()
	if errors.Is(err, redis.Nil) {
		// val 对应的 timout 不存在
		return 0, false, nil
	} else if err != nil {
		// 其他错误
		return 0, false, err
	}
	return int64(delay), true, nil
}

// UpdateFailCount 更新失败次数
func (d *redisDelayTaskDao) UpdateFailCount(ctx context.Context, pluginID string, failCount int) error {
	err := d.redisCli.HSet(ctx, model.RedisKeyMCPSyncFail, pluginID, failCount).Err()
	if err != nil {
		log.ErrorContextf(ctx, "UpdateFailCount error:%+v, pluginID:%s, failCount:%d", err, pluginID, failCount)
	}
	return err
}

// GetFailCount 获取失败次数，如果不存在，则返回0，如果存在，则返回失败次数。
func (d *redisDelayTaskDao) GetFailCount(ctx context.Context, pluginID string) (int, error) {
	value, err := d.redisCli.HGet(ctx, model.RedisKeyMCPSyncFail, pluginID).Result()
	if err != nil {
		if errors.Is(err, redis.Nil) {
			return 0, nil
		}
		log.ErrorContextf(ctx, "GetFailCount error:%+v, pluginID:%s", err, pluginID)
		return 0, err
	}
	failCount, err := strconv.Atoi(value)
	if err != nil {
		log.ErrorContextf(ctx, "GetFailCount parse failCount error:%+v, pluginID:%s, value:%s", err, pluginID, value)
		return 0, err
	}
	log.DebugContextf(ctx, "GetFailCount pluginID:%s, failCount:%d", pluginID, failCount)
	return failCount, nil
}

// DeleteFailCount 删除该插件的失败次数记录
func (d *redisDelayTaskDao) DeleteFailCount(ctx context.Context, pluginID string) error {
	err := d.redisCli.HDel(ctx, model.RedisKeyMCPSyncFail, pluginID).Err()
	if err != nil {
		log.ErrorContextf(ctx, "DeleteFailCount error:%+v, pluginID:%s", err, pluginID)
	}
	return err
}
