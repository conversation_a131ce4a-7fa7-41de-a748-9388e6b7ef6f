package metrics

import (
	"context"
	"strconv"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/trpc-go/trpc-go/metrics"
	"git.woa.com/dialogue-platform/bot-plugin/plugin-config-server/model"
)

// ReportMCPSyncMetrics 上报mcp同步工具调用结果的数据指标
// pluginInfo-插件信息  isSuccess-是否调用成功 errCode-错误码 cost-耗时（毫秒）
func ReportMCPSyncMetrics(ctx context.Context, pluginInfo *model.PluginInfo, isSuccess bool, errCode int, cost int64) {
	if pluginInfo == nil {
		log.WarnContextf(ctx, "pluginInfo is nil")
		return
	}
	// 调用失败的计数
	errCountValue := 0
	if !isSuccess {
		errCountValue = 1
	}
	dimensions := getReportDimensions(pluginInfo, errCode)
	mMetrics := []*metrics.Metrics{
		metrics.NewMetrics(MetricsNameTotal, 1, metrics.PolicySUM),
		metrics.NewMetrics(MetricsNameErrorCount, float64(errCountValue), metrics.PolicySUM),
		metrics.NewMetrics(MetricsNameCost, float64(cost), metrics.PolicyHistogram),
	}
	err := metrics.ReportMultiDimensionMetricsX(MonitorNameMCPSync, dimensions, mMetrics)
	if err != nil {
		log.ErrorContextf(ctx, "metrics.Report error: %+v", err)
	}
}

// getReportDimensions 获取上报的维度数据
func getReportDimensions(pluginInfo *model.PluginInfo, errCode int) []*metrics.Dimension {
	return []*metrics.Dimension{
		{
			Name:  DimensionUin,
			Value: pluginInfo.Uin,
		},
		{
			Name:  DimensionPluginID,
			Value: pluginInfo.PluginId,
		},
		{
			Name:  DimensionPluginName,
			Value: pluginInfo.Name,
		},
		{
			Name:  DimensionPluginType,
			Value: strconv.FormatInt(int64(pluginInfo.PluginType), 10),
		},
		{
			Name:  DimensionErrorCode,
			Value: strconv.FormatInt(int64(errCode), 10),
		},
	}
}
