package mcp

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	mcpdao "git.woa.com/dialogue-platform/bot-plugin/plugin-config-server/dao/mcp"
	"git.woa.com/dialogue-platform/bot-plugin/plugin-config-server/logic/metrics"
	"git.woa.com/dialogue-platform/bot-plugin/plugin-config-server/model"
	"git.woa.com/dialogue-platform/bot-plugin/plugin-config-server/util"
	"git.woa.com/dialogue-platform/bot-plugin/plugin-config-server/util/config"
	"github.com/google/uuid"
)

const dbLimit = 1000

// FetchMCPServerTools 获取MCP服务器工具，对外对内同时使用，需要保证错误信息完整且符合对外的标准
func FetchMCPServerTools(ctx context.Context, pluginInfo *model.PluginInfo, isOfficial bool) (tools []*model.ToolInfo,
	err error) {
	start := time.Now()
	defer func() {
		// 上报监控指标数据
		cost := time.Since(start).Milliseconds()
		var errCode int
		isSuccess := true
		if err != nil {
			errCode = errs.Code(err)
			isSuccess = false
		}
		metrics.ReportMCPSyncMetrics(ctx, pluginInfo, isSuccess, errCode, cost)
	}()
	url := pluginInfo.McpServerURL
	// 解析plugInfo中的McpMetaInfo
	var mcpMetaInfo model.McpMetaInfo
	_ = json.Unmarshal([]byte(pluginInfo.McpMetaInfo), &mcpMetaInfo)
	// McpMetaInfo 可能为空，需要兜底
	timeout := int(mcpMetaInfo.Timeout)
	// 如果McpMetaInfo为空，则取配置中定时同步的时间间隔
	if timeout <= 0 {
		timeout = config.GetMainConfig().MCP.SyncTickInterval
	}
	sseReadTimeout := int(mcpMetaInfo.SseReadTimeout)
	// 如果McpMetaInfo为空，则取配置中定时同步的时间间隔
	if sseReadTimeout <= 0 {
		sseReadTimeout = config.GetMainConfig().MCP.SyncTickInterval
	}
	headers := make(map[string]string)
	for _, header := range mcpMetaInfo.Headers {
		headers[header.ParamName] = header.ParamValue
	}
	var toolInfos []*model.MCPToolInfo
	// 同步MCP server工具信息
	mcpCli := mcpdao.New(url)
	if isOfficial {
		toolInfos, err = mcpCli.ListToolsOfficial(ctx, headers, timeout, sseReadTimeout)
	} else {
		toolInfos, err = mcpCli.ListTools(ctx, headers, timeout, sseReadTimeout)
	}
	if err != nil {
		log.WarnContextf(ctx, "MCP|FetchMCPServerTools|plugin not available, pluginID:%s, url: %s, headers: %+v, err: %v",
			pluginInfo.PluginId, url, headers, err)
		return nil, errs.New(errs.Code(model.ErrMCPConnectFail),
			fmt.Sprintf("%s,URL:%s", errs.Msg(model.ErrMCPConnectFail), url))
	}
	log.InfoContextf(ctx, "MCP|FetchMCPServerTools|plugin available, pluginID:%s, url: %s, tools: %s",
		pluginInfo.PluginId, url, util.ToJsonString(toolInfos))
	toolErrMap := make(map[string]error)
	for _, toolInfo := range toolInfos {
		lkeToolInfo, err := toolInfo.ToToolInfo()
		if err != nil {
			log.WarnContextf(ctx, "MCP|FetchMCPServerTools|convert tool info failed, tool: %+v, err: %v",
				toolInfo, err)
			toolErrMap[toolInfo.Name] = err
			continue
			// 如果单个工具解析失败，继续处理其他工具，不能影响整体流程
			// return nil, errs.New(errs.Code(model.ErrMCPToolParamInvalid),
			// 	fmt.Sprintf("%s,tool:%s", errs.Msg(model.ErrMCPToolParamInvalid), util.ToJsonString(toolInfo)))
		}
		lkeToolInfo.ToolId = uuid.NewString()
		lkeToolInfo.PluginId = pluginInfo.PluginId
		lkeToolInfo.Status = model.ToolStatusSuccess
		lkeToolInfo.Module = pluginInfo.Module
		lkeToolInfo.FinancePolicy = pluginInfo.FinancePolicy
		lkeToolInfo.Uin = pluginInfo.Uin
		lkeToolInfo.SubUin = pluginInfo.SubUin
		metaInfoBytes, err := json.Marshal(lkeToolInfo.ToolMeta)
		if err != nil {
			log.WarnContextf(ctx, "MCP|FetchMCPServerTools|marshal tool meta info failed, tool: %+v, err: %v",
				lkeToolInfo, err)
			toolErrMap[toolInfo.Name] = err
			continue
			// 如果单个工具解析失败，继续处理其他工具，不能影响整体流程
			// return nil, model.ErrInternal
		}
		lkeToolInfo.MetaInfo = string(metaInfoBytes)
		tools = append(tools, &lkeToolInfo)
	}
	if len(tools) == 0 {
		// 没有工具信息，直接返回错误
		log.WarnContextf(ctx, "MCP|FetchMCPServerTools|no tool info, url: %s, errMap: %v", url, toolErrMap)
		return nil, errs.New(errs.Code(model.ErrMCPToolParamInvalid),
			fmt.Sprintf("%s, URL:%s", errs.Msg(model.ErrMCPToolParamInvalid), url))
	}
	return tools, nil
}
