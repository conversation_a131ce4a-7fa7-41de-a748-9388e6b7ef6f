package mcp

import (
	"context"
	"errors"
	"fmt"
	"os"
	"sync"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	dbDao "git.woa.com/dialogue-platform/bot-plugin/plugin-config-server/dao"
	redisdao "git.woa.com/dialogue-platform/bot-plugin/plugin-config-server/dao/redis"
	"git.woa.com/dialogue-platform/bot-plugin/plugin-config-server/model"
	"git.woa.com/dialogue-platform/bot-plugin/plugin-config-server/util/config"
	errorsRecover "git.woa.com/dialogue-platform/common/v3/errors"
	pb "git.woa.com/dialogue-platform/lke_proto/pb-protocol/plugin_config_server"
)

var syncSingleton *Sync

// Sync MCP服务器工具同步任务执行器
type Sync struct {
	redisDelayTaskDao redisdao.RedisDelayTaskDao
	dbDao             dbDao.Dao
	initialized       bool
}

// NewSync 新建一个MCP服务器工具同步任务执行器
func NewSync() *Sync {
	if syncSingleton == nil {
		syncSingleton = &Sync{
			redisDelayTaskDao: redisdao.NewDelayTask(),
			dbDao:             dbDao.New(),
			initialized:       false,
		}
	}
	return syncSingleton
}

// getRedisKey 构造MCP服务器工具同步任务中会涉及的Key
func (s *Sync) getRedisKey(suffix string) string {
	if suffix == "" {
		return model.RedisKeyMCPSyncTask
	}
	return fmt.Sprintf("%s:%s", model.RedisKeyMCPSyncTask, suffix)
}

// initialize 初始化MCP服务器工具同步任务
func (s *Sync) initialize(ctx context.Context) {
	if s.redisDelayTaskDao == nil {
		// 防御性检查，如果为空就重新new一个，还是为空就等到下次定时触发再次new
		s.redisDelayTaskDao = redisdao.NewDelayTask()
		if s.redisDelayTaskDao == nil {
			return
		}
	}
	lockKey := s.getRedisKey("INIT")
	hostname, _ := os.Hostname()
	lockValue := fmt.Sprintf("%s-%d-%d", hostname, os.Getpid(), time.Now().UnixMilli())
	redisLockDao, err := redisdao.NewLock(lockKey, lockValue)
	if err != nil {
		log.ErrorContext(ctx, "MCP|initSyncTask|init redis lock failed, err: %v", err)
		return
	}
	// 获取分布式锁，确保只有一个节点在执行同步
	acquired, err := redisLockDao.LockWithRenewal(ctx, 1*time.Minute, 5*time.Second, 10*time.Second)
	if err != nil {
		log.WarnContext(ctx, "MCP|initSyncTask|get redis lock failed, key: %v, err: %v", lockKey, err)
		return
	}
	if !acquired {
		// 获取锁失败，其他节点可能正在执行
		log.InfoContext(ctx, "MCP|initSyncTask|lock acquired by other nodes")
		return
	}
	// 从DB中获取插件信息
	dao := dbDao.New()
	offset := int64(0)
	total := int64(1) // 插件总数会被数据库中获取到的真实数据覆盖
	var pluginList []*model.PluginInfo
	for offset < total {
		total, pluginList, err = dao.GetMCPPluginList(ctx, offset, dbLimit)
		if err != nil {
			log.WarnContext(ctx, "MCP|initSyncTask|get plugin list from db failed, error: %v", err)
			// 释放分布式锁
			_ = redisLockDao.Unlock(ctx)
			return
		}
		for _, pluginInfo := range pluginList {
			if pluginInfo.Id > offset {
				offset = pluginInfo.Id
			}
			// 设置下次检测时间，不存在就添加，存在则忽略
			_, exist, err := s.redisDelayTaskDao.GetTaskDelay(ctx, model.RedisKeyMCPSyncTask, pluginInfo.PluginId)
			if err != nil {
				log.WarnContext(ctx, "MCP|initSyncTask|check next check time failed, err: %v", err)
				continue
			}
			if !exist {
				nextCheckTime := time.Duration(config.GetMainConfig().MCP.MCPSyncInterval) * time.Second
				if pluginInfo.Status == model.PluginStatusFailure {
					nextCheckTime = time.Duration(config.GetMainConfig().MCP.MCPDegradeSyncInterval) * time.Second
				}
				err = s.redisDelayTaskDao.AddTask(ctx, model.RedisKeyMCPSyncTask, pluginInfo.PluginId, nextCheckTime)
				if err != nil {
					log.WarnContext(ctx, "MCP|initSyncTask|set next check time failed, err: %v", err)
					continue
				}
			}
		}
	}
	// 释放分布式锁
	_ = redisLockDao.Unlock(ctx)
}

// Run 运行MCP服务器工具同步任务
func (s *Sync) Run(ctx context.Context) {
	syncTimeTicker := time.NewTicker(time.Duration(config.GetMainConfig().MCP.SyncTickInterval) * time.Second)
	defer syncTimeTicker.Stop()
	loadDBTimeTicker := time.NewTicker(time.Duration(config.GetMainConfig().MCP.LoadDBTickInterval) * time.Hour)
	defer loadDBTimeTicker.Stop()
	// 检查同步任务是否有初始化
	if !s.initialized {
		s.initialize(ctx)
		s.initialized = true
	}
	dao := dbDao.New()
	for {
		select {
		case <-ctx.Done():
			return
		case <-loadDBTimeTicker.C:
			// 从DB中加载数据，避免添加redis失败后没有再次尝试
			// initialize函数中已经有分布式锁，不需要额外再加
			s.initialize(ctx)
			if !s.initialized {
				s.initialized = true
			}
		case <-syncTimeTicker.C:
			if s.redisDelayTaskDao == nil {
				// 防御性检查，如果为空就重新new一个，还是为空就等到下次定时触发再次new
				s.redisDelayTaskDao = redisdao.NewDelayTask()
				if s.redisDelayTaskDao == nil {
					continue
				}
			}
			lockKey := s.getRedisKey("SYNC")
			hostname, _ := os.Hostname()
			lockValue := fmt.Sprintf("%s-%d-%d", hostname, os.Getpid(), time.Now().UnixMilli())
			redisLockDao, err := redisdao.NewLock(lockKey, lockValue)
			if err != nil {
				log.ErrorContext(ctx, "MCP|SyncTask|init redis lock failed, err: %v", lockKey, err)
				continue
			}
			// 获取分布式锁
			acquired, err := redisLockDao.LockWithRenewal(ctx, 1*time.Minute, 5*time.Second, 10*time.Second)
			if err != nil {
				log.WarnContext(ctx, "MCP|SyncTask|get redis lock failed, key: %v, err: %v",
					lockKey, err)
				continue
			}
			if !acquired {
				// 获取锁失败，其他节点可能正在执行
				log.InfoContext(ctx, "MCP|SyncTask|lock acquired by other nodes")
				continue
			}
			// 获取同步任务
			pluginIds, err := s.redisDelayTaskDao.GetReadyTasks(ctx, model.RedisKeyMCPSyncTask,
				int64(config.GetMainConfig().MCP.SyncBatchSize))
			if err != nil {
				log.WarnContextf(ctx, "MCP|SyncTask|get ready tasks failed, key: %v, err: %v",
					model.RedisKeyMCPSyncTask, err)
				// 释放分布式锁
				_ = redisLockDao.Unlock(ctx)
				continue
			}
			if len(pluginIds) == 0 {
				// 释放分布式锁
				_ = redisLockDao.Unlock(ctx)
				continue
			}
			deletedPluginIds := make([]interface{}, 0)
			deletedPluginIdsPtr := &deletedPluginIds
			wg := sync.WaitGroup{}
			for _, pluginId := range pluginIds {
				wg.Add(1)
				go func(ctx context.Context, pluginId string, wgPtr *sync.WaitGroup) {
					defer errorsRecover.PanicHandler()
					defer wgPtr.Done()
					pluginInfo, err := dao.GetPlugin(ctx, pluginId)
					if errors.Is(err, model.ErrDataNotExist) {
						// 插件已经被删除了，需要移除任务
						log.InfoContextf(ctx, "MCP|SyncTask|plugin has been deleted, pluginId: %v", pluginId)
						*deletedPluginIdsPtr = append(*deletedPluginIdsPtr, pluginId)
						return
					} else if err != nil {
						log.WarnContextf(ctx, "MCP|SyncTask|get plugin info failed, pluginId: %s, err: %v",
							pluginId, err)
						return
					}
					// 兜底非MCP插件误入的场景
					if pluginInfo.CreateType != int32(pb.CreateTypeEnum_MCP) {
						// 非MCP插件需要移除任务
						log.InfoContextf(ctx, "MCP|SyncTask|plugin is not mcp, pluginId: %v", pluginId)
						*deletedPluginIdsPtr = append(*deletedPluginIdsPtr, pluginId)
						return
					}
					// 获取该插件同步失败次数
					failCount, err := s.redisDelayTaskDao.GetFailCount(ctx, pluginId)
					if err != nil {
						return
					}
					// 同步插件
					newCtx, cancel := context.WithTimeout(ctx, time.Duration(config.GetMainConfig().MCP.SyncTickInterval)*time.Second)
					defer cancel()
					if !pluginInfo.IsCustomPlugin() {
						pluginInfo.Tools, err = FetchMCPServerTools(newCtx, pluginInfo, true)
					} else {
						pluginInfo.Tools, err = FetchMCPServerTools(newCtx, pluginInfo, false)
					}
					if err != nil {
						// MCP服务器不可用
						failCount++
						log.InfoContextf(ctx, "MCP|SyncTask|fetch MCP tools failed, error: %v, pluginID:%s, failCount:%d",
							err, pluginId, failCount)
						if failCount >= config.GetMainConfig().MCP.MCPDegradeThreshold {
							// 超过阈值设置为不可用
							log.InfoContextf(ctx, "MCP|SyncTask|fetch MCP tools failCount over threshold, pluginID:%s", pluginId)
							s.ProcessOfflineMCPServer(ctx, pluginInfo, err.Error())
						} else {
							// 没超过阈值则只更新下次检测时间，不更新插件信息
							err1 := s.redisDelayTaskDao.AddTask(ctx, model.RedisKeyMCPSyncTask, pluginInfo.PluginId,
								time.Duration(config.GetMainConfig().MCP.MCPSyncInterval)*time.Second)
							if err1 != nil {
								log.WarnContext(ctx, "MCP|SyncTask|set next check time failed,pluginId: %s, err: %v",
									pluginInfo.PluginId, err1)
							}
						}
						// 更新失败次数，有异常不影响，下次会再更新
						_ = s.redisDelayTaskDao.UpdateFailCount(ctx, pluginId, failCount)
					} else {
						// MCP服务器可用
						log.InfoContextf(ctx, "MCP|SyncTask|fetch MCP tools success, pluginId: %s, "+
							"tools_len: %d", pluginInfo.PluginId, len(pluginInfo.Tools))
						s.ProcessOnlineMCPServer(ctx, pluginInfo)
						if failCount > 0 {
							// 可用则删除失败次数
							_ = s.redisDelayTaskDao.DeleteFailCount(ctx, pluginId)
						}
					}
				}(ctx, pluginId, &wg)
			}
			wg.Wait()
			if len(*deletedPluginIdsPtr) > 0 {
				err := s.redisDelayTaskDao.RemoveTasks(ctx, model.RedisKeyMCPSyncTask, deletedPluginIds)
				if err != nil {
					log.WarnContextf(ctx, "MCP|SyncTask|remove tasks failed, pluginIds: %v, err: %v",
						deletedPluginIds, err)
					// 释放分布式锁
					_ = redisLockDao.Unlock(ctx)
					continue
				}
			}
			// 释放分布式锁
			_ = redisLockDao.Unlock(ctx)
		}
	}
}

// ProcessOfflineMCPServer 处理不可用状态的场景
func (s *Sync) ProcessOfflineMCPServer(ctx context.Context, pluginInfo *model.PluginInfo, errMsg string) {
	// 更新不可用状态的MCP服务器的下次检测时间
	err := s.redisDelayTaskDao.AddTask(ctx, model.RedisKeyMCPSyncTask, pluginInfo.PluginId,
		time.Duration(config.GetMainConfig().MCP.MCPDegradeSyncInterval)*time.Second)
	if err != nil {
		log.WarnContext(ctx, "MCP|ProcessOfflineMCPServer|set downgraded next check time failed, "+
			"pluginId: %s, err: %v", pluginInfo.PluginId, err)
		return
	}
	// 更新插件信息
	pluginInfo.Status = model.PluginStatusFailure
	pluginInfo.FailReason = errMsg
	dbErr := s.dbDao.UpdateMCPPlugin(ctx, pluginInfo)
	if dbErr != nil {
		log.WarnContextf(ctx, "MCP|processOfflineMCPServer|update plugin failed, err: %v", dbErr)
		return
	}
}

// ProcessOnlineMCPServer 处理可用状态的场景
func (s *Sync) ProcessOnlineMCPServer(ctx context.Context, pluginInfo *model.PluginInfo) {
	// 更新可用状态的MCP服务器的下次检测时间
	err := s.redisDelayTaskDao.AddTask(ctx, model.RedisKeyMCPSyncTask, pluginInfo.PluginId,
		time.Duration(config.GetMainConfig().MCP.MCPSyncInterval)*time.Second)
	if err != nil {
		log.WarnContext(ctx, "MCP|ProcessOnlineMCPServer|set next check time failed, "+
			"pluginId: %s, err: %v", pluginInfo.PluginId, err)
		return
	}
	// 更新插件信息
	pluginInfo.Status = model.PluginStatusSuccess
	pluginInfo.FailReason = ""
	dbErr := s.dbDao.UpdateMCPPlugin(ctx, pluginInfo)
	if dbErr != nil {
		log.WarnContextf(ctx, "MCP|processOnlineMCPServer|update plugin failed, err: %v", dbErr)
	}
}
