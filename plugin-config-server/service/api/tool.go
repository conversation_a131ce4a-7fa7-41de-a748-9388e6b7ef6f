package api

import (
	"context"
	"encoding/json"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-plugin/plugin-config-server/model"
	"git.woa.com/dialogue-platform/bot-plugin/plugin-config-server/util"
	"git.woa.com/dialogue-platform/bot-plugin/plugin-config-server/util/config"
	pb "git.woa.com/dialogue-platform/lke_proto/pb-protocol/plugin_config_server"
)

// DescribeTool 获取工具详情
func (s *Service) DescribeTool(ctx context.Context, req *pb.DescribeToolReq) (*pb.DescribeToolRsp, error) {
	log.InfoContextf(ctx, "DescribeTool Req:%+v", req)
	rsp := new(pb.DescribeToolRsp)
	if req.GetPluginId() == "" || req.GetToolId() == "" {
		log.WarnContextf(ctx, "plugin_id or tool_id is empty")
		return nil, model.ErrParams
	}
	_, pluginList, err := s.dao.GetPluginList(ctx, &model.PluginReq{Ids: []string{req.GetPluginId()}})
	if err != nil {
		return nil, err
	}
	if len(pluginList) == 0 {
		log.WarnContextf(ctx, "plugin not exit")
		return nil, model.ErrDataNotExist
	}
	plugin := pluginList[0]
	var tool *model.ToolInfo
	for _, v := range plugin.Tools {
		if v.ToolId == req.GetToolId() {
			tool = v
			break
		}
	}
	if tool == nil {
		log.WarnContextf(ctx, "tool not exist")
		return nil, model.ErrDataNotExist
	}

	if tool.Example != "" {
		var example pb.ToolExample
		if err := json.Unmarshal([]byte(tool.Example), &example); err != nil {
			log.ErrorContextf(ctx, "Unmarshal err:%v", err)
			return nil, model.ErrInternal
		}
		rsp.Example = &example
	}
	rsp.PluginId = tool.PluginId
	rsp.ToolId = tool.ToolId
	rsp.Name = tool.Name
	rsp.Desc = tool.Desc
	rsp.Url = tool.ToolMeta.Url
	rsp.Path = tool.ToolMeta.Path
	rsp.Method = tool.ToolMeta.Method
	rsp.Header = tool.ToolMeta.Header
	rsp.Query = tool.ToolMeta.Query
	rsp.Body = tool.ToolMeta.Body
	rsp.Inputs = tool.ToolMeta.Inputs
	rsp.Outputs = model.AdaptOutputsDefaultValue(tool.ToolMeta.Outputs)
	rsp.AuthType = tool.AuthType
	rsp.AuthInfo = tool.AuthInfo
	rsp.Code = tool.ToolMeta.Code
	rsp.CallingMethod = tool.ToolMeta.CallingMethod.String()
	rsp.CreateType = pb.CreateTypeEnum(plugin.CreateType)
	rsp.PluginName = plugin.Name
	rsp.PluginType = pb.PluginTypeEnum(plugin.PluginType)
	if plugin.CreateType == int32(pb.CreateTypeEnum_MCP) {
		mcpMetaInfo, err := plugin.ConvertToMcpMetaInfo(ctx)
		if err != nil {
			return nil, model.ErrInternal
		}
		headers := make(map[string]string)
		for _, header := range mcpMetaInfo.Headers {
			headers[header.ParamName] = header.ParamValue
		}
		rsp.MCPServer = &pb.MCPServerInfo{
			McpServerUrl:   plugin.McpServerURL,
			Headers:        headers,
			Timeout:        mcpMetaInfo.Timeout,
			SseReadTimeout: mcpMetaInfo.SseReadTimeout,
		}
	}

	return rsp, nil
}

// ListTools 获取工具列表，内部api接口不需要鉴权，可以查看所有工具
func (s *Service) ListTools(ctx context.Context, req *pb.ListToolsReq) (*pb.ListToolsRsp, error) {
	log.InfoContextf(ctx, "ListTools Req:%+v", req)
	rsp := new(pb.ListToolsRsp)
	if len(req.GetToolIds()) == 0 {
		log.WarnContextf(ctx, "tool_ids is empty")
		return nil, model.ErrParams
	}
	tools, err := s.dao.GetToolList(ctx, req.GetToolIds())
	if err != nil {
		return nil, model.ErrInternal
	}
	for _, v := range tools {
		tool, err := v.ConvertToPbTool(model.DetailSwitchOff)
		if err != nil {
			log.ErrorContextf(ctx, "convertTool err:%v, ignore", err)
			return nil, model.ErrInternal
		}
		rsp.Tools = append(rsp.Tools, tool)
	}
	return rsp, nil
}

// CheckTool 校验工具
func (s *Service) CheckTool(ctx context.Context, req *pb.CheckToolReq) (*pb.CheckToolRsp, error) {
	log.InfoContextf(ctx, "CheckTool Req:%+v", req)
	rsp := new(pb.CheckToolRsp)
	if req.GetCreateType() == pb.CreateTypeEnum_CODE { // 代码插件
		// 执行代码
		res, err := s.dao.RunCode(ctx, config.GetMainConfig().OfficialConfig.Uin, req.GetCode(), req.GetBodyValue())
		if err != nil {
			log.WarnContextf(ctx, "RunCode err:%v", err)
			return nil, model.ErrCallApi
		}
		rsp.RawResult = res
	} else { // http插件
		// http请求
		header, query, body := make(map[string]any), make(map[string]any), make(map[string]any)
		if req.GetHeaderValue() != "" {
			if err := json.Unmarshal([]byte(req.GetHeaderValue()), &header); err != nil {
				log.WarnContextf(ctx, "Unmarshal header err:%v", err)
				return nil, model.ErrParams
			}
		}
		if req.GetQueryValue() != "" {
			if err := json.Unmarshal([]byte(req.GetQueryValue()), &query); err != nil {
				log.WarnContextf(ctx, "Unmarshal query err:%v", err)
				return nil, model.ErrParams
			}
		}
		if req.GetBodyValue() != "" {
			if err := json.Unmarshal([]byte(req.GetBodyValue()), &body); err != nil {
				log.WarnContextf(ctx, "Unmarshal body err:%v", err)
				return nil, model.ErrParams
			}
		}
		// 处理鉴权信息
		if req.GetAuthType() == pb.AuthTypeEnum_API_KEY {
			if req.GetAuthInfo().GetKeyLocation() == pb.AuthInfo_HEADER {
				header[req.GetAuthInfo().GetKeyParamName()] = req.GetAuthInfo().GetKeyParamValue()
			}
			if req.GetAuthInfo().GetKeyLocation() == pb.AuthInfo_QUERY {
				query[req.GetAuthInfo().GetKeyParamName()] = req.GetAuthInfo().GetKeyParamValue()
			}
		}
		// 请求
		res, err := util.CallApi(ctx, req.GetUrl()+req.GetPath(), req.GetMethod(), header, query, body)
		if err != nil {
			log.WarnContextf(ctx, "callApi err:%v", err)
			return nil, model.ErrCallApi
		}
		rsp.RawResult = string(res)
	}
	// 解析结果
	var err error
	rspValue := make(map[string]any)
	rspArrayValue := make([]any, 0)
	if err = json.Unmarshal([]byte(rsp.RawResult), &rspValue); err == nil {
		rsp.Result = util.ToJsonString(util.ParseOutputValue(ctx, rspValue, req.GetOutputs()))
	} else if err = json.Unmarshal([]byte(rsp.RawResult), &rspArrayValue); err == nil {
		rsp.Result = util.ToJsonString(util.ParseOutputArrayValue(ctx, rspArrayValue, req.GetOutputs()))
	}
	if err != nil {
		log.WarnContextf(ctx, "Unmarshal err:%v, result:%s", err, rsp.RawResult)
	}
	return rsp, nil
}
