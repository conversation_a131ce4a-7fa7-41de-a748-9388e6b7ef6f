package api

import (
	"context"
	"encoding/json"
	"fmt"

	"git.code.oa.com/trpc-go/trpc-go/log"
	pb "git.woa.com/dialogue-platform/lke_proto/pb-protocol/plugin_config_server"
)

// FreshStreamToolData 刷流式插件数据
func (s *Service) FreshStreamToolData(ctx context.Context, req *pb.FreshStreamToolDataReq) (*pb.FreshStreamToolDataRsp, error) {
	log.InfoContextf(ctx, "FreshStreamToolData Req:%+v", req)
	tools, err := s.dao.GetToolList(ctx, req.GetToolIds())
	if err != nil {
		log.ErrorContextf(ctx, "FreshStreamToolData.GetToolList error: %v", err)
		return nil, err
	}
	rsp := &pb.FreshStreamToolDataRsp{}
	for _, toolItem := range tools {
		if pluginItem, err := s.dao.GetPlugin(ctx, toolItem.PluginId); err != nil {
			log.ErrorContextf(ctx, "FreshStreamToolData.GetPlugin error: %v, toolId: %s, PluginId: %s",
				err, toolItem.ToolId, toolItem.PluginId)
			rsp.FailedTools = append(rsp.FailedTools, &pb.FreshStreamToolDataRsp_FailedTool{
				ToolId: toolItem.ToolId,
				Reason: fmt.Sprintf("GetPlugin error: %v", err.Error()),
			})
		} else {
			for _, pluToolItem := range pluginItem.Tools {
				if pluToolItem.ToolId == toolItem.ToolId {
					pluToolItem.ToolMeta.CallingMethod = pb.CallingMethodTypeEnum_STREAMING
					pluToolItem.MetaInfo = pluToolItem.ToolMeta.ToString()
				}
			}
			pluginItem.Version = pluginItem.Version + 1
			if err := s.dao.ModifyPlugin(ctx, pluginItem); err != nil {
				log.ErrorContextf(ctx, "FreshStreamToolData.ModifyPlugin error: %v, toolId: %s, PluginId: %s",
					err, toolItem.ToolId, toolItem.PluginId)
				rsp.FailedTools = append(rsp.FailedTools, &pb.FreshStreamToolDataRsp_FailedTool{
					ToolId: toolItem.ToolId,
					Reason: fmt.Sprintf("ModifyPlugin error: %v", err.Error()),
				})
			} else {
				data, _ := json.Marshal(pluginItem)
				log.InfoContextf(ctx, "FreshStreamToolData.ModifyPlugin success, new plugin %s", data)
			}
		}
	}
	return rsp, nil
}
