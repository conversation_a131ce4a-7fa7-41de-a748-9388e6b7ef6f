package service

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"sync"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-plugin/plugin-config-server/model"
	"git.woa.com/dialogue-platform/bot-plugin/plugin-config-server/util"
	"git.woa.com/dialogue-platform/bot-plugin/plugin-config-server/util/config"
	"git.woa.com/dialogue-platform/bot-plugin/plugin-config-server/util/types"
	"git.woa.com/dialogue-platform/common/v3/errors"
	pb "git.woa.com/dialogue-platform/lke_proto/pb-protocol/plugin_config_server"
)

// ListTools 获取工具列表
func (s *Service) ListTools(ctx context.Context, req *pb.ListToolsReq) (*pb.ListToolsRsp, error) {
	log.InfoContextf(ctx, "ListTools Req:%+v", req)
	rsp := new(pb.ListToolsRsp)
	uin, _ := util.GetUinAndSubUin(ctx)
	if uin == "" {
		log.WarnContextf(ctx, "uin or sub_uin is empty")
		return nil, model.ErrParams
	}
	if len(req.GetToolIds()) == 0 {
		log.WarnContextf(ctx, "tool_ids is empty")
		return nil, model.ErrParams
	}
	tools, err := s.dao.GetToolList(ctx, req.GetToolIds())
	if err != nil {
		return nil, err
	}
	for _, v := range tools {
		// 如果工具不属于自己，也不属于官方，则报错，没有权限
		if v.Uin != uin && v.Uin != config.GetMainConfig().OfficialConfig.Uin {
			log.WarnContextf(ctx, "tool_id:%v is not belong to uin:%v", v.ToolId, uin)
			return rsp, model.ErrNoPermission
		}
		tool, err := v.ConvertToPbTool(model.DetailSwitchOff)
		if err != nil {
			log.ErrorContextf(ctx, "ConvertToPbTool err:%v", err)
			return nil, model.ErrInternal
		}
		rsp.Tools = append(rsp.Tools, tool)
	}
	return rsp, nil
}

// ListToolRefs 获取工具引用列表
func (s *Service) ListToolRefs(ctx context.Context, req *pb.ListToolRefsReq) (*pb.ListToolRefsRsp, error) {
	log.InfoContextf(ctx, "ListToolRefs Req:%+v", req)
	rsp := new(pb.ListToolRefsRsp)
	uin, _ := util.GetUinAndSubUin(ctx)
	if uin == "" {
		log.WarnContextf(ctx, "uin or sub_uin is empty")
		return nil, model.ErrParams
	}
	var agents, workflows []*model.PluginRefInfo
	var agentErr, workflowErr error
	wg := sync.WaitGroup{}
	wg.Add(2)
	go func() { // 获取Agent引用
		defer errors.PanicHandler()
		defer wg.Done()
		agents, agentErr = s.dao.GetAgentsByPluginRef(ctx, uin, req.GetPluginId())
		if agentErr != nil {
			return
		}
	}()
	go func() { // 获取工作流引用
		defer errors.PanicHandler()
		defer wg.Done()
		workflows, workflowErr = s.dao.GetWorkflowsByPluginRef(ctx, uin, req.GetPluginId())
		if workflowErr != nil {
			return
		}
	}()
	wg.Wait()
	if agentErr != nil || workflowErr != nil {
		return nil, model.ErrInternal
	}
	refs := append(agents, workflows...)
	if len(refs) == 0 {
		return rsp, nil
	}
	// 获取app名称
	robotIDs := make([]string, 0, len(refs))
	for _, ref := range refs {
		robotIDs = append(robotIDs, ref.AppId)
	}
	uniqueRobotIDs := types.Unique(robotIDs)
	appMap, err := s.dao.GetAppNames(ctx, uniqueRobotIDs)
	if err != nil {
		log.ErrorContextf(ctx, "GetAppNames err:%v", err)
		return nil, model.ErrInternal
	}
	for i, ref := range refs {
		if i >= config.GetMainConfig().ToolRefLimit {
			break
		}
		toolRef := &pb.ToolRef{
			PluginId:     ref.PluginId,
			AppId:        ref.AppId,
			AppName:      ref.AppName,
			WorkflowId:   ref.WorkflowId,
			WorkflowName: ref.WorkflowName,
			RefType:      ref.RefType,
		}
		if name, ok := appMap[toolRef.AppId]; ok && name != "" {
			toolRef.AppName = name
		} else {
			// 取不到应用名称不需要处理，名称返回空即可
			log.ErrorContextf(ctx, "appName is empty, appId:%s", toolRef.AppId)
		}
		rsp.ToolRefs = append(rsp.ToolRefs, toolRef)
	}
	return rsp, nil
}

// CheckTool 工具校验
func (s *Service) CheckTool(ctx context.Context, req *pb.CheckToolReq) (*pb.CheckToolRsp, error) {
	log.InfoContextf(ctx, "CheckTool Req:%+v", req)
	rsp := new(pb.CheckToolRsp)
	uin, _ := util.GetUinAndSubUin(ctx)
	if req.GetCreateType() == pb.CreateTypeEnum_CODE { // 代码插件
		// 前端按base64传代码内容，需要做decode
		codeByte, err := base64.StdEncoding.DecodeString(req.GetCode())
		if err != nil {
			log.WarnContextf(ctx, "base64 decode error:%+v", err)
			return nil, model.ErrInternal
		}
		// 执行代码
		res, err := s.dao.RunCode(ctx, uin, string(codeByte), req.GetBodyValue())
		if err != nil {
			log.WarnContextf(ctx, "RunCode err:%v", err)
			return nil, model.ErrCallApi
		}
		rsp.RawResult = res
	} else { // http插件
		// http请求
		header, query, body := make(map[string]any), make(map[string]any), make(map[string]any)
		if req.GetHeaderValue() != "" {
			if err := json.Unmarshal([]byte(req.GetHeaderValue()), &header); err != nil {
				log.WarnContextf(ctx, "Unmarshal header err:%v", err)
				return nil, model.ErrParams
			}
		}
		if req.GetQueryValue() != "" {
			if err := json.Unmarshal([]byte(req.GetQueryValue()), &query); err != nil {
				log.WarnContextf(ctx, "Unmarshal query err:%v", err)
				return nil, model.ErrParams
			}
		}
		if req.GetBodyValue() != "" {
			if err := json.Unmarshal([]byte(req.GetBodyValue()), &body); err != nil {
				log.WarnContextf(ctx, "Unmarshal body err:%v", err)
				return nil, model.ErrParams
			}
		}
		// 处理鉴权信息
		if req.GetAuthType() == pb.AuthTypeEnum_API_KEY {
			if req.GetAuthInfo().GetKeyLocation() == pb.AuthInfo_HEADER {
				header[req.GetAuthInfo().GetKeyParamName()] = req.GetAuthInfo().GetKeyParamValue()
			}
			if req.GetAuthInfo().GetKeyLocation() == pb.AuthInfo_QUERY {
				query[req.GetAuthInfo().GetKeyParamName()] = req.GetAuthInfo().GetKeyParamValue()
			}
		}
		// 请求
		if req.CallingMethod == pb.CallingMethodTypeEnum_STREAMING.String() { // 流式工具
			res, err := util.CallStreamApi(ctx, req.GetUrl()+req.GetPath(), req.GetMethod(),
				header, query, body, req.GetOutputs())
			if err != nil {
				log.WarnContextf(ctx, "CallStreamApi err:%v", err)
				return nil, model.ErrCallApi
			}
			rsp.RawResult = string(res)
		} else { // 非流式工具
			res, err := util.CallApi(ctx, req.GetUrl()+req.GetPath(), req.GetMethod(), header, query, body)
			if err != nil {
				log.WarnContextf(ctx, "callApi err:%v", err)
				return nil, model.ErrCallApi
			}
			rsp.RawResult = string(res)
		}
	}
	// 解析结果
	var err error
	rspValue := make(map[string]any)
	rspArrayValue := make([]any, 0)
	if err = json.Unmarshal([]byte(rsp.RawResult), &rspValue); err == nil {
		rsp.Result = util.ToJsonString(util.ParseOutputValue(ctx, rspValue, req.GetOutputs()))
	} else if err = json.Unmarshal([]byte(rsp.RawResult), &rspArrayValue); err == nil {
		rsp.Result = util.ToJsonString(util.ParseOutputArrayValue(ctx, rspArrayValue, req.GetOutputs()))
	}
	if err != nil {
		log.WarnContextf(ctx, "Unmarshal err:%v, result:%s", err, rsp.RawResult)
	}
	return rsp, nil
}
