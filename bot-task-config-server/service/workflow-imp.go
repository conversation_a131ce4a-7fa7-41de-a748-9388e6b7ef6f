// bot-task-config-server
//
// @(#)workflow-imp.go  星期四, 九月 26, 2024
// Copyright(c) 2024, mikeljiang@Tencent. All rights reserved.

package service

import (
	"context"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	pdlLogic "git.woa.com/dialogue-platform/bot-config/bot-task-config-server/logic/pdl"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/logic/workflow"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/errors"
	"git.woa.com/dialogue-platform/go-comm/clues"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	"github.com/asaskevich/govalidator"
)

// CreateWorkflow 新建Workflow
func (imp TaskConfigImp) CreateWorkflow(ctx context.Context, req *KEP_WF.CreateWorkflowReq) (
	*KEP_WF.CreateWorkflowRsp, error) {
	sid := util.RequestID(ctx)
	log.InfoContextf(ctx, "CreateWorkflow start sid|req:|%s|%+v", sid, req)
	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 业务处理
		resp, err := workflow.CreateWorkflow(ctx, req)
		log.InfoContextf(ctx, "CreateWorkflow|RESP|%s|%v|%v", sid, resp, err)
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.WarnContextf(ctx, "CreateWorkflow|RESP|%s|%v", sid, err0)
	return nil, err0
}

// SaveWorkflow 保存Workflow
func (imp TaskConfigImp) SaveWorkflow(ctx context.Context, req *KEP_WF.SaveWorkflowReq) (*KEP_WF.SaveWorkflowRsp, error) {
	sid := util.RequestID(ctx)
	log.InfoContextf(ctx, "SaveWorkflow|REQ|%s|%v", sid, req)

	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		resp, err := workflow.SaveWorkflow(ctx, req)
		log.InfoContextf(ctx, "SaveWorkflow|RESP|%s|%v|%v", sid, resp, err)
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.Warnf("SaveWorkflow|RESP|%s|%v", sid, err0)
	return nil, err0
}

// DeleteWorkflow 删除Workflow
func (imp TaskConfigImp) DeleteWorkflow(ctx context.Context, req *KEP_WF.DeleteWorkflowReq) (
	*KEP_WF.DeleteWorkflowRsp, error) {
	sid := util.RequestID(ctx)
	log.InfoContextf(ctx, "DeleteWorkflow start sid|req:|%s|%+v", sid, req)
	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 业务处理
		resp, err := workflow.DeleteWorkflow(ctx, req)
		log.InfoContextf(ctx, "DeleteWorkflow|RESP|%s|%v|%v", sid, resp, err)
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.WarnContextf(ctx, "DeleteWorkflow|RESP|%s|%v", sid, err0)
	return nil, err0
}

// ListWorkflow 工作流列表
func (imp TaskConfigImp) ListWorkflow(ctx context.Context, req *KEP_WF.ListWorkflowReq) (*KEP_WF.ListWorkflowRsp, error) {
	sid := util.RequestID(ctx)
	log.Infof("ListWorkflow|REQ|%s|%v", sid, req)

	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 业务处理
		resp, err := workflow.ListWorkflow(ctx, req)
		log.Infof("ListWorkflow|RESP|%s|%v|%v", sid, resp, err)
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.Warnf("ListWorkflow|RESP|%s|%v", sid, err0)
	return nil, err0
}

// ListWorkflowInner 获取工作流列表（内部使用）
func (imp TaskConfigImp) ListWorkflowInner(ctx context.Context,
	req *KEP_WF.ListWorkflowInnerReq) (*KEP_WF.ListWorkflowInnerRsp, error) {
	sid := util.RequestID(ctx)
	log.Infof("ListWorkflowInner|REQ|%s|%v", sid, req)

	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 业务处理
		resp, err := workflow.ListWorkflowInner(ctx, req)
		log.Infof("ListWorkflowInner|RESP|%s|%v|%v", sid, resp, err)
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.Warnf("ListWorkflowInner|RESP|%s|%v", sid, err0)
	return nil, err0
}

// ListNodeInfo 节点列表
func (imp TaskConfigImp) ListNodeInfo(ctx context.Context,
	req *KEP_WF.ListNodeInfoReq) (*KEP_WF.ListNodeInfoRsp, error) {
	sid := util.RequestID(ctx)
	log.Infof("ListNodeInfo|REQ|%s|%v", sid, req)

	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 业务处理
		resp, err := workflow.ListNodeInfo(ctx, req)
		log.Infof("ListNodeInfo|RESP|%s|%v|%v", sid, resp, err)
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.Warnf("ListNodeInfo|RESP|%s|%v", sid, err0)
	return nil, err0
}

// GetWorkflowDetail 获取某个工作流的详细信息
func (imp TaskConfigImp) GetWorkflowDetail(ctx context.Context, req *KEP_WF.GetWorkflowDetailReq) (*KEP_WF.GetWorkflowDetailResp, error) {
	log.InfoContextf(ctx, "GetWorkflowDetail|REQ:%+v", req)

	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 业务处理
		resp, err := workflow.GetWorkflowDetail(ctx, req)
		log.InfoContextf(ctx, "GetWorkflowDetail|RESP|%v|%v", resp, err)
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.WarnContextf(ctx, "GetWorkflowDetail|BadRequest|%v", err0)
	return nil, err0
}

// SwitchWorkflowState 切换工作流状态
func (imp TaskConfigImp) SwitchWorkflowState(ctx context.Context, req *KEP_WF.SwitchWorkflowStateReq) (*KEP_WF.SwitchWorkflowStateRsp, error) {
	sid := util.RequestID(ctx)
	log.Infof("SwitchWorkflowState|REQ|%s|%v", sid, req)

	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 业务处理
		resp, err := workflow.SwitchWorkflowState(ctx, req)
		log.Infof("SwitchWorkflowState|RESP|%s|%v|%v", sid, resp, err)
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.Warnf("SwitchWorkflowState|RESP|%s|%v", sid, err0)
	return nil, err0
}

// AddWorkflowFeedback 添加工作流反馈
func (imp TaskConfigImp) AddWorkflowFeedback(ctx context.Context,
	req *KEP_WF.AddFlowFeedbackReq) (*KEP_WF.AddFlowFeedbackRsp, error) {
	sid := util.RequestID(ctx)
	log.Infof("AddWorkflowFeedback|REQ|%s|%v", sid, req)

	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 业务处理
		resp, err := workflow.AddWorkflowFeedback(ctx, req)
		log.Infof("AddWorkflowFeedback|RESP|%s|%v|%v", sid, resp, err)
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.Warnf("AddWorkflowFeedback|RESP|%s|%v", sid, err0)
	return nil, err0
}

// ListWorkflowFeedback 获取反馈列表
func (imp TaskConfigImp) ListWorkflowFeedback(ctx context.Context,
	req *KEP_WF.ListFlowFeedbackReq) (*KEP_WF.ListFlowFeedbackRsp, error) {
	sid := util.RequestID(ctx)
	log.Infof("ListWorkflowFeedback|REQ|%s|%v", sid, req)

	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 业务处理
		resp, err := workflow.ListWorkflowFeedback(ctx, req)
		log.Infof("ListWorkflowFeedback|RESP|%s|%v|%v", sid, resp, err)
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.Warnf("ListWorkflowFeedback|RESP|%s|%v", sid, err0)
	return nil, err0
}

// DescribeWorkflowFeedback 获取工作流反馈详情
func (imp TaskConfigImp) DescribeWorkflowFeedback(ctx context.Context, req *KEP_WF.DescribeWorkflowFeedReq) (*KEP_WF.DescribeWorkflowFeedRsp, error) {
	sid := util.RequestID(ctx)
	log.Infof("DescribeWorkflowFeedback|REQ|%s|%v", sid, req)

	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 业务处理
		resp, err := workflow.DescribeWorkflowFeedback(ctx, req)
		log.Infof("DescribeWorkflowFeedback|RESP|%s|%v|%v", sid, resp, err)
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.Warnf("DescribeWorkflowFeedback|RESP|%s|%v", sid, err0)
	return nil, err0
}

// DeleteWorkflowFeedback 删除工作流反馈
func (imp TaskConfigImp) DeleteWorkflowFeedback(ctx context.Context, req *KEP_WF.DeleteWorkflowFeedbackReq) (*KEP_WF.DeleteWorkflowFeedbackRsp, error) {
	sid := util.RequestID(ctx)
	log.Infof("DeleteWorkflowFeedback|REQ|%s|%v", sid, req)

	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 业务处理
		resp, err := workflow.DeleteWorkflowFeedback(ctx, req)
		log.Infof("DeleteWorkflowFeedback|RESP|%s|%v|%v", sid, resp, err)
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.Warnf("DeleteWorkflowFeedback|RESP|%s|%v", sid, err0)
	return nil, err0
}

// UpdateWorkflowFeedback 更新工作流反馈
func (imp TaskConfigImp) UpdateWorkflowFeedback(ctx context.Context, req *KEP_WF.UpdateFlowFeedbackReq) (*KEP_WF.UpdateFlowFeedbackRsp, error) {
	sid := util.RequestID(ctx)
	log.Infof("UpdateWorkflowFeedback|REQ|%s|%v", sid, req)

	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 业务处理
		resp, err := workflow.UpdateWorkflowFeedback(ctx, req)
		log.Infof("UpdateWorkflowFeedback|RESP|%s|%v|%v", sid, resp, err)
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.Warnf("UpdateWorkflowFeedback|RESP|%s|%v", sid, err0)
	return nil, err0
}

// UpdateWorkflowFeedbackStatus 更新工作流反馈状态（内部调用）
func (imp TaskConfigImp) UpdateWorkflowFeedbackStatus(ctx context.Context,
	req *KEP_WF.UpdateWorkflowFeedbackStatusReq) (*KEP_WF.UpdateWorkflowFeedbackStatusRsp, error) {
	sid := util.RequestID(ctx)
	log.Infof("UpdateWorkflowFeedbackStatus|REQ|%s|%v", sid, req)

	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 业务处理
		resp, err := workflow.UpdateWorkflowFeedbackStatus(ctx, req)
		log.Infof("UpdateWorkflowFeedback|RESP|%s|%v|%v", sid, resp, err)
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.Warnf("UpdateWorkflowFeedbackStatus|RESP|%s|%v", sid, err0)
	return nil, err0
}

// UpdateWorkflowFeedbackTapd 更新工作流反馈tapd单（内部调用）
func (imp TaskConfigImp) UpdateWorkflowFeedbackTapd(ctx context.Context,
	req *KEP_WF.UpdateWorkflowFeedbackTapdReq) (*KEP_WF.UpdateWorkflowFeedbackTapdRsp, error) {
	sid := util.RequestID(ctx)
	log.Infof("UpdateWorkflowFeedbackTapd|REQ|%s|%v", sid, req)

	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 业务处理
		resp, err := workflow.UpdateWorkflowFeedbackTapd(ctx, req)
		log.Infof("UpdateWorkflowFeedbackTapd|RESP|%s|%v|%v", sid, resp, err)
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.Warnf("UpdateWorkflowFeedbackTapd|RESP|%s|%v", sid, err0)
	return nil, err0
}

// CopyWorkflow 复制Workflow
func (imp TaskConfigImp) CopyWorkflow(ctx context.Context, req *KEP_WF.CopyWorkflowReq) (*KEP_WF.CopyWorkflowRsp, error) {
	sid := util.RequestID(ctx)
	log.InfoContextf(ctx, "CopyWorkflow|REQ|%s|%v", sid, req)

	// 请求参数校验
	_, err := govalidator.ValidateStruct(req)
	if err != nil {
		err0 := errors.BadRequestError(err.Error())
		log.WarnContextf(ctx, "CopyWorkflow|RESP|%s|%v", sid, err0)
		return nil, err0
	}
	resp, err := workflow.CopyWorkflow(ctx, req)
	log.InfoContextf(ctx, "CopyWorkflow|RESP|%s|%v|%v", sid, resp, err)
	return resp, err
}

// ImportWorkflow  导入Workflow
func (imp TaskConfigImp) ImportWorkflow(ctx context.Context, req *KEP_WF.ImportWorkflowReq) (*KEP_WF.ImportWorkflowRsp, error) {
	sid := util.RequestID(ctx)
	log.InfoContextf(ctx, "ImportWorkflow|REQ|%s|%v", sid, req)

	// 请求参数校验
	_, err := govalidator.ValidateStruct(req)
	if err != nil {
		err0 := errors.BadRequestError(err.Error())
		log.WarnContextf(ctx, "ImportWorkflow|RESP|%s|%v", sid, err0)
		return nil, err0
	}
	resp, err := workflow.ImportWorkflow(ctx, req)
	log.InfoContextf(ctx, "ImportWorkflow|RESP|%s|%v|%v", sid, resp, err)
	return resp, err
}

// ExportWorkflow 导出Workflow
func (imp TaskConfigImp) ExportWorkflow(ctx context.Context, req *KEP_WF.ExportWorkflowReq) (*KEP_WF.ExportWorkflowRsp, error) {
	sid := util.RequestID(ctx)
	log.InfoContextf(ctx, "ExportWorkflow|REQ|%s|%v", sid, req)

	// 请求参数校验
	_, err := govalidator.ValidateStruct(req)
	if err != nil {
		err0 := errors.BadRequestError(err.Error())
		log.WarnContextf(ctx, "ExportWorkflow|RESP|%s|%v", sid, err0)
		return nil, err0
	}
	resp, err := workflow.ExportWorkflow(ctx, req)
	log.InfoContextf(ctx, "ExportWorkflow|RESP|%s|%v|%v", sid, resp, err)
	return resp, err
}

// DebugWorkflowNode 调试Workflow节点
func (imp TaskConfigImp) DebugWorkflowNode(ctx context.Context, req *KEP_WF.DebugWorkflowNodeReq) (*KEP_WF.DebugWorkflowNodeRsp, error) {
	sid := util.RequestID(ctx)
	log.Infof("DebugWorkflowNode|REQ|%s|%v", sid, req)

	// 请求参数校验
	_, err := govalidator.ValidateStruct(req)
	if err != nil {
		err0 := errors.BadRequestError(err.Error())
		log.Warnf("DebugWorkflowNode|RESP|%s|%v", sid, err0)
		return nil, err0
	}
	resp, err := workflow.DebugWorkflowNode(ctx, req)
	log.Infof("DebugWorkflowNode|RESP|%s|%v|%v", sid, resp, err)
	return resp, err
}

// GetModelSupportWorkflow 查看模型支持工作流的程度
func (imp TaskConfigImp) GetModelSupportWorkflow(ctx context.Context, req *KEP_WF.GetModelSupportWorkflowReq) (*KEP_WF.GetModelSupportWorkflowResp, error) {
	ctx = clues.NewTrackContext(ctx)
	sid := util.RequestID(ctx)
	t0 := time.Now()
	log.Infof("REQ|GetModelSupportWorkflow|%s|%v", sid, req)

	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 业务处理入口
		ctx = log.WithContextFields(ctx, "RequestID", sid)
		rsp, err := workflow.GetModelSupportWorkflow(ctx, req)
		log.Infof("RESP|GetModelSupportWorkflow|%s|%v|%s|%v|%s", sid, rsp,
			clues.GetTrackDataJSON(ctx), err, time.Since(t0))
		return rsp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.Warnf("E|RESP|GetModelSupportWorkflow|%s|%v", sid, err0)
	return nil, err0
}

// GetWorkflowReleaseStatus 获取工作流的发布状态（内部chat使用）
func (imp TaskConfigImp) GetWorkflowReleaseStatus(ctx context.Context, req *KEP_WF.GetWorkflowReleaseStatusReq) (*KEP_WF.GetWorkflowReleaseStatusResp, error) {
	ctx = clues.NewTrackContext(ctx)
	sid := util.RequestID(ctx)
	t0 := time.Now()
	log.Infof("REQ|GetWorkflowReleaseStatus|%s|%v", sid, req)

	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 业务处理入口
		ctx = log.WithContextFields(ctx, "RequestID", sid)
		pattern, workflowInfo, err := imp.getAppPatternAndWorkflowInfo(ctx, req.GetAppBizId())
		if err != nil {
			log.Errorf("P|GetWorkflowReleaseStatus|%s|%v", sid, err)
			return nil, err
		}

		rsp := &KEP_WF.GetWorkflowReleaseStatusResp{}
		switch pattern {
		case entity.AppPatternStandard: // 标准模式
			rsp, err = workflow.GetWorkflowReleaseStatus(ctx, req)
		case entity.AppPatternAgent: // agent模式
			// agent模式只处理PDL
			if workflowInfo.GetUsePdl() {
				// PDL
				rsp, err = pdlLogic.GetPDLReleaseStatus(ctx, req)
			}
		case entity.AppPatternSingleWorkflow: // 单工作流模式
			// 单工作流模式只处理新工作流
			if workflowInfo.GetIsEnabled() {
				// 新工作流
				rsp, err = workflow.GetWorkflowReleaseStatus(ctx, req)
			}
		}

		log.Infof("RESP|GetWorkflowReleaseStatus|%s|%v|%s|%v|%s", sid, rsp,
			clues.GetTrackDataJSON(ctx), err, time.Since(t0))
		return rsp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.Warnf("E|RESP|GetWorkflowReleaseStatus|%s|%v", sid, err0)
	return nil, err0
}

// GetCanBeReferencedWorkflowList 获取可以被引用的工作流的列表
func (imp TaskConfigImp) GetCanBeReferencedWorkflowList(ctx context.Context,
	req *KEP_WF.GetCanBeReferencedWorkflowListReq) (*KEP_WF.GetCanBeReferencedWorkflowListRsp, error) {
	sid := util.RequestID(ctx)
	log.InfoContextf(ctx, "GetCanBeReferencedWorkflowList start sid|req:|%s|%+v", sid, req)
	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 业务处理
		resp, err := workflow.GetCanBeReferencedWorkflowList(ctx, req)
		log.InfoContextf(ctx, "GetCanBeReferencedWorkflowList|RESP|%s|%v|%v", sid, resp, err)
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.WarnContextf(ctx, "GetCanBeReferencedWorkflowList|RESP|%s|%v", sid, err0)
	return nil, err0
}

// GetHasBeenReferencedWorkflowList 批量获取指定工作流已经被引用的工作流的列表
func (imp TaskConfigImp) GetHasBeenReferencedWorkflowList(ctx context.Context,
	req *KEP_WF.GetHasBeenReferencedWorkflowListReq) (*KEP_WF.GetHasBeenReferencedWorkflowListRsp, error) {
	sid := util.RequestID(ctx)
	log.InfoContextf(ctx, "GetHasBeenReferencedWorkflowList start sid|req:|%s|%+v", sid, req)
	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 业务处理
		resp, err := workflow.GetHasBeenReferencedWorkflowList(ctx, req)
		log.InfoContextf(ctx, "GetHasBeenReferencedWorkflowList|RESP|%s|%v|%v", sid, resp, err)
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.WarnContextf(ctx, "GetHasBeenReferencedWorkflowList|RESP|%s|%v", sid, err0)
	return nil, err0
}

// GetParamsByWorkflowIds 批量获取指定工作流的输入输出参数
func (imp TaskConfigImp) GetParamsByWorkflowIds(ctx context.Context,
	req *KEP_WF.GetParamsByWorkflowIdsReq) (*KEP_WF.GetParamsByWorkflowIdsRsp, error) {
	sid := util.RequestID(ctx)
	log.InfoContextf(ctx, "GetParamsByWorkflowIds start sid|req:|%s|%+v", sid, req)
	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 业务处理
		resp, err := workflow.GetParamsByWorkflowIds(ctx, req)
		log.InfoContextf(ctx, "GetParamsByWorkflowIds|RESP|%s|%v|%v", sid, resp, err)
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.WarnContextf(ctx, "GetParamsByWorkflowIds|RESP|%s|%v", sid, err0)
	return nil, err0
}

// GetHasReferencedPluginToolWorkflowList 获取引用指定插件工具的工作流列表
func (imp TaskConfigImp) GetHasReferencedPluginToolWorkflowList(ctx context.Context,
	req *KEP_WF.GetHasReferencedPluginToolWorkflowListReq) (*KEP_WF.GetHasReferencedPluginToolWorkflowListRsp, error) {
	sid := util.RequestID(ctx)
	log.InfoContextf(ctx, "GetHasReferencedPluginToolWorkflowList start sid|req:|%s|%+v", sid, req)
	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 业务处理
		resp, err := workflow.GetHasReferencedPluginToolWorkflowList(ctx, req)
		log.InfoContextf(ctx, "GetHasReferencedPluginToolWorkflowList|RESP|%s|%v|%v", sid, resp, err)
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.WarnContextf(ctx, "GetHasReferencedPluginToolWorkflowList|RESP|%s|%v", sid, err0)
	return nil, err0
}

// GetWorkflowGuideViewed 获取工作流引导页面是否已展示
func (imp TaskConfigImp) GetWorkflowGuideViewed(ctx context.Context, req *KEP_WF.GetWorkflowGuideViewedRequest) (*KEP_WF.GetWorkflowGuideViewedResponse, error) {
	sid := util.RequestID(ctx)
	log.InfoContextf(ctx, "GetWorkflowGuideViewed|REQ|%s|%v", sid, req)

	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		resp, err := workflow.GetWorkflowGuideViewed(ctx, req)
		log.InfoContextf(ctx, "GetWorkflowGuideViewed|RESP|%s|%v|%v", sid, resp, err)
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.Warnf("GetWorkflowGuideViewed|RESP|%s|%v", sid, err0)
	return nil, err0
}

// MarkWorkflowGuideViewed 标记工作流引导页面已展示
func (imp TaskConfigImp) MarkWorkflowGuideViewed(ctx context.Context, req *KEP_WF.MarkWorkflowGuideViewedRequest) (*KEP_WF.MarkWorkflowGuideViewedResponse, error) {
	sid := util.RequestID(ctx)
	log.InfoContextf(ctx, "MarkWorkflowGuideViewed|REQ|%s|%v", sid, req)

	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		resp, err := workflow.MarkWorkflowGuideViewed(ctx, req)
		log.InfoContextf(ctx, "MarkWorkflowGuideViewed|RESP|%s|%v|%v", sid, resp, err)
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.Warnf("MarkWorkflowGuideViewed|RESP|%s|%v", sid, err0)
	return nil, err0
}

// GetEnableCustomAsk 是参数节点否支持自定义话术
func (imp TaskConfigImp) GetEnableCustomAsk(ctx context.Context, req *KEP_WF.GetEnableCustomAskReq) (*KEP_WF.GetEnableCustomAskResp, error) {
	sid := util.RequestID(ctx)
	log.InfoContextf(ctx, "GetEnableCustomAsk start sid|req:|%s|%+v", sid, req)
	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 业务处理
		resp, err := workflow.GetEnableCustomAsk(ctx, req)
		log.Infof("GetEnableCustomAsk|RESP|%s|%v|%v", sid, resp, err)
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.Warnf("GetEnableCustomAsk|RESP|%s|%v", sid, err0)
	return nil, err0
}

// ListWorkflowNodeModel 获取画布节点工作流模型
func (imp TaskConfigImp) ListWorkflowNodeModel(ctx context.Context, req *KEP_WF.ListWorkflowNodeModelReq) (*KEP_WF.ListWorkflowNodeModelRsp, error) {
	sid := util.RequestID(ctx)
	log.InfoContextf(ctx, "ListWorkflowNodeModel start sid|req:|%s|%+v", sid, req)
	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 业务处理
		resp, err := workflow.ListWorkflowNodeModel(ctx, req)
		log.Infof("ListWorkflowNodeModel|RESP|%s|%v|%v", sid, resp, err)
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.Warnf("ListWorkflowNodeModel|RESP|%s|%v", sid, err0)
	return nil, err0
}

// ListWorkflowInfoByModelNameInner 获取节点哪些模型被使用
func (imp TaskConfigImp) ListWorkflowInfoByModelNameInner(ctx context.Context,
	req *KEP_WF.ListWorkflowInfoByModelNameReq) (*KEP_WF.ListWorkflowInfoByModelNameRsp, error) {
	sid := util.RequestID(ctx)
	log.InfoContextf(ctx, "ListWorkflowInfoByModelNameInner start sid|req:|%s|%+v", sid, req)
	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 业务处理
		resp, err := workflow.ListWorkflowInfoByModelName(ctx, req)
		log.Infof("ListWorkflowInfoByModelNameInner|RESP|%s|%v|%v", sid, resp, err)
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.Warnf("ListWorkflowInfoByModelNameInner|RESP|%s|%v", sid, err0)
	return nil, err0
}

// GetExperienceWorkflowList  获取体验中心下所有工作流模版列表
func (imp TaskConfigImp) GetExperienceWorkflowList(ctx context.Context, req *KEP_WF.GetExperienceWorkflowListReq) (
	*KEP_WF.GetExperienceWorkflowListRsp, error) {
	sid := util.RequestID(ctx)
	log.InfoContextf(ctx, "GetExperienceWorkflowList start sid:%s,req:%v", sid, req)
	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 业务处理
		resp, err := workflow.GetExperienceWorkflowList(ctx, req)
		log.InfoContextf(ctx, "GetExperienceWorkflowList end sid:%s,resp:%v,err:%v", sid, resp, err)
		return resp, err
	}
	err0 := errors.BadRequestError(gve.Error())
	log.WarnContextf(ctx, "GetExperienceWorkflowList end sid:%s,err0:%v", sid, err0)
	return nil, err0
}

// CreateExperienceWorkflow  创建体验中心指定应用下的工作流
func (imp TaskConfigImp) CreateExperienceWorkflow(ctx context.Context, req *KEP_WF.CreateExperienceWorkflowReq) (
	*KEP_WF.CreateExperienceWorkflowRsp, error) {
	sid := util.RequestID(ctx)
	log.InfoContextf(ctx, "CreateExperienceWorkflow start sid:%s,req:%v", sid, req)
	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 业务处理
		resp, err := workflow.CreateExperienceWorkflow(ctx, req)
		log.InfoContextf(ctx, "CreateExperienceWorkflow end sid:%s,resp:%v,err:%v", sid, resp, err)
		return resp, err
	}
	err0 := errors.BadRequestError(gve.Error())
	log.WarnContextf(ctx, "CreateExperienceWorkflow end sid:%s,err0:%v", sid, err0)
	return nil, err0
}

// GetWorkflowListByDoc  批量获取文档被引用的工作流列表
func (imp TaskConfigImp) GetWorkflowListByDoc(ctx context.Context, req *KEP_WF.GetWorkflowListByDocReq) (
	*KEP_WF.GetWorkflowListByDocRsp, error) {
	sid := util.RequestID(ctx)
	log.InfoContextf(ctx, "GetWorkflowListByDoc start sid:%s,req:%v", sid, req)
	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 业务处理
		resp, err := workflow.GetWorkflowListByDoc(ctx, req)
		log.InfoContextf(ctx, "GetWorkflowListByDoc end sid:%s,resp:%v,err:%v", sid, resp, err)
		return resp, err
	}
	err0 := errors.BadRequestError(gve.Error())
	log.WarnContextf(ctx, "GetWorkflowListByDoc end sid:%s,err0:%v", sid, err0)
	return nil, err0
}

// GetWorkflowListByAttribute  批量获取标签被引用的工作流列表
func (imp TaskConfigImp) GetWorkflowListByAttribute(ctx context.Context, req *KEP_WF.GetWorkflowListByAttributeReq) (
	*KEP_WF.GetWorkflowListByAttributeRsp, error) {
	sid := util.RequestID(ctx)
	log.InfoContextf(ctx, "GetWorkflowListByAttribute start sid:%s,req:%v", sid, req)
	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 业务处理
		resp, err := workflow.GetWorkflowListByAttribute(ctx, req)
		log.InfoContextf(ctx, "GetWorkflowListByAttribute end sid:%s,resp:%v,err:%v", sid, resp, err)
		return resp, err
	}
	err0 := errors.BadRequestError(gve.Error())
	log.WarnContextf(ctx, "GetWorkflowListByAttribute end sid:%s,err0:%v", sid, err0)
	return nil, err0
}

// GetWorkflowListByAttributeLabel  批量获取标签值被引用的工作流列表
func (imp TaskConfigImp) GetWorkflowListByAttributeLabel(ctx context.Context,
	req *KEP_WF.GetWorkflowListByAttributeLabelReq) (
	*KEP_WF.GetWorkflowListByAttributeLabelRsp, error) {
	sid := util.RequestID(ctx)
	log.InfoContextf(ctx, "GetWorkflowListByAttributeLabel start sid:%s,req:%v", sid, req)
	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 业务处理
		resp, err := workflow.GetWorkflowListByAttributeLabel(ctx, req)
		log.InfoContextf(ctx, "GetWorkflowListByAttributeLabel end sid:%s,resp:%v,err:%v", sid, resp, err)
		return resp, err
	}
	err0 := errors.BadRequestError(gve.Error())
	log.WarnContextf(ctx, "GetWorkflowListByAttributeLabel end sid:%s,err0:%v", sid, err0)
	return nil, err0
}

// CopyWorkflowByApp  复制指定应用下的工作流到另一个应用下
func (imp TaskConfigImp) CopyWorkflowByApp(ctx context.Context, req *KEP_WF.CopyWorkflowByAppReq) (
	*KEP_WF.CopyWorkflowByAppRsp, error) {
	sid := util.RequestID(ctx)
	log.InfoContextf(ctx, "CopyWorkflowByApp start sid:%s,req:%v", sid, req)
	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 业务处理
		resp, err := workflow.CopyWorkflowByApp(ctx, req)
		log.InfoContextf(ctx, "CopyWorkflowByApp end sid:%s,resp:%v,err:%v", sid, resp, err)
		return resp, err
	}
	err0 := errors.BadRequestError(gve.Error())
	log.WarnContextf(ctx, "CopyWorkflowByApp end sid:%s,err0:%v", sid, err0)
	return nil, err0
}

//// GetCopyAPPWorkflowStatusByTaskId  查询跨应用工作流复制状态
//func (imp TaskConfigImp) GetCopyAPPWorkflowStatusByTaskId(ctx context.Context,
//	req *KEP_WF.GetCopyAPPWorkflowStatusByTaskIdReq) (
//	*KEP_WF.GetCopyAPPWorkflowStatusByTaskIdRsp, error) {
//	sid := util.RequestID(ctx)
//	log.InfoContextf(ctx, "GetCopyAPPWorkflowStatusByTaskId start sid:%s,req:%v", sid, req)
//	// 请求参数校验
//	ok, gve := govalidator.ValidateStruct(req)
//	if ok {
//		// 业务处理
//		resp, err := workflow.GetCopyAPPWorkflowStatusByTaskId(ctx, req)
//		log.InfoContextf(ctx, "GetCopyAPPWorkflowStatusByTaskId end sid:%s,resp:%v,err:%v", sid, resp, err)
//		return resp, err
//	}
//	err0 := errors.BadRequestError(gve.Error())
//	log.WarnContextf(ctx, "GetCopyAPPWorkflowStatusByTaskId end sid:%s,err0:%v", sid, err0)
//	return nil, err0
//}
