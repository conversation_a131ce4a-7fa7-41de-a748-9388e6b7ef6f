// bot-task-config-server
//
// @(#)import_experience_workflow.go  星期三, 二月 26, 2025
// Copyright(c) 2025, mikeljiang@Tencent. All rights reserved.

package bottask

import (
	"context"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/db"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/errors"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/idgenerator"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/protoutil"
	"git.woa.com/dialogue-platform/bot-config/task_scheduler"
	"gorm.io/gorm"
)

// CopyExperienceWorkflowScheduler 复制体验中心下工作流
type CopyExperienceWorkflowScheduler struct {
	task   task_scheduler.Task
	params entity.TaskCopyExperienceWorkflowParams
	//err    error
}

func init() {
	task_scheduler.Register(
		entity.TaskImportExperienceWorkflow,
		func(task task_scheduler.Task, params entity.TaskCopyExperienceWorkflowParams) task_scheduler.TaskHandler {
			return &CopyExperienceWorkflowScheduler{
				task:   task,
				params: params,
			}
		})
}

// Prepare 数据准备
func (c CopyExperienceWorkflowScheduler) Prepare(_ context.Context) (task_scheduler.TaskKV, error) {
	return task_scheduler.TaskKV{}, nil
}

// Init 初始化
func (c CopyExperienceWorkflowScheduler) Init(ctx context.Context, _ task_scheduler.TaskKV) error {
	// 填充RequestID和日志信息
	util.WithRequestID(ctx, c.params.RequestID)
	ctx = log.WithContextFields(ctx, "RequestID", util.RequestID(ctx))
	log.InfoContextf(ctx, "CopyExperienceWorkflowScheduler|Init RobotID:%s,ExperienceRobotID:%s,WorkflowID:%s,"+
		"NewWorkflowID:%s,TaskID:%s", c.params.RobotID, c.params.ExperienceRobotID, c.params.WorkflowID,
		c.params.NewWorkflowID, c.params.TaskID)
	return nil
}

// Process 任务处理
func (c CopyExperienceWorkflowScheduler) Process(ctx context.Context, _ *task_scheduler.Progress) error {
	log.InfoContextf(ctx, "ConvertWorkflowToPdl|process start")
	// 执行体验中心工作流复制操作
	err := CopyAppWorkflow(ctx, c.params.RobotID, nil, c.params.ExperienceRobotID,
		"", "", c.params.NewWorkflowID, c.params.WorkflowID,
		c.params.Uin, c.params.SubUin, c.params.StaffID, entity.CopyExperienceWorkflowType)
	if err != nil {
		log.ErrorContextf(ctx, "CopyExperienceWorkflowScheduler Process execExperienceWorkflow|RobotID:%s,"+
			"ExperienceRobotID:%s,WorkflowID:%s,NewWorkflowID:%s,TaskID:%s, err:%v", c.params.RobotID,
			c.params.ExperienceRobotID, c.params.WorkflowID, c.params.NewWorkflowID, c.params.TaskID, err)
		return err
	}
	log.InfoContextf(ctx, "CopyExperienceWorkflowScheduler|process end|taskID:%s", c.params.TaskID)
	return nil
}

// Done 任务完成回调
func (c CopyExperienceWorkflowScheduler) Done(ctx context.Context) error {
	log.InfoContextf(ctx, "CopyExperienceWorkflowScheduler|Done|taskID:%s", c.params.TaskID)
	// 发送通知

	return nil
}

// Fail 任务失败
func (c CopyExperienceWorkflowScheduler) Fail(ctx context.Context) error {
	log.ErrorContextf(ctx, "CopyExperienceWorkflowScheduler|Fail|taskID:%s", c.params.TaskID)
	return nil
}

// Stop 任务停止
func (c CopyExperienceWorkflowScheduler) Stop(ctx context.Context) error {
	log.WarnContextf(ctx, "CopyExperienceWorkflowScheduler|Stop|taskID:%s", c.params.TaskID)
	return nil
}

// CopyAppWorkflow 执行工作流A复制到工作流B的操作
// robotId 							当前登陆账号的应用ID(必填)
// sourceWorkflowIds 				源目标工作流ID集合(创建工作流使用模版)
// sourceRobotId 					源目标工作流对应应用ID(快速创建应用)
// newWorkflowName 					预置工作流名称（创建工作流使用模版)
// newWorkflowDesc 					预置工作流描述（创建工作流使用模版)
// newWorkflowID 					预置工作流ID（创建工作流使用模版和快速复制单工作流时候会传入）
// sourceNeedReplacedWorkflowId 	源目标待替换工作流ID(创建工作流使用模版和快速复制单工作流时候会传入）
func CopyAppWorkflow(ctx context.Context, robotId string, sourceWorkflowIds []string, sourceRobotId,
	newWorkflowName, newWorkflowDesc, newWorkflowID, sourceNeedReplacedWorkflowId, uin, subUin string,
	staffID uint64, copyType int) error {
	// 获取体验中心数据
	// v2.8.5 画布中节点里密钥加密存储后需要解密后返回给前端，这里已经是解密后的数据
	sourceWorkflowIds, workflowMap, workflowRefMap, paramMap, exampleMap, workflowVarMap, PDLMap, err :=
		getAppWorkflow(ctx, robotId, sourceWorkflowIds, sourceRobotId, copyType)
	if err != nil {
		log.ErrorContextf(ctx, "CopyAppWorkflow getAppWorkflow err:%v", err)
		return err
	}
	log.InfoContextf(ctx, "CopyAppWorkflow getAppWorkflow len(workflowMap):%s", len(workflowMap))
	// v2.8.5 画布中节点里密钥需要加密存储
	for workflowID, workflow := range workflowMap {
		workflow.DialogJson, err = protoutil.EncryptWorkflowJson(workflow.DialogJson)
		if err != nil {
			log.ErrorContextf(ctx, "CopyAppWorkflow EncryptWorkflowJson err:%v", err)
			return err
		}
		workflowMap[workflowID] = workflow
	}
	// 构建导入需要的数据结构
	paramDataList, err := constructExperienceParamData(ctx, sourceWorkflowIds, workflowMap, workflowRefMap, paramMap,
		exampleMap, workflowVarMap, PDLMap, robotId, newWorkflowName, newWorkflowDesc, newWorkflowID,
		sourceNeedReplacedWorkflowId, uin, subUin)
	if err != nil {
		log.ErrorContextf(ctx, "CopyAppWorkflow constructExperienceParamData Failed, err:%v", err)
		return err
	}
	log.InfoContextf(ctx, "CopyAppWorkflow constructExperienceParamData len(paramDataList):%s",
		len(paramDataList))
	if len(paramDataList) > 0 {
		// 执行数据导入
		err := importExperienceParamData(ctx, paramDataList, staffID, robotId)
		if err != nil {
			return err
		}
		log.InfoContextf(ctx, "CopyAppWorkflow importExperienceParamData process end")
		return nil
	} else {
		log.WarnContextf(ctx, "CopyAppWorkflow no data")
		return nil
	}
}

// getAppWorkflow 获取指定应用下的工作流模版数据
func getAppWorkflow(ctx context.Context, robotId string, sourceWorkflowIds []string, sourceRobotId string,
	copyType int) (
	[]string, map[string]*entity.ExportWorkflow, map[string][]*entity.ExportWorkflowRef,
	map[string][]*entity.ParamMigrationInfo, map[string][]*entity.ExportWorkflowExample,
	map[string][]*entity.ExportWorkflowVar, map[string][]*entity.ExportWorkflowPDL, error) {
	if len(sourceWorkflowIds) == 0 && len(sourceRobotId) == 0 {
		log.WarnContextf(ctx, "getAppWorkflow workflowIds and robotId is empty "+
			"workflowIds:%+v, robotId:%s", sourceWorkflowIds, sourceRobotId)
		return nil, nil, nil, nil, nil, nil, nil, errors.BadWorkflowReqError("请求参数没有应用ID，且没有工作流ID")
	}
	log.InfoContextf(ctx, "getAppWorkflow workflowIds:|%+v,robotId:|%s",
		sourceWorkflowIds, sourceRobotId)
	// 没有应用ID时，构造空集合；不能直接[]string{sourceRobotId}；会形成带空字符串元素的集合
	sourceRobotIds := make([]string, 0)
	if len(sourceRobotId) != 0 {
		sourceRobotIds = append(sourceRobotIds, sourceRobotId)
	}
	// 根据条件，获取对应工作流
	var workflowIdList []*entity.ExportWorkflow
	var err error
	if copyType == entity.CopyExperienceWorkflowType {
		workflowIdList, err = db.GetExperienceWorkflowIds(ctx, sourceRobotIds, sourceWorkflowIds)
		if err != nil {
			return nil, nil, nil, nil, nil, nil, nil, err
		}
	} else {
		workflowIdList, err = db.GetWorkflowIdsByAppID(ctx, sourceRobotId)
		if err != nil {
			return nil, nil, nil, nil, nil, nil, nil, err
		}
	}
	// sourceWorkflowIds不为空时，是模版创建(指定工作流级别复制)
	if len(sourceWorkflowIds) != 0 {
		// 判断入参的工作流是否全部存在
		if len(sourceWorkflowIds) != len(workflowIdList) {
			log.WarnContextf(ctx, "getAppWorkflow workflowIds count is error "+
				"len(sourceWorkflowIds):%d, len(workflowIdList):%d", len(sourceWorkflowIds), len(workflowIdList))
			return nil, nil, nil, nil, nil, nil, nil, errors.ErrWorkflowNotFound
		}
		// 获取工作流模版的应用ID
		workflowRobot, err := db.GetWorkflowRobotIdByWorkflow(ctx, workflowIdList[0].WorkflowID)
		if err != nil {
			return nil, nil, nil, nil, nil, nil, nil, err
		}
		sourceRobotId = workflowRobot.RobotId
	} else {
		// sourceWorkflowIds为空时，是应用级别复制；
		if len(workflowIdList) == 0 {
			log.WarnContextf(ctx, "getAppWorkflow workflowIds is empty")
			return nil, nil, nil, nil, nil, nil, nil, errors.ErrWorkflowNotFound
		}
		for _, workflow := range workflowIdList {
			sourceWorkflowIds = append(sourceWorkflowIds, workflow.WorkflowID)
		}
	}
	// 根据入参的工作流获取依赖的其他工作流,sourceRobotId和sourceWorkflowIds都不能为空
	workflowRefs, err := db.GetWorkflowRefsByFlowIds(ctx, sourceRobotId, sourceWorkflowIds)
	if err != nil {
		return nil, nil, nil, nil, nil, nil, nil, err
	}
	// 整合入参工作流和依赖工作流，作为全部工作流
	_, sourceIds := db.IDsWorkflowAndRef(workflowRefs, sourceWorkflowIds)

	// 从体验中心获取上述整合后的全部工作流数据,并过滤已经存在的示例问法
	// v2.8.5 画布中节点里密钥加密存储后需要解密后返回给前端，这里已经是解密后的数据
	workflowMap, workflowRefMap, paramMap, exampleMap, workflowVarMap, PDLMap, err := GetWorkflowSourceData(ctx,
		robotId, sourceRobotId, sourceIds, true)
	if err != nil {
		log.ErrorContextf(ctx, "getAppWorkflow GetWorkflowSourceData err:%v", err)
		return nil, nil, nil, nil, nil, nil, nil, err
	}
	if len(workflowMap) == 0 {
		log.WarnContextf(ctx, "getAppWorkflow GetWorkflowSourceData is empty")
		return nil, nil, nil, nil, nil, nil, nil, errors.ErrWorkflowNotFound
	}
	return sourceWorkflowIds, workflowMap, workflowRefMap, paramMap, exampleMap, workflowVarMap, PDLMap, nil
}

// importExperienceParamData 执行数据导入
func importExperienceParamData(ctx context.Context, paramDataList []*entity.WorkflowImportData, staffID uint64,
	robotId string) error {
	log.InfoContextf(ctx, "importExperienceParamData len(paramDataList):%s", len(paramDataList))
	for _, importParamData := range paramDataList {
		taskParams := entity.WorkflowImportParams{
			RequestID: util.RequestID(ctx),
			StaffID:   staffID,
			RobotID:   robotId,
		}
		importWorkflow := ImportWorkflowScheduler{}
		importWorkflow = SetImportWorkflow(importWorkflow, taskParams)
		sid := util.RequestID(ctx)
		// 导入数据组装
		varParams, varRefParams, paramParams, workflowRefParams, createParams, workflowRefPlugins,
			examples, workflowPDLs, err := importWorkflow.ConstructImportWorkflow(ctx, importParamData, sid)
		if err != nil {
			return err
		}
		db11 := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
		// 创建所需的变量
		if err := db.ImportVarForWorkflow(ctx, varParams, robotId); err != nil {
			//log.ErrorContextf(ctx, "sid:%s|importVarForWorkflow|err:%+v", sid, err)
			return err
		}
		// 创建所需的其他信息
		if err := db11.Transaction(func(tx *gorm.DB) error {
			err := db.ExecImportWorkflow(tx, ctx, robotId, examples, importParamData, paramParams,
				createParams, varRefParams, workflowRefParams, workflowRefPlugins, workflowPDLs)
			if err != nil {
				return err
			}
			return nil
		}); err != nil {
			log.ErrorContextf(ctx, "CreateWorkflow fail, err:%+v", err)
			return err
		}
	}
	return nil
}

// constructExperienceParamData 构建导入的工作流程数据,过滤掉当前应用下已经存在的工作流和示例问法
func constructExperienceParamData(ctx context.Context, sourceWorkflowIds []string,
	workflowMap map[string]*entity.ExportWorkflow, workflowRefMap map[string][]*entity.ExportWorkflowRef,
	paramMap map[string][]*entity.ParamMigrationInfo, exampleMap map[string][]*entity.ExportWorkflowExample,
	workflowVarMap map[string][]*entity.ExportWorkflowVar, PDLMap map[string][]*entity.ExportWorkflowPDL,
	robotId, newWorkflowName, newWorkflowDesc, newWorkflowID, sourceNeedReplacedWorkflowId, uin, subUin string) (
	[]*entity.WorkflowImportData, error) {
	paramDataList := make([]*entity.WorkflowImportData, 0) // 工作流程相关数据
	mapOld2New := make(map[string]string)
	// 获取当前账号下已经存在的工作流
	mapExistWorkflow, err := db.GetExistWorkflowByRobotID(ctx, robotId)
	if err != nil {
		log.ErrorContextf(ctx, "constructExperienceParamData getExistWorkflowByRobotID Failed, err:%v", err)
		return nil, err
	}
	for _, workflow := range workflowMap {
		// newWorkflowName在使用模版创建工作流的时候不为空，这时候我们只需要考虑模版引用的工作流是否与当前账户下工作流流重名情况
		replaceWorkflowName := workflow.WorkflowName
		paramWorkflowName, paramWorkflowDesc := "", ""
		// 只校验引用的工作流
		if !util.ContainsList(sourceWorkflowIds, workflow.WorkflowID) {
			// 引用的工作流名称和页面传入的名称对比
			if newWorkflowName != "" && newWorkflowName == workflow.WorkflowName {
				log.WarnContextf(ctx, "constructExperienceParamData newWorkflowName and refName is same")
				return nil, errors.ErrWorkflowRefNameDuplicated
			}
		} else {
			// 只校验创建工作流选择的模版的名称，替换原名称
			if newWorkflowName != "" {
				replaceWorkflowName = newWorkflowName
				paramWorkflowName = newWorkflowName
			} else {
				replaceWorkflowName = workflow.WorkflowName
			}
			if newWorkflowDesc != "" {
				paramWorkflowDesc = newWorkflowDesc
			}
		}
		// 过滤已存在的工作流，并将已存在的工作流名称替换到workflowRef里面
		if _, ok := mapExistWorkflow[replaceWorkflowName]; ok {
			log.WarnContextf(ctx, "constructExperienceParamData exist workflowName:|%s",
				replaceWorkflowName)
			for _, workflowRefList := range workflowRefMap {
				for _, workflowRef := range workflowRefList {
					if workflowRef.WorkflowRefID == workflow.WorkflowID {
						workflowRef.WorkflowRefNewID = mapExistWorkflow[workflow.WorkflowName].WorkflowID
						workflowRef.WorkflowRefName = mapExistWorkflow[workflow.WorkflowName].WorkflowName
					}
				}
			}
			continue
		}
		// 替换源目标工作流ID
		if sourceNeedReplacedWorkflowId == workflow.WorkflowID {
			if newWorkflowID != "" {
				mapOld2New[workflow.WorkflowID] = newWorkflowID
			} else {
				log.WarnContextf(ctx, "constructExperienceParamData newWorkflowID is empty "+
					"sourceNeedReplacedWorkflowId:|%s", sourceNeedReplacedWorkflowId)
			}
		} else {
			mapOld2New[workflow.WorkflowID] = idgenerator.NewUUID() // 提取分配好工作流id，方便后续替换引用的工作流id
		}

		paramDataList = append(paramDataList, &entity.WorkflowImportData{
			WorkflowData: workflow,
			Examples:     exampleMap[workflow.WorkflowID],
			Params:       paramMap[workflow.WorkflowID],
			Vars:         workflowVarMap[workflow.WorkflowID],
			WorkflowRefs: workflowRefMap[workflow.WorkflowID],
			WorkflowPDLs: PDLMap[workflow.WorkflowID],
			Old2New:      mapOld2New,
			Uin:          uin,
			SubUin:       subUin,
			WorkflowName: paramWorkflowName,
			WorkflowDesc: paramWorkflowDesc,
		})
	}
	return paramDataList, nil
}
