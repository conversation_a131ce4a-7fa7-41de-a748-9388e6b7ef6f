// Package workflow bot-task-config-server
// @(#)workflow.go  星期四, 九月 26, 2024
// Copyright(c) 2024, mikeljiang@Tencent. All rights reserved.
package workflow

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"sync"
	"time"
	"unicode/utf8"

	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/db"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/rpc"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/logic/bottask"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/logic/permission"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/logic/workflow/publish"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/errors"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/idgenerator"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/protoutil"
	"git.woa.com/dialogue-platform/go-comm/encode"
	"git.woa.com/dialogue-platform/go-comm/set"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	"gorm.io/gorm"
)

// CreateWorkflow 创建工作流
func CreateWorkflow(ctx context.Context, req *KEP_WF.CreateWorkflowReq) (*KEP_WF.CreateWorkflowRsp, error) {
	appBizId := req.GetAppBizId()
	staffID := util.StaffID(ctx)
	workflowName := strings.TrimSpace(req.GetName())
	workflowJson := strings.TrimSpace(req.GetDialogJson())
	desc := strings.TrimSpace(req.GetDesc())
	uin, subUin := util.GetUinAndSubAccountUin(ctx)
	// 尝试将字符串转换为 uint64
	_, err := strconv.ParseUint(req.GetAppBizId(), 10, 64)
	if err != nil {
		log.WarnContextf(ctx, "CreateWorkflow appBizId ParseUint err:%v", err)
		return nil, errors.BadRequestError("应用ID错误")
	}
	// 添加判断机器人是否存在
	appInfo, err := permission.CheckRobot(ctx, 1, uint64(encode.StringToInt64(appBizId)))
	if err != nil {
		log.WarnContextf(ctx, "CreateWorkflow CheckRobot err:%v", err)
		return nil, err
	}
	// 2.7 agent模式不支持工作流，后面支持需要放开
	// https://tapd.woa.com/tapd_fe/********/story/detail/10********121062607
	if util.IsAppAgentModel(appInfo) {
		return nil, errors.ErrAgentPermissionDenied
	}

	// 工作流名称校验
	log.InfoContextf(ctx, "CreateWorkflow checkWorkflowName start")
	if len(req.TemplateWorkflowId) > 0 {
		// 生成工作流名称
		getNewWorkflowName := func(oldWorkflowName string, copyCount int) string {
			// 注意中文字符的处理
			nameSuffix := "-副本" + strconv.Itoa(copyCount)
			remainLength := config.GetMainConfig().VerifyWorkflow.WorkflowNameLen - utf8.RuneCountInString(nameSuffix)

			if remainLength < utf8.RuneCountInString(oldWorkflowName) {
				return string([]rune(oldWorkflowName)[0:remainLength]) + nameSuffix
			} else {
				return oldWorkflowName + nameSuffix
			}
		}
		oldName, copyCount := getLastCopyIndex(workflowName)
		maxTotalWorkflow := config.GetMainConfig().VerifyWorkflow.WorkflowLimit
		for i := 0; i < maxTotalWorkflow; i++ {
			copyCount += 1
			workflowName = getNewWorkflowName(oldName, copyCount)
			err := checkWorkflowName(ctx, "", appBizId, workflowName)
			if err == nil {
				break
			}
			if errs.Code(err) != errs.Code(errors.ErrWorkflowNameDuplicated) {
				return nil, err
			}
			if i == maxTotalWorkflow-1 {
				return nil, errors.ErrWorkflowNameDuplicated
			}
		}
	} else {
		err = checkWorkflowName(ctx, "", appBizId, workflowName)
		if err != nil {
			log.ErrorContextf(ctx, "CreateWorkflow checkWorkflowName err:%v", err)
			return nil, err
		}
	}
	// 意图描述
	if utf8.RuneCountInString(desc) > config.GetMainConfig().VerifyWorkflow.IntentDescLen {
		log.WarnContextf(ctx, "CreateWorkflow desc:%s,err:%s", desc, errors.ErrIntentNameExceed)
		return nil, errors.ErrIntentNameExceed
	}
	// 画布JSON正确性初步校验
	tree, err := protoutil.JsonToWorkflowForPreCheck(workflowJson)
	if err != nil {
		log.ErrorContextf(ctx, "CreateWorkflow JsonToWorkflowForPreCheck err:%v", err)
		return nil, errors.ErrWorkflowUIJsonParams
	}
	log.InfoContextf(ctx, "CreateWorkflow JsonToWorkflowForPreCheck tree:%v", tree)

	// v2.8.5 画布中节点里的密钥需要加密存储
	workflowJson, err = protoutil.EncryptWorkflowJson(workflowJson)
	if err != nil {
		log.ErrorContextf(ctx, "CreateWorkflow EncryptWorkflowJson err:%v", err)
		return nil, errors.ErrWorkflowUIJsonParams
	}

	workflowData := entity.CreateWorkflowParams{
		WorkflowID:       idgenerator.NewUUID(),
		WorkflowName:     workflowName,
		WorkflowDesc:     desc,
		WorkflowState:    entity.FlowStateDraft,
		Version:          1,
		RobotId:          appBizId,
		DialogJsonDraft:  workflowJson,
		DialogJsonEnable: "", // 校验成功才入此库
		Uin:              uin,
		SubUin:           subUin,
		StaffID:          staffID,
		ReleaseStatus:    entity.ReleaseStatusUnPublished,
		Action:           entity.ActionInsert,
		CreateTime:       time.Now(),
		UpdateTime:       time.Now(),
	}
	log.InfoContextf(ctx, "CreateWorkflow workflowData:%v", workflowData)
	if len(req.TemplateWorkflowId) > 0 {
		log.InfoContextf(ctx, "CreateWorkflow TemplateWorkflowID:%s", req.TemplateWorkflowId)
		// 创建应用时校验是否存在工作流模版ID，存在则走复制体验中心工作流
		uin, subUin := util.GetUinAndSubAccountUin(ctx)
		err = bottask.CopyAppWorkflow(ctx, appBizId, []string{req.TemplateWorkflowId}, "",
			workflowData.WorkflowName, workflowData.WorkflowDesc, workflowData.WorkflowID, req.TemplateWorkflowId,
			uin, subUin, workflowData.StaffID, entity.CopyExperienceWorkflowType)
		if err != nil {
			log.ErrorContextf(ctx, "CreateWorkflow execExperienceWorkflow err:%v", err)
			return nil, err
		}
	} else {
		err = db.CreateWorkflow(ctx, workflowData)
		if err != nil {
			log.ErrorContextf(ctx, "CreateWorkflow db err:%v", err)
			return nil, err
		}
	}
	return &KEP_WF.CreateWorkflowRsp{
		WorkflowId: workflowData.WorkflowID,
	}, nil
}

// checkWorkflowName 校验工作流的名称
func checkWorkflowName(ctx context.Context, flowId, appBizId, workflowName string) error {
	name := strings.TrimSpace(workflowName)
	nameMaxLen := config.GetMainConfig().VerifyWorkflow.WorkflowNameLen
	log.InfoContextf(ctx, "checkWorkflowName nameMaxLen:%d", nameMaxLen)
	if utf8.RuneCountInString(name) > nameMaxLen {
		return errs.Newf(errors.ErrWorkflowNameTooLong, "工作流名称大于%d个字符，请重新填写", nameMaxLen)
	}
	nameMaxMin := config.GetMainConfig().VerifyWorkflow.MinWorkflowLen
	if utf8.RuneCountInString(name) < nameMaxMin {
		return errs.Newf(errors.ErrWorkflowNameTooShort, "工作流名称小于%d个字符，请重新填写", nameMaxMin)
	}
	count, err := db.GetWorkFLowCountByName(ctx, flowId, appBizId, workflowName)
	if err != nil {
		return errors.ErrSystem
	}
	if count > 0 {
		return errors.ErrWorkflowNameDuplicated
	}
	productCount, err := db.GetProductWorkFLowCountByName(ctx, flowId, appBizId, workflowName)
	if err != nil {
		return errors.ErrSystem
	}
	if productCount > 0 {
		return errors.ErrWorkflowNameDuplicatedProduct
	}
	return nil
}

// DeleteWorkflow 删除Workflow
func DeleteWorkflow(ctx context.Context,
	req *KEP_WF.DeleteWorkflowReq) (*KEP_WF.DeleteWorkflowRsp, error) {
	appBizId := req.GetAppBizId()
	flowIds := set.RemoveDuplicatesAndEmpty(req.GetWorkflowIds())
	// 尝试将字符串转换为 uint64
	_, err := strconv.ParseUint(req.GetAppBizId(), 10, 64)
	if err != nil {
		log.WarnContextf(ctx, "DeleteWorkflow appBizId ParseUint err:%v", err)
		return nil, errors.BadRequestError("应用ID错误")
	}
	// 添加判断机器人是否存在
	appInfo, err := permission.CheckRobot(ctx, 1, uint64(encode.StringToInt64(appBizId)))
	if err != nil {
		log.WarnContextf(ctx, "DeleteWorkflow permission.CheckRobot:%s|%v", err)
		return nil, err
	}
	// 2.7 agent模式不支持工作流修改，后面支持需要放开
	if appInfo.GetKnowledgeQa().GetPattern() == entity.AppPatternAgent {
		return nil, errors.ErrAgentPermissionDenied
	}

	if len(flowIds) <= 0 {
		return nil, errors.ErrWorkflowNotFound
	}
	workflowMap, err := db.GetWorkflowDetails(ctx, flowIds, appBizId)
	if err != nil {
		log.ErrorContextf(ctx, "DeleteWorkflow GetWorkFLowDetails err:%v", err)
		return nil, errors.ErrWorkflowNotFound
	}
	// 判断工作流是否有正在发布的
	var workflowNamePublishList []string
	var workflowIdDeleteList []string
	for _, workflow := range workflowMap {
		if workflow.ReleaseStatus == entity.ReleaseStatusPublishing {
			workflowNamePublishList = append(workflowNamePublishList, workflow.WorkflowName)
		} else {
			workflowIdDeleteList = append(workflowIdDeleteList, workflow.WorkflowID)
		}
	}
	if len(workflowNamePublishList) > 0 {
		log.InfoContextf(ctx, "DeleteWorkflow workflowNamePublishList:%v", workflowNamePublishList)
		return &KEP_WF.DeleteWorkflowRsp{
			FlowNameList: workflowNamePublishList,
		}, errors.ErrWorkflowPublishingNotAllowDeleted
	}
	//// 判断工作流是否有被引用
	//workflowList, err := db.GetWorkflowNamesByRefFlowIds(ctx, flowIds)
	//if err != nil {
	//	log.ErrorContextf(ctx, "DeleteWorkflow GetWorkflowNamesByRefFlowIds err:%v", err)
	//	return nil, errors.ErrWorkflowNotFound
	//}
	//if len(workflowList) > 0 {
	//	var workflowNameList []string
	//	for _, v := range workflowList {
	//		workflowNameList = append(workflowNameList, v.WorkflowName)
	//	}
	//	log.InfoContextf(ctx, "DeleteWorkflow workflowNameList:%v", workflowNameList)
	//	return &KEP_WF.DeleteWorkflowRsp{
	//		FlowNameList: workflowNameList,
	//	}, errors.ErrWorkflowRef
	//}
	if len(workflowIdDeleteList) <= 0 {
		log.ErrorContextf(ctx, "DeleteWorkflow flowIds isempty")
		return nil, errors.BadWorkflowReqError("没有可删除的应用")
	}
	if err := db.DeleteWorkflow(ctx, appBizId, workflowIdDeleteList); err != nil {
		log.ErrorContextf(ctx, "DeleteWorkflow err:%v", err)
		return nil, errors.OpDataFromDBError("删除失败")
	}

	return &KEP_WF.DeleteWorkflowRsp{}, nil
}

// GetWorkflowDetail 获取某个工作流的详细信息
func GetWorkflowDetail(ctx context.Context, req *KEP_WF.GetWorkflowDetailReq) (*KEP_WF.GetWorkflowDetailResp, error) {
	var err error
	workflowID := strings.TrimSpace(req.GetWorkflowId())
	appBizID := strings.TrimSpace(req.GetAppBizId())
	// 尝试将字符串转换为 uint64
	_, err = strconv.ParseUint(req.GetAppBizId(), 10, 64)
	if err != nil {
		log.ErrorContextf(ctx, "GetWorkflowDetail appBizId ParseUint err:%v", err)
		return nil, errors.BadRequestError("应用ID错误")
	}
	// 添加判断机器人是否存在
	_, err = permission.CheckRobot(ctx, 1, uint64(encode.StringToInt64(appBizID)))
	if err != nil {
		log.ErrorContextf(ctx, "GetWorkflowDetail,permission.CheckRobot:%+v", err)
		return nil, err
	}

	// 检查workflow是否存在且未删除
	workflow, err := db.GetWorkflowDetail(ctx, workflowID, appBizID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.ErrWorkflowNotFound
		}
		return nil, err
	}

	if workflow == nil || workflow.WorkflowID == "" {
		return nil, errors.ErrWorkflowNotFound
	}

	// 获取发布库中的数据
	prodWorkflow, err := db.GetProdWorkflowDetail(ctx, workflowID, appBizID)
	if err != nil {
		return nil, nil
	}
	// 如果没有发布，prodWorkflow返回来的就是nil
	var prodJson string
	if prodWorkflow != nil && prodWorkflow.DialogJsonEnable != "" {
		prodJson = prodWorkflow.DialogJsonEnable
	}

	workflow.IsAllowEdit()

	// v2.8.5 画布中节点里密钥加密存储后需要解密后返回给前端
	decryptedDialogJson, err := protoutil.DecryptWorkflowJson(workflow.DialogJsonDraft)
	if err != nil {
		log.ErrorContextf(ctx, "GetWorkflowDetail DecryptWorkflowJson for DialogJson err:%v", err)
		return nil, errors.ErrWorkflowUIJsonParams
	}
	decryptedDialogReleasedJson, err := protoutil.DecryptWorkflowJson(prodJson)
	if err != nil {
		log.ErrorContextf(ctx, "GetWorkflowDetail DecryptWorkflowJson for DialogReleasedJson err:%v", err)
		return nil, errors.ErrWorkflowUIJsonParams
	}

	return &KEP_WF.GetWorkflowDetailResp{
		WorkflowId:      workflowID,
		WorkflowVersion: workflow.Version,
		Name:            workflow.WorkflowName,
		Desc:            workflow.WorkflowDesc,
		//FlowState:          workflow.WorkflowState,
		//FlowStateDesc:      workflow.FrontReleaseStatus(),
		ReleaseStatus:      workflow.FrontReleaseStatus(),
		ReleaseStatusDesc:  workflow.FrontReleaseStatusDesc(),
		CreateTime:         uint32(workflow.CreateTime.Unix()),
		UpdateTime:         uint32(workflow.UpdateTime.Unix()),
		IsAllowEdit:        workflow.IsAllowEdit(),
		IsAllowDelete:      workflow.IsAllowDelete(),
		IsAllowRelease:     workflow.IsAllowRelease(),
		DialogJson:         decryptedDialogJson,
		DialogReleasedJson: decryptedDialogReleasedJson,
	}, nil

}

// ListWorkflow 获取工作流程列表
func ListWorkflow(ctx context.Context,
	req *KEP_WF.ListWorkflowReq) (*KEP_WF.ListWorkflowRsp, error) {
	appBizId := req.GetAppBizId()
	rsp := &KEP_WF.ListWorkflowRsp{}
	sid := util.RequestID(ctx)
	botId, err := util.CheckReqBotBizIDUint64(ctx, appBizId)
	if err != nil {
		return nil, err
	}
	// 判断机器人是否存在
	_, err = permission.CheckRobot(ctx, 1, botId)
	if err != nil {
		log.ErrorContextf(ctx, "ListWorkflow permission.CheckRobot:%s|%v", sid, err)
		return nil, err
	}

	params := entity.ListWorkflowParams{
		Query:         strings.TrimSpace(req.GetQuery()),
		StartTime:     time.Unix(int64(req.GetStartTime()), 0),
		EndTime:       time.Unix(int64(req.GetEndTime()), 0),
		Page:          req.GetPage(),
		PageSize:      req.GetPageSize(),
		BotBizId:      appBizId,
		Actions:       req.GetActions(),
		ReleaseStatus: req.GetStatus(),
	}
	workflows, total, err := db.ListWorkflow(ctx, params)
	if err != nil {
		log.ErrorContextf(ctx, "ListWorkflow|%s|%v", sid, err)
		return nil, errors.ErrWorkflowNotFound
	}
	// 获取staffIds
	staffIds := make([]uint64, 0)
	mm := make(map[uint64]struct{}, 0)

	for _, v := range workflows {
		if _, ok := mm[v.StaffID]; !ok {
			staffIds = append(staffIds, v.StaffID)
			mm[v.StaffID] = struct{}{}
		}
	}
	staffInfosMap, err := getStaffInfoMapByStaffIds(ctx, staffIds)
	if err != nil {
		return nil, errors.OpDataFromDBError(fmt.Sprintf("获取员工信息失败：%s", err.Error()))
	}

	list := make([]*KEP_WF.ListWorkflowRsp_Workflow, 0, len(workflows))
	for _, item := range workflows {
		nickName := staffInfosMap[item.StaffID]
		if len(nickName) == 0 {
			nickName = item.SubUin
		}
		list = append(list, &KEP_WF.ListWorkflowRsp_Workflow{
			WorkflowId:     item.WorkflowID,
			Name:           item.WorkflowName,
			Status:         item.FrontReleaseStatus(),
			StatusDesc:     item.FrontReleaseStatusDesc(),
			UpdateTime:     uint32(item.UpdateTime.Unix()),
			CreateTime:     uint32(item.CreateTime.Unix()),
			IsAllowEdit:    item.IsAllowEdit(),
			IsAllowDelete:  item.IsAllowDelete(),
			IsAllowRelease: item.IsAllowRelease(),
			Desc:           item.WorkflowDesc,
			IsEnable:       item.GetIsEnable(),
			UpdateUser:     nickName,
		})
	}
	rsp.List = list
	rsp.Total = uint32(total)

	return rsp, nil
}

// ListWorkflowInner 内部服务获取工作流程列表
func ListWorkflowInner(ctx context.Context,
	req *KEP_WF.ListWorkflowInnerReq) (*KEP_WF.ListWorkflowInnerRsp, error) {
	appBizId := req.GetAppBizId()
	rsp := &KEP_WF.ListWorkflowInnerRsp{}
	sid := util.RequestID(ctx)
	_, err := util.CheckReqBotBizIDUint64(ctx, appBizId)
	if err != nil {
		return nil, err
	}

	params := entity.ListWorkflowParams{
		Page:          req.GetPage(),
		PageSize:      req.GetPageSize(),
		BotBizId:      appBizId,
		ReleaseStatus: req.GetStatus(),
		WorkflowIds:   req.GetWorkflowIds(),
	}
	workflows, total, err := db.ListWorkflow(ctx, params)
	if err != nil {
		log.ErrorContextf(ctx, "ListWorkflowInner|%s|%v", sid, err)
		return nil, errors.ErrWorkflowNotFound
	}

	list := make([]*KEP_WF.ListWorkflowInnerRsp_Workflow, 0, len(workflows))
	for _, item := range workflows {
		list = append(list, &KEP_WF.ListWorkflowInnerRsp_Workflow{
			WorkflowId: item.WorkflowID,
			Name:       item.WorkflowName,
			Status:     item.FrontReleaseStatus(),
			StatusDesc: item.FrontReleaseStatusDesc(),
			IsEnable:   item.GetIsEnable(),
			Desc:       item.WorkflowDesc,
		})
	}
	rsp.List = list
	rsp.Total = uint32(total)

	return rsp, nil
}

// getStaffInfoMapByStaffIds 获取员工Map
func getStaffInfoMapByStaffIds(ctx context.Context, staffIds []uint64) (map[uint64]string, error) {
	// 获取员工信息
	staffInfosMap := make(map[uint64]string)
	staffIdInfoRsp, err := rpc.ListCorpStaffByIds(ctx, staffIds)
	if err != nil {
		return staffInfosMap, err
	}
	if staffIdInfoRsp != nil && len(staffIdInfoRsp.GetList()) > 0 {
		for _, v := range staffIdInfoRsp.GetList() {
			staffInfosMap[v.Id] = v.NickName
		}
	}
	return staffInfosMap, nil
}

// SwitchWorkflowState 更新工作流程状态
func SwitchWorkflowState(ctx context.Context,
	req *KEP_WF.SwitchWorkflowStateReq) (*KEP_WF.SwitchWorkflowStateRsp, error) {
	appBizId := req.GetAppBizId()
	workflowIds := req.GetWorkflowIds()
	isEnable := req.GetIsEnable()

	botId, err := util.CheckReqBotBizIDUint64(ctx, appBizId)
	if err != nil {
		return nil, err
	}
	// 判断机器人是否存在
	appInfo, err := permission.CheckRobot(ctx, 1, botId)
	if err != nil {
		return nil, err
	}
	// 2.7 agent模式不支持工作流修改，后面支持需要放开
	if util.IsAppAgentModel(appInfo) {
		return nil, errors.ErrAgentPermissionDenied
	}
	// 校验 workflowIds 是否合法
	workflows, err := db.GetWorkflowsByFlowIds(ctx, workflowIds, appBizId)
	if err != nil {
		log.ErrorContextf(ctx, "GetWorkflowsByFlowIds|err:%+v", err)
		return nil, errors.ErrWorkflowNotFound
	}
	if len(workflows) <= 0 {
		log.WarnContextf(ctx, "SwitchWorkflowState workflowIds is empty")
		return nil, errors.ErrWorkflowNotFound
	}
	enableFlowIds := make([]string, 0)
	workflowNameMap := make(map[string]*entity.Workflow)
	for _, v := range workflows {
		enableFlowIds = append(enableFlowIds, v.WorkflowID)
		v.IsEnable = isEnable
		workflowNameMap[v.WorkflowID] = v
	}
	log.InfoContextf(ctx, "enableFlowIds:%+v", enableFlowIds)
	// 判断工作流的状态
	rsp, err := updateTestEnvEnable(ctx, appBizId, workflowNameMap, isEnable)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

// updateTestEnvEnable 更新测试环境工作流状态
func updateTestEnvEnable(ctx context.Context, appBizId string,
	workflowNameMap map[string]*entity.Workflow, isEnable bool) (*KEP_WF.SwitchWorkflowStateRsp, error) {
	rsp := &KEP_WF.SwitchWorkflowStateRsp{}
	var ErrMsg string
	errSwitchId := make([]string, 0)
	// v2.6 11.13 变更，草稿态允许状态切换
	// 1. 当切换测试环境的状态为可用时，草稿态的不能切换
	//workflows, err = db.GetWorkflowsByState(ctx, appBizId, workflowIds,
	//	[]string{entity.WorkflowStateDraft, entity.WorkflowStateDraft}, []string{"PUBLISHING"})
	//if err != nil {
	//	return rsp, err
	//}
	//if len(workflows) > 0 {
	//	for _, v := range workflows {
	//		workName += v.WorkflowName + ","
	//	}
	//	eMsg := "工作流：" + workName + "是草稿态或发布中，无法切换状态，请确认后重试"
	//	return rsp, errors.BadWorkflowReqError(eMsg)
	//}
	var wg sync.WaitGroup
	errorCh := make(chan *string, len(workflowNameMap))
	for _, workflow := range workflowNameMap {
		wg.Add(1) // 为每个goroutine增加计数
		go func(workflow *entity.Workflow) {
			defer wg.Done()
			doneUpdateTestEnvEnable(ctx, workflow, appBizId, errorCh)
		}(workflow)
	}

	go func() {
		wg.Wait()
		close(errorCh) // 关闭通道
	}()

	for workflowID := range errorCh {
		errSwitchId = append(errSwitchId, *workflowID)
		ErrMsg += workflowNameMap[*workflowID].WorkflowName + ", "
	}

	successCnt := len(workflowNameMap) - len(errSwitchId)
	msg, code := switchHandlerErr(isEnable, ErrMsg, successCnt)
	rsp.Msg = msg
	rsp.Code = uint32(code)

	return rsp, nil
}

// doneUpdateTestEnvEnable 切换状态处理
func doneUpdateTestEnvEnable(ctx context.Context, workflow *entity.Workflow, appBizId string,
	errorCh chan *string) {
	defer func() {
		if r := recover(); r != nil {
			log.ErrorContextf(ctx, "SwitchWorkflowState:%+v", r)
		}
	}()
	// 以工作流级别保证向量及表中的数据切换正常
	if err := db.SwitchWorkflowState(ctx, appBizId, workflow); err != nil {
		log.ErrorContextf(ctx, "SwitchWorkflowState:%+v", err)
		// 发送失败的wid到errorCh
		errorCh <- &workflow.WorkflowID
	}
}

// switchHandlerErr ...
func switchHandlerErr(isEnable bool, errMsg string, successCnt int) (string, int) {
	if len(errMsg) > 0 {
		errMsg = "工作流:" + errMsg + "状态切换失败;" + fmt.Sprintf("状态成功切换：%d个", successCnt)
		return errMsg, entity.SwitchFailed
	} else {
		if isEnable {
			errMsg = fmt.Sprintf("已启用%d个工作流，发布应用后在生产环境中生效", successCnt)
		} else {
			errMsg = fmt.Sprintf("已停用%d个工作流，发布应用后在生产环境中生效", successCnt)
		}
		return errMsg, entity.SwitchSuccess
	}
}

// ListNodeInfo 获取画布列表
func ListNodeInfo(ctx context.Context,
	req *KEP_WF.ListNodeInfoReq) (*KEP_WF.ListNodeInfoRsp, error) {
	var jsonStr string
	var flowName string
	var err error
	appBizId := req.GetAppBizId()
	flowId := req.GetWorkflowId()
	workflow := req.GetWorkflow()
	rsp := &KEP_WF.ListNodeInfoRsp{}

	botId, err := util.CheckReqBotBizIDUint64(ctx, appBizId)
	if err != nil {
		return nil, err
	}

	// 判断机器人是否存在
	_, err = permission.CheckRobot(ctx, 1, botId)
	if err != nil {
		return nil, err
	}
	switch workflow {
	case entity.WorkflowNew:
		flowDetail, err := db.GetWorkflowDetail(ctx, flowId, appBizId)
		if err != nil {
			return rsp, errors.ErrWorkflowNotFound
		}
		jsonStr = flowDetail.DialogJsonDraft
		// v2.8.5 画布中节点里密钥加密存储后需要解密后返回给前端
		jsonStr, err = protoutil.DecryptWorkflowJson(jsonStr)
		if err != nil {
			return rsp, errors.OpDataFromDBError("解析画布信息失败")
		}
		flowName = flowDetail.WorkflowName
	case entity.WorkflowOld:
		flowDetail, err := db.GetTaskFlowDetail(ctx, flowId, appBizId)
		if err != nil {
			log.WarnContextf(ctx, "ListNodeInfo|GetTaskFlowDetail:%+v", err)
			return rsp, errors.ErrTaskFlowNotFound
		}
		jsonStr = flowDetail.DialogJsonDraft
		// v2.8.5 画布中节点里密钥加密存储后需要解密后返回给前端 - 老工作流不支持，跳过加解密
		flowName = flowDetail.IntentName
	}
	list, total, err := getNodeFlowInfo(jsonStr, workflow)
	if err != nil {
		log.WarnContextf(ctx, "ListNodeInfo|getNodeFlowInfo:%+v", err)
		return rsp, errors.OpDataFromDBError("获取画布节点信息失败")
	}
	rsp.Total = total
	rsp.FlowName = flowName
	rsp.List = list

	return rsp, nil
}

// GetWorkflowNodeInfoMap 获取工作流画布节点信息
func GetWorkflowNodeInfoMap(flowStr string) (map[string]string, error) {
	nodeInfo := make(map[string]string)
	workflowJson, err := protoutil.JsonToWorkflowForPreCheck(flowStr)
	if err != nil {
		return nil, err
	}
	if workflowJson != nil {
		for _, v := range workflowJson.Nodes {
			nodeInfo[v.NodeID] = v.NodeName
		}
	}
	return nodeInfo, nil
}

// getNodeFlowInfo 获取节点信息
func getNodeFlowInfo(jsonStr string,
	workflow string) ([]*KEP_WF.ListNodeInfoRsp_NodeInfo, uint32, error) {
	list := make([]*KEP_WF.ListNodeInfoRsp_NodeInfo, 0)
	switch workflow {
	case entity.WorkflowNew:
		workflow, err := protoutil.JsonToWorkflow(jsonStr)
		if err != nil {
			return list, 0, nil
		}
		for _, v := range workflow.Nodes {
			list = append(list, &KEP_WF.ListNodeInfoRsp_NodeInfo{
				NodeId:   v.NodeID,
				NodeName: v.NodeName,
				NodeType: v.NodeType.String(),
			})
		}
	case entity.WorkflowOld:
		taskflow, err := protoutil.JsonToTaskFlow(jsonStr)
		if err != nil {
			return list, 0, nil
		}
		for _, v := range taskflow.Nodes {
			list = append(list, &KEP_WF.ListNodeInfoRsp_NodeInfo{
				NodeId:   v.NodeID,
				NodeName: v.NodeName,
				NodeType: v.NodeType.String(),
			})
		}
	}
	return list, uint32(len(list)), nil
}

// GetModelSupportWorkflow 获取模型支持工作流的程度
func GetModelSupportWorkflow(ctx context.Context, req *KEP_WF.GetModelSupportWorkflowReq) (*KEP_WF.GetModelSupportWorkflowResp, error) {
	rsp := new(KEP_WF.GetModelSupportWorkflowResp)
	appId := req.GetAppBizId()
	botId, err := util.CheckReqBotBizIDUint64(ctx, appId)
	mn := strings.TrimSpace(req.GetModelName())
	if err != nil {
		return nil, err
	}
	// 添加判断机器人是否存在
	_, err = permission.CheckRobot(ctx, 1, botId)
	if err != nil {
		return nil, err
	}
	supportList := config.GetMainConfig().LlmSupportWorkflow[entity.LLMSupportWorkflowKey]
	poorSupportList := config.GetMainConfig().LlmSupportWorkflow[entity.LLMPoorSupportWorkflowKey]
	notSupportList := config.GetMainConfig().LlmSupportWorkflow[entity.LLMNotSupportWorkflowKey]
	log.InfoContextf(ctx, "GetModelSupportWorkflow:s:%+v|p:%+v|n:%+v", supportList, poorSupportList, notSupportList)
	for _, v := range supportList {
		if mn == v {
			rsp.Status = entity.LLMSupportWorkflowValue
			rsp.StatusDesc = entity.LLMSupportWorkflowDesc
			return rsp, nil
		}
	}
	for _, v := range poorSupportList {
		if mn == v {
			rsp.Status = entity.LLMPoorSupportWorkflowValue
			rsp.StatusDesc = entity.LLMPoorSupportWorkflowDesc
			return rsp, nil
		}
	}
	for _, v := range notSupportList {
		if mn == v {
			rsp.Status = entity.LLMNotSupportWorkflowValue
			rsp.StatusDesc = entity.LLMNotSupportWorkflowDesc
			return rsp, nil
		}
	}
	return rsp, nil
}

// getWorkflowExist 获取工作流是否存在
func getWorkflowExist(ctx context.Context, appId, workflowId string, envType uint32) (*entity.Workflow, error) {
	env := KEP.GetTaskFlowReleaseStatusReq_EnvType(int32(envType))
	workflow := &entity.Workflow{}
	var err error
	//  测试环境
	if env == KEP.GetTaskFlowReleaseStatusReq_TEST {
		workflow, err = db.GetWorkflowDetail(ctx, workflowId, appId)
		if err != nil {
			return workflow, err
		}
	}

	// 生产环境
	if env == KEP.GetTaskFlowReleaseStatusReq_PROD {
		workflow, err = db.GetProdWorkflowDetail(ctx, workflowId, appId)
		if err != nil {
			return workflow, err
		}
	}

	return workflow, nil
}

// getWorkflowIsDebug 判断工作流是否处于调试模式
func getWorkflowIsDebug(w *entity.Workflow, envType uint32) bool {
	env := KEP.GetTaskFlowReleaseStatusReq_EnvType(int32(envType))

	// 生产环境
	if env == KEP.GetTaskFlowReleaseStatusReq_PROD {
		// 生产环境的工作流没有调试模式
		return false
	}
	//  测试环境
	if env == KEP.GetTaskFlowReleaseStatusReq_TEST {
		// 测试环境 草稿态 及 已发布-草稿态，属于调试模式
		if w.WorkflowState == entity.WorkflowStatePublishedDraft ||
			w.WorkflowState == entity.WorkflowStateDraft {
			return true // 调试模式
		}
	}
	return false
}
func getWorkflowIsEnable(w *entity.Workflow, envType uint32) bool {
	env := KEP.GetTaskFlowReleaseStatusReq_EnvType(int32(envType))
	// 生产环境
	if env == KEP.GetTaskFlowReleaseStatusReq_PROD {
		// env为生产环境时，获取到的就是生产库里面的工作流信息, 待调试不会进行发布
		return w.IsEnable
	}
	//  测试环境
	if env == KEP.GetTaskFlowReleaseStatusReq_TEST {
		// 测试环境 草稿态 及 已发布-草稿态，属于调试模式，调试模式不可检索（启用状态无效）
		if w.WorkflowState == entity.WorkflowStatePublishedDraft ||
			w.WorkflowState == entity.WorkflowStateDraft {
			return false // 调试模式
		}

		// 待发布 / 已发布 的启用有效
		return w.IsEnable
	}
	return false
}

// GetWorkflowReleaseStatus 获取工作流的发布状态
func GetWorkflowReleaseStatus(ctx context.Context,
	req *KEP_WF.GetWorkflowReleaseStatusReq) (*KEP_WF.GetWorkflowReleaseStatusResp, error) {
	rsp := new(KEP_WF.GetWorkflowReleaseStatusResp)
	workflowId := req.GetWorkflowId()
	appId := req.GetAppBizId()
	envType := req.GetEnvTag()
	var scene uint32 = 1
	if envType == 1 {
		scene = 2
	}
	botId, err := util.CheckReqBotBizIDUint64(ctx, appId)
	if err != nil {
		return nil, err
	}
	// 添加判断机器人是否存在
	_, err = permission.CheckRobotWithNoUserLogin(ctx, scene, botId)
	if errors.Is(err, errors.ErrRobotNotFound) {
		log.WarnContextf(ctx, "P|GetWorkflowReleaseStatus|%v", err)
		return rsp, nil
	}
	if err != nil {
		log.ErrorContextf(ctx, "P|GetWorkflowReleaseStatus|%v", err)
		return rsp, err
	}

	// 如果传 工作流Id，则获取工作流的发布状态
	if len(workflowId) > 0 {
		// 判断该应用下工作流是否存在
		workflow, err := getWorkflowExist(ctx, appId, workflowId, envType)
		if errors.Is(err, gorm.ErrRecordNotFound) || workflow == nil {
			rsp.IsExist = false
			return rsp, nil
		} else if err != nil {
			log.ErrorContextf(ctx, "GetWorkflowReleaseStatus|err:%+v", err)
			rsp.IsExist = false
			return rsp, err
		} else {
			rsp.IsExist = true
		}

		// 工作流存在，判断工作流是否处于调试模式
		isDebug := getWorkflowIsDebug(workflow, envType)
		// 判断工作流是否再不同环境发布（在测平环境：指是否可用，与IsEnable效果一样，
		// 在正式环境发布了指数据发布到了prod环境，可用需要看IsEnable）
		isRelease, err := publish.NewPublish().IsWorkflowPublishedQueryById(ctx, appId, workflowId, envType)
		if err != nil {
			return rsp, err
		}
		// 判断工作流是否开启
		isEnable := getWorkflowIsEnable(workflow, envType)
		rsp.IsDebug = isDebug
		rsp.IsRelease = isRelease
		rsp.IsEnable = isEnable
		return rsp, nil
	}

	// 如果不传工作流Id，则获取应用下工作流的发布状态,只要有已经发布过的则isRelease为true
	isRelease, err := publish.NewPublish().IsWorkflowPublished(ctx, appId, envType)
	rsp = &KEP_WF.GetWorkflowReleaseStatusResp{
		IsRelease: isRelease,
	}
	log.Infof("O|GetWorkflowReleaseStatus|%s|%s|ERR:%v", appId, envType)
	if err != nil {
		log.Errorf("E|GetWorkflowReleaseStatus|%s", err.Error())
		return rsp, err
	}
	return rsp, nil
}

// GetCanBeReferencedWorkflowList 获取可以被引用的工作流的列表
func GetCanBeReferencedWorkflowList(ctx context.Context,
	req *KEP_WF.GetCanBeReferencedWorkflowListReq) (*KEP_WF.GetCanBeReferencedWorkflowListRsp, error) {
	appBizId := req.GetAppBizId()
	rsp := &KEP_WF.GetCanBeReferencedWorkflowListRsp{}
	botId, err := util.CheckReqBotBizIDUint64(ctx, appBizId)
	if err != nil {
		return nil, err
	}
	// 判断机器人是否存在
	_, err = permission.CheckRobot(ctx, 1, botId)
	if err != nil {
		log.WarnContextf(ctx, "GetCanBeReferencedWorkflowList permission.CheckRobot:%v", err)
		return nil, err
	}
	if len(req.GetWorkflowId()) <= 0 {
		log.WarnContextf(ctx, "GetCanBeReferencedWorkflowList GetWorkflowId is empty:%s", req.GetWorkflowId())
		return nil, errors.ErrWorkflowNotFound
	}
	page := req.GetPage()
	pageSize := req.GetPageSize()
	if page == 0 {
		page = 1
	}
	if pageSize == 0 {
		pageSize = 10000
	}
	// 获取所有工作流记录
	workflows, workflowMap, err := db.GetAllWorkflowBasics(ctx, appBizId)
	if err != nil {
		log.ErrorContextf(ctx, "GetCanBeReferencedWorkflowList db.GetAllWorkflowBasics err|%v", err)
		return nil, errors.OpDataFromDBError("获取可以被引用的工作流的列表失败")
	}
	// 获取当前应用下所有工作流引用关系
	workflowRefs, err := db.GetAllWorkflowRefs(ctx, appBizId)
	if err != nil {
		log.ErrorContextf(ctx, "GetCanBeReferencedWorkflowList db.GetAllWorkflowRefs err|%v", err)
		return nil, errors.OpDataFromDBError("获取可以被引用的工作流的列表失败")
	}
	// 构建工作流直接上下引用关系
	db.BuildWorkflowDirectReferences(workflows, workflowRefs)
	// 构建工作流所有上下引用关系和深度
	db.GetWorkflowsDeep(ctx, workflows, workflowMap)
	//var logMessage strings.Builder
	//for _, workflow := range workflows {
	//
	//	logMessage.WriteString(fmt.Sprintf("%+v\n\n", *workflow))
	//}

	// 找出可以被引用的工作流
	canBeReferencedWorkflows, total := db.GetCanBeReferencedWorkflows(workflows, workflowMap,
		strings.TrimSpace(req.GetQuery()), req.GetWorkflowId(), pageSize, page)
	list := make([]*KEP_WF.WorkflowRef, 0, len(canBeReferencedWorkflows))
	for _, item := range canBeReferencedWorkflows {
		list = append(list, &KEP_WF.WorkflowRef{
			WorkflowId:   item.WorkflowID,
			WorkflowName: item.WorkflowName,
			WorkflowDesc: item.WorkflowDesc,
			UpdateTime:   uint32(item.UpdateTime.Unix()),
		})
	}
	rsp.PageNumber = page
	rsp.List = list
	rsp.Total = uint32(total)
	return rsp, nil
}

// GetHasBeenReferencedWorkflowList 批量获取指定工作流已经被引用的工作流的列表
func GetHasBeenReferencedWorkflowList(ctx context.Context,
	req *KEP_WF.GetHasBeenReferencedWorkflowListReq) (*KEP_WF.GetHasBeenReferencedWorkflowListRsp, error) {
	appBizId := req.GetAppBizId()
	rsp := &KEP_WF.GetHasBeenReferencedWorkflowListRsp{}
	botId, err := util.CheckReqBotBizIDUint64(ctx, appBizId)
	if err != nil {
		return nil, err
	}
	// 判断机器人是否存在
	_, err = permission.CheckRobot(ctx, 1, botId)
	if err != nil {
		log.WarnContextf(ctx, "GetHasBeenReferencedWorkflowList permission.CheckRobot:%v", err)
		return nil, err
	}
	if len(req.GetWorkflowIds()) <= 0 {
		log.WarnContextf(ctx, "GetHasBeenReferencedWorkflowList GetWorkflowId is empty:%v", req.GetWorkflowIds())
		return nil, errors.ErrWorkflowNotFound
	}
	// 批量获取所有引用关系数据，并收集涉及到的引用和被引用的工作流ID
	workflowRefList, err := db.GetWorkflowInfoByRefFlowIds(ctx, req.GetWorkflowIds())
	if err != nil {
		log.ErrorContextf(ctx, "GetHasBeenReferencedWorkflowList GetWorkflowInfoByRefFlowIds err:%v", err)
		return nil, err
	}
	ids := make([]string, 0)
	refIds := make([]string, 0)
	for _, workflowRef := range workflowRefList {
		ids = append(ids, workflowRef.WorkflowID)
		ids = append(ids, workflowRef.WorkflowRefID)
		refIds = append(refIds, workflowRef.WorkflowRefID)
	}
	ids = set.RemoveDuplicatesAndEmpty(ids)
	refIds = set.RemoveDuplicatesAndEmpty(refIds)
	// 获取引用和被引用的工作流信息
	workflowMap, err := db.GetWorkflowNamesByFlowIds(ctx, ids)
	if err != nil {
		log.ErrorContextf(ctx, "GetHasBeenReferencedWorkflowList GetWorkflowNamesByFlowIds err:%v", err)
		return nil, err
	}
	list := make([]*KEP_WF.WorkflowInfoRef, 0)
	// 根据请求信息，匹配外层被引用的工作流
	for _, workflowID := range refIds {
		if _, ok := workflowMap[workflowID]; ok {
			workflow := KEP_WF.WorkflowInfoRef{
				WorkflowId:   workflowID,
				WorkflowName: workflowMap[workflowID].WorkflowName,
				WorkflowDesc: workflowMap[workflowID].WorkflowDesc,
				List:         []*KEP_WF.WorkflowRef{},
			}
			refList := make([]*KEP_WF.WorkflowRef, 0)
			// 根据工作流，组装被哪些工作流引用的信息
			for _, workflowRef := range workflowRefList {
				if workflowRef.WorkflowRefID == workflowID {
					refList = append(refList, &KEP_WF.WorkflowRef{
						WorkflowId:   workflowRef.WorkflowID,
						WorkflowName: workflowMap[workflowRef.WorkflowID].WorkflowName,
						WorkflowDesc: workflowMap[workflowRef.WorkflowID].WorkflowDesc,
					})
				}
			}
			workflow.List = refList
			list = append(list, &workflow)
		}
	}
	rsp.List = list
	return rsp, nil
}

// GetParamsByWorkflowIds 批量获取指定工作流的输入输出参数
func GetParamsByWorkflowIds(ctx context.Context,
	req *KEP_WF.GetParamsByWorkflowIdsReq) (*KEP_WF.GetParamsByWorkflowIdsRsp, error) {
	appBizId := req.GetAppBizId()
	rsp := &KEP_WF.GetParamsByWorkflowIdsRsp{}
	botId, err := util.CheckReqBotBizIDUint64(ctx, appBizId)
	if err != nil {
		return nil, err
	}
	// 判断机器人是否存在
	_, err = permission.CheckRobot(ctx, 1, botId)
	if err != nil {
		log.WarnContextf(ctx, "GetParamsByWorkflowIds permission.CheckRobot:%v", err)
		return nil, err
	}
	if len(req.GetWorkflowIds()) <= 0 {
		log.WarnContextf(ctx, "GetParamsByWorkflowIds GetWorkflowId is empty:%s", req.GetWorkflowIds())
		return nil, errors.ErrWorkflowNotFound
	}
	workflowList, err := db.GetWorkflowDetails(ctx, req.GetWorkflowIds(), appBizId)
	if err != nil {
		log.ErrorContextf(ctx, "GetParamsByWorkflowIds GetWorkflowDetails err:%v", err)
		return nil, err
	}
	list, err := parseWorkflowParam(ctx, workflowList)
	if err != nil {
		log.ErrorContextf(ctx, "GetParamsByWorkflowIds GetWorkflowDetails err:%v", err)
		return nil, err
	}
	rsp.List = list
	return rsp, nil
}

func parseWorkflowParam(ctx context.Context, workflowList map[string]*entity.Workflow) (
	[]*KEP_WF.WorkflowParamDetail, error) {
	list := make([]*KEP_WF.WorkflowParamDetail, 0)
	for _, item := range workflowList {
		detail := KEP_WF.WorkflowParamDetail{
			WorkflowId:   item.WorkflowID,
			WorkflowName: item.WorkflowName,
			Inputs:       []*KEP_WF.WorkflowParam{},
			Outputs:      []*KEP_WF.WorkflowParam{},
		}
		if len(item.DialogJsonEnable) == 0 {
			log.WarnContextf(ctx, "GetParamsByWorkflowIds JsonToWorkflow DialogJsonEnable is empty")
			list = append(list, &detail)
			continue
		}
		workflow, err := protoutil.JsonToWorkflow(item.DialogJsonEnable)
		if err != nil {
			log.ErrorContextf(ctx, "GetParamsByWorkflowIds JsonToWorkflow json:%s|err:%+v",
				item.DialogJsonEnable, err)
			return list, err
		}
		inputs := make([]*KEP_WF.WorkflowParam, 0)
		outputs := make([]*KEP_WF.WorkflowParam, 0)
		if workflow == nil {
			continue
		}
		for _, node := range workflow.GetNodes() {
			switch node.GetNodeType() {
			case KEP_WF.NodeType_START:
				for _, input := range node.GetInputs() {
					param := parseSubInputParam(input)
					inputs = append(inputs, param)
				}
			case KEP_WF.NodeType_END:
				for _, output := range node.GetOutputs() {
					param := parseSubOutputParam(output)
					outputs = append(outputs, param)
				}
			}
		}
		detail.Inputs = inputs
		detail.Outputs = outputs
		list = append(list, &detail)
	}
	return list, nil
}

// parseSubInputParam 解析输入子层级
func parseSubInputParam(input *KEP_WF.InputParam) *KEP_WF.WorkflowParam {
	param := &KEP_WF.WorkflowParam{
		Name:            input.GetName(),
		Type:            input.GetType().String(),
		Desc:            input.GetDesc(),
		IsRequired:      input.GetIsRequired(),
		DefaultValue:    input.GetDefaultValue(),
		DefaultFileName: input.GetDefaultFileName(),
	}
	subInputs := input.GetSubInputs()
	for _, subInput := range subInputs {
		subParam := parseSubInputParam(subInput)
		param.Properties = append(param.Properties, subParam)
	}
	return param
}

// parseSubOutputParam 解析输出子层级
func parseSubOutputParam(output *KEP_WF.OutputParam) *KEP_WF.WorkflowParam {
	param := &KEP_WF.WorkflowParam{
		Name: output.GetTitle(),
		Type: output.GetType().String(),
		Desc: output.GetDesc(),
	}
	subOutputs := output.GetProperties()
	for _, output := range subOutputs {
		subOutputs := parseSubOutputParam(output)
		param.Properties = append(param.Properties, subOutputs)
	}
	return param
}

// GetHasReferencedPluginToolWorkflowList 获取引用指定插件工具的工作流列表
func GetHasReferencedPluginToolWorkflowList(ctx context.Context,
	req *KEP_WF.GetHasReferencedPluginToolWorkflowListReq) (*KEP_WF.GetHasReferencedPluginToolWorkflowListRsp, error) {
	rsp := &KEP_WF.GetHasReferencedPluginToolWorkflowListRsp{
		List: []*KEP_WF.WorkflowRef{},
	}
	workflowRefPlugins, err := db.GetWorkflowRefPlugin(ctx, req.GetPluginId(), req.GetToolId(), "")
	if err != nil {
		log.ErrorContextf(ctx, "GetHasReferencedPluginToolWorkflowList GetWorkflowRefKnowledge err:%v", err)
		return nil, err
	}
	if len(workflowRefPlugins) == 0 {
		return rsp, nil
	}

	workflowIdMap := make(map[string]struct{})
	for _, workflowRefPlugin := range workflowRefPlugins {
		workflowIdMap[workflowRefPlugin.WorkflowID] = struct{}{}
	}
	workflowIds := make([]string, 0)
	for workflowId := range workflowIdMap {
		workflowIds = append(workflowIds, workflowId)
	}

	workflowMap, err := db.GetWorkflowNamesByFlowIds(ctx, workflowIds)
	if err != nil {
		log.ErrorContextf(ctx, "GetHasReferencedPluginToolWorkflowList GetWorkflowNamesByFlowIds err:%v", err)
		return nil, err
	}

	workflowRefPluginMap := make(map[string]struct{})
	for _, workflowRefPlugin := range workflowRefPlugins {
		key := fmt.Sprintf("%s|%s", workflowRefPlugin.RobotID, workflowRefPlugin.WorkflowID)
		if _, ok := workflowRefPluginMap[key]; ok {
			// 去重，因为同一个工作流可能多次引用同一个插件工具
			continue
		}
		workflowRef := &KEP_WF.WorkflowRef{
			WorkflowId:   workflowRefPlugin.WorkflowID,
			WorkflowName: workflowMap[workflowRefPlugin.WorkflowID].WorkflowName,
			WorkflowDesc: workflowMap[workflowRefPlugin.WorkflowID].WorkflowDesc,
			AppBizId:     workflowRefPlugin.RobotID,
		}
		rsp.List = append(rsp.List, workflowRef)
		workflowRefPluginMap[key] = struct{}{}
	}
	return rsp, nil
}

// GetEnableCustomAsk 参数节点否支持自定义话术
func GetEnableCustomAsk(ctx context.Context, req *KEP_WF.GetEnableCustomAskReq) (*KEP_WF.GetEnableCustomAskResp, error) {
	rsp := &KEP_WF.GetEnableCustomAskResp{}
	appBizId := req.GetAppBizId()
	botId, err := util.CheckReqBotBizIDUint64(ctx, appBizId)
	if err != nil {
		return rsp, err
	}
	// 判断机器人是否存在
	_, err = permission.CheckRobot(ctx, 1, botId)
	if err != nil {
		log.WarnContextf(ctx, "GetParamsByWorkflowIds permission.CheckRobot:%v", err)
		return rsp, err
	}
	sid := fmt.Sprintf("%d", util.SID(ctx))
	log.DebugContextf(ctx, "GetEnableCustomAsk|sid:%s", sid)
	enableSids := config.GetMainConfig().VerifyParameter.CustomAskEnableSid
	if util.ContainsList(enableSids, sid) {
		rsp.Enable = true
	}
	return rsp, nil
}

// GetWorkflowListByDoc 批量获取文档被引用的工作流列表
func GetWorkflowListByDoc(ctx context.Context,
	req *KEP_WF.GetWorkflowListByDocReq) (*KEP_WF.GetWorkflowListByDocRsp, error) {
	rsp := &KEP_WF.GetWorkflowListByDocRsp{}
	//appBizId := req.GetAppBizId()
	//botId, err := util.CheckReqBotBizIDUint64(ctx, appBizId)
	//if err != nil {
	//	return nil, err
	//}
	//// 判断机器人是否存在
	//_, err = permission.CheckRobot(ctx, 1, botId)
	//if err != nil {
	//	log.WarnContextf(ctx, "GetWorkflowListByDoc permission.CheckRobot:%v", err)
	//	return nil, err
	//}
	if len(req.GetDocBizIds()) <= 0 {
		log.WarnContextf(ctx, "GetWorkflowListByDoc DocBizIds is empty")
		return nil, errors.BadWorkflowReqError("请求参数没有文档ID")
	}
	// 批量获取所有引用关系数据，并收集涉及到的文档ID和引用文档的工作流ID
	workflowRefByDocList, err := db.GetWorkflowListByDoc(ctx, req.GetDocBizIds())
	if err != nil {
		log.ErrorContextf(ctx, "GetWorkflowListByDoc db.GetWorkflowListByDoc err:%v", err)
		return nil, err
	}
	workflowIDs := make([]string, 0)
	docIDS := make([]string, 0)
	for _, workflowRefByDoc := range workflowRefByDocList {
		workflowIDs = append(workflowIDs, workflowRefByDoc.WorkflowID)
		docIDS = append(docIDS, workflowRefByDoc.BizId)
	}
	workflowIDs = set.RemoveDuplicatesAndEmpty(workflowIDs)
	docIDS = set.RemoveDuplicatesAndEmpty(docIDS)
	// 获取引用文档的工作流信息
	workflowMap, err := db.GetWorkflowNamesByFlowIds(ctx, workflowIDs)
	if err != nil {
		log.ErrorContextf(ctx, "GetWorkflowListByDoc db.GetWorkflowNamesByFlowIds err:%v", err)
		return nil, err
	}
	list := make([]*KEP_WF.DocRefByWorkflow, 0)
	publishEnable := config.GetMainConfig().VerifyWorkflow.DebuggedOrPublishedEnable
	// 根据请求信息，匹配外层被引用的工作流
	for _, docID := range docIDS {
		workflow := KEP_WF.DocRefByWorkflow{
			DocBizId:     docID,
			WorkflowList: []*KEP_WF.WorkflowRef{},
		}
		refList := make([]*KEP_WF.WorkflowRef, 0)
		// 根据文档，组装引用了文档的工作流信息
		for _, workflowRefByDoc := range workflowRefByDocList {
			if workflowRefByDoc.BizId == docID {
				if workflowRef, ok := workflowMap[workflowRefByDoc.WorkflowID]; ok {
					if publishEnable && !(workflowRef.ReleaseStatus == entity.WorkflowReleaseStatusPublished ||
						workflowRef.WorkflowState == entity.WorkflowStateEnable ||
						workflowRef.WorkflowState == entity.WorkflowStatePublishedChange) {
						continue
					}
					refList = append(refList, &KEP_WF.WorkflowRef{
						WorkflowId:   workflowRefByDoc.WorkflowID,
						WorkflowName: workflowMap[workflowRefByDoc.WorkflowID].WorkflowName,
						WorkflowDesc: workflowMap[workflowRefByDoc.WorkflowID].WorkflowDesc,
					})
				}
			}
		}
		if len(refList) > 0 {
			workflow.WorkflowList = refList
			list = append(list, &workflow)
		}
	}
	rsp.List = list
	return rsp, nil
}

// GetWorkflowListByAttribute 批量获取标签被引用的工作流列表
func GetWorkflowListByAttribute(ctx context.Context,
	req *KEP_WF.GetWorkflowListByAttributeReq) (*KEP_WF.GetWorkflowListByAttributeRsp, error) {
	rsp := &KEP_WF.GetWorkflowListByAttributeRsp{}
	//appBizId := req.GetAppBizId()
	//botId, err := util.CheckReqBotBizIDUint64(ctx, appBizId)
	//if err != nil {
	//	return nil, err
	//}
	//// 判断机器人是否存在
	//_, err = permission.CheckRobot(ctx, 1, botId)
	//if err != nil {
	//	log.WarnContextf(ctx, "GetWorkflowListByAttribute permission.CheckRobot:%v", err)
	//	return nil, err
	//}
	if len(req.GetAttributeBizIds()) <= 0 {
		log.WarnContextf(ctx, "GetWorkflowListByAttribute AttributeBizIds is empty")
		return nil, errors.BadWorkflowReqError("请求参数没有标签ID")
	}
	// 批量获取所有引用关系数据，并收集涉及到的标签ID和引用文档的工作流ID
	workflowRefByAttributeList, err := db.GetWorkflowListByAttribute(ctx, req.GetAttributeBizIds())
	if err != nil {
		log.ErrorContextf(ctx, "GetWorkflowListByAttribute db.GetWorkflowListByAttribute err:%v", err)
		return nil, err
	}
	workflowIDs := make([]string, 0)
	attributeIDS := make([]string, 0)
	for _, workflowRefByAttribute := range workflowRefByAttributeList {
		workflowIDs = append(workflowIDs, workflowRefByAttribute.WorkflowID)
		attributeIDS = append(attributeIDS, workflowRefByAttribute.BizId)
	}
	workflowIDs = set.RemoveDuplicatesAndEmpty(workflowIDs)
	attributeIDS = set.RemoveDuplicatesAndEmpty(attributeIDS)
	// 获取引用标签的工作流信息
	workflowMap, err := db.GetWorkflowNamesByFlowIds(ctx, workflowIDs)
	if err != nil {
		log.ErrorContextf(ctx, "GetWorkflowListByAttribute db.GetWorkflowNamesByFlowIds err:%v", err)
		return nil, err
	}
	list := make([]*KEP_WF.AttributeRefByWorkflow, 0)
	publishEnable := config.GetMainConfig().VerifyWorkflow.DebuggedOrPublishedEnable
	// 根据请求信息，匹配外层被引用的工作流
	for _, attributeID := range attributeIDS {
		workflow := KEP_WF.AttributeRefByWorkflow{
			AttributeBizId: attributeID,
			WorkflowList:   []*KEP_WF.WorkflowRef{},
		}
		refList := make([]*KEP_WF.WorkflowRef, 0)
		// 根据文档，组装引用了文档的工作流信息
		for _, workflowRefByAttribute := range workflowRefByAttributeList {
			if workflowRefByAttribute.BizId == attributeID {
				if workflowRef, ok := workflowMap[workflowRefByAttribute.WorkflowID]; ok {
					if publishEnable && !(workflowRef.ReleaseStatus == entity.WorkflowReleaseStatusPublished ||
						workflowRef.WorkflowState == entity.WorkflowStateEnable ||
						workflowRef.WorkflowState == entity.WorkflowStatePublishedChange) {
						continue
					}
					refList = append(refList, &KEP_WF.WorkflowRef{
						WorkflowId:   workflowRefByAttribute.WorkflowID,
						WorkflowName: workflowMap[workflowRefByAttribute.WorkflowID].WorkflowName,
						WorkflowDesc: workflowMap[workflowRefByAttribute.WorkflowID].WorkflowDesc,
					})
				}
			}
		}
		if len(refList) > 0 {
			workflow.WorkflowList = refList
			list = append(list, &workflow)
		}
	}
	rsp.List = list
	return rsp, nil
}

// GetWorkflowListByAttributeLabel 批量获取标签值被引用的工作流列表
func GetWorkflowListByAttributeLabel(ctx context.Context,
	req *KEP_WF.GetWorkflowListByAttributeLabelReq) (*KEP_WF.GetWorkflowListByAttributeLabelRsp, error) {
	rsp := &KEP_WF.GetWorkflowListByAttributeLabelRsp{}
	//appBizId := req.GetAppBizId()
	//botId, err := util.CheckReqBotBizIDUint64(ctx, appBizId)
	//if err != nil {
	//	return nil, err
	//}
	//// 判断机器人是否存在
	//_, err = permission.CheckRobot(ctx, 1, botId)
	//if err != nil {
	//	log.WarnContextf(ctx, "GetWorkflowListByAttributeLabel permission.CheckRobot:%v", err)
	//	return nil, err
	//}
	if len(req.GetAttributeLabelBizIds()) <= 0 {
		log.WarnContextf(ctx, "GetWorkflowListByAttributeLabel AttributeLabelBizIds is empty")
		return nil, errors.BadWorkflowReqError("请求参数没有标签值ID")
	}
	// 批量获取所有引用关系数据，并收集涉及到的标签ID和引用文档的工作流ID
	workflowRefByAttributeLabelList, err := db.GetWorkflowListByAttributeLabel(ctx, req.GetAttributeLabelBizIds())
	if err != nil {
		log.ErrorContextf(ctx, "GetWorkflowListByAttributeLabel db.GetWorkflowListByAttributeLabel err:%v", err)
		return nil, err
	}
	workflowIDs := make([]string, 0)
	attributeLabelIDS := make([]string, 0)
	for _, workflowRefByAttributeLabel := range workflowRefByAttributeLabelList {
		workflowIDs = append(workflowIDs, workflowRefByAttributeLabel.WorkflowID)
		attributeLabelIDS = append(attributeLabelIDS, workflowRefByAttributeLabel.LabelId)
	}
	workflowIDs = set.RemoveDuplicatesAndEmpty(workflowIDs)
	attributeLabelIDS = set.RemoveDuplicatesAndEmpty(attributeLabelIDS)
	// 获取引用标签值的工作流信息
	workflowMap, err := db.GetWorkflowNamesByFlowIds(ctx, workflowIDs)
	if err != nil {
		log.ErrorContextf(ctx, "GetWorkflowListByAttributeLabel db.GetWorkflowNamesByFlowIds err:%v", err)
		return nil, err
	}
	list := make([]*KEP_WF.AttributeLabelRefByWorkflow, 0)
	publishEnable := config.GetMainConfig().VerifyWorkflow.DebuggedOrPublishedEnable
	// 根据请求信息，匹配外层被引用的工作流
	for _, attributeLabelID := range attributeLabelIDS {
		workflow := KEP_WF.AttributeLabelRefByWorkflow{
			AttributeLabelBizId: attributeLabelID,
			WorkflowList:        []*KEP_WF.WorkflowRef{},
		}
		refList := make([]*KEP_WF.WorkflowRef, 0)
		// 根据文档，组装引用了文档的工作流信息
		for _, workflowRefByAttributeLabel := range workflowRefByAttributeLabelList {
			if workflowRefByAttributeLabel.LabelId == attributeLabelID {
				if workflowRef, ok := workflowMap[workflowRefByAttributeLabel.WorkflowID]; ok {
					if publishEnable && !(workflowRef.ReleaseStatus == entity.WorkflowReleaseStatusPublished ||
						workflowRef.WorkflowState == entity.WorkflowStateEnable ||
						workflowRef.WorkflowState == entity.WorkflowStatePublishedChange) {
						continue
					}
					refList = append(refList, &KEP_WF.WorkflowRef{
						WorkflowId:   workflowRefByAttributeLabel.WorkflowID,
						WorkflowName: workflowMap[workflowRefByAttributeLabel.WorkflowID].WorkflowName,
						WorkflowDesc: workflowMap[workflowRefByAttributeLabel.WorkflowID].WorkflowDesc,
					})
				}
			}
		}
		if len(refList) > 0 {
			workflow.WorkflowList = refList
			list = append(list, &workflow)
		}
	}
	rsp.List = list
	return rsp, nil
}
