-- 工作流文档关联关系表增加类型枚举(当前只需要在沙箱环境执行)---新数据库---workFlow
ALTER TABLE `t_workflow_ref_knowledge`
    MODIFY COLUMN f_type ENUM('ALL', 'DOC', 'DOC_CATE', 'QA', 'TAG') NOT NULL DEFAULT 'ALL'
    COMMENT '知识类型 ALL: 内外层全部知识|DOC:文档｜DOC_CATE:文档分类｜QA：问答（默认是全部）｜TAG：标签';


-- 工作流文档关联关系表增加知识库ID字段(当前只需要在沙箱环境执行)---新数据库---workFlow
ALTER TABLE `t_workflow_ref_knowledge`
    ADD COLUMN `f_knowledge_biz_id` varchar(64) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '知识库ID' AFTER `f_node_id`;

-- 工作流文档关联关系表增加知识库类型字段(当前只需要在沙箱环境执行)---新数据库---workFlow
ALTER TABLE `t_workflow_ref_knowledge`
    ADD COLUMN `f_knowledge_type` enum ('DEFAULT', 'SHARED', 'ALL_KNOWLEDGE') COLLATE utf8mb4_bin NOT NULL DEFAULT 'DEFAULT'
    COMMENT '知识库类型 DEFAULT: 默认知识库|SHARED: 共享知识库|ALL_KNOWLEDGE: 外层全部知识' AFTER `f_node_id`;


-- 工作流embedding向量缓存t_vector_store表f_type字段增加向量embedding类型
ALTER TABLE t_vector_store
    MODIFY COLUMN f_type ENUM(
    'FLOW_NAME',
    'EXAMPLE',
    'FLOW_NAME_EXAMPLE_DESC'  -- 新增枚举值
    ) NOT NULL DEFAULT 'FLOW_NAME' COMMENT '类型';

-- 工作流向量库t_vector_store表更新唯一键idx_vector_id
ALTER TABLE `t_vector_store`
    DROP INDEX idx_vector_id;
ALTER TABLE `t_vector_store`
    ADD UNIQUE idx_vector_id(`f_biz_id`,`f_robot_id`,`f_type`,`f_embedding_model`) USING BTREE;

-- 工作流向量库t_vector_group表增加应用向量库升级状态字段
ALTER TABLE `t_vector_group`
    ADD COLUMN `f_upgrade_status` TINYINT(1) DEFAULT 0 COMMENT '向量库是否在升级中（0=升级完成，1=升级中）';

-- 工作流运行节点记录t_node_run增加错误码字段
ALTER TABLE `t_node_run`
    ADD COLUMN `f_fail_code` varchar(100) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '错误码' AFTER `f_fail_message`;
