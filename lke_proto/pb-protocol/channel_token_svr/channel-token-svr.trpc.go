// Code generated by trpc-go/trpc-go-cmdline v2.8.30. DO NOT EDIT.
// source: channel-token-svr.proto

package channel_token_svr

import (
	"context"
	"errors"
	"fmt"

	_ "git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/codec"
	_ "git.code.oa.com/trpc-go/trpc-go/http"
	"git.code.oa.com/trpc-go/trpc-go/server"
)

// START ======================================= Server Service Definition ======================================= START

// ChannelTokenServiceService defines service.
type ChannelTokenServiceService interface {
	// GetChannelToken GetChannelToken 获取渠道token接口
	GetChannelToken(ctx context.Context, req *GetChannelTokenReq) (*GetChannelTokenRsp, error)
	// GetWechatPlatformInfo GetChannelToken 获取渠道token接口
	GetWechatPlatformInfo(ctx context.Context, req *GetWechatPlatformInfoReq) (*GetWechatPlatformInfoRsp, error)
	// RefreshChannelToken RefreshChannelToken 刷新渠道token接口
	RefreshChannelToken(ctx context.Context, req *RefreshChannelTokenReq) (*RefreshChannelTokenRsp, error)
	// GetWecomAccessToken GetWecomAccessToken 用来判断用户传入的企微参数是否有效
	GetWecomAccessToken(ctx context.Context, req *GetWecomAccessTokenReq) (*GetWecomAccessTokenRsp, error)
}

func ChannelTokenServiceService_GetChannelToken_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetChannelTokenReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ChannelTokenServiceService).GetChannelToken(ctx, reqbody.(*GetChannelTokenReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ChannelTokenServiceService_GetWechatPlatformInfo_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetWechatPlatformInfoReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ChannelTokenServiceService).GetWechatPlatformInfo(ctx, reqbody.(*GetWechatPlatformInfoReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ChannelTokenServiceService_RefreshChannelToken_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &RefreshChannelTokenReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ChannelTokenServiceService).RefreshChannelToken(ctx, reqbody.(*RefreshChannelTokenReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ChannelTokenServiceService_GetWecomAccessToken_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetWecomAccessTokenReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ChannelTokenServiceService).GetWecomAccessToken(ctx, reqbody.(*GetWecomAccessTokenReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

// ChannelTokenServiceServer_ServiceDesc descriptor for server.RegisterService.
var ChannelTokenServiceServer_ServiceDesc = server.ServiceDesc{
	ServiceName: "trpc.KEP.channel_token_svr.ChannelTokenService",
	HandlerType: ((*ChannelTokenServiceService)(nil)),
	Methods: []server.Method{
		{
			Name: "/trpc.KEP.channel_token_svr.ChannelTokenService/GetChannelToken",
			Func: ChannelTokenServiceService_GetChannelToken_Handler,
		},
		{
			Name: "/trpc.KEP.channel_token_svr.ChannelTokenService/GetWechatPlatformInfo",
			Func: ChannelTokenServiceService_GetWechatPlatformInfo_Handler,
		},
		{
			Name: "/trpc.KEP.channel_token_svr.ChannelTokenService/RefreshChannelToken",
			Func: ChannelTokenServiceService_RefreshChannelToken_Handler,
		},
		{
			Name: "/trpc.KEP.channel_token_svr.ChannelTokenService/GetWecomAccessToken",
			Func: ChannelTokenServiceService_GetWecomAccessToken_Handler,
		},
	},
}

// RegisterChannelTokenServiceService registers service.
func RegisterChannelTokenServiceService(s server.Service, svr ChannelTokenServiceService) {
	if err := s.Register(&ChannelTokenServiceServer_ServiceDesc, svr); err != nil {
		panic(fmt.Sprintf("ChannelTokenService register error:%v", err))
	}
}

// START --------------------------------- Default Unimplemented Server Service --------------------------------- START

type UnimplementedChannelTokenService struct{}

// GetChannelToken GetChannelToken 获取渠道token接口
func (s *UnimplementedChannelTokenService) GetChannelToken(ctx context.Context, req *GetChannelTokenReq) (*GetChannelTokenRsp, error) {
	return nil, errors.New("rpc GetChannelToken of service ChannelTokenService is not implemented")
}

// GetWechatPlatformInfo GetChannelToken 获取渠道token接口
func (s *UnimplementedChannelTokenService) GetWechatPlatformInfo(ctx context.Context, req *GetWechatPlatformInfoReq) (*GetWechatPlatformInfoRsp, error) {
	return nil, errors.New("rpc GetWechatPlatformInfo of service ChannelTokenService is not implemented")
}

// RefreshChannelToken RefreshChannelToken 刷新渠道token接口
func (s *UnimplementedChannelTokenService) RefreshChannelToken(ctx context.Context, req *RefreshChannelTokenReq) (*RefreshChannelTokenRsp, error) {
	return nil, errors.New("rpc RefreshChannelToken of service ChannelTokenService is not implemented")
}

// GetWecomAccessToken GetWecomAccessToken 用来判断用户传入的企微参数是否有效
func (s *UnimplementedChannelTokenService) GetWecomAccessToken(ctx context.Context, req *GetWecomAccessTokenReq) (*GetWecomAccessTokenRsp, error) {
	return nil, errors.New("rpc GetWecomAccessToken of service ChannelTokenService is not implemented")
}

// END --------------------------------- Default Unimplemented Server Service --------------------------------- END

// END ======================================= Server Service Definition ======================================= END

// START ======================================= Client Service Definition ======================================= START

// ChannelTokenServiceClientProxy defines service client proxy
type ChannelTokenServiceClientProxy interface {
	// GetChannelToken GetChannelToken 获取渠道token接口
	GetChannelToken(ctx context.Context, req *GetChannelTokenReq, opts ...client.Option) (rsp *GetChannelTokenRsp, err error)

	// GetWechatPlatformInfo GetChannelToken 获取渠道token接口
	GetWechatPlatformInfo(ctx context.Context, req *GetWechatPlatformInfoReq, opts ...client.Option) (rsp *GetWechatPlatformInfoRsp, err error)

	// RefreshChannelToken RefreshChannelToken 刷新渠道token接口
	RefreshChannelToken(ctx context.Context, req *RefreshChannelTokenReq, opts ...client.Option) (rsp *RefreshChannelTokenRsp, err error)

	// GetWecomAccessToken GetWecomAccessToken 用来判断用户传入的企微参数是否有效
	GetWecomAccessToken(ctx context.Context, req *GetWecomAccessTokenReq, opts ...client.Option) (rsp *GetWecomAccessTokenRsp, err error)
}

type ChannelTokenServiceClientProxyImpl struct {
	client client.Client
	opts   []client.Option
}

var NewChannelTokenServiceClientProxy = func(opts ...client.Option) ChannelTokenServiceClientProxy {
	return &ChannelTokenServiceClientProxyImpl{client: client.DefaultClient, opts: opts}
}

func (c *ChannelTokenServiceClientProxyImpl) GetChannelToken(ctx context.Context, req *GetChannelTokenReq, opts ...client.Option) (*GetChannelTokenRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.channel_token_svr.ChannelTokenService/GetChannelToken")
	msg.WithCalleeServiceName(ChannelTokenServiceServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("channel_token_svr")
	msg.WithCalleeService("ChannelTokenService")
	msg.WithCalleeMethod("GetChannelToken")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetChannelTokenRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ChannelTokenServiceClientProxyImpl) GetWechatPlatformInfo(ctx context.Context, req *GetWechatPlatformInfoReq, opts ...client.Option) (*GetWechatPlatformInfoRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.channel_token_svr.ChannelTokenService/GetWechatPlatformInfo")
	msg.WithCalleeServiceName(ChannelTokenServiceServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("channel_token_svr")
	msg.WithCalleeService("ChannelTokenService")
	msg.WithCalleeMethod("GetWechatPlatformInfo")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetWechatPlatformInfoRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ChannelTokenServiceClientProxyImpl) RefreshChannelToken(ctx context.Context, req *RefreshChannelTokenReq, opts ...client.Option) (*RefreshChannelTokenRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.channel_token_svr.ChannelTokenService/RefreshChannelToken")
	msg.WithCalleeServiceName(ChannelTokenServiceServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("channel_token_svr")
	msg.WithCalleeService("ChannelTokenService")
	msg.WithCalleeMethod("RefreshChannelToken")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &RefreshChannelTokenRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ChannelTokenServiceClientProxyImpl) GetWecomAccessToken(ctx context.Context, req *GetWecomAccessTokenReq, opts ...client.Option) (*GetWecomAccessTokenRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.channel_token_svr.ChannelTokenService/GetWecomAccessToken")
	msg.WithCalleeServiceName(ChannelTokenServiceServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("channel_token_svr")
	msg.WithCalleeService("ChannelTokenService")
	msg.WithCalleeMethod("GetWecomAccessToken")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetWecomAccessTokenRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

// END ======================================= Client Service Definition ======================================= END
