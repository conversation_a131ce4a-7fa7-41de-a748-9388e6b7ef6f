// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.31.1
// source: bot-admin-login.proto

package bot_admin_config_server

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 登录请求
type LoginReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 手机号
	Telephone string `protobuf:"bytes,1,opt,name=telephone,proto3" json:"telephone,omitempty"`
	// 验证码
	Code string `protobuf:"bytes,2,opt,name=code,proto3" json:"code,omitempty"`
	// 是否同意用户协议/隐私政策
	IsAgreed bool `protobuf:"varint,3,opt,name=is_agreed,json=isAgreed,proto3" json:"is_agreed,omitempty"`
	// 登录方式 0（默认手机号登录），1（账户密码登录），2（邮箱登录）
	LoginType uint32 `protobuf:"varint,4,opt,name=login_type,json=loginType,proto3" json:"login_type,omitempty"`
	// 用户账户
	Account string `protobuf:"bytes,5,opt,name=account,proto3" json:"account,omitempty"`
	// 用户密码 md5加密
	Password string `protobuf:"bytes,6,opt,name=password,proto3" json:"password,omitempty"`
	// 用户类型：0（正常用户），1（体验用户）
	UserType uint32 `protobuf:"varint,7,opt,name=user_type,json=userType,proto3" json:"user_type,omitempty"`
	// 邮箱
	Email         string `protobuf:"bytes,8,opt,name=email,proto3" json:"email,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LoginReq) Reset() {
	*x = LoginReq{}
	mi := &file_bot_admin_login_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LoginReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoginReq) ProtoMessage() {}

func (x *LoginReq) ProtoReflect() protoreflect.Message {
	mi := &file_bot_admin_login_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoginReq.ProtoReflect.Descriptor instead.
func (*LoginReq) Descriptor() ([]byte, []int) {
	return file_bot_admin_login_proto_rawDescGZIP(), []int{0}
}

func (x *LoginReq) GetTelephone() string {
	if x != nil {
		return x.Telephone
	}
	return ""
}

func (x *LoginReq) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *LoginReq) GetIsAgreed() bool {
	if x != nil {
		return x.IsAgreed
	}
	return false
}

func (x *LoginReq) GetLoginType() uint32 {
	if x != nil {
		return x.LoginType
	}
	return 0
}

func (x *LoginReq) GetAccount() string {
	if x != nil {
		return x.Account
	}
	return ""
}

func (x *LoginReq) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *LoginReq) GetUserType() uint32 {
	if x != nil {
		return x.UserType
	}
	return 0
}

func (x *LoginReq) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

// 登录响应
type LoginRsp struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// cookie映射值
	Token string `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`
	// 用户名
	UserName string `protobuf:"bytes,2,opt,name=user_name,json=userName,proto3" json:"user_name,omitempty"`
	// 用户头像
	UserAvatar string `protobuf:"bytes,3,opt,name=user_avatar,json=userAvatar,proto3" json:"user_avatar,omitempty"`
	// 手机号
	Telephone string `protobuf:"bytes,4,opt,name=telephone,proto3" json:"telephone,omitempty"`
	// 用户ID
	StaffBizId string `protobuf:"bytes,6,opt,name=staff_biz_id,json=staffBizId,proto3" json:"staff_biz_id,omitempty"`
	// 邮箱
	Email         string `protobuf:"bytes,7,opt,name=email,proto3" json:"email,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LoginRsp) Reset() {
	*x = LoginRsp{}
	mi := &file_bot_admin_login_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LoginRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoginRsp) ProtoMessage() {}

func (x *LoginRsp) ProtoReflect() protoreflect.Message {
	mi := &file_bot_admin_login_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoginRsp.ProtoReflect.Descriptor instead.
func (*LoginRsp) Descriptor() ([]byte, []int) {
	return file_bot_admin_login_proto_rawDescGZIP(), []int{1}
}

func (x *LoginRsp) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *LoginRsp) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

func (x *LoginRsp) GetUserAvatar() string {
	if x != nil {
		return x.UserAvatar
	}
	return ""
}

func (x *LoginRsp) GetTelephone() string {
	if x != nil {
		return x.Telephone
	}
	return ""
}

func (x *LoginRsp) GetStaffBizId() string {
	if x != nil {
		return x.StaffBizId
	}
	return ""
}

func (x *LoginRsp) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

// 登出请求
type LogoutReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LogoutReq) Reset() {
	*x = LogoutReq{}
	mi := &file_bot_admin_login_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LogoutReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LogoutReq) ProtoMessage() {}

func (x *LogoutReq) ProtoReflect() protoreflect.Message {
	mi := &file_bot_admin_login_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LogoutReq.ProtoReflect.Descriptor instead.
func (*LogoutReq) Descriptor() ([]byte, []int) {
	return file_bot_admin_login_proto_rawDescGZIP(), []int{2}
}

// 登出响应
type LogoutRsp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LogoutRsp) Reset() {
	*x = LogoutRsp{}
	mi := &file_bot_admin_login_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LogoutRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LogoutRsp) ProtoMessage() {}

func (x *LogoutRsp) ProtoReflect() protoreflect.Message {
	mi := &file_bot_admin_login_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LogoutRsp.ProtoReflect.Descriptor instead.
func (*LogoutRsp) Descriptor() ([]byte, []int) {
	return file_bot_admin_login_proto_rawDescGZIP(), []int{3}
}

// 检查session请求
type CheckSessionReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CheckSessionReq) Reset() {
	*x = CheckSessionReq{}
	mi := &file_bot_admin_login_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckSessionReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckSessionReq) ProtoMessage() {}

func (x *CheckSessionReq) ProtoReflect() protoreflect.Message {
	mi := &file_bot_admin_login_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckSessionReq.ProtoReflect.Descriptor instead.
func (*CheckSessionReq) Descriptor() ([]byte, []int) {
	return file_bot_admin_login_proto_rawDescGZIP(), []int{4}
}

// 检查session响应
type CheckSessionRsp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StaffId       uint64                 `protobuf:"varint,1,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	StaffBizId    uint64                 `protobuf:"varint,2,opt,name=staff_biz_id,json=staffBizId,proto3" json:"staff_biz_id,omitempty"`
	CorpId        uint64                 `protobuf:"varint,3,opt,name=corp_id,json=corpId,proto3" json:"corp_id,omitempty"`
	SId           uint64                 `protobuf:"varint,4,opt,name=s_id,json=sId,proto3" json:"s_id,omitempty"`
	UserType      uint32                 `protobuf:"varint,5,opt,name=user_type,json=userType,proto3" json:"user_type,omitempty"`
	CorpBizId     uint64                 `protobuf:"varint,6,opt,name=corp_biz_id,json=corpBizId,proto3" json:"corp_biz_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CheckSessionRsp) Reset() {
	*x = CheckSessionRsp{}
	mi := &file_bot_admin_login_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckSessionRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckSessionRsp) ProtoMessage() {}

func (x *CheckSessionRsp) ProtoReflect() protoreflect.Message {
	mi := &file_bot_admin_login_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckSessionRsp.ProtoReflect.Descriptor instead.
func (*CheckSessionRsp) Descriptor() ([]byte, []int) {
	return file_bot_admin_login_proto_rawDescGZIP(), []int{5}
}

func (x *CheckSessionRsp) GetStaffId() uint64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *CheckSessionRsp) GetStaffBizId() uint64 {
	if x != nil {
		return x.StaffBizId
	}
	return 0
}

func (x *CheckSessionRsp) GetCorpId() uint64 {
	if x != nil {
		return x.CorpId
	}
	return 0
}

func (x *CheckSessionRsp) GetSId() uint64 {
	if x != nil {
		return x.SId
	}
	return 0
}

func (x *CheckSessionRsp) GetUserType() uint32 {
	if x != nil {
		return x.UserType
	}
	return 0
}

func (x *CheckSessionRsp) GetCorpBizId() uint64 {
	if x != nil {
		return x.CorpBizId
	}
	return 0
}

// 校验权限请求
type CheckPermissionReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Action        string                 `protobuf:"bytes,1,opt,name=action,proto3" json:"action,omitempty"`
	AppType       string                 `protobuf:"bytes,2,opt,name=app_type,json=appType,proto3" json:"app_type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CheckPermissionReq) Reset() {
	*x = CheckPermissionReq{}
	mi := &file_bot_admin_login_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckPermissionReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckPermissionReq) ProtoMessage() {}

func (x *CheckPermissionReq) ProtoReflect() protoreflect.Message {
	mi := &file_bot_admin_login_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckPermissionReq.ProtoReflect.Descriptor instead.
func (*CheckPermissionReq) Descriptor() ([]byte, []int) {
	return file_bot_admin_login_proto_rawDescGZIP(), []int{6}
}

func (x *CheckPermissionReq) GetAction() string {
	if x != nil {
		return x.Action
	}
	return ""
}

func (x *CheckPermissionReq) GetAppType() string {
	if x != nil {
		return x.AppType
	}
	return ""
}

// 校验权限响应
type CheckPermissionRsp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	HasPermission bool                   `protobuf:"varint,1,opt,name=has_permission,json=hasPermission,proto3" json:"has_permission,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CheckPermissionRsp) Reset() {
	*x = CheckPermissionRsp{}
	mi := &file_bot_admin_login_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckPermissionRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckPermissionRsp) ProtoMessage() {}

func (x *CheckPermissionRsp) ProtoReflect() protoreflect.Message {
	mi := &file_bot_admin_login_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckPermissionRsp.ProtoReflect.Descriptor instead.
func (*CheckPermissionRsp) Descriptor() ([]byte, []int) {
	return file_bot_admin_login_proto_rawDescGZIP(), []int{7}
}

func (x *CheckPermissionRsp) GetHasPermission() bool {
	if x != nil {
		return x.HasPermission
	}
	return false
}

// 获取验证码请求
type SendVerifyCodeReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 手机号
	Telephone     string `protobuf:"bytes,1,opt,name=telephone,proto3" json:"telephone,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendVerifyCodeReq) Reset() {
	*x = SendVerifyCodeReq{}
	mi := &file_bot_admin_login_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendVerifyCodeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendVerifyCodeReq) ProtoMessage() {}

func (x *SendVerifyCodeReq) ProtoReflect() protoreflect.Message {
	mi := &file_bot_admin_login_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendVerifyCodeReq.ProtoReflect.Descriptor instead.
func (*SendVerifyCodeReq) Descriptor() ([]byte, []int) {
	return file_bot_admin_login_proto_rawDescGZIP(), []int{8}
}

func (x *SendVerifyCodeReq) GetTelephone() string {
	if x != nil {
		return x.Telephone
	}
	return ""
}

// 获取验证码响应
type SendVerifyCodeRsp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendVerifyCodeRsp) Reset() {
	*x = SendVerifyCodeRsp{}
	mi := &file_bot_admin_login_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendVerifyCodeRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendVerifyCodeRsp) ProtoMessage() {}

func (x *SendVerifyCodeRsp) ProtoReflect() protoreflect.Message {
	mi := &file_bot_admin_login_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendVerifyCodeRsp.ProtoReflect.Descriptor instead.
func (*SendVerifyCodeRsp) Descriptor() ([]byte, []int) {
	return file_bot_admin_login_proto_rawDescGZIP(), []int{9}
}

// 获取验证码请求
type SendVerifyCodeNewReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 手机号
	Telephone string `protobuf:"bytes,1,opt,name=telephone,proto3" json:"telephone,omitempty"`
	// 发送短信类型：cloud-腾讯云；qidian-企点
	SmsType string `protobuf:"bytes,2,opt,name=sms_type,json=smsType,proto3" json:"sms_type,omitempty"`
	// 图片验证码验证信息
	CaptchaTicket *VerifyCaptchaTicket `protobuf:"bytes,3,opt,name=captcha_ticket,json=captchaTicket,proto3" json:"captcha_ticket,omitempty"`
	// 发送类型：0 默认 短信; 1 邮件
	MessageType   uint32 `protobuf:"varint,4,opt,name=message_type,json=messageType,proto3" json:"message_type,omitempty"`
	Email         string `protobuf:"bytes,5,opt,name=email,proto3" json:"email,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendVerifyCodeNewReq) Reset() {
	*x = SendVerifyCodeNewReq{}
	mi := &file_bot_admin_login_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendVerifyCodeNewReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendVerifyCodeNewReq) ProtoMessage() {}

func (x *SendVerifyCodeNewReq) ProtoReflect() protoreflect.Message {
	mi := &file_bot_admin_login_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendVerifyCodeNewReq.ProtoReflect.Descriptor instead.
func (*SendVerifyCodeNewReq) Descriptor() ([]byte, []int) {
	return file_bot_admin_login_proto_rawDescGZIP(), []int{10}
}

func (x *SendVerifyCodeNewReq) GetTelephone() string {
	if x != nil {
		return x.Telephone
	}
	return ""
}

func (x *SendVerifyCodeNewReq) GetSmsType() string {
	if x != nil {
		return x.SmsType
	}
	return ""
}

func (x *SendVerifyCodeNewReq) GetCaptchaTicket() *VerifyCaptchaTicket {
	if x != nil {
		return x.CaptchaTicket
	}
	return nil
}

func (x *SendVerifyCodeNewReq) GetMessageType() uint32 {
	if x != nil {
		return x.MessageType
	}
	return 0
}

func (x *SendVerifyCodeNewReq) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

// VerifyCaptchaTicket 验证ticket信息
type VerifyCaptchaTicket struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 验证码客户端验证回调的票据
	Ticket string `protobuf:"bytes,1,opt,name=ticket,proto3" json:"ticket,omitempty"`
	// 验证码客户端验证回调的随机串
	RandStr       string `protobuf:"bytes,2,opt,name=rand_str,json=randStr,proto3" json:"rand_str,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VerifyCaptchaTicket) Reset() {
	*x = VerifyCaptchaTicket{}
	mi := &file_bot_admin_login_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VerifyCaptchaTicket) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyCaptchaTicket) ProtoMessage() {}

func (x *VerifyCaptchaTicket) ProtoReflect() protoreflect.Message {
	mi := &file_bot_admin_login_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyCaptchaTicket.ProtoReflect.Descriptor instead.
func (*VerifyCaptchaTicket) Descriptor() ([]byte, []int) {
	return file_bot_admin_login_proto_rawDescGZIP(), []int{11}
}

func (x *VerifyCaptchaTicket) GetTicket() string {
	if x != nil {
		return x.Ticket
	}
	return ""
}

func (x *VerifyCaptchaTicket) GetRandStr() string {
	if x != nil {
		return x.RandStr
	}
	return ""
}

// 获取验证码响应
type SendVerifyCodeNewRsp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendVerifyCodeNewRsp) Reset() {
	*x = SendVerifyCodeNewRsp{}
	mi := &file_bot_admin_login_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendVerifyCodeNewRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendVerifyCodeNewRsp) ProtoMessage() {}

func (x *SendVerifyCodeNewRsp) ProtoReflect() protoreflect.Message {
	mi := &file_bot_admin_login_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendVerifyCodeNewRsp.ProtoReflect.Descriptor instead.
func (*SendVerifyCodeNewRsp) Descriptor() ([]byte, []int) {
	return file_bot_admin_login_proto_rawDescGZIP(), []int{12}
}

// 注册企业请求
type RegisterCorpReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 企业全称
	CorpFullName string `protobuf:"bytes,1,opt,name=corp_full_name,json=corpFullName,proto3" json:"corp_full_name,omitempty"`
	// 联系人名称
	ContactName string `protobuf:"bytes,2,opt,name=contact_name,json=contactName,proto3" json:"contact_name,omitempty"`
	// 邮箱地址
	Email string `protobuf:"bytes,3,opt,name=email,proto3" json:"email,omitempty"`
	// 手机号
	Telephone string `protobuf:"bytes,4,opt,name=telephone,proto3" json:"telephone,omitempty"`
	// 验证码
	Code          string `protobuf:"bytes,5,opt,name=code,proto3" json:"code,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RegisterCorpReq) Reset() {
	*x = RegisterCorpReq{}
	mi := &file_bot_admin_login_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RegisterCorpReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegisterCorpReq) ProtoMessage() {}

func (x *RegisterCorpReq) ProtoReflect() protoreflect.Message {
	mi := &file_bot_admin_login_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegisterCorpReq.ProtoReflect.Descriptor instead.
func (*RegisterCorpReq) Descriptor() ([]byte, []int) {
	return file_bot_admin_login_proto_rawDescGZIP(), []int{13}
}

func (x *RegisterCorpReq) GetCorpFullName() string {
	if x != nil {
		return x.CorpFullName
	}
	return ""
}

func (x *RegisterCorpReq) GetContactName() string {
	if x != nil {
		return x.ContactName
	}
	return ""
}

func (x *RegisterCorpReq) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *RegisterCorpReq) GetTelephone() string {
	if x != nil {
		return x.Telephone
	}
	return ""
}

func (x *RegisterCorpReq) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

// 注册企业响应
type RegisterCorpRsp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RegisterCorpRsp) Reset() {
	*x = RegisterCorpRsp{}
	mi := &file_bot_admin_login_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RegisterCorpRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegisterCorpRsp) ProtoMessage() {}

func (x *RegisterCorpRsp) ProtoReflect() protoreflect.Message {
	mi := &file_bot_admin_login_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegisterCorpRsp.ProtoReflect.Descriptor instead.
func (*RegisterCorpRsp) Descriptor() ([]byte, []int) {
	return file_bot_admin_login_proto_rawDescGZIP(), []int{14}
}

// 校验企业与员工 请求
type CheckCorpAndStaffReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 企业ID
	CorpBizId uint64 `protobuf:"varint,1,opt,name=corp_biz_id,json=corpBizId,proto3" json:"corp_biz_id,omitempty"`
	// 员工ID
	StaffBizId    uint64 `protobuf:"varint,2,opt,name=staff_biz_id,json=staffBizId,proto3" json:"staff_biz_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CheckCorpAndStaffReq) Reset() {
	*x = CheckCorpAndStaffReq{}
	mi := &file_bot_admin_login_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckCorpAndStaffReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckCorpAndStaffReq) ProtoMessage() {}

func (x *CheckCorpAndStaffReq) ProtoReflect() protoreflect.Message {
	mi := &file_bot_admin_login_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckCorpAndStaffReq.ProtoReflect.Descriptor instead.
func (*CheckCorpAndStaffReq) Descriptor() ([]byte, []int) {
	return file_bot_admin_login_proto_rawDescGZIP(), []int{15}
}

func (x *CheckCorpAndStaffReq) GetCorpBizId() uint64 {
	if x != nil {
		return x.CorpBizId
	}
	return 0
}

func (x *CheckCorpAndStaffReq) GetStaffBizId() uint64 {
	if x != nil {
		return x.StaffBizId
	}
	return 0
}

// 校验企业与员工 响应
type CheckCorpAndStaffRsp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CheckCorpAndStaffRsp) Reset() {
	*x = CheckCorpAndStaffRsp{}
	mi := &file_bot_admin_login_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckCorpAndStaffRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckCorpAndStaffRsp) ProtoMessage() {}

func (x *CheckCorpAndStaffRsp) ProtoReflect() protoreflect.Message {
	mi := &file_bot_admin_login_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckCorpAndStaffRsp.ProtoReflect.Descriptor instead.
func (*CheckCorpAndStaffRsp) Descriptor() ([]byte, []int) {
	return file_bot_admin_login_proto_rawDescGZIP(), []int{16}
}

// 加入企业请求
type JoinCorpReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 企业ID
	CorpBizId uint64 `protobuf:"varint,1,opt,name=corp_biz_id,json=corpBizId,proto3" json:"corp_biz_id,omitempty"`
	// 员工ID
	StaffBizId uint64 `protobuf:"varint,2,opt,name=staff_biz_id,json=staffBizId,proto3" json:"staff_biz_id,omitempty"`
	// 受邀人姓名
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// 受邀人手机号
	Telephone string `protobuf:"bytes,4,opt,name=telephone,proto3" json:"telephone,omitempty"`
	// 验证码
	Code          string `protobuf:"bytes,5,opt,name=code,proto3" json:"code,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *JoinCorpReq) Reset() {
	*x = JoinCorpReq{}
	mi := &file_bot_admin_login_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *JoinCorpReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JoinCorpReq) ProtoMessage() {}

func (x *JoinCorpReq) ProtoReflect() protoreflect.Message {
	mi := &file_bot_admin_login_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JoinCorpReq.ProtoReflect.Descriptor instead.
func (*JoinCorpReq) Descriptor() ([]byte, []int) {
	return file_bot_admin_login_proto_rawDescGZIP(), []int{17}
}

func (x *JoinCorpReq) GetCorpBizId() uint64 {
	if x != nil {
		return x.CorpBizId
	}
	return 0
}

func (x *JoinCorpReq) GetStaffBizId() uint64 {
	if x != nil {
		return x.StaffBizId
	}
	return 0
}

func (x *JoinCorpReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *JoinCorpReq) GetTelephone() string {
	if x != nil {
		return x.Telephone
	}
	return ""
}

func (x *JoinCorpReq) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

// 加入企业响应
type JoinCorpRsp struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// cookie映射值
	Token string `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`
	// 用户名
	UserName string `protobuf:"bytes,2,opt,name=user_name,json=userName,proto3" json:"user_name,omitempty"`
	// 用户头像
	UserAvatar string `protobuf:"bytes,3,opt,name=user_avatar,json=userAvatar,proto3" json:"user_avatar,omitempty"`
	// 手机号
	Telephone     string `protobuf:"bytes,4,opt,name=telephone,proto3" json:"telephone,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *JoinCorpRsp) Reset() {
	*x = JoinCorpRsp{}
	mi := &file_bot_admin_login_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *JoinCorpRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JoinCorpRsp) ProtoMessage() {}

func (x *JoinCorpRsp) ProtoReflect() protoreflect.Message {
	mi := &file_bot_admin_login_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JoinCorpRsp.ProtoReflect.Descriptor instead.
func (*JoinCorpRsp) Descriptor() ([]byte, []int) {
	return file_bot_admin_login_proto_rawDescGZIP(), []int{18}
}

func (x *JoinCorpRsp) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *JoinCorpRsp) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

func (x *JoinCorpRsp) GetUserAvatar() string {
	if x != nil {
		return x.UserAvatar
	}
	return ""
}

func (x *JoinCorpRsp) GetTelephone() string {
	if x != nil {
		return x.Telephone
	}
	return ""
}

// 退出企业
type ExitCorpReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ExitCorpReq) Reset() {
	*x = ExitCorpReq{}
	mi := &file_bot_admin_login_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExitCorpReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExitCorpReq) ProtoMessage() {}

func (x *ExitCorpReq) ProtoReflect() protoreflect.Message {
	mi := &file_bot_admin_login_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExitCorpReq.ProtoReflect.Descriptor instead.
func (*ExitCorpReq) Descriptor() ([]byte, []int) {
	return file_bot_admin_login_proto_rawDescGZIP(), []int{19}
}

// 退出企业响应
type ExitCorpRsp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ExitCorpRsp) Reset() {
	*x = ExitCorpRsp{}
	mi := &file_bot_admin_login_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExitCorpRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExitCorpRsp) ProtoMessage() {}

func (x *ExitCorpRsp) ProtoReflect() protoreflect.Message {
	mi := &file_bot_admin_login_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExitCorpRsp.ProtoReflect.Descriptor instead.
func (*ExitCorpRsp) Descriptor() ([]byte, []int) {
	return file_bot_admin_login_proto_rawDescGZIP(), []int{20}
}

var File_bot_admin_login_proto protoreflect.FileDescriptor

const file_bot_admin_login_proto_rawDesc = "" +
	"\n" +
	"\x15bot-admin-login.proto\x12 trpc.KEP.bot_admin_config_server\x1a\x0evalidate.proto\"\x80\x02\n" +
	"\bLoginReq\x12\x1c\n" +
	"\ttelephone\x18\x01 \x01(\tR\ttelephone\x12\x12\n" +
	"\x04code\x18\x02 \x01(\tR\x04code\x12\x1b\n" +
	"\tis_agreed\x18\x03 \x01(\bR\bisAgreed\x12*\n" +
	"\n" +
	"login_type\x18\x04 \x01(\rB\v\xfaB\b*\x060\x000\x010\x02R\tloginType\x12!\n" +
	"\aaccount\x18\x05 \x01(\tB\a\xfaB\x04r\x02\x18\x14R\aaccount\x12#\n" +
	"\bpassword\x18\x06 \x01(\tB\a\xfaB\x04r\x02\x18 R\bpassword\x12\x1b\n" +
	"\tuser_type\x18\a \x01(\rR\buserType\x12\x14\n" +
	"\x05email\x18\b \x01(\tR\x05email\"\xb4\x01\n" +
	"\bLoginRsp\x12\x14\n" +
	"\x05token\x18\x01 \x01(\tR\x05token\x12\x1b\n" +
	"\tuser_name\x18\x02 \x01(\tR\buserName\x12\x1f\n" +
	"\vuser_avatar\x18\x03 \x01(\tR\n" +
	"userAvatar\x12\x1c\n" +
	"\ttelephone\x18\x04 \x01(\tR\ttelephone\x12 \n" +
	"\fstaff_biz_id\x18\x06 \x01(\tR\n" +
	"staffBizId\x12\x14\n" +
	"\x05email\x18\a \x01(\tR\x05email\"\v\n" +
	"\tLogoutReq\"\v\n" +
	"\tLogoutRsp\"\x11\n" +
	"\x0fCheckSessionReq\"\xb7\x01\n" +
	"\x0fCheckSessionRsp\x12\x19\n" +
	"\bstaff_id\x18\x01 \x01(\x04R\astaffId\x12 \n" +
	"\fstaff_biz_id\x18\x02 \x01(\x04R\n" +
	"staffBizId\x12\x17\n" +
	"\acorp_id\x18\x03 \x01(\x04R\x06corpId\x12\x11\n" +
	"\x04s_id\x18\x04 \x01(\x04R\x03sId\x12\x1b\n" +
	"\tuser_type\x18\x05 \x01(\rR\buserType\x12\x1e\n" +
	"\vcorp_biz_id\x18\x06 \x01(\x04R\tcorpBizId\"G\n" +
	"\x12CheckPermissionReq\x12\x16\n" +
	"\x06action\x18\x01 \x01(\tR\x06action\x12\x19\n" +
	"\bapp_type\x18\x02 \x01(\tR\aappType\";\n" +
	"\x12CheckPermissionRsp\x12%\n" +
	"\x0ehas_permission\x18\x01 \x01(\bR\rhasPermission\":\n" +
	"\x11SendVerifyCodeReq\x12%\n" +
	"\ttelephone\x18\x01 \x01(\tB\a\xfaB\x04r\x02\x10\x01R\ttelephone\"\x13\n" +
	"\x11SendVerifyCodeRsp\"\xe6\x01\n" +
	"\x14SendVerifyCodeNewReq\x12\x1c\n" +
	"\ttelephone\x18\x01 \x01(\tR\ttelephone\x12\x19\n" +
	"\bsms_type\x18\x02 \x01(\tR\asmsType\x12\\\n" +
	"\x0ecaptcha_ticket\x18\x03 \x01(\v25.trpc.KEP.bot_admin_config_server.VerifyCaptchaTicketR\rcaptchaTicket\x12!\n" +
	"\fmessage_type\x18\x04 \x01(\rR\vmessageType\x12\x14\n" +
	"\x05email\x18\x05 \x01(\tR\x05email\"H\n" +
	"\x13VerifyCaptchaTicket\x12\x16\n" +
	"\x06ticket\x18\x01 \x01(\tR\x06ticket\x12\x19\n" +
	"\brand_str\x18\x02 \x01(\tR\arandStr\"\x16\n" +
	"\x14SendVerifyCodeNewRsp\"\xcf\x01\n" +
	"\x0fRegisterCorpReq\x12-\n" +
	"\x0ecorp_full_name\x18\x01 \x01(\tB\a\xfaB\x04r\x02\x10\x01R\fcorpFullName\x12*\n" +
	"\fcontact_name\x18\x02 \x01(\tB\a\xfaB\x04r\x02\x10\x01R\vcontactName\x12\x1d\n" +
	"\x05email\x18\x03 \x01(\tB\a\xfaB\x04r\x02\x10\x01R\x05email\x12%\n" +
	"\ttelephone\x18\x04 \x01(\tB\a\xfaB\x04r\x02\x10\x01R\ttelephone\x12\x1b\n" +
	"\x04code\x18\x05 \x01(\tB\a\xfaB\x04r\x02\x10\x01R\x04code\"\x11\n" +
	"\x0fRegisterCorpRsp\"j\n" +
	"\x14CheckCorpAndStaffReq\x12'\n" +
	"\vcorp_biz_id\x18\x01 \x01(\x04B\a\xfaB\x042\x02 \x00R\tcorpBizId\x12)\n" +
	"\fstaff_biz_id\x18\x02 \x01(\x04B\a\xfaB\x042\x02 \x00R\n" +
	"staffBizId\"\x16\n" +
	"\x14CheckCorpAndStaffRsp\"\xc2\x01\n" +
	"\vJoinCorpReq\x12'\n" +
	"\vcorp_biz_id\x18\x01 \x01(\x04B\a\xfaB\x042\x02 \x00R\tcorpBizId\x12)\n" +
	"\fstaff_biz_id\x18\x02 \x01(\x04B\a\xfaB\x042\x02 \x00R\n" +
	"staffBizId\x12\x1b\n" +
	"\x04name\x18\x03 \x01(\tB\a\xfaB\x04r\x02\x10\x01R\x04name\x12%\n" +
	"\ttelephone\x18\x04 \x01(\tB\a\xfaB\x04r\x02\x10\x01R\ttelephone\x12\x1b\n" +
	"\x04code\x18\x05 \x01(\tB\a\xfaB\x04r\x02\x10\x01R\x04code\"\x7f\n" +
	"\vJoinCorpRsp\x12\x14\n" +
	"\x05token\x18\x01 \x01(\tR\x05token\x12\x1b\n" +
	"\tuser_name\x18\x02 \x01(\tR\buserName\x12\x1f\n" +
	"\vuser_avatar\x18\x03 \x01(\tR\n" +
	"userAvatar\x12\x1c\n" +
	"\ttelephone\x18\x04 \x01(\tR\ttelephone\"\r\n" +
	"\vExitCorpReq\"\r\n" +
	"\vExitCorpRsp2\x93\t\n" +
	"\x05Login\x12_\n" +
	"\x05Login\x12*.trpc.KEP.bot_admin_config_server.LoginReq\x1a*.trpc.KEP.bot_admin_config_server.LoginRsp\x12b\n" +
	"\x06Logout\x12+.trpc.KEP.bot_admin_config_server.LogoutReq\x1a+.trpc.KEP.bot_admin_config_server.LogoutRsp\x12t\n" +
	"\fCheckSession\x121.trpc.KEP.bot_admin_config_server.CheckSessionReq\x1a1.trpc.KEP.bot_admin_config_server.CheckSessionRsp\x12}\n" +
	"\x0fCheckPermission\x124.trpc.KEP.bot_admin_config_server.CheckPermissionReq\x1a4.trpc.KEP.bot_admin_config_server.CheckPermissionRsp\x12z\n" +
	"\x0eSendVerifyCode\x123.trpc.KEP.bot_admin_config_server.SendVerifyCodeReq\x1a3.trpc.KEP.bot_admin_config_server.SendVerifyCodeRsp\x12\x83\x01\n" +
	"\x11SendVerifyCodeNew\x126.trpc.KEP.bot_admin_config_server.SendVerifyCodeNewReq\x1a6.trpc.KEP.bot_admin_config_server.SendVerifyCodeNewRsp\x12t\n" +
	"\fRegisterCorp\x121.trpc.KEP.bot_admin_config_server.RegisterCorpReq\x1a1.trpc.KEP.bot_admin_config_server.RegisterCorpRsp\x12\x83\x01\n" +
	"\x11CheckCorpAndStaff\x126.trpc.KEP.bot_admin_config_server.CheckCorpAndStaffReq\x1a6.trpc.KEP.bot_admin_config_server.CheckCorpAndStaffRsp\x12h\n" +
	"\bJoinCorp\x12-.trpc.KEP.bot_admin_config_server.JoinCorpReq\x1a-.trpc.KEP.bot_admin_config_server.JoinCorpRsp\x12h\n" +
	"\bExitCorp\x12-.trpc.KEP.bot_admin_config_server.ExitCorpReq\x1a-.trpc.KEP.bot_admin_config_server.ExitCorpRspBMZKgit.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_admin_config_serverb\x06proto3"

var (
	file_bot_admin_login_proto_rawDescOnce sync.Once
	file_bot_admin_login_proto_rawDescData []byte
)

func file_bot_admin_login_proto_rawDescGZIP() []byte {
	file_bot_admin_login_proto_rawDescOnce.Do(func() {
		file_bot_admin_login_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_bot_admin_login_proto_rawDesc), len(file_bot_admin_login_proto_rawDesc)))
	})
	return file_bot_admin_login_proto_rawDescData
}

var file_bot_admin_login_proto_msgTypes = make([]protoimpl.MessageInfo, 21)
var file_bot_admin_login_proto_goTypes = []any{
	(*LoginReq)(nil),             // 0: trpc.KEP.bot_admin_config_server.LoginReq
	(*LoginRsp)(nil),             // 1: trpc.KEP.bot_admin_config_server.LoginRsp
	(*LogoutReq)(nil),            // 2: trpc.KEP.bot_admin_config_server.LogoutReq
	(*LogoutRsp)(nil),            // 3: trpc.KEP.bot_admin_config_server.LogoutRsp
	(*CheckSessionReq)(nil),      // 4: trpc.KEP.bot_admin_config_server.CheckSessionReq
	(*CheckSessionRsp)(nil),      // 5: trpc.KEP.bot_admin_config_server.CheckSessionRsp
	(*CheckPermissionReq)(nil),   // 6: trpc.KEP.bot_admin_config_server.CheckPermissionReq
	(*CheckPermissionRsp)(nil),   // 7: trpc.KEP.bot_admin_config_server.CheckPermissionRsp
	(*SendVerifyCodeReq)(nil),    // 8: trpc.KEP.bot_admin_config_server.SendVerifyCodeReq
	(*SendVerifyCodeRsp)(nil),    // 9: trpc.KEP.bot_admin_config_server.SendVerifyCodeRsp
	(*SendVerifyCodeNewReq)(nil), // 10: trpc.KEP.bot_admin_config_server.SendVerifyCodeNewReq
	(*VerifyCaptchaTicket)(nil),  // 11: trpc.KEP.bot_admin_config_server.VerifyCaptchaTicket
	(*SendVerifyCodeNewRsp)(nil), // 12: trpc.KEP.bot_admin_config_server.SendVerifyCodeNewRsp
	(*RegisterCorpReq)(nil),      // 13: trpc.KEP.bot_admin_config_server.RegisterCorpReq
	(*RegisterCorpRsp)(nil),      // 14: trpc.KEP.bot_admin_config_server.RegisterCorpRsp
	(*CheckCorpAndStaffReq)(nil), // 15: trpc.KEP.bot_admin_config_server.CheckCorpAndStaffReq
	(*CheckCorpAndStaffRsp)(nil), // 16: trpc.KEP.bot_admin_config_server.CheckCorpAndStaffRsp
	(*JoinCorpReq)(nil),          // 17: trpc.KEP.bot_admin_config_server.JoinCorpReq
	(*JoinCorpRsp)(nil),          // 18: trpc.KEP.bot_admin_config_server.JoinCorpRsp
	(*ExitCorpReq)(nil),          // 19: trpc.KEP.bot_admin_config_server.ExitCorpReq
	(*ExitCorpRsp)(nil),          // 20: trpc.KEP.bot_admin_config_server.ExitCorpRsp
}
var file_bot_admin_login_proto_depIdxs = []int32{
	11, // 0: trpc.KEP.bot_admin_config_server.SendVerifyCodeNewReq.captcha_ticket:type_name -> trpc.KEP.bot_admin_config_server.VerifyCaptchaTicket
	0,  // 1: trpc.KEP.bot_admin_config_server.Login.Login:input_type -> trpc.KEP.bot_admin_config_server.LoginReq
	2,  // 2: trpc.KEP.bot_admin_config_server.Login.Logout:input_type -> trpc.KEP.bot_admin_config_server.LogoutReq
	4,  // 3: trpc.KEP.bot_admin_config_server.Login.CheckSession:input_type -> trpc.KEP.bot_admin_config_server.CheckSessionReq
	6,  // 4: trpc.KEP.bot_admin_config_server.Login.CheckPermission:input_type -> trpc.KEP.bot_admin_config_server.CheckPermissionReq
	8,  // 5: trpc.KEP.bot_admin_config_server.Login.SendVerifyCode:input_type -> trpc.KEP.bot_admin_config_server.SendVerifyCodeReq
	10, // 6: trpc.KEP.bot_admin_config_server.Login.SendVerifyCodeNew:input_type -> trpc.KEP.bot_admin_config_server.SendVerifyCodeNewReq
	13, // 7: trpc.KEP.bot_admin_config_server.Login.RegisterCorp:input_type -> trpc.KEP.bot_admin_config_server.RegisterCorpReq
	15, // 8: trpc.KEP.bot_admin_config_server.Login.CheckCorpAndStaff:input_type -> trpc.KEP.bot_admin_config_server.CheckCorpAndStaffReq
	17, // 9: trpc.KEP.bot_admin_config_server.Login.JoinCorp:input_type -> trpc.KEP.bot_admin_config_server.JoinCorpReq
	19, // 10: trpc.KEP.bot_admin_config_server.Login.ExitCorp:input_type -> trpc.KEP.bot_admin_config_server.ExitCorpReq
	1,  // 11: trpc.KEP.bot_admin_config_server.Login.Login:output_type -> trpc.KEP.bot_admin_config_server.LoginRsp
	3,  // 12: trpc.KEP.bot_admin_config_server.Login.Logout:output_type -> trpc.KEP.bot_admin_config_server.LogoutRsp
	5,  // 13: trpc.KEP.bot_admin_config_server.Login.CheckSession:output_type -> trpc.KEP.bot_admin_config_server.CheckSessionRsp
	7,  // 14: trpc.KEP.bot_admin_config_server.Login.CheckPermission:output_type -> trpc.KEP.bot_admin_config_server.CheckPermissionRsp
	9,  // 15: trpc.KEP.bot_admin_config_server.Login.SendVerifyCode:output_type -> trpc.KEP.bot_admin_config_server.SendVerifyCodeRsp
	12, // 16: trpc.KEP.bot_admin_config_server.Login.SendVerifyCodeNew:output_type -> trpc.KEP.bot_admin_config_server.SendVerifyCodeNewRsp
	14, // 17: trpc.KEP.bot_admin_config_server.Login.RegisterCorp:output_type -> trpc.KEP.bot_admin_config_server.RegisterCorpRsp
	16, // 18: trpc.KEP.bot_admin_config_server.Login.CheckCorpAndStaff:output_type -> trpc.KEP.bot_admin_config_server.CheckCorpAndStaffRsp
	18, // 19: trpc.KEP.bot_admin_config_server.Login.JoinCorp:output_type -> trpc.KEP.bot_admin_config_server.JoinCorpRsp
	20, // 20: trpc.KEP.bot_admin_config_server.Login.ExitCorp:output_type -> trpc.KEP.bot_admin_config_server.ExitCorpRsp
	11, // [11:21] is the sub-list for method output_type
	1,  // [1:11] is the sub-list for method input_type
	1,  // [1:1] is the sub-list for extension type_name
	1,  // [1:1] is the sub-list for extension extendee
	0,  // [0:1] is the sub-list for field type_name
}

func init() { file_bot_admin_login_proto_init() }
func file_bot_admin_login_proto_init() {
	if File_bot_admin_login_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_bot_admin_login_proto_rawDesc), len(file_bot_admin_login_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   21,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_bot_admin_login_proto_goTypes,
		DependencyIndexes: file_bot_admin_login_proto_depIdxs,
		MessageInfos:      file_bot_admin_login_proto_msgTypes,
	}.Build()
	File_bot_admin_login_proto = out.File
	file_bot_admin_login_proto_goTypes = nil
	file_bot_admin_login_proto_depIdxs = nil
}
