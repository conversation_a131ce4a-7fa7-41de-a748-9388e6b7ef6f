// Code generated by trpc-go/trpc-go-cmdline v2.8.32. DO NOT EDIT.
// source: bot-admin-api.proto

package bot_admin_config_server

import (
	"context"
	"errors"
	"fmt"

	_ "git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/codec"
	_ "git.code.oa.com/trpc-go/trpc-go/http"
	"git.code.oa.com/trpc-go/trpc-go/server"
)

// START ======================================= Server Service Definition ======================================= START

// ApiService defines service.
type ApiService interface {
	// CreateCorp 创建企业信息
	CreateCorp(ctx context.Context, req *CreateCorpReq) (*CreateCorpRsp, error)
	// ListCorp 企业列表
	ListCorp(ctx context.Context, req *ListCorpReq) (*ListCorpRsp, error)
	// GetCorp 根据企业ID获取企业
	GetCorp(ctx context.Context, req *GetCorpReq) (*GetCorpRsp, error)
	// ModifyInfosecBizType 修改企业安全审核策略
	ModifyInfosecBizType(ctx context.Context, req *ModifyInfosecBizTypeReq) (*ModifyInfosecBizTypeRsp, error)
	// ModifyMaxTokenUsage 修改企业机器人token数
	ModifyMaxTokenUsage(ctx context.Context, req *ModifyMaxTokenUsageReq) (*ModifyMaxTokenUsageRsp, error)
	// ModifyMaxCharSize 修改企业机器人字符数
	ModifyMaxCharSize(ctx context.Context, req *ModifyMaxCharSizeReq) (*ModifyMaxCharSizeRsp, error)
	// AuditCorp 审核企业
	AuditCorp(ctx context.Context, req *AuditCorpReq) (*AuditCorpRsp, error)
	// ModifyCorpRobotQuota 修改企业机器人配额
	ModifyCorpRobotQuota(ctx context.Context, req *ModifyCorpRobotQuotaReq) (*ModifyCorpRobotQuotaRsp, error)
	// CorpStaffList 获取企业员工列表
	CorpStaffList(ctx context.Context, req *CorpStaffListReq) (*CorpStaffListRsp, error)
	// ListCorpStaffByIds 通过Ids获取员工信息列表
	ListCorpStaffByIds(ctx context.Context, req *ListCorpStaffByIdsReq) (*ListCorpStaffByIdsRsp, error)
	// ReleaseDetailNotify 更新QA/Segment状态回调
	ReleaseDetailNotify(ctx context.Context, req *ReleaseDetailNotifyReq) (*ReleaseDetailNotifyRsp, error)
	// ReleaseNotify 更新发布任务回调
	ReleaseNotify(ctx context.Context, req *ReleaseNotifyReq) (*ReleaseNotifyRsp, error)
	// GetPresignedURL 获取临时链接
	GetPresignedURL(ctx context.Context, req *GetPresignedURLReq) (*GetPresignedURLRsp, error)
	// GetRobotInfo Deprecated 获取机器人
	GetRobotInfo(ctx context.Context, req *GetRobotInfoReq) (*GetRobotInfoRsp, error)
	// DescribeRobotInfo 获取机器人
	DescribeRobotInfo(ctx context.Context, req *DescribeRobotInfoReq) (*DescribeRobotInfoRsp, error)
	// SearchPreview 对话评测
	SearchPreview(ctx context.Context, req *SearchPreviewReq) (*SearchPreviewRsp, error)
	// Search 向量特征检索
	Search(ctx context.Context, req *SearchReq) (*SearchRsp, error)
	// CustomSearchPreview 对话评测
	CustomSearchPreview(ctx context.Context, req *CustomSearchPreviewReq) (*CustomSearchPreviewRsp, error)
	// CustomSearch 查找
	CustomSearch(ctx context.Context, req *CustomSearchReq) (*CustomSearchRsp, error)
	// MatchRefer 匹配来源
	MatchRefer(ctx context.Context, req *MatchReferReq) (*MatchReferRsp, error)
	// AuditResultCallback 审核回调
	AuditResultCallback(ctx context.Context, req *CheckResultReq) (*CheckResultRsp, error)
	// GetRobotList 获取机器人列表
	GetRobotList(ctx context.Context, req *GetRobotListReq) (*GetRobotListRsp, error)
	// GetAppList 获取应用列表
	GetAppList(ctx context.Context, req *GetAppListReq) (*GetAppListRsp, error)
	// GetRobotByAppKey 通过AppKey获取机器人
	GetRobotByAppKey(ctx context.Context, req *GetRobotByAppKeyReq) (*GetRobotByAppKeyRsp, error)
	// GetAppByAppKey 通过AppKey获取应用
	GetAppByAppKey(ctx context.Context, req *GetAppByAppKeyReq) (*GetAppByAppKeyRsp, error)
	// GetAppInfo 获取应用信息
	GetAppInfo(ctx context.Context, req *GetAppInfoReq) (*GetAppInfoRsp, error)
	// EditRobot 编辑机器人信息
	EditRobot(ctx context.Context, req *EditRobotReq) (*EditRobotRsp, error)
	// GetCorpList 获取企业信息
	GetCorpList(ctx context.Context, req *GetCorpListReq) (*GetCorpListRsp, error)
	// GetDocs 获取文档内容
	GetDocs(ctx context.Context, req *GetDocsReq) (*GetDocsRsp, error)
	// SearchPreviewRejectedQuestion 拒答问题测评库查询
	SearchPreviewRejectedQuestion(ctx context.Context, req *SearchPreviewRejectedQuestionReq) (*SearchPreviewRejectedQuestionRsp, error)
	// SearchReleaseRejectedQuestion 拒答问题线上库查询
	SearchReleaseRejectedQuestion(ctx context.Context, req *SearchReleaseRejectedQuestionReq) (*SearchReleaseRejectedQuestionRsp, error)
	// ListRejectedQuestion 获取拒答问题列表
	ListRejectedQuestion(ctx context.Context, req *ListRejectedQuestionReq) (*ListRejectedQuestionRsp, error)
	// AddUnsatisfiedReply 添加不满意回复
	AddUnsatisfiedReply(ctx context.Context, req *AddUnsatisfiedReplyReq) (*AddUnsatisfiedReplyRsp, error)
	// ListQA 获取QA列表
	ListQA(ctx context.Context, req *ListQAReq) (*ListQARsp, error)
	// GetAdminTaskList 获取admin任务列表
	GetAdminTaskList(ctx context.Context, req *GetAdminTaskListReq) (*GetAdminTaskListRsp, error)
	// GetAdminTaskHistoryList 获取admin历史任务列表
	GetAdminTaskHistoryList(ctx context.Context, req *GetAdminTaskHistoryListReq) (*GetAdminTaskHistoryListRsp, error)
	// GetVectorDocTaskList 获取获取vector_doc任务列表
	GetVectorDocTaskList(ctx context.Context, req *GetVectorDocTaskListReq) (*GetVectorDocTaskListRsp, error)
	// GetVectorDocTaskHistoryList 获取vector_doc任务历史列表
	GetVectorDocTaskHistoryList(ctx context.Context, req *GetVectorDocTaskHistoryListReq) (*GetVectorDocTaskHistoryListRsp, error)
	// GetReleaseList 发布记录列表
	GetReleaseList(ctx context.Context, req *GetReleaseListReq) (*GetReleaseListRsp, error)
	// AddCorpStaff 添加企业成员
	AddCorpStaff(ctx context.Context, req *AddCorpStaffReq) (*AddCorpStaffRsp, error)
	// EditCorpStaffPassword 修改企业员工密码
	EditCorpStaffPassword(ctx context.Context, req *EditCorpStaffPasswordReq) (*EditCorpStaffPasswordRsp, error)
	// LeaveCorp 企业员工退出企业
	LeaveCorp(ctx context.Context, req *LeaveCorpReq) (*LeaveCorpRsp, error)
	// UpdateAuditStatus 更新审核单状态
	UpdateAuditStatus(ctx context.Context, req *UpdateAuditStatusReq) (*UpdateAuditStatusRsp, error)
	// GetRobotDefaultConfig 获取机器人默认配置信息
	GetRobotDefaultConfig(ctx context.Context, req *GetRobotDefaultConfigReq) (*GetRobotDefaultConfigRsp, error)
	// ClearRobotCustomConfig 清除机器人自定义配置
	ClearRobotCustomConfig(ctx context.Context, req *ClearRobotCustomConfigReq) (*ClearRobotCustomConfigRsp, error)
	// ListGlobalKnowledge 全局干预知识列表
	ListGlobalKnowledge(ctx context.Context, req *ListGlobalKnowledgeReq) (*ListGlobalKnowledgeRsp, error)
	// AddGlobalKnowledge 添加全局干预知识
	AddGlobalKnowledge(ctx context.Context, req *AddGlobalKnowledgeReq) (*AddGlobalKnowledgeRsp, error)
	// DelGlobalKnowledge 删除全局干预知识
	DelGlobalKnowledge(ctx context.Context, req *DelGlobalKnowledgeReq) (*DelGlobalKnowledgeRsp, error)
	// UpdGlobalKnowledge 更新全局干预知识
	UpdGlobalKnowledge(ctx context.Context, req *UpdGlobalKnowledgeReq) (*UpdGlobalKnowledgeRsp, error)
	// GlobalKnowledge 全局干预知识
	GlobalKnowledge(ctx context.Context, req *GlobalKnowledgeReq) (*GlobalKnowledgeRsp, error)
	// ForceSyncGlobalKnowledge 强制同步全局干预知识
	ForceSyncGlobalKnowledge(ctx context.Context, req *ForceSyncGlobalKnowledgeReq) (*ForceSyncGlobalKnowledgeRsp, error)
	// CustomSimilarity 计算相似度
	CustomSimilarity(ctx context.Context, req *CustomSimilarityReq) (*CustomSimilarityRsp, error)
	// EnableCorp 启用企业请求
	EnableCorp(ctx context.Context, req *EnableCorpReq) (*EnableCorpRsp, error)
	// DisableCorp 禁用企业请求
	DisableCorp(ctx context.Context, req *DisableCorpReq) (*DisableCorpRsp, error)
	// GetCustomResource 底座获取资源列表
	GetCustomResource(ctx context.Context, req *GetCustomResourceReq) (*GetCustomResourceRsp, error)
	// ActivateProduct 底座通知产品开通
	ActivateProduct(ctx context.Context, req *ActivateProductReq) (*ActivateProductRsp, error)
	// CreateNotice 创建通知
	CreateNotice(ctx context.Context, req *CreateNoticeReq) (*CreateNoticeRsp, error)
	// CreateNoticeByUin 主账号维度创建通知
	CreateNoticeByUin(ctx context.Context, req *CreateNoticeByUinReq) (*CreateNoticeByUinRsp, error)
	// DescribeIntegrator 查询集成商信息
	DescribeIntegrator(ctx context.Context, req *DescribeIntegratorReq) (*DescribeIntegratorRsp, error)
	// DescribeRobotBizIDByAppKey 通过appKey获取机器人业务id
	DescribeRobotBizIDByAppKey(ctx context.Context, req *DescribeRobotBizIDByAppKeyReq) (*DescribeRobotBizIDByAppKeyRsp, error)
	// DescribeLatestReleaseStatus 获取机器人最新发布状态
	DescribeLatestReleaseStatus(ctx context.Context, req *DescribeLatestReleaseStatusReq) (*DescribeLatestReleaseStatusRsp, error)
	// TrialProduct 试用开通
	TrialProduct(ctx context.Context, req *TrialProductReq) (*TrialProductRsp, error)
	// EditApp 编辑应用
	EditApp(ctx context.Context, req *EditAppReq) (*EditAppRsp, error)
	// GetAppDefaultConfig 获取应用默认配置
	GetAppDefaultConfig(ctx context.Context, req *GetAppDefaultConfigReq) (*GetAppDefaultConfigRsp, error)
	// ClearAppCustomConfig 清除应用自定义配置
	ClearAppCustomConfig(ctx context.Context, req *ClearAppCustomConfigReq) (*ClearAppCustomConfigRsp, error)
	// GetIntent 获取意图
	GetIntent(ctx context.Context, req *GetIntentReq) (*GetIntentRsp, error)
	// ListIntent 获取意图列表
	ListIntent(ctx context.Context, req *ListIntentReq) (*ListIntentRsp, error)
	// CreateIntent 创建意图
	CreateIntent(ctx context.Context, req *CreateIntentReq) (*CreateIntentRsp, error)
	// UpdateIntent 更新意图
	UpdateIntent(ctx context.Context, req *UpdateIntentReq) (*UpdateIntentRsp, error)
	// DeleteIntent 删除意图
	DeleteIntent(ctx context.Context, req *DeleteIntentReq) (*DeleteIntentRsp, error)
	// ListIntentByPolicyID 获取策略绑定的意图列表
	ListIntentByPolicyID(ctx context.Context, req *ListIntentByPolicyIDReq) (*ListIntentByPolicyIDRsp, error)
	// ListIntentPolicy 获取策略列表
	ListIntentPolicy(ctx context.Context, req *ListIntentPolicyReq) (*ListIntentPolicyRsp, error)
	// CreateIntentPolicy 创建策略
	CreateIntentPolicy(ctx context.Context, req *CreateIntentPolicyReq) (*CreateIntentPolicyRsp, error)
	// UpdateIntentPolicy 更新策略
	UpdateIntentPolicy(ctx context.Context, req *UpdateIntentPolicyReq) (*UpdateIntentPolicyRsp, error)
	// DeleteIntentPolicy 删除策略
	DeleteIntentPolicy(ctx context.Context, req *DeleteIntentPolicyReq) (*DeleteIntentPolicyRsp, error)
	// ListUnusedIntentKeyMap 获取未使用的意图列表
	ListUnusedIntentKeyMap(ctx context.Context, req *ListUnusedIntentKeyMapReq) (*ListUnusedIntentKeyMapRsp, error)
	// ListIntentPolicyKeyMap 获取策略列表映射关系
	ListIntentPolicyKeyMap(ctx context.Context, req *ListIntentPolicyKeyMapReq) (*ListIntentPolicyKeyMapRsp, error)
	// CreateCorpCustomModel 新增企业自定义模型
	CreateCorpCustomModel(ctx context.Context, req *CreateCorpCustomModelReq) (*CreateCorpCustomModelRsp, error)
	// GetRobotConfigByVersionID 获取版本配置
	GetRobotConfigByVersionID(ctx context.Context, req *GetRobotConfigByVersionIDReq) (*GetRobotConfigByVersionIDRsp, error)
	// ListAgentFeedbackInner ListAgentFeedbackInner 查询 agent反馈信息列表
	ListAgentFeedbackInner(ctx context.Context, req *ListAgentFeedbackInnerReq) (*ListAgentFeedbackInnerRsp, error)
	// UpdateAgentFeedbackStatus UpdateFeedbackStatus 修改 agent反馈信息状态
	UpdateAgentFeedbackStatus(ctx context.Context, req *UpdateAgentFeedbackStatusReq) (*UpdateAgentFeedbackStatusRsp, error)
	// UpdateAgentFeedbackTapd UpdateAgentFeedbackTapd 修改 agent反馈信息关联的tapd
	UpdateAgentFeedbackTapd(ctx context.Context, req *UpdateAgentFeedbackTapdReq) (*UpdateAgentFeedbackTapdRsp, error)
	// CountAgentFeedback CountAgentFeedback 获取 agent新增反馈个数
	CountAgentFeedback(ctx context.Context, req *CountAgentFeedbackReq) (*CountAgentFeedbackRsp, error)
	// GetAgentFeedback GetAgentFeedback 获取 agent反馈信息
	GetAgentFeedback(ctx context.Context, req *GetAgentFeedbackReq) (*GetAgentFeedbackRsp, error)
	// UpdateAgentFeedbackAndonInfo UpdateAgentFeedbackAndonInfo 修改 agent反馈信息状态
	UpdateAgentFeedbackAndonInfo(ctx context.Context, req *UpdateAgentFeedbackAndonInfoReq) (*UpdateAgentFeedbackAndonInfoRsp, error)

	DeleteFeedbackByFlowIds(ctx context.Context, req *DeleteFeedbackByFlowIdsReq) (*DeleteFeedbackByFlowIdsRsp, error)
	// ListFeedbackInner ListFeedbackInner 查询反馈信息列表
	ListFeedbackInner(ctx context.Context, req *ListFeedbackInnerReq) (*ListFeedbackInnerRsp, error)
	// ListFeedbackByBizIDInner ListFeedbackByBizIDInner 查询反馈信息列表
	ListFeedbackByBizIDInner(ctx context.Context, req *ListFeedbackByBizIDInnerReq) (*ListFeedbackByBizIDInnerRsp, error)
	// UpdateFeedbackClassification UpdateFeedbackClassification 修改一级分类，二级分类
	UpdateFeedbackClassification(ctx context.Context, req *UpdateFeedbackClassificationReq) (*UpdateFeedbackClassificationRsp, error)
	// UpdateFeedbackAndonInfo UpdateFeedbackAndonIfoReq 修改安灯状态信息
	UpdateFeedbackAndonInfo(ctx context.Context, req *UpdateFeedbackAndonInfoReq) (*UpdateFeedbackAndonInfoRsp, error)
	// UpdateFeedbackStatus UpdateFeedbackStatus 修改反馈信息状态
	UpdateFeedbackStatus(ctx context.Context, req *UpdateFeedbackStatusReq) (*UpdateFeedbackStatusRsp, error)
	// UpdateFeedbackTapd UpdateFeedbackTapd 修改反馈信息关联的tapd
	UpdateFeedbackTapd(ctx context.Context, req *UpdateFeedbackTapdReq) (*UpdateFeedbackTapdRsp, error)
	// CountFeedback CountFeedback 获取新增反馈个数
	CountFeedback(ctx context.Context, req *CountFeedbackReq) (*CountFeedbackRsp, error)
	// GetFeedback CountFeedback 获取新增反馈个数
	GetFeedback(ctx context.Context, req *GetFeedbackReq) (*GetFeedbackRsp, error)
	// DescribeAccountBalance 获取企业账户余额信息
	DescribeAccountBalance(ctx context.Context, req *DescribeAccountBalanceReq) (*DescribeAccountBalanceRsp, error)
	// GetAppType GetAppType 获取应用类型
	GetAppType(ctx context.Context, req *GetAppTypeReq) (*GetAppTypeRsp, error)
	// GetDescribeLicense 获取应用License
	GetDescribeLicense(ctx context.Context, req *GetDescribeLicenseReq) (*GetDescribeLicenseRsp, error)
	// GetExperienceApps GetExperienceApps 体验中心-获取体验应用列表
	GetExperienceApps(ctx context.Context, req *GetExperienceAppsReq) (*GetExperienceAppsRsp, error)
	// ModifyExperienceApp ModifyExperienceApp 修改体验应用
	ModifyExperienceApp(ctx context.Context, req *ModifyExperienceAppReq) (*ModifyExperienceAppRsp, error)
	// ListSelectDoc ListSelectDoc 选择文档列表
	ListSelectDoc(ctx context.Context, req *ListSelectDocReq) (*ListSelectDocRsp, error)
	// CreateCorpAndAssignPermission CreateCorpAndAssignPermission
	CreateCorpAndAssignPermission(ctx context.Context, req *CreateCorpAndAssignPermissionReq) (*CreateCorpAndAssignPermissionRsp, error)
	// CheckCorpAndPermission CheckCorpAndPermission
	CheckCorpAndPermission(ctx context.Context, req *CheckCorpAndPermissionReq) (*CheckCorpAndPermissionRsp, error)
	// DescribeCropByUin DescribeCropByUin
	DescribeCropByUin(ctx context.Context, req *DescribeCropByUinReq) (*DescribeCropByUinRsp, error)
	// GetListModel GetListModel 获取企业模型列表
	GetListModel(ctx context.Context, req *GetListModelReq) (*GetListModelRsp, error)
	// ModifyAppInfosecBizType ModifyAppInfosecBizType 修改应用安全审核策略
	ModifyAppInfosecBizType(ctx context.Context, req *ModifyAppInfosecBizTypeReq) (*ModifyAppInfosecBizTypeRsp, error)
	// GetTotalConcurrency GetTotalConcurrency 获取企业总并发数
	GetTotalConcurrency(ctx context.Context, req *GetTotalConcurrencyReq) (*GetTotalConcurrencyRsp, error)
	// MultiLock MultiLock 加锁
	MultiLock(ctx context.Context, req *MultiLockReq) (*MultiLockRsp, error)
	// MultiUnlock MultiUnlock 解锁
	MultiUnlock(ctx context.Context, req *MultiUnlockReq) (*MultiUnlockRsp, error)
	// CheckVarIsUsed 检查自定义参数是否被使用
	CheckVarIsUsed(ctx context.Context, req *CheckVarIsUsedReq) (*CheckVarIsUsedRsp, error)
	// ModifyAppVar 修改应用检索范围自定义参数
	ModifyAppVar(ctx context.Context, req *ModifyAppVarReq) (*ModifyAppVarRsp, error)
	// ClearAppKnowledgeCallback 清理应用知识资源回调结果
	ClearAppKnowledgeCallback(ctx context.Context, req *ClearAppKnowledgeCallbackReq) (*ClearAppKnowledgeCallbackRsp, error)
	// ClearAppFlowCallback 清理应用流程资源回调
	ClearAppFlowCallback(ctx context.Context, req *ClearAppFlowCallbackReq) (*ClearAppFlowCallbackRsp, error)
	// ClearAppVectorCallback 清理应用向量库资源回调
	ClearAppVectorCallback(ctx context.Context, req *ClearAppVectorCallbackReq) (*ClearAppVectorCallbackRsp, error)
	// ClearAppResourceCallback 清理应用资源回调
	ClearAppResourceCallback(ctx context.Context, req *ClearAppResourceCallbackReq) (*ClearAppResourceCallbackRsp, error)
	// GetAppByPluginId 查询插件关联的应用信息
	GetAppByPluginId(ctx context.Context, req *GetAppByPluginIdReq) (*GetAppByPluginIdRsp, error)
	// GetCorpStaffName 查询用户昵称
	GetCorpStaffName(ctx context.Context, req *GetCorpStaffNameReq) (*GetCorpStaffNameRsp, error)
	// GetUserInfo 查询用户信息
	GetUserInfo(ctx context.Context, req *GetUserInfoReq) (*GetUserInfoRsp, error)
	// GetModelFinanceInfo 获取模型的计费控制信息
	GetModelFinanceInfo(ctx context.Context, req *GetModelFinanceInfoReq) (*GetModelFinanceInfoRsp, error)
	// GetModelInfo 获取企业模型信息
	GetModelInfo(ctx context.Context, req *GetModelInfoReq) (*GetModelInfoRsp, error)
	// GetValidExperienceApps 获取有效的体验应用信息
	GetValidExperienceApps(ctx context.Context, req *GetValidExperienceAppsReq) (*GetValidExperienceAppsRsp, error)
	// GetModelList 获取模型列表信息
	GetModelList(ctx context.Context, req *GetModelListReq) (*GetModelListRsp, error)
	// GetAppChatInputNum 获取应用对话query输入长度限制
	GetAppChatInputNum(ctx context.Context, req *GetAppChatInputNumReq) (*GetAppChatInputNumRsp, error)
	// CreatePromptVersion 创建Prompt版本信息
	CreatePromptVersion(ctx context.Context, req *CreatePromptVersionReq) (*CreatePromptVersionRsp, error)
	// EditPromptVersion 编辑prompt版本信息
	EditPromptVersion(ctx context.Context, req *EditPromptVersionReq) (*EditPromptVersionRsp, error)
	// GetPromptVersionList 查询prompt版本信息
	GetPromptVersionList(ctx context.Context, req *GetPromptVersionListReq) (*GetPromptVersionListRsp, error)
	// UpgradePromptVersion 升级Prompt版本
	UpgradePromptVersion(ctx context.Context, req *UpgradePromptVersionReq) (*UpgradePromptVersionRsp, error)
	// CreateCorpConcurrencyWL 创建账户并发白名单
	CreateCorpConcurrencyWL(ctx context.Context, req *CreateCorpConcurrencyWLReq) (*CreateCorpConcurrencyWLRsp, error)
	// EditCorpConcurrencyWL 修改账户并发白名单
	EditCorpConcurrencyWL(ctx context.Context, req *EditCorpConcurrencyWLReq) (*EditCorpConcurrencyWLRsp, error)
	// DeleteCorpConcurrencyWL 删除账户并发白名单
	DeleteCorpConcurrencyWL(ctx context.Context, req *DeleteCorpConcurrencyWLReq) (*DeleteCorpConcurrencyWLRsp, error)
	// GetCorpConcurrencyWLList 查询账户并发白名单
	GetCorpConcurrencyWLList(ctx context.Context, req *GetCorpConcurrencyWLListReq) (*GetCorpConcurrencyWLListRsp, error)
	// StartEmbeddingUpgradeApp 应用升级embedding开始
	StartEmbeddingUpgradeApp(ctx context.Context, req *StartEmbeddingUpgradeAppReq) (*StartEmbeddingUpgradeAppRsp, error)
	// FinishEmbeddingUpgradeApp 应用升级embedding结束
	FinishEmbeddingUpgradeApp(ctx context.Context, req *FinishEmbeddingUpgradeAppReq) (*FinishEmbeddingUpgradeAppRsp, error)
	// GetAppsByBizIDs 通过应用业务ID获取应用信息，基于缓存读取
	GetAppsByBizIDs(ctx context.Context, req *GetAppsByBizIDsReq) (*GetAppsByBizIDsRsp, error)
	// GetCorpStaffsByBizIDs 通过员工业务ID获取员工信息，基于缓存读取
	GetCorpStaffsByBizIDs(ctx context.Context, req *GetCorpStaffsByBizIDsReq) (*GetCorpStaffsByBizIDsRsp, error)
	// CopyAppConfig 复制应用配置
	CopyAppConfig(ctx context.Context, req *CopyAppConfigReq) (*CopyAppConfigRsp, error)
	// EditCorpCustomModelWL 更新账户自定义模型白名单
	EditCorpCustomModelWL(ctx context.Context, req *EditCorpCustomModelWLReq) (*EditCorpCustomModelWLRsp, error)
	// DeleteCorpCustomModelWL 删除账户自定义模型白名单
	DeleteCorpCustomModelWL(ctx context.Context, req *DeleteCorpCustomModelWLReq) (*DeleteCorpCustomModelWLRsp, error)
	// GetCorpCustomModelWLList 获取账户自定义模型白名单列表
	GetCorpCustomModelWLList(ctx context.Context, req *GetCorpCustomModelWLListReq) (*GetCorpCustomModelWLListRsp, error)
	// GetCorpModelQpmTpmLimit 获取账户模型默认的QPM、TPM上限值
	GetCorpModelQpmTpmLimit(ctx context.Context, req *GetCorpModelQpmTpmLimitReq) (*GetCorpModelQpmTpmLimitRsp, error)
	// GetDefaultQpmTpmLimit 获取账户模型的默认QPM、TPM值
	GetDefaultQpmTpmLimit(ctx context.Context, req *GetDefaultQpmTpmLimitReq) (*GetDefaultQpmTpmLimitRsp, error)
	// EditCorpQpmTpmLimit 编辑账户模型QPM、TPM配置
	EditCorpQpmTpmLimit(ctx context.Context, req *EditCorpQpmTpmLimitReq) (*EditCorpQpmTpmLimitRsp, error)
	// DeleteCorpQpmTpmLimit 删除账户模型QPM、TPM配置
	DeleteCorpQpmTpmLimit(ctx context.Context, req *DeleteCorpQpmTpmLimitReq) (*DeleteCorpQpmTpmLimitRsp, error)
	// GetCorpQpmTpmLimitList 获取账户模型QPM、TPM配置
	GetCorpQpmTpmLimitList(ctx context.Context, req *GetCorpQpmTpmLimitListReq) (*GetCorpQpmTpmLimitListRsp, error)
	// CreateShareKnowledgeBaseApp 创建共享知识库应用
	CreateShareKnowledgeBaseApp(ctx context.Context, req *CreateShareKnowledgeBaseAppReq) (*CreateShareKnowledgeBaseAppRsp, error)
	// DeleteShareKnowledgeBaseApp 删除共享知识库应用
	DeleteShareKnowledgeBaseApp(ctx context.Context, req *DeleteShareKnowledgeBaseAppReq) (*DeleteShareKnowledgeBaseAppRsp, error)
	// CopyAppCallback 复制应用回调结果
	CopyAppCallback(ctx context.Context, req *CopyAppCallbackReq) (*CopyAppCallbackRsp, error)
	// CreateApproval 创建审批单
	CreateApproval(ctx context.Context, req *CreateApprovalReq) (*CreateApprovalRsp, error)
	// GetLastApproval 获取最新一条审批
	GetLastApproval(ctx context.Context, req *GetLastApprovalReq) (*GetLastApprovalRsp, error)
}

func ApiService_CreateCorp_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &CreateCorpReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).CreateCorp(ctx, reqbody.(*CreateCorpReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_ListCorp_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ListCorpReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).ListCorp(ctx, reqbody.(*ListCorpReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_GetCorp_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetCorpReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).GetCorp(ctx, reqbody.(*GetCorpReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_ModifyInfosecBizType_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ModifyInfosecBizTypeReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).ModifyInfosecBizType(ctx, reqbody.(*ModifyInfosecBizTypeReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_ModifyMaxTokenUsage_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ModifyMaxTokenUsageReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).ModifyMaxTokenUsage(ctx, reqbody.(*ModifyMaxTokenUsageReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_ModifyMaxCharSize_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ModifyMaxCharSizeReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).ModifyMaxCharSize(ctx, reqbody.(*ModifyMaxCharSizeReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_AuditCorp_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &AuditCorpReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).AuditCorp(ctx, reqbody.(*AuditCorpReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_ModifyCorpRobotQuota_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ModifyCorpRobotQuotaReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).ModifyCorpRobotQuota(ctx, reqbody.(*ModifyCorpRobotQuotaReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_CorpStaffList_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &CorpStaffListReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).CorpStaffList(ctx, reqbody.(*CorpStaffListReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_ListCorpStaffByIds_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ListCorpStaffByIdsReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).ListCorpStaffByIds(ctx, reqbody.(*ListCorpStaffByIdsReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_ReleaseDetailNotify_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ReleaseDetailNotifyReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).ReleaseDetailNotify(ctx, reqbody.(*ReleaseDetailNotifyReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_ReleaseNotify_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ReleaseNotifyReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).ReleaseNotify(ctx, reqbody.(*ReleaseNotifyReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_GetPresignedURL_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetPresignedURLReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).GetPresignedURL(ctx, reqbody.(*GetPresignedURLReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_GetRobotInfo_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetRobotInfoReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).GetRobotInfo(ctx, reqbody.(*GetRobotInfoReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_DescribeRobotInfo_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DescribeRobotInfoReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).DescribeRobotInfo(ctx, reqbody.(*DescribeRobotInfoReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_SearchPreview_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &SearchPreviewReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).SearchPreview(ctx, reqbody.(*SearchPreviewReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_Search_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &SearchReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).Search(ctx, reqbody.(*SearchReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_CustomSearchPreview_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &CustomSearchPreviewReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).CustomSearchPreview(ctx, reqbody.(*CustomSearchPreviewReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_CustomSearch_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &CustomSearchReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).CustomSearch(ctx, reqbody.(*CustomSearchReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_MatchRefer_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &MatchReferReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).MatchRefer(ctx, reqbody.(*MatchReferReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_AuditResultCallback_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &CheckResultReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).AuditResultCallback(ctx, reqbody.(*CheckResultReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_GetRobotList_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetRobotListReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).GetRobotList(ctx, reqbody.(*GetRobotListReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_GetAppList_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetAppListReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).GetAppList(ctx, reqbody.(*GetAppListReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_GetRobotByAppKey_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetRobotByAppKeyReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).GetRobotByAppKey(ctx, reqbody.(*GetRobotByAppKeyReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_GetAppByAppKey_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetAppByAppKeyReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).GetAppByAppKey(ctx, reqbody.(*GetAppByAppKeyReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_GetAppInfo_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetAppInfoReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).GetAppInfo(ctx, reqbody.(*GetAppInfoReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_EditRobot_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &EditRobotReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).EditRobot(ctx, reqbody.(*EditRobotReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_GetCorpList_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetCorpListReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).GetCorpList(ctx, reqbody.(*GetCorpListReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_GetDocs_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetDocsReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).GetDocs(ctx, reqbody.(*GetDocsReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_SearchPreviewRejectedQuestion_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &SearchPreviewRejectedQuestionReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).SearchPreviewRejectedQuestion(ctx, reqbody.(*SearchPreviewRejectedQuestionReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_SearchReleaseRejectedQuestion_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &SearchReleaseRejectedQuestionReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).SearchReleaseRejectedQuestion(ctx, reqbody.(*SearchReleaseRejectedQuestionReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_ListRejectedQuestion_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ListRejectedQuestionReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).ListRejectedQuestion(ctx, reqbody.(*ListRejectedQuestionReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_AddUnsatisfiedReply_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &AddUnsatisfiedReplyReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).AddUnsatisfiedReply(ctx, reqbody.(*AddUnsatisfiedReplyReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_ListQA_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ListQAReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).ListQA(ctx, reqbody.(*ListQAReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_GetAdminTaskList_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetAdminTaskListReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).GetAdminTaskList(ctx, reqbody.(*GetAdminTaskListReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_GetAdminTaskHistoryList_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetAdminTaskHistoryListReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).GetAdminTaskHistoryList(ctx, reqbody.(*GetAdminTaskHistoryListReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_GetVectorDocTaskList_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetVectorDocTaskListReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).GetVectorDocTaskList(ctx, reqbody.(*GetVectorDocTaskListReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_GetVectorDocTaskHistoryList_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetVectorDocTaskHistoryListReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).GetVectorDocTaskHistoryList(ctx, reqbody.(*GetVectorDocTaskHistoryListReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_GetReleaseList_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetReleaseListReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).GetReleaseList(ctx, reqbody.(*GetReleaseListReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_AddCorpStaff_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &AddCorpStaffReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).AddCorpStaff(ctx, reqbody.(*AddCorpStaffReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_EditCorpStaffPassword_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &EditCorpStaffPasswordReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).EditCorpStaffPassword(ctx, reqbody.(*EditCorpStaffPasswordReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_LeaveCorp_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &LeaveCorpReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).LeaveCorp(ctx, reqbody.(*LeaveCorpReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_UpdateAuditStatus_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &UpdateAuditStatusReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).UpdateAuditStatus(ctx, reqbody.(*UpdateAuditStatusReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_GetRobotDefaultConfig_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetRobotDefaultConfigReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).GetRobotDefaultConfig(ctx, reqbody.(*GetRobotDefaultConfigReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_ClearRobotCustomConfig_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ClearRobotCustomConfigReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).ClearRobotCustomConfig(ctx, reqbody.(*ClearRobotCustomConfigReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_ListGlobalKnowledge_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ListGlobalKnowledgeReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).ListGlobalKnowledge(ctx, reqbody.(*ListGlobalKnowledgeReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_AddGlobalKnowledge_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &AddGlobalKnowledgeReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).AddGlobalKnowledge(ctx, reqbody.(*AddGlobalKnowledgeReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_DelGlobalKnowledge_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DelGlobalKnowledgeReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).DelGlobalKnowledge(ctx, reqbody.(*DelGlobalKnowledgeReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_UpdGlobalKnowledge_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &UpdGlobalKnowledgeReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).UpdGlobalKnowledge(ctx, reqbody.(*UpdGlobalKnowledgeReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_GlobalKnowledge_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GlobalKnowledgeReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).GlobalKnowledge(ctx, reqbody.(*GlobalKnowledgeReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_ForceSyncGlobalKnowledge_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ForceSyncGlobalKnowledgeReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).ForceSyncGlobalKnowledge(ctx, reqbody.(*ForceSyncGlobalKnowledgeReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_CustomSimilarity_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &CustomSimilarityReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).CustomSimilarity(ctx, reqbody.(*CustomSimilarityReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_EnableCorp_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &EnableCorpReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).EnableCorp(ctx, reqbody.(*EnableCorpReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_DisableCorp_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DisableCorpReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).DisableCorp(ctx, reqbody.(*DisableCorpReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_GetCustomResource_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetCustomResourceReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).GetCustomResource(ctx, reqbody.(*GetCustomResourceReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_ActivateProduct_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ActivateProductReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).ActivateProduct(ctx, reqbody.(*ActivateProductReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_CreateNotice_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &CreateNoticeReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).CreateNotice(ctx, reqbody.(*CreateNoticeReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_CreateNoticeByUin_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &CreateNoticeByUinReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).CreateNoticeByUin(ctx, reqbody.(*CreateNoticeByUinReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_DescribeIntegrator_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DescribeIntegratorReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).DescribeIntegrator(ctx, reqbody.(*DescribeIntegratorReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_DescribeRobotBizIDByAppKey_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DescribeRobotBizIDByAppKeyReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).DescribeRobotBizIDByAppKey(ctx, reqbody.(*DescribeRobotBizIDByAppKeyReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_DescribeLatestReleaseStatus_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DescribeLatestReleaseStatusReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).DescribeLatestReleaseStatus(ctx, reqbody.(*DescribeLatestReleaseStatusReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_TrialProduct_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &TrialProductReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).TrialProduct(ctx, reqbody.(*TrialProductReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_EditApp_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &EditAppReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).EditApp(ctx, reqbody.(*EditAppReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_GetAppDefaultConfig_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetAppDefaultConfigReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).GetAppDefaultConfig(ctx, reqbody.(*GetAppDefaultConfigReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_ClearAppCustomConfig_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ClearAppCustomConfigReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).ClearAppCustomConfig(ctx, reqbody.(*ClearAppCustomConfigReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_GetIntent_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetIntentReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).GetIntent(ctx, reqbody.(*GetIntentReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_ListIntent_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ListIntentReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).ListIntent(ctx, reqbody.(*ListIntentReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_CreateIntent_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &CreateIntentReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).CreateIntent(ctx, reqbody.(*CreateIntentReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_UpdateIntent_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &UpdateIntentReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).UpdateIntent(ctx, reqbody.(*UpdateIntentReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_DeleteIntent_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DeleteIntentReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).DeleteIntent(ctx, reqbody.(*DeleteIntentReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_ListIntentByPolicyID_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ListIntentByPolicyIDReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).ListIntentByPolicyID(ctx, reqbody.(*ListIntentByPolicyIDReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_ListIntentPolicy_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ListIntentPolicyReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).ListIntentPolicy(ctx, reqbody.(*ListIntentPolicyReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_CreateIntentPolicy_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &CreateIntentPolicyReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).CreateIntentPolicy(ctx, reqbody.(*CreateIntentPolicyReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_UpdateIntentPolicy_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &UpdateIntentPolicyReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).UpdateIntentPolicy(ctx, reqbody.(*UpdateIntentPolicyReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_DeleteIntentPolicy_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DeleteIntentPolicyReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).DeleteIntentPolicy(ctx, reqbody.(*DeleteIntentPolicyReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_ListUnusedIntentKeyMap_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ListUnusedIntentKeyMapReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).ListUnusedIntentKeyMap(ctx, reqbody.(*ListUnusedIntentKeyMapReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_ListIntentPolicyKeyMap_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ListIntentPolicyKeyMapReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).ListIntentPolicyKeyMap(ctx, reqbody.(*ListIntentPolicyKeyMapReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_CreateCorpCustomModel_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &CreateCorpCustomModelReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).CreateCorpCustomModel(ctx, reqbody.(*CreateCorpCustomModelReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_GetRobotConfigByVersionID_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetRobotConfigByVersionIDReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).GetRobotConfigByVersionID(ctx, reqbody.(*GetRobotConfigByVersionIDReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_ListAgentFeedbackInner_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ListAgentFeedbackInnerReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).ListAgentFeedbackInner(ctx, reqbody.(*ListAgentFeedbackInnerReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_UpdateAgentFeedbackStatus_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &UpdateAgentFeedbackStatusReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).UpdateAgentFeedbackStatus(ctx, reqbody.(*UpdateAgentFeedbackStatusReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_UpdateAgentFeedbackTapd_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &UpdateAgentFeedbackTapdReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).UpdateAgentFeedbackTapd(ctx, reqbody.(*UpdateAgentFeedbackTapdReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_CountAgentFeedback_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &CountAgentFeedbackReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).CountAgentFeedback(ctx, reqbody.(*CountAgentFeedbackReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_GetAgentFeedback_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetAgentFeedbackReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).GetAgentFeedback(ctx, reqbody.(*GetAgentFeedbackReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_UpdateAgentFeedbackAndonInfo_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &UpdateAgentFeedbackAndonInfoReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).UpdateAgentFeedbackAndonInfo(ctx, reqbody.(*UpdateAgentFeedbackAndonInfoReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_DeleteFeedbackByFlowIds_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DeleteFeedbackByFlowIdsReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).DeleteFeedbackByFlowIds(ctx, reqbody.(*DeleteFeedbackByFlowIdsReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_ListFeedbackInner_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ListFeedbackInnerReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).ListFeedbackInner(ctx, reqbody.(*ListFeedbackInnerReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_ListFeedbackByBizIDInner_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ListFeedbackByBizIDInnerReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).ListFeedbackByBizIDInner(ctx, reqbody.(*ListFeedbackByBizIDInnerReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_UpdateFeedbackClassification_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &UpdateFeedbackClassificationReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).UpdateFeedbackClassification(ctx, reqbody.(*UpdateFeedbackClassificationReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_UpdateFeedbackAndonInfo_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &UpdateFeedbackAndonInfoReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).UpdateFeedbackAndonInfo(ctx, reqbody.(*UpdateFeedbackAndonInfoReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_UpdateFeedbackStatus_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &UpdateFeedbackStatusReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).UpdateFeedbackStatus(ctx, reqbody.(*UpdateFeedbackStatusReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_UpdateFeedbackTapd_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &UpdateFeedbackTapdReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).UpdateFeedbackTapd(ctx, reqbody.(*UpdateFeedbackTapdReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_CountFeedback_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &CountFeedbackReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).CountFeedback(ctx, reqbody.(*CountFeedbackReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_GetFeedback_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetFeedbackReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).GetFeedback(ctx, reqbody.(*GetFeedbackReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_DescribeAccountBalance_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DescribeAccountBalanceReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).DescribeAccountBalance(ctx, reqbody.(*DescribeAccountBalanceReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_GetAppType_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetAppTypeReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).GetAppType(ctx, reqbody.(*GetAppTypeReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_GetDescribeLicense_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetDescribeLicenseReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).GetDescribeLicense(ctx, reqbody.(*GetDescribeLicenseReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_GetExperienceApps_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetExperienceAppsReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).GetExperienceApps(ctx, reqbody.(*GetExperienceAppsReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_ModifyExperienceApp_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ModifyExperienceAppReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).ModifyExperienceApp(ctx, reqbody.(*ModifyExperienceAppReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_ListSelectDoc_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ListSelectDocReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).ListSelectDoc(ctx, reqbody.(*ListSelectDocReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_CreateCorpAndAssignPermission_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &CreateCorpAndAssignPermissionReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).CreateCorpAndAssignPermission(ctx, reqbody.(*CreateCorpAndAssignPermissionReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_CheckCorpAndPermission_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &CheckCorpAndPermissionReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).CheckCorpAndPermission(ctx, reqbody.(*CheckCorpAndPermissionReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_DescribeCropByUin_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DescribeCropByUinReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).DescribeCropByUin(ctx, reqbody.(*DescribeCropByUinReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_GetListModel_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetListModelReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).GetListModel(ctx, reqbody.(*GetListModelReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_ModifyAppInfosecBizType_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ModifyAppInfosecBizTypeReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).ModifyAppInfosecBizType(ctx, reqbody.(*ModifyAppInfosecBizTypeReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_GetTotalConcurrency_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetTotalConcurrencyReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).GetTotalConcurrency(ctx, reqbody.(*GetTotalConcurrencyReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_MultiLock_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &MultiLockReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).MultiLock(ctx, reqbody.(*MultiLockReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_MultiUnlock_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &MultiUnlockReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).MultiUnlock(ctx, reqbody.(*MultiUnlockReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_CheckVarIsUsed_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &CheckVarIsUsedReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).CheckVarIsUsed(ctx, reqbody.(*CheckVarIsUsedReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_ModifyAppVar_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ModifyAppVarReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).ModifyAppVar(ctx, reqbody.(*ModifyAppVarReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_ClearAppKnowledgeCallback_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ClearAppKnowledgeCallbackReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).ClearAppKnowledgeCallback(ctx, reqbody.(*ClearAppKnowledgeCallbackReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_ClearAppFlowCallback_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ClearAppFlowCallbackReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).ClearAppFlowCallback(ctx, reqbody.(*ClearAppFlowCallbackReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_ClearAppVectorCallback_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ClearAppVectorCallbackReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).ClearAppVectorCallback(ctx, reqbody.(*ClearAppVectorCallbackReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_ClearAppResourceCallback_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ClearAppResourceCallbackReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).ClearAppResourceCallback(ctx, reqbody.(*ClearAppResourceCallbackReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_GetAppByPluginId_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetAppByPluginIdReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).GetAppByPluginId(ctx, reqbody.(*GetAppByPluginIdReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_GetCorpStaffName_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetCorpStaffNameReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).GetCorpStaffName(ctx, reqbody.(*GetCorpStaffNameReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_GetUserInfo_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetUserInfoReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).GetUserInfo(ctx, reqbody.(*GetUserInfoReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_GetModelFinanceInfo_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetModelFinanceInfoReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).GetModelFinanceInfo(ctx, reqbody.(*GetModelFinanceInfoReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_GetModelInfo_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetModelInfoReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).GetModelInfo(ctx, reqbody.(*GetModelInfoReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_GetValidExperienceApps_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetValidExperienceAppsReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).GetValidExperienceApps(ctx, reqbody.(*GetValidExperienceAppsReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_GetModelList_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetModelListReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).GetModelList(ctx, reqbody.(*GetModelListReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_GetAppChatInputNum_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetAppChatInputNumReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).GetAppChatInputNum(ctx, reqbody.(*GetAppChatInputNumReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_CreatePromptVersion_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &CreatePromptVersionReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).CreatePromptVersion(ctx, reqbody.(*CreatePromptVersionReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_EditPromptVersion_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &EditPromptVersionReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).EditPromptVersion(ctx, reqbody.(*EditPromptVersionReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_GetPromptVersionList_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetPromptVersionListReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).GetPromptVersionList(ctx, reqbody.(*GetPromptVersionListReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_UpgradePromptVersion_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &UpgradePromptVersionReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).UpgradePromptVersion(ctx, reqbody.(*UpgradePromptVersionReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_CreateCorpConcurrencyWL_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &CreateCorpConcurrencyWLReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).CreateCorpConcurrencyWL(ctx, reqbody.(*CreateCorpConcurrencyWLReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_EditCorpConcurrencyWL_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &EditCorpConcurrencyWLReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).EditCorpConcurrencyWL(ctx, reqbody.(*EditCorpConcurrencyWLReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_DeleteCorpConcurrencyWL_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DeleteCorpConcurrencyWLReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).DeleteCorpConcurrencyWL(ctx, reqbody.(*DeleteCorpConcurrencyWLReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_GetCorpConcurrencyWLList_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetCorpConcurrencyWLListReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).GetCorpConcurrencyWLList(ctx, reqbody.(*GetCorpConcurrencyWLListReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_StartEmbeddingUpgradeApp_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &StartEmbeddingUpgradeAppReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).StartEmbeddingUpgradeApp(ctx, reqbody.(*StartEmbeddingUpgradeAppReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_FinishEmbeddingUpgradeApp_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &FinishEmbeddingUpgradeAppReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).FinishEmbeddingUpgradeApp(ctx, reqbody.(*FinishEmbeddingUpgradeAppReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_GetAppsByBizIDs_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetAppsByBizIDsReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).GetAppsByBizIDs(ctx, reqbody.(*GetAppsByBizIDsReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_GetCorpStaffsByBizIDs_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetCorpStaffsByBizIDsReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).GetCorpStaffsByBizIDs(ctx, reqbody.(*GetCorpStaffsByBizIDsReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_CopyAppConfig_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &CopyAppConfigReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).CopyAppConfig(ctx, reqbody.(*CopyAppConfigReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_EditCorpCustomModelWL_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &EditCorpCustomModelWLReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).EditCorpCustomModelWL(ctx, reqbody.(*EditCorpCustomModelWLReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_DeleteCorpCustomModelWL_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DeleteCorpCustomModelWLReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).DeleteCorpCustomModelWL(ctx, reqbody.(*DeleteCorpCustomModelWLReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_GetCorpCustomModelWLList_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetCorpCustomModelWLListReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).GetCorpCustomModelWLList(ctx, reqbody.(*GetCorpCustomModelWLListReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_GetCorpModelQpmTpmLimit_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetCorpModelQpmTpmLimitReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).GetCorpModelQpmTpmLimit(ctx, reqbody.(*GetCorpModelQpmTpmLimitReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_GetDefaultQpmTpmLimit_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetDefaultQpmTpmLimitReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).GetDefaultQpmTpmLimit(ctx, reqbody.(*GetDefaultQpmTpmLimitReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_EditCorpQpmTpmLimit_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &EditCorpQpmTpmLimitReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).EditCorpQpmTpmLimit(ctx, reqbody.(*EditCorpQpmTpmLimitReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_DeleteCorpQpmTpmLimit_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DeleteCorpQpmTpmLimitReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).DeleteCorpQpmTpmLimit(ctx, reqbody.(*DeleteCorpQpmTpmLimitReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_GetCorpQpmTpmLimitList_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetCorpQpmTpmLimitListReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).GetCorpQpmTpmLimitList(ctx, reqbody.(*GetCorpQpmTpmLimitListReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_CreateShareKnowledgeBaseApp_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &CreateShareKnowledgeBaseAppReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).CreateShareKnowledgeBaseApp(ctx, reqbody.(*CreateShareKnowledgeBaseAppReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_DeleteShareKnowledgeBaseApp_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DeleteShareKnowledgeBaseAppReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).DeleteShareKnowledgeBaseApp(ctx, reqbody.(*DeleteShareKnowledgeBaseAppReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_CopyAppCallback_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &CopyAppCallbackReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).CopyAppCallback(ctx, reqbody.(*CopyAppCallbackReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_CreateApproval_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &CreateApprovalReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).CreateApproval(ctx, reqbody.(*CreateApprovalReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_GetLastApproval_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetLastApprovalReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).GetLastApproval(ctx, reqbody.(*GetLastApprovalReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

// ApiServer_ServiceDesc descriptor for server.RegisterService.
var ApiServer_ServiceDesc = server.ServiceDesc{
	ServiceName: "trpc.KEP.bot_admin_config_server.Api",
	HandlerType: ((*ApiService)(nil)),
	Methods: []server.Method{
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/CreateCorp",
			Func: ApiService_CreateCorp_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/ListCorp",
			Func: ApiService_ListCorp_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/GetCorp",
			Func: ApiService_GetCorp_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/ModifyInfosecBizType",
			Func: ApiService_ModifyInfosecBizType_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/ModifyMaxTokenUsage",
			Func: ApiService_ModifyMaxTokenUsage_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/ModifyMaxCharSize",
			Func: ApiService_ModifyMaxCharSize_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/AuditCorp",
			Func: ApiService_AuditCorp_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/ModifyCorpRobotQuota",
			Func: ApiService_ModifyCorpRobotQuota_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/CorpStaffList",
			Func: ApiService_CorpStaffList_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/ListCorpStaffByIds",
			Func: ApiService_ListCorpStaffByIds_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/ReleaseDetailNotify",
			Func: ApiService_ReleaseDetailNotify_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/ReleaseNotify",
			Func: ApiService_ReleaseNotify_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/GetPresignedURL",
			Func: ApiService_GetPresignedURL_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/GetRobotInfo",
			Func: ApiService_GetRobotInfo_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/DescribeRobotInfo",
			Func: ApiService_DescribeRobotInfo_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/SearchPreview",
			Func: ApiService_SearchPreview_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/Search",
			Func: ApiService_Search_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/CustomSearchPreview",
			Func: ApiService_CustomSearchPreview_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/CustomSearch",
			Func: ApiService_CustomSearch_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/MatchRefer",
			Func: ApiService_MatchRefer_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/AuditResultCallback",
			Func: ApiService_AuditResultCallback_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/GetRobotList",
			Func: ApiService_GetRobotList_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/GetAppList",
			Func: ApiService_GetAppList_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/GetRobotByAppKey",
			Func: ApiService_GetRobotByAppKey_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/GetAppByAppKey",
			Func: ApiService_GetAppByAppKey_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/GetAppInfo",
			Func: ApiService_GetAppInfo_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/EditRobot",
			Func: ApiService_EditRobot_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/GetCorpList",
			Func: ApiService_GetCorpList_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/GetDocs",
			Func: ApiService_GetDocs_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/SearchPreviewRejectedQuestion",
			Func: ApiService_SearchPreviewRejectedQuestion_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/SearchReleaseRejectedQuestion",
			Func: ApiService_SearchReleaseRejectedQuestion_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/ListRejectedQuestion",
			Func: ApiService_ListRejectedQuestion_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/AddUnsatisfiedReply",
			Func: ApiService_AddUnsatisfiedReply_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/ListQA",
			Func: ApiService_ListQA_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/GetAdminTaskList",
			Func: ApiService_GetAdminTaskList_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/GetAdminTaskHistoryList",
			Func: ApiService_GetAdminTaskHistoryList_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/GetVectorDocTaskList",
			Func: ApiService_GetVectorDocTaskList_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/GetVectorDocTaskHistoryList",
			Func: ApiService_GetVectorDocTaskHistoryList_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/GetReleaseList",
			Func: ApiService_GetReleaseList_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/AddCorpStaff",
			Func: ApiService_AddCorpStaff_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/EditCorpStaffPassword",
			Func: ApiService_EditCorpStaffPassword_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/LeaveCorp",
			Func: ApiService_LeaveCorp_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/UpdateAuditStatus",
			Func: ApiService_UpdateAuditStatus_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/GetRobotDefaultConfig",
			Func: ApiService_GetRobotDefaultConfig_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/ClearRobotCustomConfig",
			Func: ApiService_ClearRobotCustomConfig_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/ListGlobalKnowledge",
			Func: ApiService_ListGlobalKnowledge_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/AddGlobalKnowledge",
			Func: ApiService_AddGlobalKnowledge_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/DelGlobalKnowledge",
			Func: ApiService_DelGlobalKnowledge_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/UpdGlobalKnowledge",
			Func: ApiService_UpdGlobalKnowledge_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/GlobalKnowledge",
			Func: ApiService_GlobalKnowledge_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/ForceSyncGlobalKnowledge",
			Func: ApiService_ForceSyncGlobalKnowledge_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/CustomSimilarity",
			Func: ApiService_CustomSimilarity_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/EnableCorp",
			Func: ApiService_EnableCorp_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/DisableCorp",
			Func: ApiService_DisableCorp_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/GetCustomResource",
			Func: ApiService_GetCustomResource_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/ActivateProduct",
			Func: ApiService_ActivateProduct_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/CreateNotice",
			Func: ApiService_CreateNotice_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/CreateNoticeByUin",
			Func: ApiService_CreateNoticeByUin_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/DescribeIntegrator",
			Func: ApiService_DescribeIntegrator_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/DescribeRobotBizIDByAppKey",
			Func: ApiService_DescribeRobotBizIDByAppKey_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/DescribeLatestReleaseStatus",
			Func: ApiService_DescribeLatestReleaseStatus_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/TrialProduct",
			Func: ApiService_TrialProduct_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/EditApp",
			Func: ApiService_EditApp_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/GetAppDefaultConfig",
			Func: ApiService_GetAppDefaultConfig_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/ClearAppCustomConfig",
			Func: ApiService_ClearAppCustomConfig_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/GetIntent",
			Func: ApiService_GetIntent_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/ListIntent",
			Func: ApiService_ListIntent_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/CreateIntent",
			Func: ApiService_CreateIntent_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/UpdateIntent",
			Func: ApiService_UpdateIntent_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/DeleteIntent",
			Func: ApiService_DeleteIntent_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/ListIntentByPolicyID",
			Func: ApiService_ListIntentByPolicyID_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/ListIntentPolicy",
			Func: ApiService_ListIntentPolicy_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/CreateIntentPolicy",
			Func: ApiService_CreateIntentPolicy_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/UpdateIntentPolicy",
			Func: ApiService_UpdateIntentPolicy_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/DeleteIntentPolicy",
			Func: ApiService_DeleteIntentPolicy_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/ListUnusedIntentKeyMap",
			Func: ApiService_ListUnusedIntentKeyMap_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/ListIntentPolicyKeyMap",
			Func: ApiService_ListIntentPolicyKeyMap_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/CreateCorpCustomModel",
			Func: ApiService_CreateCorpCustomModel_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/GetRobotConfigByVersionID",
			Func: ApiService_GetRobotConfigByVersionID_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/ListAgentFeedbackInner",
			Func: ApiService_ListAgentFeedbackInner_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/UpdateAgentFeedbackStatus",
			Func: ApiService_UpdateAgentFeedbackStatus_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/UpdateAgentFeedbackTapd",
			Func: ApiService_UpdateAgentFeedbackTapd_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/CountAgentFeedback",
			Func: ApiService_CountAgentFeedback_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/GetAgentFeedback",
			Func: ApiService_GetAgentFeedback_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/UpdateAgentFeedbackAndonInfo",
			Func: ApiService_UpdateAgentFeedbackAndonInfo_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/DeleteFeedbackByFlowIds",
			Func: ApiService_DeleteFeedbackByFlowIds_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/ListFeedbackInner",
			Func: ApiService_ListFeedbackInner_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/ListFeedbackByBizIDInner",
			Func: ApiService_ListFeedbackByBizIDInner_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/UpdateFeedbackClassification",
			Func: ApiService_UpdateFeedbackClassification_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/UpdateFeedbackAndonInfo",
			Func: ApiService_UpdateFeedbackAndonInfo_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/UpdateFeedbackStatus",
			Func: ApiService_UpdateFeedbackStatus_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/UpdateFeedbackTapd",
			Func: ApiService_UpdateFeedbackTapd_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/CountFeedback",
			Func: ApiService_CountFeedback_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/GetFeedback",
			Func: ApiService_GetFeedback_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/DescribeAccountBalance",
			Func: ApiService_DescribeAccountBalance_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/GetAppType",
			Func: ApiService_GetAppType_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/GetDescribeLicense",
			Func: ApiService_GetDescribeLicense_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/GetExperienceApps",
			Func: ApiService_GetExperienceApps_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/ModifyExperienceApp",
			Func: ApiService_ModifyExperienceApp_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/ListSelectDoc",
			Func: ApiService_ListSelectDoc_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/CreateCorpAndAssignPermission",
			Func: ApiService_CreateCorpAndAssignPermission_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/CheckCorpAndPermission",
			Func: ApiService_CheckCorpAndPermission_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/DescribeCropByUin",
			Func: ApiService_DescribeCropByUin_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/GetListModel",
			Func: ApiService_GetListModel_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/ModifyAppInfosecBizType",
			Func: ApiService_ModifyAppInfosecBizType_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/GetTotalConcurrency",
			Func: ApiService_GetTotalConcurrency_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/MultiLock",
			Func: ApiService_MultiLock_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/MultiUnlock",
			Func: ApiService_MultiUnlock_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/CheckVarIsUsed",
			Func: ApiService_CheckVarIsUsed_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/ModifyAppVar",
			Func: ApiService_ModifyAppVar_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/ClearAppKnowledgeCallback",
			Func: ApiService_ClearAppKnowledgeCallback_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/ClearAppFlowCallback",
			Func: ApiService_ClearAppFlowCallback_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/ClearAppVectorCallback",
			Func: ApiService_ClearAppVectorCallback_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/ClearAppResourceCallback",
			Func: ApiService_ClearAppResourceCallback_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/GetAppByPluginId",
			Func: ApiService_GetAppByPluginId_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/GetCorpStaffName",
			Func: ApiService_GetCorpStaffName_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/GetUserInfo",
			Func: ApiService_GetUserInfo_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/GetModelFinanceInfo",
			Func: ApiService_GetModelFinanceInfo_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/GetModelInfo",
			Func: ApiService_GetModelInfo_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/GetValidExperienceApps",
			Func: ApiService_GetValidExperienceApps_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/GetModelList",
			Func: ApiService_GetModelList_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/GetAppChatInputNum",
			Func: ApiService_GetAppChatInputNum_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/CreatePromptVersion",
			Func: ApiService_CreatePromptVersion_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/EditPromptVersion",
			Func: ApiService_EditPromptVersion_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/GetPromptVersionList",
			Func: ApiService_GetPromptVersionList_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/UpgradePromptVersion",
			Func: ApiService_UpgradePromptVersion_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/CreateCorpConcurrencyWL",
			Func: ApiService_CreateCorpConcurrencyWL_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/EditCorpConcurrencyWL",
			Func: ApiService_EditCorpConcurrencyWL_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/DeleteCorpConcurrencyWL",
			Func: ApiService_DeleteCorpConcurrencyWL_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/GetCorpConcurrencyWLList",
			Func: ApiService_GetCorpConcurrencyWLList_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/StartEmbeddingUpgradeApp",
			Func: ApiService_StartEmbeddingUpgradeApp_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/FinishEmbeddingUpgradeApp",
			Func: ApiService_FinishEmbeddingUpgradeApp_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/GetAppsByBizIDs",
			Func: ApiService_GetAppsByBizIDs_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/GetCorpStaffsByBizIDs",
			Func: ApiService_GetCorpStaffsByBizIDs_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/CopyAppConfig",
			Func: ApiService_CopyAppConfig_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/EditCorpCustomModelWL",
			Func: ApiService_EditCorpCustomModelWL_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/DeleteCorpCustomModelWL",
			Func: ApiService_DeleteCorpCustomModelWL_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/GetCorpCustomModelWLList",
			Func: ApiService_GetCorpCustomModelWLList_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/GetCorpModelQpmTpmLimit",
			Func: ApiService_GetCorpModelQpmTpmLimit_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/GetDefaultQpmTpmLimit",
			Func: ApiService_GetDefaultQpmTpmLimit_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/EditCorpQpmTpmLimit",
			Func: ApiService_EditCorpQpmTpmLimit_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/DeleteCorpQpmTpmLimit",
			Func: ApiService_DeleteCorpQpmTpmLimit_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/GetCorpQpmTpmLimitList",
			Func: ApiService_GetCorpQpmTpmLimitList_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/CreateShareKnowledgeBaseApp",
			Func: ApiService_CreateShareKnowledgeBaseApp_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/DeleteShareKnowledgeBaseApp",
			Func: ApiService_DeleteShareKnowledgeBaseApp_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/CopyAppCallback",
			Func: ApiService_CopyAppCallback_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/CreateApproval",
			Func: ApiService_CreateApproval_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Api/GetLastApproval",
			Func: ApiService_GetLastApproval_Handler,
		},
	},
}

// RegisterApiService registers service.
func RegisterApiService(s server.Service, svr ApiService) {
	if err := s.Register(&ApiServer_ServiceDesc, svr); err != nil {
		panic(fmt.Sprintf("Api register error:%v", err))
	}
}

// START --------------------------------- Default Unimplemented Server Service --------------------------------- START

type UnimplementedApi struct{}

// CreateCorp 创建企业信息
func (s *UnimplementedApi) CreateCorp(ctx context.Context, req *CreateCorpReq) (*CreateCorpRsp, error) {
	return nil, errors.New("rpc CreateCorp of service Api is not implemented")
}

// ListCorp 企业列表
func (s *UnimplementedApi) ListCorp(ctx context.Context, req *ListCorpReq) (*ListCorpRsp, error) {
	return nil, errors.New("rpc ListCorp of service Api is not implemented")
}

// GetCorp 根据企业ID获取企业
func (s *UnimplementedApi) GetCorp(ctx context.Context, req *GetCorpReq) (*GetCorpRsp, error) {
	return nil, errors.New("rpc GetCorp of service Api is not implemented")
}

// ModifyInfosecBizType 修改企业安全审核策略
func (s *UnimplementedApi) ModifyInfosecBizType(ctx context.Context, req *ModifyInfosecBizTypeReq) (*ModifyInfosecBizTypeRsp, error) {
	return nil, errors.New("rpc ModifyInfosecBizType of service Api is not implemented")
}

// ModifyMaxTokenUsage 修改企业机器人token数
func (s *UnimplementedApi) ModifyMaxTokenUsage(ctx context.Context, req *ModifyMaxTokenUsageReq) (*ModifyMaxTokenUsageRsp, error) {
	return nil, errors.New("rpc ModifyMaxTokenUsage of service Api is not implemented")
}

// ModifyMaxCharSize 修改企业机器人字符数
func (s *UnimplementedApi) ModifyMaxCharSize(ctx context.Context, req *ModifyMaxCharSizeReq) (*ModifyMaxCharSizeRsp, error) {
	return nil, errors.New("rpc ModifyMaxCharSize of service Api is not implemented")
}

// AuditCorp 审核企业
func (s *UnimplementedApi) AuditCorp(ctx context.Context, req *AuditCorpReq) (*AuditCorpRsp, error) {
	return nil, errors.New("rpc AuditCorp of service Api is not implemented")
}

// ModifyCorpRobotQuota 修改企业机器人配额
func (s *UnimplementedApi) ModifyCorpRobotQuota(ctx context.Context, req *ModifyCorpRobotQuotaReq) (*ModifyCorpRobotQuotaRsp, error) {
	return nil, errors.New("rpc ModifyCorpRobotQuota of service Api is not implemented")
}

// CorpStaffList 获取企业员工列表
func (s *UnimplementedApi) CorpStaffList(ctx context.Context, req *CorpStaffListReq) (*CorpStaffListRsp, error) {
	return nil, errors.New("rpc CorpStaffList of service Api is not implemented")
}

// ListCorpStaffByIds 通过Ids获取员工信息列表
func (s *UnimplementedApi) ListCorpStaffByIds(ctx context.Context, req *ListCorpStaffByIdsReq) (*ListCorpStaffByIdsRsp, error) {
	return nil, errors.New("rpc ListCorpStaffByIds of service Api is not implemented")
}

// ReleaseDetailNotify 更新QA/Segment状态回调
func (s *UnimplementedApi) ReleaseDetailNotify(ctx context.Context, req *ReleaseDetailNotifyReq) (*ReleaseDetailNotifyRsp, error) {
	return nil, errors.New("rpc ReleaseDetailNotify of service Api is not implemented")
}

// ReleaseNotify 更新发布任务回调
func (s *UnimplementedApi) ReleaseNotify(ctx context.Context, req *ReleaseNotifyReq) (*ReleaseNotifyRsp, error) {
	return nil, errors.New("rpc ReleaseNotify of service Api is not implemented")
}

// GetPresignedURL 获取临时链接
func (s *UnimplementedApi) GetPresignedURL(ctx context.Context, req *GetPresignedURLReq) (*GetPresignedURLRsp, error) {
	return nil, errors.New("rpc GetPresignedURL of service Api is not implemented")
}

// GetRobotInfo Deprecated 获取机器人
func (s *UnimplementedApi) GetRobotInfo(ctx context.Context, req *GetRobotInfoReq) (*GetRobotInfoRsp, error) {
	return nil, errors.New("rpc GetRobotInfo of service Api is not implemented")
}

// DescribeRobotInfo 获取机器人
func (s *UnimplementedApi) DescribeRobotInfo(ctx context.Context, req *DescribeRobotInfoReq) (*DescribeRobotInfoRsp, error) {
	return nil, errors.New("rpc DescribeRobotInfo of service Api is not implemented")
}

// SearchPreview 对话评测
func (s *UnimplementedApi) SearchPreview(ctx context.Context, req *SearchPreviewReq) (*SearchPreviewRsp, error) {
	return nil, errors.New("rpc SearchPreview of service Api is not implemented")
}

// Search 向量特征检索
func (s *UnimplementedApi) Search(ctx context.Context, req *SearchReq) (*SearchRsp, error) {
	return nil, errors.New("rpc Search of service Api is not implemented")
}

// CustomSearchPreview 对话评测
func (s *UnimplementedApi) CustomSearchPreview(ctx context.Context, req *CustomSearchPreviewReq) (*CustomSearchPreviewRsp, error) {
	return nil, errors.New("rpc CustomSearchPreview of service Api is not implemented")
}

// CustomSearch 查找
func (s *UnimplementedApi) CustomSearch(ctx context.Context, req *CustomSearchReq) (*CustomSearchRsp, error) {
	return nil, errors.New("rpc CustomSearch of service Api is not implemented")
}

// MatchRefer 匹配来源
func (s *UnimplementedApi) MatchRefer(ctx context.Context, req *MatchReferReq) (*MatchReferRsp, error) {
	return nil, errors.New("rpc MatchRefer of service Api is not implemented")
}

// AuditResultCallback 审核回调
func (s *UnimplementedApi) AuditResultCallback(ctx context.Context, req *CheckResultReq) (*CheckResultRsp, error) {
	return nil, errors.New("rpc AuditResultCallback of service Api is not implemented")
}

// GetRobotList 获取机器人列表
func (s *UnimplementedApi) GetRobotList(ctx context.Context, req *GetRobotListReq) (*GetRobotListRsp, error) {
	return nil, errors.New("rpc GetRobotList of service Api is not implemented")
}

// GetAppList 获取应用列表
func (s *UnimplementedApi) GetAppList(ctx context.Context, req *GetAppListReq) (*GetAppListRsp, error) {
	return nil, errors.New("rpc GetAppList of service Api is not implemented")
}

// GetRobotByAppKey 通过AppKey获取机器人
func (s *UnimplementedApi) GetRobotByAppKey(ctx context.Context, req *GetRobotByAppKeyReq) (*GetRobotByAppKeyRsp, error) {
	return nil, errors.New("rpc GetRobotByAppKey of service Api is not implemented")
}

// GetAppByAppKey 通过AppKey获取应用
func (s *UnimplementedApi) GetAppByAppKey(ctx context.Context, req *GetAppByAppKeyReq) (*GetAppByAppKeyRsp, error) {
	return nil, errors.New("rpc GetAppByAppKey of service Api is not implemented")
}

// GetAppInfo 获取应用信息
func (s *UnimplementedApi) GetAppInfo(ctx context.Context, req *GetAppInfoReq) (*GetAppInfoRsp, error) {
	return nil, errors.New("rpc GetAppInfo of service Api is not implemented")
}

// EditRobot 编辑机器人信息
func (s *UnimplementedApi) EditRobot(ctx context.Context, req *EditRobotReq) (*EditRobotRsp, error) {
	return nil, errors.New("rpc EditRobot of service Api is not implemented")
}

// GetCorpList 获取企业信息
func (s *UnimplementedApi) GetCorpList(ctx context.Context, req *GetCorpListReq) (*GetCorpListRsp, error) {
	return nil, errors.New("rpc GetCorpList of service Api is not implemented")
}

// GetDocs 获取文档内容
func (s *UnimplementedApi) GetDocs(ctx context.Context, req *GetDocsReq) (*GetDocsRsp, error) {
	return nil, errors.New("rpc GetDocs of service Api is not implemented")
}

// SearchPreviewRejectedQuestion 拒答问题测评库查询
func (s *UnimplementedApi) SearchPreviewRejectedQuestion(ctx context.Context, req *SearchPreviewRejectedQuestionReq) (*SearchPreviewRejectedQuestionRsp, error) {
	return nil, errors.New("rpc SearchPreviewRejectedQuestion of service Api is not implemented")
}

// SearchReleaseRejectedQuestion 拒答问题线上库查询
func (s *UnimplementedApi) SearchReleaseRejectedQuestion(ctx context.Context, req *SearchReleaseRejectedQuestionReq) (*SearchReleaseRejectedQuestionRsp, error) {
	return nil, errors.New("rpc SearchReleaseRejectedQuestion of service Api is not implemented")
}

// ListRejectedQuestion 获取拒答问题列表
func (s *UnimplementedApi) ListRejectedQuestion(ctx context.Context, req *ListRejectedQuestionReq) (*ListRejectedQuestionRsp, error) {
	return nil, errors.New("rpc ListRejectedQuestion of service Api is not implemented")
}

// AddUnsatisfiedReply 添加不满意回复
func (s *UnimplementedApi) AddUnsatisfiedReply(ctx context.Context, req *AddUnsatisfiedReplyReq) (*AddUnsatisfiedReplyRsp, error) {
	return nil, errors.New("rpc AddUnsatisfiedReply of service Api is not implemented")
}

// ListQA 获取QA列表
func (s *UnimplementedApi) ListQA(ctx context.Context, req *ListQAReq) (*ListQARsp, error) {
	return nil, errors.New("rpc ListQA of service Api is not implemented")
}

// GetAdminTaskList 获取admin任务列表
func (s *UnimplementedApi) GetAdminTaskList(ctx context.Context, req *GetAdminTaskListReq) (*GetAdminTaskListRsp, error) {
	return nil, errors.New("rpc GetAdminTaskList of service Api is not implemented")
}

// GetAdminTaskHistoryList 获取admin历史任务列表
func (s *UnimplementedApi) GetAdminTaskHistoryList(ctx context.Context, req *GetAdminTaskHistoryListReq) (*GetAdminTaskHistoryListRsp, error) {
	return nil, errors.New("rpc GetAdminTaskHistoryList of service Api is not implemented")
}

// GetVectorDocTaskList 获取获取vector_doc任务列表
func (s *UnimplementedApi) GetVectorDocTaskList(ctx context.Context, req *GetVectorDocTaskListReq) (*GetVectorDocTaskListRsp, error) {
	return nil, errors.New("rpc GetVectorDocTaskList of service Api is not implemented")
}

// GetVectorDocTaskHistoryList 获取vector_doc任务历史列表
func (s *UnimplementedApi) GetVectorDocTaskHistoryList(ctx context.Context, req *GetVectorDocTaskHistoryListReq) (*GetVectorDocTaskHistoryListRsp, error) {
	return nil, errors.New("rpc GetVectorDocTaskHistoryList of service Api is not implemented")
}

// GetReleaseList 发布记录列表
func (s *UnimplementedApi) GetReleaseList(ctx context.Context, req *GetReleaseListReq) (*GetReleaseListRsp, error) {
	return nil, errors.New("rpc GetReleaseList of service Api is not implemented")
}

// AddCorpStaff 添加企业成员
func (s *UnimplementedApi) AddCorpStaff(ctx context.Context, req *AddCorpStaffReq) (*AddCorpStaffRsp, error) {
	return nil, errors.New("rpc AddCorpStaff of service Api is not implemented")
}

// EditCorpStaffPassword 修改企业员工密码
func (s *UnimplementedApi) EditCorpStaffPassword(ctx context.Context, req *EditCorpStaffPasswordReq) (*EditCorpStaffPasswordRsp, error) {
	return nil, errors.New("rpc EditCorpStaffPassword of service Api is not implemented")
}

// LeaveCorp 企业员工退出企业
func (s *UnimplementedApi) LeaveCorp(ctx context.Context, req *LeaveCorpReq) (*LeaveCorpRsp, error) {
	return nil, errors.New("rpc LeaveCorp of service Api is not implemented")
}

// UpdateAuditStatus 更新审核单状态
func (s *UnimplementedApi) UpdateAuditStatus(ctx context.Context, req *UpdateAuditStatusReq) (*UpdateAuditStatusRsp, error) {
	return nil, errors.New("rpc UpdateAuditStatus of service Api is not implemented")
}

// GetRobotDefaultConfig 获取机器人默认配置信息
func (s *UnimplementedApi) GetRobotDefaultConfig(ctx context.Context, req *GetRobotDefaultConfigReq) (*GetRobotDefaultConfigRsp, error) {
	return nil, errors.New("rpc GetRobotDefaultConfig of service Api is not implemented")
}

// ClearRobotCustomConfig 清除机器人自定义配置
func (s *UnimplementedApi) ClearRobotCustomConfig(ctx context.Context, req *ClearRobotCustomConfigReq) (*ClearRobotCustomConfigRsp, error) {
	return nil, errors.New("rpc ClearRobotCustomConfig of service Api is not implemented")
}

// ListGlobalKnowledge 全局干预知识列表
func (s *UnimplementedApi) ListGlobalKnowledge(ctx context.Context, req *ListGlobalKnowledgeReq) (*ListGlobalKnowledgeRsp, error) {
	return nil, errors.New("rpc ListGlobalKnowledge of service Api is not implemented")
}

// AddGlobalKnowledge 添加全局干预知识
func (s *UnimplementedApi) AddGlobalKnowledge(ctx context.Context, req *AddGlobalKnowledgeReq) (*AddGlobalKnowledgeRsp, error) {
	return nil, errors.New("rpc AddGlobalKnowledge of service Api is not implemented")
}

// DelGlobalKnowledge 删除全局干预知识
func (s *UnimplementedApi) DelGlobalKnowledge(ctx context.Context, req *DelGlobalKnowledgeReq) (*DelGlobalKnowledgeRsp, error) {
	return nil, errors.New("rpc DelGlobalKnowledge of service Api is not implemented")
}

// UpdGlobalKnowledge 更新全局干预知识
func (s *UnimplementedApi) UpdGlobalKnowledge(ctx context.Context, req *UpdGlobalKnowledgeReq) (*UpdGlobalKnowledgeRsp, error) {
	return nil, errors.New("rpc UpdGlobalKnowledge of service Api is not implemented")
}

// GlobalKnowledge 全局干预知识
func (s *UnimplementedApi) GlobalKnowledge(ctx context.Context, req *GlobalKnowledgeReq) (*GlobalKnowledgeRsp, error) {
	return nil, errors.New("rpc GlobalKnowledge of service Api is not implemented")
}

// ForceSyncGlobalKnowledge 强制同步全局干预知识
func (s *UnimplementedApi) ForceSyncGlobalKnowledge(ctx context.Context, req *ForceSyncGlobalKnowledgeReq) (*ForceSyncGlobalKnowledgeRsp, error) {
	return nil, errors.New("rpc ForceSyncGlobalKnowledge of service Api is not implemented")
}

// CustomSimilarity 计算相似度
func (s *UnimplementedApi) CustomSimilarity(ctx context.Context, req *CustomSimilarityReq) (*CustomSimilarityRsp, error) {
	return nil, errors.New("rpc CustomSimilarity of service Api is not implemented")
}

// EnableCorp 启用企业请求
func (s *UnimplementedApi) EnableCorp(ctx context.Context, req *EnableCorpReq) (*EnableCorpRsp, error) {
	return nil, errors.New("rpc EnableCorp of service Api is not implemented")
}

// DisableCorp 禁用企业请求
func (s *UnimplementedApi) DisableCorp(ctx context.Context, req *DisableCorpReq) (*DisableCorpRsp, error) {
	return nil, errors.New("rpc DisableCorp of service Api is not implemented")
}

// GetCustomResource 底座获取资源列表
func (s *UnimplementedApi) GetCustomResource(ctx context.Context, req *GetCustomResourceReq) (*GetCustomResourceRsp, error) {
	return nil, errors.New("rpc GetCustomResource of service Api is not implemented")
}

// ActivateProduct 底座通知产品开通
func (s *UnimplementedApi) ActivateProduct(ctx context.Context, req *ActivateProductReq) (*ActivateProductRsp, error) {
	return nil, errors.New("rpc ActivateProduct of service Api is not implemented")
}

// CreateNotice 创建通知
func (s *UnimplementedApi) CreateNotice(ctx context.Context, req *CreateNoticeReq) (*CreateNoticeRsp, error) {
	return nil, errors.New("rpc CreateNotice of service Api is not implemented")
}

// CreateNoticeByUin 主账号维度创建通知
func (s *UnimplementedApi) CreateNoticeByUin(ctx context.Context, req *CreateNoticeByUinReq) (*CreateNoticeByUinRsp, error) {
	return nil, errors.New("rpc CreateNoticeByUin of service Api is not implemented")
}

// DescribeIntegrator 查询集成商信息
func (s *UnimplementedApi) DescribeIntegrator(ctx context.Context, req *DescribeIntegratorReq) (*DescribeIntegratorRsp, error) {
	return nil, errors.New("rpc DescribeIntegrator of service Api is not implemented")
}

// DescribeRobotBizIDByAppKey 通过appKey获取机器人业务id
func (s *UnimplementedApi) DescribeRobotBizIDByAppKey(ctx context.Context, req *DescribeRobotBizIDByAppKeyReq) (*DescribeRobotBizIDByAppKeyRsp, error) {
	return nil, errors.New("rpc DescribeRobotBizIDByAppKey of service Api is not implemented")
}

// DescribeLatestReleaseStatus 获取机器人最新发布状态
func (s *UnimplementedApi) DescribeLatestReleaseStatus(ctx context.Context, req *DescribeLatestReleaseStatusReq) (*DescribeLatestReleaseStatusRsp, error) {
	return nil, errors.New("rpc DescribeLatestReleaseStatus of service Api is not implemented")
}

// TrialProduct 试用开通
func (s *UnimplementedApi) TrialProduct(ctx context.Context, req *TrialProductReq) (*TrialProductRsp, error) {
	return nil, errors.New("rpc TrialProduct of service Api is not implemented")
}

// EditApp 编辑应用
func (s *UnimplementedApi) EditApp(ctx context.Context, req *EditAppReq) (*EditAppRsp, error) {
	return nil, errors.New("rpc EditApp of service Api is not implemented")
}

// GetAppDefaultConfig 获取应用默认配置
func (s *UnimplementedApi) GetAppDefaultConfig(ctx context.Context, req *GetAppDefaultConfigReq) (*GetAppDefaultConfigRsp, error) {
	return nil, errors.New("rpc GetAppDefaultConfig of service Api is not implemented")
}

// ClearAppCustomConfig 清除应用自定义配置
func (s *UnimplementedApi) ClearAppCustomConfig(ctx context.Context, req *ClearAppCustomConfigReq) (*ClearAppCustomConfigRsp, error) {
	return nil, errors.New("rpc ClearAppCustomConfig of service Api is not implemented")
}

// GetIntent 获取意图
func (s *UnimplementedApi) GetIntent(ctx context.Context, req *GetIntentReq) (*GetIntentRsp, error) {
	return nil, errors.New("rpc GetIntent of service Api is not implemented")
}

// ListIntent 获取意图列表
func (s *UnimplementedApi) ListIntent(ctx context.Context, req *ListIntentReq) (*ListIntentRsp, error) {
	return nil, errors.New("rpc ListIntent of service Api is not implemented")
}

// CreateIntent 创建意图
func (s *UnimplementedApi) CreateIntent(ctx context.Context, req *CreateIntentReq) (*CreateIntentRsp, error) {
	return nil, errors.New("rpc CreateIntent of service Api is not implemented")
}

// UpdateIntent 更新意图
func (s *UnimplementedApi) UpdateIntent(ctx context.Context, req *UpdateIntentReq) (*UpdateIntentRsp, error) {
	return nil, errors.New("rpc UpdateIntent of service Api is not implemented")
}

// DeleteIntent 删除意图
func (s *UnimplementedApi) DeleteIntent(ctx context.Context, req *DeleteIntentReq) (*DeleteIntentRsp, error) {
	return nil, errors.New("rpc DeleteIntent of service Api is not implemented")
}

// ListIntentByPolicyID 获取策略绑定的意图列表
func (s *UnimplementedApi) ListIntentByPolicyID(ctx context.Context, req *ListIntentByPolicyIDReq) (*ListIntentByPolicyIDRsp, error) {
	return nil, errors.New("rpc ListIntentByPolicyID of service Api is not implemented")
}

// ListIntentPolicy 获取策略列表
func (s *UnimplementedApi) ListIntentPolicy(ctx context.Context, req *ListIntentPolicyReq) (*ListIntentPolicyRsp, error) {
	return nil, errors.New("rpc ListIntentPolicy of service Api is not implemented")
}

// CreateIntentPolicy 创建策略
func (s *UnimplementedApi) CreateIntentPolicy(ctx context.Context, req *CreateIntentPolicyReq) (*CreateIntentPolicyRsp, error) {
	return nil, errors.New("rpc CreateIntentPolicy of service Api is not implemented")
}

// UpdateIntentPolicy 更新策略
func (s *UnimplementedApi) UpdateIntentPolicy(ctx context.Context, req *UpdateIntentPolicyReq) (*UpdateIntentPolicyRsp, error) {
	return nil, errors.New("rpc UpdateIntentPolicy of service Api is not implemented")
}

// DeleteIntentPolicy 删除策略
func (s *UnimplementedApi) DeleteIntentPolicy(ctx context.Context, req *DeleteIntentPolicyReq) (*DeleteIntentPolicyRsp, error) {
	return nil, errors.New("rpc DeleteIntentPolicy of service Api is not implemented")
}

// ListUnusedIntentKeyMap 获取未使用的意图列表
func (s *UnimplementedApi) ListUnusedIntentKeyMap(ctx context.Context, req *ListUnusedIntentKeyMapReq) (*ListUnusedIntentKeyMapRsp, error) {
	return nil, errors.New("rpc ListUnusedIntentKeyMap of service Api is not implemented")
}

// ListIntentPolicyKeyMap 获取策略列表映射关系
func (s *UnimplementedApi) ListIntentPolicyKeyMap(ctx context.Context, req *ListIntentPolicyKeyMapReq) (*ListIntentPolicyKeyMapRsp, error) {
	return nil, errors.New("rpc ListIntentPolicyKeyMap of service Api is not implemented")
}

// CreateCorpCustomModel 新增企业自定义模型
func (s *UnimplementedApi) CreateCorpCustomModel(ctx context.Context, req *CreateCorpCustomModelReq) (*CreateCorpCustomModelRsp, error) {
	return nil, errors.New("rpc CreateCorpCustomModel of service Api is not implemented")
}

// GetRobotConfigByVersionID 获取版本配置
func (s *UnimplementedApi) GetRobotConfigByVersionID(ctx context.Context, req *GetRobotConfigByVersionIDReq) (*GetRobotConfigByVersionIDRsp, error) {
	return nil, errors.New("rpc GetRobotConfigByVersionID of service Api is not implemented")
}

// ListAgentFeedbackInner ListAgentFeedbackInner 查询 agent反馈信息列表
func (s *UnimplementedApi) ListAgentFeedbackInner(ctx context.Context, req *ListAgentFeedbackInnerReq) (*ListAgentFeedbackInnerRsp, error) {
	return nil, errors.New("rpc ListAgentFeedbackInner of service Api is not implemented")
}

// UpdateAgentFeedbackStatus UpdateFeedbackStatus 修改 agent反馈信息状态
func (s *UnimplementedApi) UpdateAgentFeedbackStatus(ctx context.Context, req *UpdateAgentFeedbackStatusReq) (*UpdateAgentFeedbackStatusRsp, error) {
	return nil, errors.New("rpc UpdateAgentFeedbackStatus of service Api is not implemented")
}

// UpdateAgentFeedbackTapd UpdateAgentFeedbackTapd 修改 agent反馈信息关联的tapd
func (s *UnimplementedApi) UpdateAgentFeedbackTapd(ctx context.Context, req *UpdateAgentFeedbackTapdReq) (*UpdateAgentFeedbackTapdRsp, error) {
	return nil, errors.New("rpc UpdateAgentFeedbackTapd of service Api is not implemented")
}

// CountAgentFeedback CountAgentFeedback 获取 agent新增反馈个数
func (s *UnimplementedApi) CountAgentFeedback(ctx context.Context, req *CountAgentFeedbackReq) (*CountAgentFeedbackRsp, error) {
	return nil, errors.New("rpc CountAgentFeedback of service Api is not implemented")
}

// GetAgentFeedback GetAgentFeedback 获取 agent反馈信息
func (s *UnimplementedApi) GetAgentFeedback(ctx context.Context, req *GetAgentFeedbackReq) (*GetAgentFeedbackRsp, error) {
	return nil, errors.New("rpc GetAgentFeedback of service Api is not implemented")
}

// UpdateAgentFeedbackAndonInfo UpdateAgentFeedbackAndonInfo 修改 agent反馈信息状态
func (s *UnimplementedApi) UpdateAgentFeedbackAndonInfo(ctx context.Context, req *UpdateAgentFeedbackAndonInfoReq) (*UpdateAgentFeedbackAndonInfoRsp, error) {
	return nil, errors.New("rpc UpdateAgentFeedbackAndonInfo of service Api is not implemented")
}
func (s *UnimplementedApi) DeleteFeedbackByFlowIds(ctx context.Context, req *DeleteFeedbackByFlowIdsReq) (*DeleteFeedbackByFlowIdsRsp, error) {
	return nil, errors.New("rpc DeleteFeedbackByFlowIds of service Api is not implemented")
}

// ListFeedbackInner ListFeedbackInner 查询反馈信息列表
func (s *UnimplementedApi) ListFeedbackInner(ctx context.Context, req *ListFeedbackInnerReq) (*ListFeedbackInnerRsp, error) {
	return nil, errors.New("rpc ListFeedbackInner of service Api is not implemented")
}

// ListFeedbackByBizIDInner ListFeedbackByBizIDInner 查询反馈信息列表
func (s *UnimplementedApi) ListFeedbackByBizIDInner(ctx context.Context, req *ListFeedbackByBizIDInnerReq) (*ListFeedbackByBizIDInnerRsp, error) {
	return nil, errors.New("rpc ListFeedbackByBizIDInner of service Api is not implemented")
}

// UpdateFeedbackClassification UpdateFeedbackClassification 修改一级分类，二级分类
func (s *UnimplementedApi) UpdateFeedbackClassification(ctx context.Context, req *UpdateFeedbackClassificationReq) (*UpdateFeedbackClassificationRsp, error) {
	return nil, errors.New("rpc UpdateFeedbackClassification of service Api is not implemented")
}

// UpdateFeedbackAndonInfo UpdateFeedbackAndonIfoReq 修改安灯状态信息
func (s *UnimplementedApi) UpdateFeedbackAndonInfo(ctx context.Context, req *UpdateFeedbackAndonInfoReq) (*UpdateFeedbackAndonInfoRsp, error) {
	return nil, errors.New("rpc UpdateFeedbackAndonInfo of service Api is not implemented")
}

// UpdateFeedbackStatus UpdateFeedbackStatus 修改反馈信息状态
func (s *UnimplementedApi) UpdateFeedbackStatus(ctx context.Context, req *UpdateFeedbackStatusReq) (*UpdateFeedbackStatusRsp, error) {
	return nil, errors.New("rpc UpdateFeedbackStatus of service Api is not implemented")
}

// UpdateFeedbackTapd UpdateFeedbackTapd 修改反馈信息关联的tapd
func (s *UnimplementedApi) UpdateFeedbackTapd(ctx context.Context, req *UpdateFeedbackTapdReq) (*UpdateFeedbackTapdRsp, error) {
	return nil, errors.New("rpc UpdateFeedbackTapd of service Api is not implemented")
}

// CountFeedback CountFeedback 获取新增反馈个数
func (s *UnimplementedApi) CountFeedback(ctx context.Context, req *CountFeedbackReq) (*CountFeedbackRsp, error) {
	return nil, errors.New("rpc CountFeedback of service Api is not implemented")
}

// GetFeedback CountFeedback 获取新增反馈个数
func (s *UnimplementedApi) GetFeedback(ctx context.Context, req *GetFeedbackReq) (*GetFeedbackRsp, error) {
	return nil, errors.New("rpc GetFeedback of service Api is not implemented")
}

// DescribeAccountBalance 获取企业账户余额信息
func (s *UnimplementedApi) DescribeAccountBalance(ctx context.Context, req *DescribeAccountBalanceReq) (*DescribeAccountBalanceRsp, error) {
	return nil, errors.New("rpc DescribeAccountBalance of service Api is not implemented")
}

// GetAppType GetAppType 获取应用类型
func (s *UnimplementedApi) GetAppType(ctx context.Context, req *GetAppTypeReq) (*GetAppTypeRsp, error) {
	return nil, errors.New("rpc GetAppType of service Api is not implemented")
}

// GetDescribeLicense 获取应用License
func (s *UnimplementedApi) GetDescribeLicense(ctx context.Context, req *GetDescribeLicenseReq) (*GetDescribeLicenseRsp, error) {
	return nil, errors.New("rpc GetDescribeLicense of service Api is not implemented")
}

// GetExperienceApps GetExperienceApps 体验中心-获取体验应用列表
func (s *UnimplementedApi) GetExperienceApps(ctx context.Context, req *GetExperienceAppsReq) (*GetExperienceAppsRsp, error) {
	return nil, errors.New("rpc GetExperienceApps of service Api is not implemented")
}

// ModifyExperienceApp ModifyExperienceApp 修改体验应用
func (s *UnimplementedApi) ModifyExperienceApp(ctx context.Context, req *ModifyExperienceAppReq) (*ModifyExperienceAppRsp, error) {
	return nil, errors.New("rpc ModifyExperienceApp of service Api is not implemented")
}

// ListSelectDoc ListSelectDoc 选择文档列表
func (s *UnimplementedApi) ListSelectDoc(ctx context.Context, req *ListSelectDocReq) (*ListSelectDocRsp, error) {
	return nil, errors.New("rpc ListSelectDoc of service Api is not implemented")
}

// CreateCorpAndAssignPermission CreateCorpAndAssignPermission
func (s *UnimplementedApi) CreateCorpAndAssignPermission(ctx context.Context, req *CreateCorpAndAssignPermissionReq) (*CreateCorpAndAssignPermissionRsp, error) {
	return nil, errors.New("rpc CreateCorpAndAssignPermission of service Api is not implemented")
}

// CheckCorpAndPermission CheckCorpAndPermission
func (s *UnimplementedApi) CheckCorpAndPermission(ctx context.Context, req *CheckCorpAndPermissionReq) (*CheckCorpAndPermissionRsp, error) {
	return nil, errors.New("rpc CheckCorpAndPermission of service Api is not implemented")
}

// DescribeCropByUin DescribeCropByUin
func (s *UnimplementedApi) DescribeCropByUin(ctx context.Context, req *DescribeCropByUinReq) (*DescribeCropByUinRsp, error) {
	return nil, errors.New("rpc DescribeCropByUin of service Api is not implemented")
}

// GetListModel GetListModel 获取企业模型列表
func (s *UnimplementedApi) GetListModel(ctx context.Context, req *GetListModelReq) (*GetListModelRsp, error) {
	return nil, errors.New("rpc GetListModel of service Api is not implemented")
}

// ModifyAppInfosecBizType ModifyAppInfosecBizType 修改应用安全审核策略
func (s *UnimplementedApi) ModifyAppInfosecBizType(ctx context.Context, req *ModifyAppInfosecBizTypeReq) (*ModifyAppInfosecBizTypeRsp, error) {
	return nil, errors.New("rpc ModifyAppInfosecBizType of service Api is not implemented")
}

// GetTotalConcurrency GetTotalConcurrency 获取企业总并发数
func (s *UnimplementedApi) GetTotalConcurrency(ctx context.Context, req *GetTotalConcurrencyReq) (*GetTotalConcurrencyRsp, error) {
	return nil, errors.New("rpc GetTotalConcurrency of service Api is not implemented")
}

// MultiLock MultiLock 加锁
func (s *UnimplementedApi) MultiLock(ctx context.Context, req *MultiLockReq) (*MultiLockRsp, error) {
	return nil, errors.New("rpc MultiLock of service Api is not implemented")
}

// MultiUnlock MultiUnlock 解锁
func (s *UnimplementedApi) MultiUnlock(ctx context.Context, req *MultiUnlockReq) (*MultiUnlockRsp, error) {
	return nil, errors.New("rpc MultiUnlock of service Api is not implemented")
}

// CheckVarIsUsed 检查自定义参数是否被使用
func (s *UnimplementedApi) CheckVarIsUsed(ctx context.Context, req *CheckVarIsUsedReq) (*CheckVarIsUsedRsp, error) {
	return nil, errors.New("rpc CheckVarIsUsed of service Api is not implemented")
}

// ModifyAppVar 修改应用检索范围自定义参数
func (s *UnimplementedApi) ModifyAppVar(ctx context.Context, req *ModifyAppVarReq) (*ModifyAppVarRsp, error) {
	return nil, errors.New("rpc ModifyAppVar of service Api is not implemented")
}

// ClearAppKnowledgeCallback 清理应用知识资源回调结果
func (s *UnimplementedApi) ClearAppKnowledgeCallback(ctx context.Context, req *ClearAppKnowledgeCallbackReq) (*ClearAppKnowledgeCallbackRsp, error) {
	return nil, errors.New("rpc ClearAppKnowledgeCallback of service Api is not implemented")
}

// ClearAppFlowCallback 清理应用流程资源回调
func (s *UnimplementedApi) ClearAppFlowCallback(ctx context.Context, req *ClearAppFlowCallbackReq) (*ClearAppFlowCallbackRsp, error) {
	return nil, errors.New("rpc ClearAppFlowCallback of service Api is not implemented")
}

// ClearAppVectorCallback 清理应用向量库资源回调
func (s *UnimplementedApi) ClearAppVectorCallback(ctx context.Context, req *ClearAppVectorCallbackReq) (*ClearAppVectorCallbackRsp, error) {
	return nil, errors.New("rpc ClearAppVectorCallback of service Api is not implemented")
}

// ClearAppResourceCallback 清理应用资源回调
func (s *UnimplementedApi) ClearAppResourceCallback(ctx context.Context, req *ClearAppResourceCallbackReq) (*ClearAppResourceCallbackRsp, error) {
	return nil, errors.New("rpc ClearAppResourceCallback of service Api is not implemented")
}

// GetAppByPluginId 查询插件关联的应用信息
func (s *UnimplementedApi) GetAppByPluginId(ctx context.Context, req *GetAppByPluginIdReq) (*GetAppByPluginIdRsp, error) {
	return nil, errors.New("rpc GetAppByPluginId of service Api is not implemented")
}

// GetCorpStaffName 查询用户昵称
func (s *UnimplementedApi) GetCorpStaffName(ctx context.Context, req *GetCorpStaffNameReq) (*GetCorpStaffNameRsp, error) {
	return nil, errors.New("rpc GetCorpStaffName of service Api is not implemented")
}

// GetUserInfo 查询用户信息
func (s *UnimplementedApi) GetUserInfo(ctx context.Context, req *GetUserInfoReq) (*GetUserInfoRsp, error) {
	return nil, errors.New("rpc GetUserInfo of service Api is not implemented")
}

// GetModelFinanceInfo 获取模型的计费控制信息
func (s *UnimplementedApi) GetModelFinanceInfo(ctx context.Context, req *GetModelFinanceInfoReq) (*GetModelFinanceInfoRsp, error) {
	return nil, errors.New("rpc GetModelFinanceInfo of service Api is not implemented")
}

// GetModelInfo 获取企业模型信息
func (s *UnimplementedApi) GetModelInfo(ctx context.Context, req *GetModelInfoReq) (*GetModelInfoRsp, error) {
	return nil, errors.New("rpc GetModelInfo of service Api is not implemented")
}

// GetValidExperienceApps 获取有效的体验应用信息
func (s *UnimplementedApi) GetValidExperienceApps(ctx context.Context, req *GetValidExperienceAppsReq) (*GetValidExperienceAppsRsp, error) {
	return nil, errors.New("rpc GetValidExperienceApps of service Api is not implemented")
}

// GetModelList 获取模型列表信息
func (s *UnimplementedApi) GetModelList(ctx context.Context, req *GetModelListReq) (*GetModelListRsp, error) {
	return nil, errors.New("rpc GetModelList of service Api is not implemented")
}

// GetAppChatInputNum 获取应用对话query输入长度限制
func (s *UnimplementedApi) GetAppChatInputNum(ctx context.Context, req *GetAppChatInputNumReq) (*GetAppChatInputNumRsp, error) {
	return nil, errors.New("rpc GetAppChatInputNum of service Api is not implemented")
}

// CreatePromptVersion 创建Prompt版本信息
func (s *UnimplementedApi) CreatePromptVersion(ctx context.Context, req *CreatePromptVersionReq) (*CreatePromptVersionRsp, error) {
	return nil, errors.New("rpc CreatePromptVersion of service Api is not implemented")
}

// EditPromptVersion 编辑prompt版本信息
func (s *UnimplementedApi) EditPromptVersion(ctx context.Context, req *EditPromptVersionReq) (*EditPromptVersionRsp, error) {
	return nil, errors.New("rpc EditPromptVersion of service Api is not implemented")
}

// GetPromptVersionList 查询prompt版本信息
func (s *UnimplementedApi) GetPromptVersionList(ctx context.Context, req *GetPromptVersionListReq) (*GetPromptVersionListRsp, error) {
	return nil, errors.New("rpc GetPromptVersionList of service Api is not implemented")
}

// UpgradePromptVersion 升级Prompt版本
func (s *UnimplementedApi) UpgradePromptVersion(ctx context.Context, req *UpgradePromptVersionReq) (*UpgradePromptVersionRsp, error) {
	return nil, errors.New("rpc UpgradePromptVersion of service Api is not implemented")
}

// CreateCorpConcurrencyWL 创建账户并发白名单
func (s *UnimplementedApi) CreateCorpConcurrencyWL(ctx context.Context, req *CreateCorpConcurrencyWLReq) (*CreateCorpConcurrencyWLRsp, error) {
	return nil, errors.New("rpc CreateCorpConcurrencyWL of service Api is not implemented")
}

// EditCorpConcurrencyWL 修改账户并发白名单
func (s *UnimplementedApi) EditCorpConcurrencyWL(ctx context.Context, req *EditCorpConcurrencyWLReq) (*EditCorpConcurrencyWLRsp, error) {
	return nil, errors.New("rpc EditCorpConcurrencyWL of service Api is not implemented")
}

// DeleteCorpConcurrencyWL 删除账户并发白名单
func (s *UnimplementedApi) DeleteCorpConcurrencyWL(ctx context.Context, req *DeleteCorpConcurrencyWLReq) (*DeleteCorpConcurrencyWLRsp, error) {
	return nil, errors.New("rpc DeleteCorpConcurrencyWL of service Api is not implemented")
}

// GetCorpConcurrencyWLList 查询账户并发白名单
func (s *UnimplementedApi) GetCorpConcurrencyWLList(ctx context.Context, req *GetCorpConcurrencyWLListReq) (*GetCorpConcurrencyWLListRsp, error) {
	return nil, errors.New("rpc GetCorpConcurrencyWLList of service Api is not implemented")
}

// StartEmbeddingUpgradeApp 应用升级embedding开始
func (s *UnimplementedApi) StartEmbeddingUpgradeApp(ctx context.Context, req *StartEmbeddingUpgradeAppReq) (*StartEmbeddingUpgradeAppRsp, error) {
	return nil, errors.New("rpc StartEmbeddingUpgradeApp of service Api is not implemented")
}

// FinishEmbeddingUpgradeApp 应用升级embedding结束
func (s *UnimplementedApi) FinishEmbeddingUpgradeApp(ctx context.Context, req *FinishEmbeddingUpgradeAppReq) (*FinishEmbeddingUpgradeAppRsp, error) {
	return nil, errors.New("rpc FinishEmbeddingUpgradeApp of service Api is not implemented")
}

// GetAppsByBizIDs 通过应用业务ID获取应用信息，基于缓存读取
func (s *UnimplementedApi) GetAppsByBizIDs(ctx context.Context, req *GetAppsByBizIDsReq) (*GetAppsByBizIDsRsp, error) {
	return nil, errors.New("rpc GetAppsByBizIDs of service Api is not implemented")
}

// GetCorpStaffsByBizIDs 通过员工业务ID获取员工信息，基于缓存读取
func (s *UnimplementedApi) GetCorpStaffsByBizIDs(ctx context.Context, req *GetCorpStaffsByBizIDsReq) (*GetCorpStaffsByBizIDsRsp, error) {
	return nil, errors.New("rpc GetCorpStaffsByBizIDs of service Api is not implemented")
}

// CopyAppConfig 复制应用配置
func (s *UnimplementedApi) CopyAppConfig(ctx context.Context, req *CopyAppConfigReq) (*CopyAppConfigRsp, error) {
	return nil, errors.New("rpc CopyAppConfig of service Api is not implemented")
}

// EditCorpCustomModelWL 更新账户自定义模型白名单
func (s *UnimplementedApi) EditCorpCustomModelWL(ctx context.Context, req *EditCorpCustomModelWLReq) (*EditCorpCustomModelWLRsp, error) {
	return nil, errors.New("rpc EditCorpCustomModelWL of service Api is not implemented")
}

// DeleteCorpCustomModelWL 删除账户自定义模型白名单
func (s *UnimplementedApi) DeleteCorpCustomModelWL(ctx context.Context, req *DeleteCorpCustomModelWLReq) (*DeleteCorpCustomModelWLRsp, error) {
	return nil, errors.New("rpc DeleteCorpCustomModelWL of service Api is not implemented")
}

// GetCorpCustomModelWLList 获取账户自定义模型白名单列表
func (s *UnimplementedApi) GetCorpCustomModelWLList(ctx context.Context, req *GetCorpCustomModelWLListReq) (*GetCorpCustomModelWLListRsp, error) {
	return nil, errors.New("rpc GetCorpCustomModelWLList of service Api is not implemented")
}

// GetCorpModelQpmTpmLimit 获取账户模型默认的QPM、TPM上限值
func (s *UnimplementedApi) GetCorpModelQpmTpmLimit(ctx context.Context, req *GetCorpModelQpmTpmLimitReq) (*GetCorpModelQpmTpmLimitRsp, error) {
	return nil, errors.New("rpc GetCorpModelQpmTpmLimit of service Api is not implemented")
}

// GetDefaultQpmTpmLimit 获取账户模型的默认QPM、TPM值
func (s *UnimplementedApi) GetDefaultQpmTpmLimit(ctx context.Context, req *GetDefaultQpmTpmLimitReq) (*GetDefaultQpmTpmLimitRsp, error) {
	return nil, errors.New("rpc GetDefaultQpmTpmLimit of service Api is not implemented")
}

// EditCorpQpmTpmLimit 编辑账户模型QPM、TPM配置
func (s *UnimplementedApi) EditCorpQpmTpmLimit(ctx context.Context, req *EditCorpQpmTpmLimitReq) (*EditCorpQpmTpmLimitRsp, error) {
	return nil, errors.New("rpc EditCorpQpmTpmLimit of service Api is not implemented")
}

// DeleteCorpQpmTpmLimit 删除账户模型QPM、TPM配置
func (s *UnimplementedApi) DeleteCorpQpmTpmLimit(ctx context.Context, req *DeleteCorpQpmTpmLimitReq) (*DeleteCorpQpmTpmLimitRsp, error) {
	return nil, errors.New("rpc DeleteCorpQpmTpmLimit of service Api is not implemented")
}

// GetCorpQpmTpmLimitList 获取账户模型QPM、TPM配置
func (s *UnimplementedApi) GetCorpQpmTpmLimitList(ctx context.Context, req *GetCorpQpmTpmLimitListReq) (*GetCorpQpmTpmLimitListRsp, error) {
	return nil, errors.New("rpc GetCorpQpmTpmLimitList of service Api is not implemented")
}

// CreateShareKnowledgeBaseApp 创建共享知识库应用
func (s *UnimplementedApi) CreateShareKnowledgeBaseApp(ctx context.Context, req *CreateShareKnowledgeBaseAppReq) (*CreateShareKnowledgeBaseAppRsp, error) {
	return nil, errors.New("rpc CreateShareKnowledgeBaseApp of service Api is not implemented")
}

// DeleteShareKnowledgeBaseApp 删除共享知识库应用
func (s *UnimplementedApi) DeleteShareKnowledgeBaseApp(ctx context.Context, req *DeleteShareKnowledgeBaseAppReq) (*DeleteShareKnowledgeBaseAppRsp, error) {
	return nil, errors.New("rpc DeleteShareKnowledgeBaseApp of service Api is not implemented")
}

// CopyAppCallback 复制应用回调结果
func (s *UnimplementedApi) CopyAppCallback(ctx context.Context, req *CopyAppCallbackReq) (*CopyAppCallbackRsp, error) {
	return nil, errors.New("rpc CopyAppCallback of service Api is not implemented")
}

// CreateApproval 创建审批单
func (s *UnimplementedApi) CreateApproval(ctx context.Context, req *CreateApprovalReq) (*CreateApprovalRsp, error) {
	return nil, errors.New("rpc CreateApproval of service Api is not implemented")
}

// GetLastApproval 获取最新一条审批
func (s *UnimplementedApi) GetLastApproval(ctx context.Context, req *GetLastApprovalReq) (*GetLastApprovalRsp, error) {
	return nil, errors.New("rpc GetLastApproval of service Api is not implemented")
}

// END --------------------------------- Default Unimplemented Server Service --------------------------------- END

// END ======================================= Server Service Definition ======================================= END

// START ======================================= Client Service Definition ======================================= START

// ApiClientProxy defines service client proxy
type ApiClientProxy interface {
	// CreateCorp 创建企业信息
	CreateCorp(ctx context.Context, req *CreateCorpReq, opts ...client.Option) (rsp *CreateCorpRsp, err error)

	// ListCorp 企业列表
	ListCorp(ctx context.Context, req *ListCorpReq, opts ...client.Option) (rsp *ListCorpRsp, err error)

	// GetCorp 根据企业ID获取企业
	GetCorp(ctx context.Context, req *GetCorpReq, opts ...client.Option) (rsp *GetCorpRsp, err error)

	// ModifyInfosecBizType 修改企业安全审核策略
	ModifyInfosecBizType(ctx context.Context, req *ModifyInfosecBizTypeReq, opts ...client.Option) (rsp *ModifyInfosecBizTypeRsp, err error)

	// ModifyMaxTokenUsage 修改企业机器人token数
	ModifyMaxTokenUsage(ctx context.Context, req *ModifyMaxTokenUsageReq, opts ...client.Option) (rsp *ModifyMaxTokenUsageRsp, err error)

	// ModifyMaxCharSize 修改企业机器人字符数
	ModifyMaxCharSize(ctx context.Context, req *ModifyMaxCharSizeReq, opts ...client.Option) (rsp *ModifyMaxCharSizeRsp, err error)

	// AuditCorp 审核企业
	AuditCorp(ctx context.Context, req *AuditCorpReq, opts ...client.Option) (rsp *AuditCorpRsp, err error)

	// ModifyCorpRobotQuota 修改企业机器人配额
	ModifyCorpRobotQuota(ctx context.Context, req *ModifyCorpRobotQuotaReq, opts ...client.Option) (rsp *ModifyCorpRobotQuotaRsp, err error)

	// CorpStaffList 获取企业员工列表
	CorpStaffList(ctx context.Context, req *CorpStaffListReq, opts ...client.Option) (rsp *CorpStaffListRsp, err error)

	// ListCorpStaffByIds 通过Ids获取员工信息列表
	ListCorpStaffByIds(ctx context.Context, req *ListCorpStaffByIdsReq, opts ...client.Option) (rsp *ListCorpStaffByIdsRsp, err error)

	// ReleaseDetailNotify 更新QA/Segment状态回调
	ReleaseDetailNotify(ctx context.Context, req *ReleaseDetailNotifyReq, opts ...client.Option) (rsp *ReleaseDetailNotifyRsp, err error)

	// ReleaseNotify 更新发布任务回调
	ReleaseNotify(ctx context.Context, req *ReleaseNotifyReq, opts ...client.Option) (rsp *ReleaseNotifyRsp, err error)

	// GetPresignedURL 获取临时链接
	GetPresignedURL(ctx context.Context, req *GetPresignedURLReq, opts ...client.Option) (rsp *GetPresignedURLRsp, err error)

	// GetRobotInfo Deprecated 获取机器人
	GetRobotInfo(ctx context.Context, req *GetRobotInfoReq, opts ...client.Option) (rsp *GetRobotInfoRsp, err error)

	// DescribeRobotInfo 获取机器人
	DescribeRobotInfo(ctx context.Context, req *DescribeRobotInfoReq, opts ...client.Option) (rsp *DescribeRobotInfoRsp, err error)

	// SearchPreview 对话评测
	SearchPreview(ctx context.Context, req *SearchPreviewReq, opts ...client.Option) (rsp *SearchPreviewRsp, err error)

	// Search 向量特征检索
	Search(ctx context.Context, req *SearchReq, opts ...client.Option) (rsp *SearchRsp, err error)

	// CustomSearchPreview 对话评测
	CustomSearchPreview(ctx context.Context, req *CustomSearchPreviewReq, opts ...client.Option) (rsp *CustomSearchPreviewRsp, err error)

	// CustomSearch 查找
	CustomSearch(ctx context.Context, req *CustomSearchReq, opts ...client.Option) (rsp *CustomSearchRsp, err error)

	// MatchRefer 匹配来源
	MatchRefer(ctx context.Context, req *MatchReferReq, opts ...client.Option) (rsp *MatchReferRsp, err error)

	// AuditResultCallback 审核回调
	AuditResultCallback(ctx context.Context, req *CheckResultReq, opts ...client.Option) (rsp *CheckResultRsp, err error)

	// GetRobotList 获取机器人列表
	GetRobotList(ctx context.Context, req *GetRobotListReq, opts ...client.Option) (rsp *GetRobotListRsp, err error)

	// GetAppList 获取应用列表
	GetAppList(ctx context.Context, req *GetAppListReq, opts ...client.Option) (rsp *GetAppListRsp, err error)

	// GetRobotByAppKey 通过AppKey获取机器人
	GetRobotByAppKey(ctx context.Context, req *GetRobotByAppKeyReq, opts ...client.Option) (rsp *GetRobotByAppKeyRsp, err error)

	// GetAppByAppKey 通过AppKey获取应用
	GetAppByAppKey(ctx context.Context, req *GetAppByAppKeyReq, opts ...client.Option) (rsp *GetAppByAppKeyRsp, err error)

	// GetAppInfo 获取应用信息
	GetAppInfo(ctx context.Context, req *GetAppInfoReq, opts ...client.Option) (rsp *GetAppInfoRsp, err error)

	// EditRobot 编辑机器人信息
	EditRobot(ctx context.Context, req *EditRobotReq, opts ...client.Option) (rsp *EditRobotRsp, err error)

	// GetCorpList 获取企业信息
	GetCorpList(ctx context.Context, req *GetCorpListReq, opts ...client.Option) (rsp *GetCorpListRsp, err error)

	// GetDocs 获取文档内容
	GetDocs(ctx context.Context, req *GetDocsReq, opts ...client.Option) (rsp *GetDocsRsp, err error)

	// SearchPreviewRejectedQuestion 拒答问题测评库查询
	SearchPreviewRejectedQuestion(ctx context.Context, req *SearchPreviewRejectedQuestionReq, opts ...client.Option) (rsp *SearchPreviewRejectedQuestionRsp, err error)

	// SearchReleaseRejectedQuestion 拒答问题线上库查询
	SearchReleaseRejectedQuestion(ctx context.Context, req *SearchReleaseRejectedQuestionReq, opts ...client.Option) (rsp *SearchReleaseRejectedQuestionRsp, err error)

	// ListRejectedQuestion 获取拒答问题列表
	ListRejectedQuestion(ctx context.Context, req *ListRejectedQuestionReq, opts ...client.Option) (rsp *ListRejectedQuestionRsp, err error)

	// AddUnsatisfiedReply 添加不满意回复
	AddUnsatisfiedReply(ctx context.Context, req *AddUnsatisfiedReplyReq, opts ...client.Option) (rsp *AddUnsatisfiedReplyRsp, err error)

	// ListQA 获取QA列表
	ListQA(ctx context.Context, req *ListQAReq, opts ...client.Option) (rsp *ListQARsp, err error)

	// GetAdminTaskList 获取admin任务列表
	GetAdminTaskList(ctx context.Context, req *GetAdminTaskListReq, opts ...client.Option) (rsp *GetAdminTaskListRsp, err error)

	// GetAdminTaskHistoryList 获取admin历史任务列表
	GetAdminTaskHistoryList(ctx context.Context, req *GetAdminTaskHistoryListReq, opts ...client.Option) (rsp *GetAdminTaskHistoryListRsp, err error)

	// GetVectorDocTaskList 获取获取vector_doc任务列表
	GetVectorDocTaskList(ctx context.Context, req *GetVectorDocTaskListReq, opts ...client.Option) (rsp *GetVectorDocTaskListRsp, err error)

	// GetVectorDocTaskHistoryList 获取vector_doc任务历史列表
	GetVectorDocTaskHistoryList(ctx context.Context, req *GetVectorDocTaskHistoryListReq, opts ...client.Option) (rsp *GetVectorDocTaskHistoryListRsp, err error)

	// GetReleaseList 发布记录列表
	GetReleaseList(ctx context.Context, req *GetReleaseListReq, opts ...client.Option) (rsp *GetReleaseListRsp, err error)

	// AddCorpStaff 添加企业成员
	AddCorpStaff(ctx context.Context, req *AddCorpStaffReq, opts ...client.Option) (rsp *AddCorpStaffRsp, err error)

	// EditCorpStaffPassword 修改企业员工密码
	EditCorpStaffPassword(ctx context.Context, req *EditCorpStaffPasswordReq, opts ...client.Option) (rsp *EditCorpStaffPasswordRsp, err error)

	// LeaveCorp 企业员工退出企业
	LeaveCorp(ctx context.Context, req *LeaveCorpReq, opts ...client.Option) (rsp *LeaveCorpRsp, err error)

	// UpdateAuditStatus 更新审核单状态
	UpdateAuditStatus(ctx context.Context, req *UpdateAuditStatusReq, opts ...client.Option) (rsp *UpdateAuditStatusRsp, err error)

	// GetRobotDefaultConfig 获取机器人默认配置信息
	GetRobotDefaultConfig(ctx context.Context, req *GetRobotDefaultConfigReq, opts ...client.Option) (rsp *GetRobotDefaultConfigRsp, err error)

	// ClearRobotCustomConfig 清除机器人自定义配置
	ClearRobotCustomConfig(ctx context.Context, req *ClearRobotCustomConfigReq, opts ...client.Option) (rsp *ClearRobotCustomConfigRsp, err error)

	// ListGlobalKnowledge 全局干预知识列表
	ListGlobalKnowledge(ctx context.Context, req *ListGlobalKnowledgeReq, opts ...client.Option) (rsp *ListGlobalKnowledgeRsp, err error)

	// AddGlobalKnowledge 添加全局干预知识
	AddGlobalKnowledge(ctx context.Context, req *AddGlobalKnowledgeReq, opts ...client.Option) (rsp *AddGlobalKnowledgeRsp, err error)

	// DelGlobalKnowledge 删除全局干预知识
	DelGlobalKnowledge(ctx context.Context, req *DelGlobalKnowledgeReq, opts ...client.Option) (rsp *DelGlobalKnowledgeRsp, err error)

	// UpdGlobalKnowledge 更新全局干预知识
	UpdGlobalKnowledge(ctx context.Context, req *UpdGlobalKnowledgeReq, opts ...client.Option) (rsp *UpdGlobalKnowledgeRsp, err error)

	// GlobalKnowledge 全局干预知识
	GlobalKnowledge(ctx context.Context, req *GlobalKnowledgeReq, opts ...client.Option) (rsp *GlobalKnowledgeRsp, err error)

	// ForceSyncGlobalKnowledge 强制同步全局干预知识
	ForceSyncGlobalKnowledge(ctx context.Context, req *ForceSyncGlobalKnowledgeReq, opts ...client.Option) (rsp *ForceSyncGlobalKnowledgeRsp, err error)

	// CustomSimilarity 计算相似度
	CustomSimilarity(ctx context.Context, req *CustomSimilarityReq, opts ...client.Option) (rsp *CustomSimilarityRsp, err error)

	// EnableCorp 启用企业请求
	EnableCorp(ctx context.Context, req *EnableCorpReq, opts ...client.Option) (rsp *EnableCorpRsp, err error)

	// DisableCorp 禁用企业请求
	DisableCorp(ctx context.Context, req *DisableCorpReq, opts ...client.Option) (rsp *DisableCorpRsp, err error)

	// GetCustomResource 底座获取资源列表
	GetCustomResource(ctx context.Context, req *GetCustomResourceReq, opts ...client.Option) (rsp *GetCustomResourceRsp, err error)

	// ActivateProduct 底座通知产品开通
	ActivateProduct(ctx context.Context, req *ActivateProductReq, opts ...client.Option) (rsp *ActivateProductRsp, err error)

	// CreateNotice 创建通知
	CreateNotice(ctx context.Context, req *CreateNoticeReq, opts ...client.Option) (rsp *CreateNoticeRsp, err error)

	// CreateNoticeByUin 主账号维度创建通知
	CreateNoticeByUin(ctx context.Context, req *CreateNoticeByUinReq, opts ...client.Option) (rsp *CreateNoticeByUinRsp, err error)

	// DescribeIntegrator 查询集成商信息
	DescribeIntegrator(ctx context.Context, req *DescribeIntegratorReq, opts ...client.Option) (rsp *DescribeIntegratorRsp, err error)

	// DescribeRobotBizIDByAppKey 通过appKey获取机器人业务id
	DescribeRobotBizIDByAppKey(ctx context.Context, req *DescribeRobotBizIDByAppKeyReq, opts ...client.Option) (rsp *DescribeRobotBizIDByAppKeyRsp, err error)

	// DescribeLatestReleaseStatus 获取机器人最新发布状态
	DescribeLatestReleaseStatus(ctx context.Context, req *DescribeLatestReleaseStatusReq, opts ...client.Option) (rsp *DescribeLatestReleaseStatusRsp, err error)

	// TrialProduct 试用开通
	TrialProduct(ctx context.Context, req *TrialProductReq, opts ...client.Option) (rsp *TrialProductRsp, err error)

	// EditApp 编辑应用
	EditApp(ctx context.Context, req *EditAppReq, opts ...client.Option) (rsp *EditAppRsp, err error)

	// GetAppDefaultConfig 获取应用默认配置
	GetAppDefaultConfig(ctx context.Context, req *GetAppDefaultConfigReq, opts ...client.Option) (rsp *GetAppDefaultConfigRsp, err error)

	// ClearAppCustomConfig 清除应用自定义配置
	ClearAppCustomConfig(ctx context.Context, req *ClearAppCustomConfigReq, opts ...client.Option) (rsp *ClearAppCustomConfigRsp, err error)

	// GetIntent 获取意图
	GetIntent(ctx context.Context, req *GetIntentReq, opts ...client.Option) (rsp *GetIntentRsp, err error)

	// ListIntent 获取意图列表
	ListIntent(ctx context.Context, req *ListIntentReq, opts ...client.Option) (rsp *ListIntentRsp, err error)

	// CreateIntent 创建意图
	CreateIntent(ctx context.Context, req *CreateIntentReq, opts ...client.Option) (rsp *CreateIntentRsp, err error)

	// UpdateIntent 更新意图
	UpdateIntent(ctx context.Context, req *UpdateIntentReq, opts ...client.Option) (rsp *UpdateIntentRsp, err error)

	// DeleteIntent 删除意图
	DeleteIntent(ctx context.Context, req *DeleteIntentReq, opts ...client.Option) (rsp *DeleteIntentRsp, err error)

	// ListIntentByPolicyID 获取策略绑定的意图列表
	ListIntentByPolicyID(ctx context.Context, req *ListIntentByPolicyIDReq, opts ...client.Option) (rsp *ListIntentByPolicyIDRsp, err error)

	// ListIntentPolicy 获取策略列表
	ListIntentPolicy(ctx context.Context, req *ListIntentPolicyReq, opts ...client.Option) (rsp *ListIntentPolicyRsp, err error)

	// CreateIntentPolicy 创建策略
	CreateIntentPolicy(ctx context.Context, req *CreateIntentPolicyReq, opts ...client.Option) (rsp *CreateIntentPolicyRsp, err error)

	// UpdateIntentPolicy 更新策略
	UpdateIntentPolicy(ctx context.Context, req *UpdateIntentPolicyReq, opts ...client.Option) (rsp *UpdateIntentPolicyRsp, err error)

	// DeleteIntentPolicy 删除策略
	DeleteIntentPolicy(ctx context.Context, req *DeleteIntentPolicyReq, opts ...client.Option) (rsp *DeleteIntentPolicyRsp, err error)

	// ListUnusedIntentKeyMap 获取未使用的意图列表
	ListUnusedIntentKeyMap(ctx context.Context, req *ListUnusedIntentKeyMapReq, opts ...client.Option) (rsp *ListUnusedIntentKeyMapRsp, err error)

	// ListIntentPolicyKeyMap 获取策略列表映射关系
	ListIntentPolicyKeyMap(ctx context.Context, req *ListIntentPolicyKeyMapReq, opts ...client.Option) (rsp *ListIntentPolicyKeyMapRsp, err error)

	// CreateCorpCustomModel 新增企业自定义模型
	CreateCorpCustomModel(ctx context.Context, req *CreateCorpCustomModelReq, opts ...client.Option) (rsp *CreateCorpCustomModelRsp, err error)

	// GetRobotConfigByVersionID 获取版本配置
	GetRobotConfigByVersionID(ctx context.Context, req *GetRobotConfigByVersionIDReq, opts ...client.Option) (rsp *GetRobotConfigByVersionIDRsp, err error)

	// ListAgentFeedbackInner ListAgentFeedbackInner 查询 agent反馈信息列表
	ListAgentFeedbackInner(ctx context.Context, req *ListAgentFeedbackInnerReq, opts ...client.Option) (rsp *ListAgentFeedbackInnerRsp, err error)

	// UpdateAgentFeedbackStatus UpdateFeedbackStatus 修改 agent反馈信息状态
	UpdateAgentFeedbackStatus(ctx context.Context, req *UpdateAgentFeedbackStatusReq, opts ...client.Option) (rsp *UpdateAgentFeedbackStatusRsp, err error)

	// UpdateAgentFeedbackTapd UpdateAgentFeedbackTapd 修改 agent反馈信息关联的tapd
	UpdateAgentFeedbackTapd(ctx context.Context, req *UpdateAgentFeedbackTapdReq, opts ...client.Option) (rsp *UpdateAgentFeedbackTapdRsp, err error)

	// CountAgentFeedback CountAgentFeedback 获取 agent新增反馈个数
	CountAgentFeedback(ctx context.Context, req *CountAgentFeedbackReq, opts ...client.Option) (rsp *CountAgentFeedbackRsp, err error)

	// GetAgentFeedback GetAgentFeedback 获取 agent反馈信息
	GetAgentFeedback(ctx context.Context, req *GetAgentFeedbackReq, opts ...client.Option) (rsp *GetAgentFeedbackRsp, err error)

	// UpdateAgentFeedbackAndonInfo UpdateAgentFeedbackAndonInfo 修改 agent反馈信息状态
	UpdateAgentFeedbackAndonInfo(ctx context.Context, req *UpdateAgentFeedbackAndonInfoReq, opts ...client.Option) (rsp *UpdateAgentFeedbackAndonInfoRsp, err error)

	DeleteFeedbackByFlowIds(ctx context.Context, req *DeleteFeedbackByFlowIdsReq, opts ...client.Option) (rsp *DeleteFeedbackByFlowIdsRsp, err error)

	// ListFeedbackInner ListFeedbackInner 查询反馈信息列表
	ListFeedbackInner(ctx context.Context, req *ListFeedbackInnerReq, opts ...client.Option) (rsp *ListFeedbackInnerRsp, err error)

	// ListFeedbackByBizIDInner ListFeedbackByBizIDInner 查询反馈信息列表
	ListFeedbackByBizIDInner(ctx context.Context, req *ListFeedbackByBizIDInnerReq, opts ...client.Option) (rsp *ListFeedbackByBizIDInnerRsp, err error)

	// UpdateFeedbackClassification UpdateFeedbackClassification 修改一级分类，二级分类
	UpdateFeedbackClassification(ctx context.Context, req *UpdateFeedbackClassificationReq, opts ...client.Option) (rsp *UpdateFeedbackClassificationRsp, err error)

	// UpdateFeedbackAndonInfo UpdateFeedbackAndonIfoReq 修改安灯状态信息
	UpdateFeedbackAndonInfo(ctx context.Context, req *UpdateFeedbackAndonInfoReq, opts ...client.Option) (rsp *UpdateFeedbackAndonInfoRsp, err error)

	// UpdateFeedbackStatus UpdateFeedbackStatus 修改反馈信息状态
	UpdateFeedbackStatus(ctx context.Context, req *UpdateFeedbackStatusReq, opts ...client.Option) (rsp *UpdateFeedbackStatusRsp, err error)

	// UpdateFeedbackTapd UpdateFeedbackTapd 修改反馈信息关联的tapd
	UpdateFeedbackTapd(ctx context.Context, req *UpdateFeedbackTapdReq, opts ...client.Option) (rsp *UpdateFeedbackTapdRsp, err error)

	// CountFeedback CountFeedback 获取新增反馈个数
	CountFeedback(ctx context.Context, req *CountFeedbackReq, opts ...client.Option) (rsp *CountFeedbackRsp, err error)

	// GetFeedback CountFeedback 获取新增反馈个数
	GetFeedback(ctx context.Context, req *GetFeedbackReq, opts ...client.Option) (rsp *GetFeedbackRsp, err error)

	// DescribeAccountBalance 获取企业账户余额信息
	DescribeAccountBalance(ctx context.Context, req *DescribeAccountBalanceReq, opts ...client.Option) (rsp *DescribeAccountBalanceRsp, err error)

	// GetAppType GetAppType 获取应用类型
	GetAppType(ctx context.Context, req *GetAppTypeReq, opts ...client.Option) (rsp *GetAppTypeRsp, err error)

	// GetDescribeLicense 获取应用License
	GetDescribeLicense(ctx context.Context, req *GetDescribeLicenseReq, opts ...client.Option) (rsp *GetDescribeLicenseRsp, err error)

	// GetExperienceApps GetExperienceApps 体验中心-获取体验应用列表
	GetExperienceApps(ctx context.Context, req *GetExperienceAppsReq, opts ...client.Option) (rsp *GetExperienceAppsRsp, err error)

	// ModifyExperienceApp ModifyExperienceApp 修改体验应用
	ModifyExperienceApp(ctx context.Context, req *ModifyExperienceAppReq, opts ...client.Option) (rsp *ModifyExperienceAppRsp, err error)

	// ListSelectDoc ListSelectDoc 选择文档列表
	ListSelectDoc(ctx context.Context, req *ListSelectDocReq, opts ...client.Option) (rsp *ListSelectDocRsp, err error)

	// CreateCorpAndAssignPermission CreateCorpAndAssignPermission
	CreateCorpAndAssignPermission(ctx context.Context, req *CreateCorpAndAssignPermissionReq, opts ...client.Option) (rsp *CreateCorpAndAssignPermissionRsp, err error)

	// CheckCorpAndPermission CheckCorpAndPermission
	CheckCorpAndPermission(ctx context.Context, req *CheckCorpAndPermissionReq, opts ...client.Option) (rsp *CheckCorpAndPermissionRsp, err error)

	// DescribeCropByUin DescribeCropByUin
	DescribeCropByUin(ctx context.Context, req *DescribeCropByUinReq, opts ...client.Option) (rsp *DescribeCropByUinRsp, err error)

	// GetListModel GetListModel 获取企业模型列表
	GetListModel(ctx context.Context, req *GetListModelReq, opts ...client.Option) (rsp *GetListModelRsp, err error)

	// ModifyAppInfosecBizType ModifyAppInfosecBizType 修改应用安全审核策略
	ModifyAppInfosecBizType(ctx context.Context, req *ModifyAppInfosecBizTypeReq, opts ...client.Option) (rsp *ModifyAppInfosecBizTypeRsp, err error)

	// GetTotalConcurrency GetTotalConcurrency 获取企业总并发数
	GetTotalConcurrency(ctx context.Context, req *GetTotalConcurrencyReq, opts ...client.Option) (rsp *GetTotalConcurrencyRsp, err error)

	// MultiLock MultiLock 加锁
	MultiLock(ctx context.Context, req *MultiLockReq, opts ...client.Option) (rsp *MultiLockRsp, err error)

	// MultiUnlock MultiUnlock 解锁
	MultiUnlock(ctx context.Context, req *MultiUnlockReq, opts ...client.Option) (rsp *MultiUnlockRsp, err error)

	// CheckVarIsUsed 检查自定义参数是否被使用
	CheckVarIsUsed(ctx context.Context, req *CheckVarIsUsedReq, opts ...client.Option) (rsp *CheckVarIsUsedRsp, err error)

	// ModifyAppVar 修改应用检索范围自定义参数
	ModifyAppVar(ctx context.Context, req *ModifyAppVarReq, opts ...client.Option) (rsp *ModifyAppVarRsp, err error)

	// ClearAppKnowledgeCallback 清理应用知识资源回调结果
	ClearAppKnowledgeCallback(ctx context.Context, req *ClearAppKnowledgeCallbackReq, opts ...client.Option) (rsp *ClearAppKnowledgeCallbackRsp, err error)

	// ClearAppFlowCallback 清理应用流程资源回调
	ClearAppFlowCallback(ctx context.Context, req *ClearAppFlowCallbackReq, opts ...client.Option) (rsp *ClearAppFlowCallbackRsp, err error)

	// ClearAppVectorCallback 清理应用向量库资源回调
	ClearAppVectorCallback(ctx context.Context, req *ClearAppVectorCallbackReq, opts ...client.Option) (rsp *ClearAppVectorCallbackRsp, err error)

	// ClearAppResourceCallback 清理应用资源回调
	ClearAppResourceCallback(ctx context.Context, req *ClearAppResourceCallbackReq, opts ...client.Option) (rsp *ClearAppResourceCallbackRsp, err error)

	// GetAppByPluginId 查询插件关联的应用信息
	GetAppByPluginId(ctx context.Context, req *GetAppByPluginIdReq, opts ...client.Option) (rsp *GetAppByPluginIdRsp, err error)

	// GetCorpStaffName 查询用户昵称
	GetCorpStaffName(ctx context.Context, req *GetCorpStaffNameReq, opts ...client.Option) (rsp *GetCorpStaffNameRsp, err error)

	// GetUserInfo 查询用户信息
	GetUserInfo(ctx context.Context, req *GetUserInfoReq, opts ...client.Option) (rsp *GetUserInfoRsp, err error)

	// GetModelFinanceInfo 获取模型的计费控制信息
	GetModelFinanceInfo(ctx context.Context, req *GetModelFinanceInfoReq, opts ...client.Option) (rsp *GetModelFinanceInfoRsp, err error)

	// GetModelInfo 获取企业模型信息
	GetModelInfo(ctx context.Context, req *GetModelInfoReq, opts ...client.Option) (rsp *GetModelInfoRsp, err error)

	// GetValidExperienceApps 获取有效的体验应用信息
	GetValidExperienceApps(ctx context.Context, req *GetValidExperienceAppsReq, opts ...client.Option) (rsp *GetValidExperienceAppsRsp, err error)

	// GetModelList 获取模型列表信息
	GetModelList(ctx context.Context, req *GetModelListReq, opts ...client.Option) (rsp *GetModelListRsp, err error)

	// GetAppChatInputNum 获取应用对话query输入长度限制
	GetAppChatInputNum(ctx context.Context, req *GetAppChatInputNumReq, opts ...client.Option) (rsp *GetAppChatInputNumRsp, err error)

	// CreatePromptVersion 创建Prompt版本信息
	CreatePromptVersion(ctx context.Context, req *CreatePromptVersionReq, opts ...client.Option) (rsp *CreatePromptVersionRsp, err error)

	// EditPromptVersion 编辑prompt版本信息
	EditPromptVersion(ctx context.Context, req *EditPromptVersionReq, opts ...client.Option) (rsp *EditPromptVersionRsp, err error)

	// GetPromptVersionList 查询prompt版本信息
	GetPromptVersionList(ctx context.Context, req *GetPromptVersionListReq, opts ...client.Option) (rsp *GetPromptVersionListRsp, err error)

	// UpgradePromptVersion 升级Prompt版本
	UpgradePromptVersion(ctx context.Context, req *UpgradePromptVersionReq, opts ...client.Option) (rsp *UpgradePromptVersionRsp, err error)

	// CreateCorpConcurrencyWL 创建账户并发白名单
	CreateCorpConcurrencyWL(ctx context.Context, req *CreateCorpConcurrencyWLReq, opts ...client.Option) (rsp *CreateCorpConcurrencyWLRsp, err error)

	// EditCorpConcurrencyWL 修改账户并发白名单
	EditCorpConcurrencyWL(ctx context.Context, req *EditCorpConcurrencyWLReq, opts ...client.Option) (rsp *EditCorpConcurrencyWLRsp, err error)

	// DeleteCorpConcurrencyWL 删除账户并发白名单
	DeleteCorpConcurrencyWL(ctx context.Context, req *DeleteCorpConcurrencyWLReq, opts ...client.Option) (rsp *DeleteCorpConcurrencyWLRsp, err error)

	// GetCorpConcurrencyWLList 查询账户并发白名单
	GetCorpConcurrencyWLList(ctx context.Context, req *GetCorpConcurrencyWLListReq, opts ...client.Option) (rsp *GetCorpConcurrencyWLListRsp, err error)

	// StartEmbeddingUpgradeApp 应用升级embedding开始
	StartEmbeddingUpgradeApp(ctx context.Context, req *StartEmbeddingUpgradeAppReq, opts ...client.Option) (rsp *StartEmbeddingUpgradeAppRsp, err error)

	// FinishEmbeddingUpgradeApp 应用升级embedding结束
	FinishEmbeddingUpgradeApp(ctx context.Context, req *FinishEmbeddingUpgradeAppReq, opts ...client.Option) (rsp *FinishEmbeddingUpgradeAppRsp, err error)

	// GetAppsByBizIDs 通过应用业务ID获取应用信息，基于缓存读取
	GetAppsByBizIDs(ctx context.Context, req *GetAppsByBizIDsReq, opts ...client.Option) (rsp *GetAppsByBizIDsRsp, err error)

	// GetCorpStaffsByBizIDs 通过员工业务ID获取员工信息，基于缓存读取
	GetCorpStaffsByBizIDs(ctx context.Context, req *GetCorpStaffsByBizIDsReq, opts ...client.Option) (rsp *GetCorpStaffsByBizIDsRsp, err error)

	// CopyAppConfig 复制应用配置
	CopyAppConfig(ctx context.Context, req *CopyAppConfigReq, opts ...client.Option) (rsp *CopyAppConfigRsp, err error)

	// EditCorpCustomModelWL 更新账户自定义模型白名单
	EditCorpCustomModelWL(ctx context.Context, req *EditCorpCustomModelWLReq, opts ...client.Option) (rsp *EditCorpCustomModelWLRsp, err error)

	// DeleteCorpCustomModelWL 删除账户自定义模型白名单
	DeleteCorpCustomModelWL(ctx context.Context, req *DeleteCorpCustomModelWLReq, opts ...client.Option) (rsp *DeleteCorpCustomModelWLRsp, err error)

	// GetCorpCustomModelWLList 获取账户自定义模型白名单列表
	GetCorpCustomModelWLList(ctx context.Context, req *GetCorpCustomModelWLListReq, opts ...client.Option) (rsp *GetCorpCustomModelWLListRsp, err error)

	// GetCorpModelQpmTpmLimit 获取账户模型默认的QPM、TPM上限值
	GetCorpModelQpmTpmLimit(ctx context.Context, req *GetCorpModelQpmTpmLimitReq, opts ...client.Option) (rsp *GetCorpModelQpmTpmLimitRsp, err error)

	// GetDefaultQpmTpmLimit 获取账户模型的默认QPM、TPM值
	GetDefaultQpmTpmLimit(ctx context.Context, req *GetDefaultQpmTpmLimitReq, opts ...client.Option) (rsp *GetDefaultQpmTpmLimitRsp, err error)

	// EditCorpQpmTpmLimit 编辑账户模型QPM、TPM配置
	EditCorpQpmTpmLimit(ctx context.Context, req *EditCorpQpmTpmLimitReq, opts ...client.Option) (rsp *EditCorpQpmTpmLimitRsp, err error)

	// DeleteCorpQpmTpmLimit 删除账户模型QPM、TPM配置
	DeleteCorpQpmTpmLimit(ctx context.Context, req *DeleteCorpQpmTpmLimitReq, opts ...client.Option) (rsp *DeleteCorpQpmTpmLimitRsp, err error)

	// GetCorpQpmTpmLimitList 获取账户模型QPM、TPM配置
	GetCorpQpmTpmLimitList(ctx context.Context, req *GetCorpQpmTpmLimitListReq, opts ...client.Option) (rsp *GetCorpQpmTpmLimitListRsp, err error)

	// CreateShareKnowledgeBaseApp 创建共享知识库应用
	CreateShareKnowledgeBaseApp(ctx context.Context, req *CreateShareKnowledgeBaseAppReq, opts ...client.Option) (rsp *CreateShareKnowledgeBaseAppRsp, err error)

	// DeleteShareKnowledgeBaseApp 删除共享知识库应用
	DeleteShareKnowledgeBaseApp(ctx context.Context, req *DeleteShareKnowledgeBaseAppReq, opts ...client.Option) (rsp *DeleteShareKnowledgeBaseAppRsp, err error)

	// CopyAppCallback 复制应用回调结果
	CopyAppCallback(ctx context.Context, req *CopyAppCallbackReq, opts ...client.Option) (rsp *CopyAppCallbackRsp, err error)

	// CreateApproval 创建审批单
	CreateApproval(ctx context.Context, req *CreateApprovalReq, opts ...client.Option) (rsp *CreateApprovalRsp, err error)

	// GetLastApproval 获取最新一条审批
	GetLastApproval(ctx context.Context, req *GetLastApprovalReq, opts ...client.Option) (rsp *GetLastApprovalRsp, err error)
}

type ApiClientProxyImpl struct {
	client client.Client
	opts   []client.Option
}

var NewApiClientProxy = func(opts ...client.Option) ApiClientProxy {
	return &ApiClientProxyImpl{client: client.DefaultClient, opts: opts}
}

func (c *ApiClientProxyImpl) CreateCorp(ctx context.Context, req *CreateCorpReq, opts ...client.Option) (*CreateCorpRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/CreateCorp")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("CreateCorp")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &CreateCorpRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) ListCorp(ctx context.Context, req *ListCorpReq, opts ...client.Option) (*ListCorpRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/ListCorp")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("ListCorp")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ListCorpRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) GetCorp(ctx context.Context, req *GetCorpReq, opts ...client.Option) (*GetCorpRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/GetCorp")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("GetCorp")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetCorpRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) ModifyInfosecBizType(ctx context.Context, req *ModifyInfosecBizTypeReq, opts ...client.Option) (*ModifyInfosecBizTypeRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/ModifyInfosecBizType")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("ModifyInfosecBizType")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ModifyInfosecBizTypeRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) ModifyMaxTokenUsage(ctx context.Context, req *ModifyMaxTokenUsageReq, opts ...client.Option) (*ModifyMaxTokenUsageRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/ModifyMaxTokenUsage")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("ModifyMaxTokenUsage")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ModifyMaxTokenUsageRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) ModifyMaxCharSize(ctx context.Context, req *ModifyMaxCharSizeReq, opts ...client.Option) (*ModifyMaxCharSizeRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/ModifyMaxCharSize")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("ModifyMaxCharSize")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ModifyMaxCharSizeRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) AuditCorp(ctx context.Context, req *AuditCorpReq, opts ...client.Option) (*AuditCorpRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/AuditCorp")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("AuditCorp")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &AuditCorpRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) ModifyCorpRobotQuota(ctx context.Context, req *ModifyCorpRobotQuotaReq, opts ...client.Option) (*ModifyCorpRobotQuotaRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/ModifyCorpRobotQuota")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("ModifyCorpRobotQuota")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ModifyCorpRobotQuotaRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) CorpStaffList(ctx context.Context, req *CorpStaffListReq, opts ...client.Option) (*CorpStaffListRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/CorpStaffList")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("CorpStaffList")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &CorpStaffListRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) ListCorpStaffByIds(ctx context.Context, req *ListCorpStaffByIdsReq, opts ...client.Option) (*ListCorpStaffByIdsRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/ListCorpStaffByIds")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("ListCorpStaffByIds")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ListCorpStaffByIdsRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) ReleaseDetailNotify(ctx context.Context, req *ReleaseDetailNotifyReq, opts ...client.Option) (*ReleaseDetailNotifyRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/ReleaseDetailNotify")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("ReleaseDetailNotify")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ReleaseDetailNotifyRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) ReleaseNotify(ctx context.Context, req *ReleaseNotifyReq, opts ...client.Option) (*ReleaseNotifyRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/ReleaseNotify")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("ReleaseNotify")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ReleaseNotifyRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) GetPresignedURL(ctx context.Context, req *GetPresignedURLReq, opts ...client.Option) (*GetPresignedURLRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/GetPresignedURL")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("GetPresignedURL")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetPresignedURLRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) GetRobotInfo(ctx context.Context, req *GetRobotInfoReq, opts ...client.Option) (*GetRobotInfoRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/GetRobotInfo")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("GetRobotInfo")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetRobotInfoRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) DescribeRobotInfo(ctx context.Context, req *DescribeRobotInfoReq, opts ...client.Option) (*DescribeRobotInfoRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/DescribeRobotInfo")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("DescribeRobotInfo")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DescribeRobotInfoRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) SearchPreview(ctx context.Context, req *SearchPreviewReq, opts ...client.Option) (*SearchPreviewRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/SearchPreview")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("SearchPreview")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &SearchPreviewRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) Search(ctx context.Context, req *SearchReq, opts ...client.Option) (*SearchRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/Search")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("Search")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &SearchRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) CustomSearchPreview(ctx context.Context, req *CustomSearchPreviewReq, opts ...client.Option) (*CustomSearchPreviewRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/CustomSearchPreview")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("CustomSearchPreview")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &CustomSearchPreviewRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) CustomSearch(ctx context.Context, req *CustomSearchReq, opts ...client.Option) (*CustomSearchRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/CustomSearch")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("CustomSearch")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &CustomSearchRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) MatchRefer(ctx context.Context, req *MatchReferReq, opts ...client.Option) (*MatchReferRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/MatchRefer")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("MatchRefer")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &MatchReferRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) AuditResultCallback(ctx context.Context, req *CheckResultReq, opts ...client.Option) (*CheckResultRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/AuditResultCallback")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("AuditResultCallback")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &CheckResultRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) GetRobotList(ctx context.Context, req *GetRobotListReq, opts ...client.Option) (*GetRobotListRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/GetRobotList")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("GetRobotList")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetRobotListRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) GetAppList(ctx context.Context, req *GetAppListReq, opts ...client.Option) (*GetAppListRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/GetAppList")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("GetAppList")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetAppListRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) GetRobotByAppKey(ctx context.Context, req *GetRobotByAppKeyReq, opts ...client.Option) (*GetRobotByAppKeyRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/GetRobotByAppKey")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("GetRobotByAppKey")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetRobotByAppKeyRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) GetAppByAppKey(ctx context.Context, req *GetAppByAppKeyReq, opts ...client.Option) (*GetAppByAppKeyRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/GetAppByAppKey")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("GetAppByAppKey")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetAppByAppKeyRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) GetAppInfo(ctx context.Context, req *GetAppInfoReq, opts ...client.Option) (*GetAppInfoRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/GetAppInfo")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("GetAppInfo")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetAppInfoRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) EditRobot(ctx context.Context, req *EditRobotReq, opts ...client.Option) (*EditRobotRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/EditRobot")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("EditRobot")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &EditRobotRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) GetCorpList(ctx context.Context, req *GetCorpListReq, opts ...client.Option) (*GetCorpListRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/GetCorpList")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("GetCorpList")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetCorpListRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) GetDocs(ctx context.Context, req *GetDocsReq, opts ...client.Option) (*GetDocsRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/GetDocs")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("GetDocs")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetDocsRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) SearchPreviewRejectedQuestion(ctx context.Context, req *SearchPreviewRejectedQuestionReq, opts ...client.Option) (*SearchPreviewRejectedQuestionRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/SearchPreviewRejectedQuestion")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("SearchPreviewRejectedQuestion")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &SearchPreviewRejectedQuestionRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) SearchReleaseRejectedQuestion(ctx context.Context, req *SearchReleaseRejectedQuestionReq, opts ...client.Option) (*SearchReleaseRejectedQuestionRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/SearchReleaseRejectedQuestion")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("SearchReleaseRejectedQuestion")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &SearchReleaseRejectedQuestionRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) ListRejectedQuestion(ctx context.Context, req *ListRejectedQuestionReq, opts ...client.Option) (*ListRejectedQuestionRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/ListRejectedQuestion")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("ListRejectedQuestion")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ListRejectedQuestionRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) AddUnsatisfiedReply(ctx context.Context, req *AddUnsatisfiedReplyReq, opts ...client.Option) (*AddUnsatisfiedReplyRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/AddUnsatisfiedReply")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("AddUnsatisfiedReply")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &AddUnsatisfiedReplyRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) ListQA(ctx context.Context, req *ListQAReq, opts ...client.Option) (*ListQARsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/ListQA")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("ListQA")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ListQARsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) GetAdminTaskList(ctx context.Context, req *GetAdminTaskListReq, opts ...client.Option) (*GetAdminTaskListRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/GetAdminTaskList")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("GetAdminTaskList")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetAdminTaskListRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) GetAdminTaskHistoryList(ctx context.Context, req *GetAdminTaskHistoryListReq, opts ...client.Option) (*GetAdminTaskHistoryListRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/GetAdminTaskHistoryList")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("GetAdminTaskHistoryList")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetAdminTaskHistoryListRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) GetVectorDocTaskList(ctx context.Context, req *GetVectorDocTaskListReq, opts ...client.Option) (*GetVectorDocTaskListRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/GetVectorDocTaskList")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("GetVectorDocTaskList")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetVectorDocTaskListRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) GetVectorDocTaskHistoryList(ctx context.Context, req *GetVectorDocTaskHistoryListReq, opts ...client.Option) (*GetVectorDocTaskHistoryListRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/GetVectorDocTaskHistoryList")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("GetVectorDocTaskHistoryList")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetVectorDocTaskHistoryListRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) GetReleaseList(ctx context.Context, req *GetReleaseListReq, opts ...client.Option) (*GetReleaseListRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/GetReleaseList")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("GetReleaseList")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetReleaseListRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) AddCorpStaff(ctx context.Context, req *AddCorpStaffReq, opts ...client.Option) (*AddCorpStaffRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/AddCorpStaff")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("AddCorpStaff")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &AddCorpStaffRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) EditCorpStaffPassword(ctx context.Context, req *EditCorpStaffPasswordReq, opts ...client.Option) (*EditCorpStaffPasswordRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/EditCorpStaffPassword")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("EditCorpStaffPassword")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &EditCorpStaffPasswordRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) LeaveCorp(ctx context.Context, req *LeaveCorpReq, opts ...client.Option) (*LeaveCorpRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/LeaveCorp")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("LeaveCorp")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &LeaveCorpRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) UpdateAuditStatus(ctx context.Context, req *UpdateAuditStatusReq, opts ...client.Option) (*UpdateAuditStatusRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/UpdateAuditStatus")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("UpdateAuditStatus")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &UpdateAuditStatusRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) GetRobotDefaultConfig(ctx context.Context, req *GetRobotDefaultConfigReq, opts ...client.Option) (*GetRobotDefaultConfigRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/GetRobotDefaultConfig")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("GetRobotDefaultConfig")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetRobotDefaultConfigRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) ClearRobotCustomConfig(ctx context.Context, req *ClearRobotCustomConfigReq, opts ...client.Option) (*ClearRobotCustomConfigRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/ClearRobotCustomConfig")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("ClearRobotCustomConfig")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ClearRobotCustomConfigRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) ListGlobalKnowledge(ctx context.Context, req *ListGlobalKnowledgeReq, opts ...client.Option) (*ListGlobalKnowledgeRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/ListGlobalKnowledge")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("ListGlobalKnowledge")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ListGlobalKnowledgeRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) AddGlobalKnowledge(ctx context.Context, req *AddGlobalKnowledgeReq, opts ...client.Option) (*AddGlobalKnowledgeRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/AddGlobalKnowledge")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("AddGlobalKnowledge")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &AddGlobalKnowledgeRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) DelGlobalKnowledge(ctx context.Context, req *DelGlobalKnowledgeReq, opts ...client.Option) (*DelGlobalKnowledgeRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/DelGlobalKnowledge")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("DelGlobalKnowledge")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DelGlobalKnowledgeRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) UpdGlobalKnowledge(ctx context.Context, req *UpdGlobalKnowledgeReq, opts ...client.Option) (*UpdGlobalKnowledgeRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/UpdGlobalKnowledge")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("UpdGlobalKnowledge")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &UpdGlobalKnowledgeRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) GlobalKnowledge(ctx context.Context, req *GlobalKnowledgeReq, opts ...client.Option) (*GlobalKnowledgeRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/GlobalKnowledge")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("GlobalKnowledge")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GlobalKnowledgeRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) ForceSyncGlobalKnowledge(ctx context.Context, req *ForceSyncGlobalKnowledgeReq, opts ...client.Option) (*ForceSyncGlobalKnowledgeRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/ForceSyncGlobalKnowledge")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("ForceSyncGlobalKnowledge")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ForceSyncGlobalKnowledgeRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) CustomSimilarity(ctx context.Context, req *CustomSimilarityReq, opts ...client.Option) (*CustomSimilarityRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/CustomSimilarity")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("CustomSimilarity")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &CustomSimilarityRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) EnableCorp(ctx context.Context, req *EnableCorpReq, opts ...client.Option) (*EnableCorpRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/EnableCorp")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("EnableCorp")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &EnableCorpRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) DisableCorp(ctx context.Context, req *DisableCorpReq, opts ...client.Option) (*DisableCorpRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/DisableCorp")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("DisableCorp")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DisableCorpRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) GetCustomResource(ctx context.Context, req *GetCustomResourceReq, opts ...client.Option) (*GetCustomResourceRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/GetCustomResource")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("GetCustomResource")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetCustomResourceRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) ActivateProduct(ctx context.Context, req *ActivateProductReq, opts ...client.Option) (*ActivateProductRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/ActivateProduct")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("ActivateProduct")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ActivateProductRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) CreateNotice(ctx context.Context, req *CreateNoticeReq, opts ...client.Option) (*CreateNoticeRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/CreateNotice")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("CreateNotice")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &CreateNoticeRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) CreateNoticeByUin(ctx context.Context, req *CreateNoticeByUinReq, opts ...client.Option) (*CreateNoticeByUinRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/CreateNoticeByUin")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("CreateNoticeByUin")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &CreateNoticeByUinRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) DescribeIntegrator(ctx context.Context, req *DescribeIntegratorReq, opts ...client.Option) (*DescribeIntegratorRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/DescribeIntegrator")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("DescribeIntegrator")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DescribeIntegratorRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) DescribeRobotBizIDByAppKey(ctx context.Context, req *DescribeRobotBizIDByAppKeyReq, opts ...client.Option) (*DescribeRobotBizIDByAppKeyRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/DescribeRobotBizIDByAppKey")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("DescribeRobotBizIDByAppKey")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DescribeRobotBizIDByAppKeyRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) DescribeLatestReleaseStatus(ctx context.Context, req *DescribeLatestReleaseStatusReq, opts ...client.Option) (*DescribeLatestReleaseStatusRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/DescribeLatestReleaseStatus")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("DescribeLatestReleaseStatus")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DescribeLatestReleaseStatusRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) TrialProduct(ctx context.Context, req *TrialProductReq, opts ...client.Option) (*TrialProductRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/TrialProduct")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("TrialProduct")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &TrialProductRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) EditApp(ctx context.Context, req *EditAppReq, opts ...client.Option) (*EditAppRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/EditApp")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("EditApp")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &EditAppRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) GetAppDefaultConfig(ctx context.Context, req *GetAppDefaultConfigReq, opts ...client.Option) (*GetAppDefaultConfigRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/GetAppDefaultConfig")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("GetAppDefaultConfig")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetAppDefaultConfigRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) ClearAppCustomConfig(ctx context.Context, req *ClearAppCustomConfigReq, opts ...client.Option) (*ClearAppCustomConfigRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/ClearAppCustomConfig")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("ClearAppCustomConfig")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ClearAppCustomConfigRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) GetIntent(ctx context.Context, req *GetIntentReq, opts ...client.Option) (*GetIntentRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/GetIntent")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("GetIntent")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetIntentRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) ListIntent(ctx context.Context, req *ListIntentReq, opts ...client.Option) (*ListIntentRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/ListIntent")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("ListIntent")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ListIntentRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) CreateIntent(ctx context.Context, req *CreateIntentReq, opts ...client.Option) (*CreateIntentRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/CreateIntent")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("CreateIntent")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &CreateIntentRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) UpdateIntent(ctx context.Context, req *UpdateIntentReq, opts ...client.Option) (*UpdateIntentRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/UpdateIntent")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("UpdateIntent")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &UpdateIntentRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) DeleteIntent(ctx context.Context, req *DeleteIntentReq, opts ...client.Option) (*DeleteIntentRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/DeleteIntent")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("DeleteIntent")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DeleteIntentRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) ListIntentByPolicyID(ctx context.Context, req *ListIntentByPolicyIDReq, opts ...client.Option) (*ListIntentByPolicyIDRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/ListIntentByPolicyID")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("ListIntentByPolicyID")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ListIntentByPolicyIDRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) ListIntentPolicy(ctx context.Context, req *ListIntentPolicyReq, opts ...client.Option) (*ListIntentPolicyRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/ListIntentPolicy")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("ListIntentPolicy")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ListIntentPolicyRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) CreateIntentPolicy(ctx context.Context, req *CreateIntentPolicyReq, opts ...client.Option) (*CreateIntentPolicyRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/CreateIntentPolicy")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("CreateIntentPolicy")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &CreateIntentPolicyRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) UpdateIntentPolicy(ctx context.Context, req *UpdateIntentPolicyReq, opts ...client.Option) (*UpdateIntentPolicyRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/UpdateIntentPolicy")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("UpdateIntentPolicy")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &UpdateIntentPolicyRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) DeleteIntentPolicy(ctx context.Context, req *DeleteIntentPolicyReq, opts ...client.Option) (*DeleteIntentPolicyRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/DeleteIntentPolicy")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("DeleteIntentPolicy")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DeleteIntentPolicyRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) ListUnusedIntentKeyMap(ctx context.Context, req *ListUnusedIntentKeyMapReq, opts ...client.Option) (*ListUnusedIntentKeyMapRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/ListUnusedIntentKeyMap")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("ListUnusedIntentKeyMap")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ListUnusedIntentKeyMapRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) ListIntentPolicyKeyMap(ctx context.Context, req *ListIntentPolicyKeyMapReq, opts ...client.Option) (*ListIntentPolicyKeyMapRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/ListIntentPolicyKeyMap")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("ListIntentPolicyKeyMap")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ListIntentPolicyKeyMapRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) CreateCorpCustomModel(ctx context.Context, req *CreateCorpCustomModelReq, opts ...client.Option) (*CreateCorpCustomModelRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/CreateCorpCustomModel")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("CreateCorpCustomModel")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &CreateCorpCustomModelRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) GetRobotConfigByVersionID(ctx context.Context, req *GetRobotConfigByVersionIDReq, opts ...client.Option) (*GetRobotConfigByVersionIDRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/GetRobotConfigByVersionID")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("GetRobotConfigByVersionID")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetRobotConfigByVersionIDRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) ListAgentFeedbackInner(ctx context.Context, req *ListAgentFeedbackInnerReq, opts ...client.Option) (*ListAgentFeedbackInnerRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/ListAgentFeedbackInner")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("ListAgentFeedbackInner")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ListAgentFeedbackInnerRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) UpdateAgentFeedbackStatus(ctx context.Context, req *UpdateAgentFeedbackStatusReq, opts ...client.Option) (*UpdateAgentFeedbackStatusRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/UpdateAgentFeedbackStatus")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("UpdateAgentFeedbackStatus")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &UpdateAgentFeedbackStatusRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) UpdateAgentFeedbackTapd(ctx context.Context, req *UpdateAgentFeedbackTapdReq, opts ...client.Option) (*UpdateAgentFeedbackTapdRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/UpdateAgentFeedbackTapd")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("UpdateAgentFeedbackTapd")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &UpdateAgentFeedbackTapdRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) CountAgentFeedback(ctx context.Context, req *CountAgentFeedbackReq, opts ...client.Option) (*CountAgentFeedbackRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/CountAgentFeedback")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("CountAgentFeedback")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &CountAgentFeedbackRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) GetAgentFeedback(ctx context.Context, req *GetAgentFeedbackReq, opts ...client.Option) (*GetAgentFeedbackRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/GetAgentFeedback")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("GetAgentFeedback")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetAgentFeedbackRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) UpdateAgentFeedbackAndonInfo(ctx context.Context, req *UpdateAgentFeedbackAndonInfoReq, opts ...client.Option) (*UpdateAgentFeedbackAndonInfoRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/UpdateAgentFeedbackAndonInfo")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("UpdateAgentFeedbackAndonInfo")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &UpdateAgentFeedbackAndonInfoRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) DeleteFeedbackByFlowIds(ctx context.Context, req *DeleteFeedbackByFlowIdsReq, opts ...client.Option) (*DeleteFeedbackByFlowIdsRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/DeleteFeedbackByFlowIds")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("DeleteFeedbackByFlowIds")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DeleteFeedbackByFlowIdsRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) ListFeedbackInner(ctx context.Context, req *ListFeedbackInnerReq, opts ...client.Option) (*ListFeedbackInnerRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/ListFeedbackInner")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("ListFeedbackInner")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ListFeedbackInnerRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) ListFeedbackByBizIDInner(ctx context.Context, req *ListFeedbackByBizIDInnerReq, opts ...client.Option) (*ListFeedbackByBizIDInnerRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/ListFeedbackByBizIDInner")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("ListFeedbackByBizIDInner")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ListFeedbackByBizIDInnerRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) UpdateFeedbackClassification(ctx context.Context, req *UpdateFeedbackClassificationReq, opts ...client.Option) (*UpdateFeedbackClassificationRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/UpdateFeedbackClassification")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("UpdateFeedbackClassification")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &UpdateFeedbackClassificationRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) UpdateFeedbackAndonInfo(ctx context.Context, req *UpdateFeedbackAndonInfoReq, opts ...client.Option) (*UpdateFeedbackAndonInfoRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/UpdateFeedbackAndonInfo")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("UpdateFeedbackAndonInfo")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &UpdateFeedbackAndonInfoRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) UpdateFeedbackStatus(ctx context.Context, req *UpdateFeedbackStatusReq, opts ...client.Option) (*UpdateFeedbackStatusRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/UpdateFeedbackStatus")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("UpdateFeedbackStatus")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &UpdateFeedbackStatusRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) UpdateFeedbackTapd(ctx context.Context, req *UpdateFeedbackTapdReq, opts ...client.Option) (*UpdateFeedbackTapdRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/UpdateFeedbackTapd")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("UpdateFeedbackTapd")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &UpdateFeedbackTapdRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) CountFeedback(ctx context.Context, req *CountFeedbackReq, opts ...client.Option) (*CountFeedbackRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/CountFeedback")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("CountFeedback")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &CountFeedbackRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) GetFeedback(ctx context.Context, req *GetFeedbackReq, opts ...client.Option) (*GetFeedbackRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/GetFeedback")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("GetFeedback")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetFeedbackRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) DescribeAccountBalance(ctx context.Context, req *DescribeAccountBalanceReq, opts ...client.Option) (*DescribeAccountBalanceRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/DescribeAccountBalance")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("DescribeAccountBalance")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DescribeAccountBalanceRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) GetAppType(ctx context.Context, req *GetAppTypeReq, opts ...client.Option) (*GetAppTypeRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/GetAppType")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("GetAppType")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetAppTypeRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) GetDescribeLicense(ctx context.Context, req *GetDescribeLicenseReq, opts ...client.Option) (*GetDescribeLicenseRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/GetDescribeLicense")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("GetDescribeLicense")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetDescribeLicenseRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) GetExperienceApps(ctx context.Context, req *GetExperienceAppsReq, opts ...client.Option) (*GetExperienceAppsRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/GetExperienceApps")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("GetExperienceApps")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetExperienceAppsRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) ModifyExperienceApp(ctx context.Context, req *ModifyExperienceAppReq, opts ...client.Option) (*ModifyExperienceAppRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/ModifyExperienceApp")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("ModifyExperienceApp")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ModifyExperienceAppRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) ListSelectDoc(ctx context.Context, req *ListSelectDocReq, opts ...client.Option) (*ListSelectDocRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/ListSelectDoc")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("ListSelectDoc")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ListSelectDocRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) CreateCorpAndAssignPermission(ctx context.Context, req *CreateCorpAndAssignPermissionReq, opts ...client.Option) (*CreateCorpAndAssignPermissionRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/CreateCorpAndAssignPermission")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("CreateCorpAndAssignPermission")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &CreateCorpAndAssignPermissionRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) CheckCorpAndPermission(ctx context.Context, req *CheckCorpAndPermissionReq, opts ...client.Option) (*CheckCorpAndPermissionRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/CheckCorpAndPermission")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("CheckCorpAndPermission")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &CheckCorpAndPermissionRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) DescribeCropByUin(ctx context.Context, req *DescribeCropByUinReq, opts ...client.Option) (*DescribeCropByUinRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/DescribeCropByUin")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("DescribeCropByUin")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DescribeCropByUinRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) GetListModel(ctx context.Context, req *GetListModelReq, opts ...client.Option) (*GetListModelRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/GetListModel")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("GetListModel")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetListModelRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) ModifyAppInfosecBizType(ctx context.Context, req *ModifyAppInfosecBizTypeReq, opts ...client.Option) (*ModifyAppInfosecBizTypeRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/ModifyAppInfosecBizType")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("ModifyAppInfosecBizType")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ModifyAppInfosecBizTypeRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) GetTotalConcurrency(ctx context.Context, req *GetTotalConcurrencyReq, opts ...client.Option) (*GetTotalConcurrencyRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/GetTotalConcurrency")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("GetTotalConcurrency")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetTotalConcurrencyRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) MultiLock(ctx context.Context, req *MultiLockReq, opts ...client.Option) (*MultiLockRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/MultiLock")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("MultiLock")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &MultiLockRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) MultiUnlock(ctx context.Context, req *MultiUnlockReq, opts ...client.Option) (*MultiUnlockRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/MultiUnlock")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("MultiUnlock")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &MultiUnlockRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) CheckVarIsUsed(ctx context.Context, req *CheckVarIsUsedReq, opts ...client.Option) (*CheckVarIsUsedRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/CheckVarIsUsed")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("CheckVarIsUsed")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &CheckVarIsUsedRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) ModifyAppVar(ctx context.Context, req *ModifyAppVarReq, opts ...client.Option) (*ModifyAppVarRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/ModifyAppVar")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("ModifyAppVar")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ModifyAppVarRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) ClearAppKnowledgeCallback(ctx context.Context, req *ClearAppKnowledgeCallbackReq, opts ...client.Option) (*ClearAppKnowledgeCallbackRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/ClearAppKnowledgeCallback")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("ClearAppKnowledgeCallback")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ClearAppKnowledgeCallbackRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) ClearAppFlowCallback(ctx context.Context, req *ClearAppFlowCallbackReq, opts ...client.Option) (*ClearAppFlowCallbackRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/ClearAppFlowCallback")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("ClearAppFlowCallback")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ClearAppFlowCallbackRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) ClearAppVectorCallback(ctx context.Context, req *ClearAppVectorCallbackReq, opts ...client.Option) (*ClearAppVectorCallbackRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/ClearAppVectorCallback")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("ClearAppVectorCallback")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ClearAppVectorCallbackRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) ClearAppResourceCallback(ctx context.Context, req *ClearAppResourceCallbackReq, opts ...client.Option) (*ClearAppResourceCallbackRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/ClearAppResourceCallback")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("ClearAppResourceCallback")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ClearAppResourceCallbackRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) GetAppByPluginId(ctx context.Context, req *GetAppByPluginIdReq, opts ...client.Option) (*GetAppByPluginIdRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/GetAppByPluginId")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("GetAppByPluginId")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetAppByPluginIdRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) GetCorpStaffName(ctx context.Context, req *GetCorpStaffNameReq, opts ...client.Option) (*GetCorpStaffNameRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/GetCorpStaffName")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("GetCorpStaffName")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetCorpStaffNameRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) GetUserInfo(ctx context.Context, req *GetUserInfoReq, opts ...client.Option) (*GetUserInfoRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/GetUserInfo")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("GetUserInfo")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetUserInfoRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) GetModelFinanceInfo(ctx context.Context, req *GetModelFinanceInfoReq, opts ...client.Option) (*GetModelFinanceInfoRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/GetModelFinanceInfo")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("GetModelFinanceInfo")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetModelFinanceInfoRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) GetModelInfo(ctx context.Context, req *GetModelInfoReq, opts ...client.Option) (*GetModelInfoRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/GetModelInfo")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("GetModelInfo")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetModelInfoRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) GetValidExperienceApps(ctx context.Context, req *GetValidExperienceAppsReq, opts ...client.Option) (*GetValidExperienceAppsRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/GetValidExperienceApps")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("GetValidExperienceApps")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetValidExperienceAppsRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) GetModelList(ctx context.Context, req *GetModelListReq, opts ...client.Option) (*GetModelListRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/GetModelList")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("GetModelList")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetModelListRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) GetAppChatInputNum(ctx context.Context, req *GetAppChatInputNumReq, opts ...client.Option) (*GetAppChatInputNumRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/GetAppChatInputNum")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("GetAppChatInputNum")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetAppChatInputNumRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) CreatePromptVersion(ctx context.Context, req *CreatePromptVersionReq, opts ...client.Option) (*CreatePromptVersionRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/CreatePromptVersion")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("CreatePromptVersion")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &CreatePromptVersionRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) EditPromptVersion(ctx context.Context, req *EditPromptVersionReq, opts ...client.Option) (*EditPromptVersionRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/EditPromptVersion")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("EditPromptVersion")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &EditPromptVersionRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) GetPromptVersionList(ctx context.Context, req *GetPromptVersionListReq, opts ...client.Option) (*GetPromptVersionListRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/GetPromptVersionList")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("GetPromptVersionList")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetPromptVersionListRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) UpgradePromptVersion(ctx context.Context, req *UpgradePromptVersionReq, opts ...client.Option) (*UpgradePromptVersionRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/UpgradePromptVersion")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("UpgradePromptVersion")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &UpgradePromptVersionRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) CreateCorpConcurrencyWL(ctx context.Context, req *CreateCorpConcurrencyWLReq, opts ...client.Option) (*CreateCorpConcurrencyWLRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/CreateCorpConcurrencyWL")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("CreateCorpConcurrencyWL")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &CreateCorpConcurrencyWLRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) EditCorpConcurrencyWL(ctx context.Context, req *EditCorpConcurrencyWLReq, opts ...client.Option) (*EditCorpConcurrencyWLRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/EditCorpConcurrencyWL")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("EditCorpConcurrencyWL")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &EditCorpConcurrencyWLRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) DeleteCorpConcurrencyWL(ctx context.Context, req *DeleteCorpConcurrencyWLReq, opts ...client.Option) (*DeleteCorpConcurrencyWLRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/DeleteCorpConcurrencyWL")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("DeleteCorpConcurrencyWL")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DeleteCorpConcurrencyWLRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) GetCorpConcurrencyWLList(ctx context.Context, req *GetCorpConcurrencyWLListReq, opts ...client.Option) (*GetCorpConcurrencyWLListRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/GetCorpConcurrencyWLList")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("GetCorpConcurrencyWLList")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetCorpConcurrencyWLListRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) StartEmbeddingUpgradeApp(ctx context.Context, req *StartEmbeddingUpgradeAppReq, opts ...client.Option) (*StartEmbeddingUpgradeAppRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/StartEmbeddingUpgradeApp")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("StartEmbeddingUpgradeApp")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &StartEmbeddingUpgradeAppRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) FinishEmbeddingUpgradeApp(ctx context.Context, req *FinishEmbeddingUpgradeAppReq, opts ...client.Option) (*FinishEmbeddingUpgradeAppRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/FinishEmbeddingUpgradeApp")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("FinishEmbeddingUpgradeApp")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &FinishEmbeddingUpgradeAppRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) GetAppsByBizIDs(ctx context.Context, req *GetAppsByBizIDsReq, opts ...client.Option) (*GetAppsByBizIDsRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/GetAppsByBizIDs")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("GetAppsByBizIDs")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetAppsByBizIDsRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) GetCorpStaffsByBizIDs(ctx context.Context, req *GetCorpStaffsByBizIDsReq, opts ...client.Option) (*GetCorpStaffsByBizIDsRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/GetCorpStaffsByBizIDs")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("GetCorpStaffsByBizIDs")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetCorpStaffsByBizIDsRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) CopyAppConfig(ctx context.Context, req *CopyAppConfigReq, opts ...client.Option) (*CopyAppConfigRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/CopyAppConfig")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("CopyAppConfig")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &CopyAppConfigRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) EditCorpCustomModelWL(ctx context.Context, req *EditCorpCustomModelWLReq, opts ...client.Option) (*EditCorpCustomModelWLRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/EditCorpCustomModelWL")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("EditCorpCustomModelWL")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &EditCorpCustomModelWLRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) DeleteCorpCustomModelWL(ctx context.Context, req *DeleteCorpCustomModelWLReq, opts ...client.Option) (*DeleteCorpCustomModelWLRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/DeleteCorpCustomModelWL")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("DeleteCorpCustomModelWL")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DeleteCorpCustomModelWLRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) GetCorpCustomModelWLList(ctx context.Context, req *GetCorpCustomModelWLListReq, opts ...client.Option) (*GetCorpCustomModelWLListRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/GetCorpCustomModelWLList")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("GetCorpCustomModelWLList")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetCorpCustomModelWLListRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) GetCorpModelQpmTpmLimit(ctx context.Context, req *GetCorpModelQpmTpmLimitReq, opts ...client.Option) (*GetCorpModelQpmTpmLimitRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/GetCorpModelQpmTpmLimit")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("GetCorpModelQpmTpmLimit")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetCorpModelQpmTpmLimitRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) GetDefaultQpmTpmLimit(ctx context.Context, req *GetDefaultQpmTpmLimitReq, opts ...client.Option) (*GetDefaultQpmTpmLimitRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/GetDefaultQpmTpmLimit")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("GetDefaultQpmTpmLimit")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetDefaultQpmTpmLimitRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) EditCorpQpmTpmLimit(ctx context.Context, req *EditCorpQpmTpmLimitReq, opts ...client.Option) (*EditCorpQpmTpmLimitRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/EditCorpQpmTpmLimit")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("EditCorpQpmTpmLimit")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &EditCorpQpmTpmLimitRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) DeleteCorpQpmTpmLimit(ctx context.Context, req *DeleteCorpQpmTpmLimitReq, opts ...client.Option) (*DeleteCorpQpmTpmLimitRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/DeleteCorpQpmTpmLimit")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("DeleteCorpQpmTpmLimit")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DeleteCorpQpmTpmLimitRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) GetCorpQpmTpmLimitList(ctx context.Context, req *GetCorpQpmTpmLimitListReq, opts ...client.Option) (*GetCorpQpmTpmLimitListRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/GetCorpQpmTpmLimitList")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("GetCorpQpmTpmLimitList")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetCorpQpmTpmLimitListRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) CreateShareKnowledgeBaseApp(ctx context.Context, req *CreateShareKnowledgeBaseAppReq, opts ...client.Option) (*CreateShareKnowledgeBaseAppRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/CreateShareKnowledgeBaseApp")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("CreateShareKnowledgeBaseApp")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &CreateShareKnowledgeBaseAppRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) DeleteShareKnowledgeBaseApp(ctx context.Context, req *DeleteShareKnowledgeBaseAppReq, opts ...client.Option) (*DeleteShareKnowledgeBaseAppRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/DeleteShareKnowledgeBaseApp")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("DeleteShareKnowledgeBaseApp")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DeleteShareKnowledgeBaseAppRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) CopyAppCallback(ctx context.Context, req *CopyAppCallbackReq, opts ...client.Option) (*CopyAppCallbackRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/CopyAppCallback")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("CopyAppCallback")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &CopyAppCallbackRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) CreateApproval(ctx context.Context, req *CreateApprovalReq, opts ...client.Option) (*CreateApprovalRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/CreateApproval")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("CreateApproval")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &CreateApprovalRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) GetLastApproval(ctx context.Context, req *GetLastApprovalReq, opts ...client.Option) (*GetLastApprovalRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Api/GetLastApproval")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("GetLastApproval")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetLastApprovalRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

// END ======================================= Client Service Definition ======================================= END
