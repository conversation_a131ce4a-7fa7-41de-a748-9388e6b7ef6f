// Code generated by trpc-go/trpc-go-cmdline v2.8.30. DO NOT EDIT.
// source: channel-resource-convert-svr.proto

package channel_resource_convert_svr

import (
	"context"
	"errors"
	"fmt"

	_ "git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/codec"
	_ "git.code.oa.com/trpc-go/trpc-go/http"
	"git.code.oa.com/trpc-go/trpc-go/server"
)

// START ======================================= Server Service Definition ======================================= START

// ResourceManagerServiceService defines service.
type ResourceManagerServiceService interface {
	// ResourceConvert 资源转换接口
	ResourceConvert(ctx context.Context, req *ResourceConvertReq) (*ResourceConvertRsp, error)
	// ResourceUpload 资源上传接口
	ResourceUpload(ctx context.Context, req *ResourceUploadReq) (*ResourceUploadRsp, error)
	// ResourceDownload 资源下载接口
	ResourceDownload(ctx context.Context, req *ResourceDownloadReq) (*ResourceDownloadRsp, error)
}

func ResourceManagerServiceService_ResourceConvert_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ResourceConvertReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ResourceManagerServiceService).ResourceConvert(ctx, reqbody.(*ResourceConvertReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ResourceManagerServiceService_ResourceUpload_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ResourceUploadReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ResourceManagerServiceService).ResourceUpload(ctx, reqbody.(*ResourceUploadReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ResourceManagerServiceService_ResourceDownload_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ResourceDownloadReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ResourceManagerServiceService).ResourceDownload(ctx, reqbody.(*ResourceDownloadReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

// ResourceManagerServiceServer_ServiceDesc descriptor for server.RegisterService.
var ResourceManagerServiceServer_ServiceDesc = server.ServiceDesc{
	ServiceName: "trpc.KEP.channel_resource_convert_svr.ResourceManagerService",
	HandlerType: ((*ResourceManagerServiceService)(nil)),
	Methods: []server.Method{
		{
			Name: "/trpc.KEP.channel_resource_convert_svr.ResourceManagerService/ResourceConvert",
			Func: ResourceManagerServiceService_ResourceConvert_Handler,
		},
		{
			Name: "/trpc.KEP.channel_resource_convert_svr.ResourceManagerService/ResourceUpload",
			Func: ResourceManagerServiceService_ResourceUpload_Handler,
		},
		{
			Name: "/trpc.KEP.channel_resource_convert_svr.ResourceManagerService/ResourceDownload",
			Func: ResourceManagerServiceService_ResourceDownload_Handler,
		},
	},
}

// RegisterResourceManagerServiceService registers service.
func RegisterResourceManagerServiceService(s server.Service, svr ResourceManagerServiceService) {
	if err := s.Register(&ResourceManagerServiceServer_ServiceDesc, svr); err != nil {
		panic(fmt.Sprintf("ResourceManagerService register error:%v", err))
	}
}

// START --------------------------------- Default Unimplemented Server Service --------------------------------- START

type UnimplementedResourceManagerService struct{}

// ResourceConvert 资源转换接口
func (s *UnimplementedResourceManagerService) ResourceConvert(ctx context.Context, req *ResourceConvertReq) (*ResourceConvertRsp, error) {
	return nil, errors.New("rpc ResourceConvert of service ResourceManagerService is not implemented")
}

// ResourceUpload 资源上传接口
func (s *UnimplementedResourceManagerService) ResourceUpload(ctx context.Context, req *ResourceUploadReq) (*ResourceUploadRsp, error) {
	return nil, errors.New("rpc ResourceUpload of service ResourceManagerService is not implemented")
}

// ResourceDownload 资源下载接口
func (s *UnimplementedResourceManagerService) ResourceDownload(ctx context.Context, req *ResourceDownloadReq) (*ResourceDownloadRsp, error) {
	return nil, errors.New("rpc ResourceDownload of service ResourceManagerService is not implemented")
}

// END --------------------------------- Default Unimplemented Server Service --------------------------------- END

// END ======================================= Server Service Definition ======================================= END

// START ======================================= Client Service Definition ======================================= START

// ResourceManagerServiceClientProxy defines service client proxy
type ResourceManagerServiceClientProxy interface {
	// ResourceConvert 资源转换接口
	ResourceConvert(ctx context.Context, req *ResourceConvertReq, opts ...client.Option) (rsp *ResourceConvertRsp, err error)

	// ResourceUpload 资源上传接口
	ResourceUpload(ctx context.Context, req *ResourceUploadReq, opts ...client.Option) (rsp *ResourceUploadRsp, err error)

	// ResourceDownload 资源下载接口
	ResourceDownload(ctx context.Context, req *ResourceDownloadReq, opts ...client.Option) (rsp *ResourceDownloadRsp, err error)
}

type ResourceManagerServiceClientProxyImpl struct {
	client client.Client
	opts   []client.Option
}

var NewResourceManagerServiceClientProxy = func(opts ...client.Option) ResourceManagerServiceClientProxy {
	return &ResourceManagerServiceClientProxyImpl{client: client.DefaultClient, opts: opts}
}

func (c *ResourceManagerServiceClientProxyImpl) ResourceConvert(ctx context.Context, req *ResourceConvertReq, opts ...client.Option) (*ResourceConvertRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.channel_resource_convert_svr.ResourceManagerService/ResourceConvert")
	msg.WithCalleeServiceName(ResourceManagerServiceServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("channel_resource_convert_svr")
	msg.WithCalleeService("ResourceManagerService")
	msg.WithCalleeMethod("ResourceConvert")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ResourceConvertRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ResourceManagerServiceClientProxyImpl) ResourceUpload(ctx context.Context, req *ResourceUploadReq, opts ...client.Option) (*ResourceUploadRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.channel_resource_convert_svr.ResourceManagerService/ResourceUpload")
	msg.WithCalleeServiceName(ResourceManagerServiceServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("channel_resource_convert_svr")
	msg.WithCalleeService("ResourceManagerService")
	msg.WithCalleeMethod("ResourceUpload")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ResourceUploadRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ResourceManagerServiceClientProxyImpl) ResourceDownload(ctx context.Context, req *ResourceDownloadReq, opts ...client.Option) (*ResourceDownloadRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.channel_resource_convert_svr.ResourceManagerService/ResourceDownload")
	msg.WithCalleeServiceName(ResourceManagerServiceServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("channel_resource_convert_svr")
	msg.WithCalleeService("ResourceManagerService")
	msg.WithCalleeMethod("ResourceDownload")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ResourceDownloadRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

// END ======================================= Client Service Definition ======================================= END
