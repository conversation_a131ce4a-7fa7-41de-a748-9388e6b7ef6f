// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        v3.13.0
// source: channel-resource-convert-svr.proto

package channel_resource_convert_svr

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ResourceType int32

const (
	ResourceType_RT_DEFAULT   ResourceType = 0 //默认
	ResourceType_RT_LONG_TEXT ResourceType = 1 //长文本
	ResourceType_RT_IMAGE     ResourceType = 2 //图片
	ResourceType_RT_VIDEO     ResourceType = 3 //视频
	ResourceType_RT_FILE      ResourceType = 4 //文件
	ResourceType_RT_VOICE     ResourceType = 5 //语音
)

// Enum value maps for ResourceType.
var (
	ResourceType_name = map[int32]string{
		0: "RT_DEFAULT",
		1: "RT_LONG_TEXT",
		2: "RT_IMAGE",
		3: "RT_VIDEO",
		4: "RT_FILE",
		5: "RT_VOICE",
	}
	ResourceType_value = map[string]int32{
		"RT_DEFAULT":   0,
		"RT_LONG_TEXT": 1,
		"RT_IMAGE":     2,
		"RT_VIDEO":     3,
		"RT_FILE":      4,
		"RT_VOICE":     5,
	}
)

func (x ResourceType) Enum() *ResourceType {
	p := new(ResourceType)
	*p = x
	return p
}

func (x ResourceType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ResourceType) Descriptor() protoreflect.EnumDescriptor {
	return file_channel_resource_convert_svr_proto_enumTypes[0].Descriptor()
}

func (ResourceType) Type() protoreflect.EnumType {
	return &file_channel_resource_convert_svr_proto_enumTypes[0]
}

func (x ResourceType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ResourceType.Descriptor instead.
func (ResourceType) EnumDescriptor() ([]byte, []int) {
	return file_channel_resource_convert_svr_proto_rawDescGZIP(), []int{0}
}

type ResourceReqComm struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	C2BDirection       *bool   `protobuf:"varint,1,opt,name=c2b_direction,json=c2bDirection,proto3,oneof" json:"c2b_direction,omitempty"`                     //c2b方向标识
	B2CDirection       *bool   `protobuf:"varint,2,opt,name=b2c_direction,json=b2cDirection,proto3,oneof" json:"b2c_direction,omitempty"`                     //b2c方向标识
	StrResourceId      *string `protobuf:"bytes,3,opt,name=str_resource_id,json=strResourceId,proto3,oneof" json:"str_resource_id,omitempty"`                 //资源id，可能是微信的media-id
	Uint32ResourceType *uint32 `protobuf:"varint,4,opt,name=uint32_resource_type,json=uint32ResourceType,proto3,oneof" json:"uint32_resource_type,omitempty"` //参考ResourceType
	ChannelType        *uint32 `protobuf:"varint,5,opt,name=channel_type,json=channelType,proto3,oneof" json:"channel_type,omitempty"`                        //渠道id  10001为公众号 10002为企微应用 10003为网页组件 10004为微信客服 10005为小程序
	UserId             *string `protobuf:"bytes,6,opt,name=user_id,json=userId,proto3,oneof" json:"user_id,omitempty"`                                        // 第三方用户id  上传cos生成uuid用 格式为wx/{用户ID}/{应用ID}/test.png
	AppBizId           *uint64 `protobuf:"varint,7,opt,name=app_biz_id,json=appBizId,proto3,oneof" json:"app_biz_id,omitempty"`                               // 应用id       上传cos生成uuid用
	Domain             *string `protobuf:"bytes,8,opt,name=domain,proto3,oneof" json:"domain,omitempty"`                                                      // 企微用户自己配置的host,资源转换的时候要通过用户的ng请求企微下载资源
}

func (x *ResourceReqComm) Reset() {
	*x = ResourceReqComm{}
	if protoimpl.UnsafeEnabled {
		mi := &file_channel_resource_convert_svr_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResourceReqComm) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResourceReqComm) ProtoMessage() {}

func (x *ResourceReqComm) ProtoReflect() protoreflect.Message {
	mi := &file_channel_resource_convert_svr_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResourceReqComm.ProtoReflect.Descriptor instead.
func (*ResourceReqComm) Descriptor() ([]byte, []int) {
	return file_channel_resource_convert_svr_proto_rawDescGZIP(), []int{0}
}

func (x *ResourceReqComm) GetC2BDirection() bool {
	if x != nil && x.C2BDirection != nil {
		return *x.C2BDirection
	}
	return false
}

func (x *ResourceReqComm) GetB2CDirection() bool {
	if x != nil && x.B2CDirection != nil {
		return *x.B2CDirection
	}
	return false
}

func (x *ResourceReqComm) GetStrResourceId() string {
	if x != nil && x.StrResourceId != nil {
		return *x.StrResourceId
	}
	return ""
}

func (x *ResourceReqComm) GetUint32ResourceType() uint32 {
	if x != nil && x.Uint32ResourceType != nil {
		return *x.Uint32ResourceType
	}
	return 0
}

func (x *ResourceReqComm) GetChannelType() uint32 {
	if x != nil && x.ChannelType != nil {
		return *x.ChannelType
	}
	return 0
}

func (x *ResourceReqComm) GetUserId() string {
	if x != nil && x.UserId != nil {
		return *x.UserId
	}
	return ""
}

func (x *ResourceReqComm) GetAppBizId() uint64 {
	if x != nil && x.AppBizId != nil {
		return *x.AppBizId
	}
	return 0
}

func (x *ResourceReqComm) GetDomain() string {
	if x != nil && x.Domain != nil {
		return *x.Domain
	}
	return ""
}

type ResourceConvertReqWxExt struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StrAccessToken *string `protobuf:"bytes,1,opt,name=str_access_token,json=strAccessToken,proto3,oneof" json:"str_access_token,omitempty"` //微信需要传token,不用再去调用rpc获取
}

func (x *ResourceConvertReqWxExt) Reset() {
	*x = ResourceConvertReqWxExt{}
	if protoimpl.UnsafeEnabled {
		mi := &file_channel_resource_convert_svr_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResourceConvertReqWxExt) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResourceConvertReqWxExt) ProtoMessage() {}

func (x *ResourceConvertReqWxExt) ProtoReflect() protoreflect.Message {
	mi := &file_channel_resource_convert_svr_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResourceConvertReqWxExt.ProtoReflect.Descriptor instead.
func (*ResourceConvertReqWxExt) Descriptor() ([]byte, []int) {
	return file_channel_resource_convert_svr_proto_rawDescGZIP(), []int{1}
}

func (x *ResourceConvertReqWxExt) GetStrAccessToken() string {
	if x != nil && x.StrAccessToken != nil {
		return *x.StrAccessToken
	}
	return ""
}

type ResourceConvertReqExt struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MsgReqWxExt *ResourceConvertReqWxExt `protobuf:"bytes,1,opt,name=msg_req_wx_ext,json=msgReqWxExt,proto3" json:"msg_req_wx_ext,omitempty"` //微信相关ext字段
}

func (x *ResourceConvertReqExt) Reset() {
	*x = ResourceConvertReqExt{}
	if protoimpl.UnsafeEnabled {
		mi := &file_channel_resource_convert_svr_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResourceConvertReqExt) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResourceConvertReqExt) ProtoMessage() {}

func (x *ResourceConvertReqExt) ProtoReflect() protoreflect.Message {
	mi := &file_channel_resource_convert_svr_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResourceConvertReqExt.ProtoReflect.Descriptor instead.
func (*ResourceConvertReqExt) Descriptor() ([]byte, []int) {
	return file_channel_resource_convert_svr_proto_rawDescGZIP(), []int{2}
}

func (x *ResourceConvertReqExt) GetMsgReqWxExt() *ResourceConvertReqWxExt {
	if x != nil {
		return x.MsgReqWxExt
	}
	return nil
}

// 资源转换请求
type ResourceConvertReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ComReq *ResourceReqComm       `protobuf:"bytes,1,opt,name=com_req,json=comReq,proto3,oneof" json:"com_req,omitempty"` //资源服务请求通用字段
	ExtReq *ResourceConvertReqExt `protobuf:"bytes,2,opt,name=ext_req,json=extReq,proto3,oneof" json:"ext_req,omitempty"` //资源服务请求额外需要的字段，通常是某些场景需要
}

func (x *ResourceConvertReq) Reset() {
	*x = ResourceConvertReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_channel_resource_convert_svr_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResourceConvertReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResourceConvertReq) ProtoMessage() {}

func (x *ResourceConvertReq) ProtoReflect() protoreflect.Message {
	mi := &file_channel_resource_convert_svr_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResourceConvertReq.ProtoReflect.Descriptor instead.
func (*ResourceConvertReq) Descriptor() ([]byte, []int) {
	return file_channel_resource_convert_svr_proto_rawDescGZIP(), []int{3}
}

func (x *ResourceConvertReq) GetComReq() *ResourceReqComm {
	if x != nil {
		return x.ComReq
	}
	return nil
}

func (x *ResourceConvertReq) GetExtReq() *ResourceConvertReqExt {
	if x != nil {
		return x.ExtReq
	}
	return nil
}

type ResourceRspComm struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StrResourceIdRsp *string `protobuf:"bytes,1,opt,name=str_resource_id_rsp,json=strResourceIdRsp,proto3,oneof" json:"str_resource_id_rsp,omitempty"` //转换之后的资源id
	StrMd5           *string `protobuf:"bytes,2,opt,name=str_md5,json=strMd5,proto3,oneof" json:"str_md5,omitempty"`                                   //转换之后的资源md5值
	StrFileName      *string `protobuf:"bytes,3,opt,name=str_file_name,json=strFileName,proto3,oneof" json:"str_file_name,omitempty"`                  //资源的文件名
	Uint32FileSize   *uint32 `protobuf:"varint,4,opt,name=uint32_file_size,json=uint32FileSize,proto3,oneof" json:"uint32_file_size,omitempty"`        //资源大小
	StrUrl           *string `protobuf:"bytes,5,opt,name=str_url,json=strUrl,proto3,oneof" json:"str_url,omitempty"`                                   //转换之后的资源url，有些资源需要填
	Uint32ResType    *uint32 `protobuf:"varint,6,opt,name=uint32_res_type,json=uint32ResType,proto3,oneof" json:"uint32_res_type,omitempty"`           //资源type
}

func (x *ResourceRspComm) Reset() {
	*x = ResourceRspComm{}
	if protoimpl.UnsafeEnabled {
		mi := &file_channel_resource_convert_svr_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResourceRspComm) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResourceRspComm) ProtoMessage() {}

func (x *ResourceRspComm) ProtoReflect() protoreflect.Message {
	mi := &file_channel_resource_convert_svr_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResourceRspComm.ProtoReflect.Descriptor instead.
func (*ResourceRspComm) Descriptor() ([]byte, []int) {
	return file_channel_resource_convert_svr_proto_rawDescGZIP(), []int{4}
}

func (x *ResourceRspComm) GetStrResourceIdRsp() string {
	if x != nil && x.StrResourceIdRsp != nil {
		return *x.StrResourceIdRsp
	}
	return ""
}

func (x *ResourceRspComm) GetStrMd5() string {
	if x != nil && x.StrMd5 != nil {
		return *x.StrMd5
	}
	return ""
}

func (x *ResourceRspComm) GetStrFileName() string {
	if x != nil && x.StrFileName != nil {
		return *x.StrFileName
	}
	return ""
}

func (x *ResourceRspComm) GetUint32FileSize() uint32 {
	if x != nil && x.Uint32FileSize != nil {
		return *x.Uint32FileSize
	}
	return 0
}

func (x *ResourceRspComm) GetStrUrl() string {
	if x != nil && x.StrUrl != nil {
		return *x.StrUrl
	}
	return ""
}

func (x *ResourceRspComm) GetUint32ResType() uint32 {
	if x != nil && x.Uint32ResType != nil {
		return *x.Uint32ResType
	}
	return 0
}

// 资源转换返回
type ResourceConvertRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ComRsp *ResourceRspComm `protobuf:"bytes,1,opt,name=com_rsp,json=comRsp,proto3,oneof" json:"com_rsp,omitempty"` //资源服务返回通用字段
}

func (x *ResourceConvertRsp) Reset() {
	*x = ResourceConvertRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_channel_resource_convert_svr_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResourceConvertRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResourceConvertRsp) ProtoMessage() {}

func (x *ResourceConvertRsp) ProtoReflect() protoreflect.Message {
	mi := &file_channel_resource_convert_svr_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResourceConvertRsp.ProtoReflect.Descriptor instead.
func (*ResourceConvertRsp) Descriptor() ([]byte, []int) {
	return file_channel_resource_convert_svr_proto_rawDescGZIP(), []int{5}
}

func (x *ResourceConvertRsp) GetComRsp() *ResourceRspComm {
	if x != nil {
		return x.ComRsp
	}
	return nil
}

// 下载接口
type ResourceDownloadReqExt struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StrAccessToken *string `protobuf:"bytes,1,opt,name=str_access_token,json=strAccessToken,proto3,oneof" json:"str_access_token,omitempty"` //微信需要传token,不用再去调用rpc获取
}

func (x *ResourceDownloadReqExt) Reset() {
	*x = ResourceDownloadReqExt{}
	if protoimpl.UnsafeEnabled {
		mi := &file_channel_resource_convert_svr_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResourceDownloadReqExt) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResourceDownloadReqExt) ProtoMessage() {}

func (x *ResourceDownloadReqExt) ProtoReflect() protoreflect.Message {
	mi := &file_channel_resource_convert_svr_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResourceDownloadReqExt.ProtoReflect.Descriptor instead.
func (*ResourceDownloadReqExt) Descriptor() ([]byte, []int) {
	return file_channel_resource_convert_svr_proto_rawDescGZIP(), []int{6}
}

func (x *ResourceDownloadReqExt) GetStrAccessToken() string {
	if x != nil && x.StrAccessToken != nil {
		return *x.StrAccessToken
	}
	return ""
}

// 资源下载请求
type ResourceDownloadReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ComReq *ResourceReqComm        `protobuf:"bytes,1,opt,name=com_req,json=comReq,proto3,oneof" json:"com_req,omitempty"` //资源服务请求常用字段
	ExtReq *ResourceDownloadReqExt `protobuf:"bytes,2,opt,name=ext_req,json=extReq,proto3,oneof" json:"ext_req,omitempty"` //资源服务请求额外需要的字段，通常是某些场景需要
}

func (x *ResourceDownloadReq) Reset() {
	*x = ResourceDownloadReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_channel_resource_convert_svr_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResourceDownloadReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResourceDownloadReq) ProtoMessage() {}

func (x *ResourceDownloadReq) ProtoReflect() protoreflect.Message {
	mi := &file_channel_resource_convert_svr_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResourceDownloadReq.ProtoReflect.Descriptor instead.
func (*ResourceDownloadReq) Descriptor() ([]byte, []int) {
	return file_channel_resource_convert_svr_proto_rawDescGZIP(), []int{7}
}

func (x *ResourceDownloadReq) GetComReq() *ResourceReqComm {
	if x != nil {
		return x.ComReq
	}
	return nil
}

func (x *ResourceDownloadReq) GetExtReq() *ResourceDownloadReqExt {
	if x != nil {
		return x.ExtReq
	}
	return nil
}

// 下载的长文本内容
type LongText struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BytesText []byte `protobuf:"bytes,1,opt,name=bytes_text,json=bytesText,proto3,oneof" json:"bytes_text,omitempty"` //下载的长文本内容
}

func (x *LongText) Reset() {
	*x = LongText{}
	if protoimpl.UnsafeEnabled {
		mi := &file_channel_resource_convert_svr_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LongText) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LongText) ProtoMessage() {}

func (x *LongText) ProtoReflect() protoreflect.Message {
	mi := &file_channel_resource_convert_svr_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LongText.ProtoReflect.Descriptor instead.
func (*LongText) Descriptor() ([]byte, []int) {
	return file_channel_resource_convert_svr_proto_rawDescGZIP(), []int{8}
}

func (x *LongText) GetBytesText() []byte {
	if x != nil {
		return x.BytesText
	}
	return nil
}

type FileResource struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BytesFileContext []byte  `protobuf:"bytes,1,opt,name=bytes_file_context,json=bytesFileContext,proto3,oneof" json:"bytes_file_context,omitempty"` //文件内容
	StrMd5           *string `protobuf:"bytes,2,opt,name=str_md5,json=strMd5,proto3,oneof" json:"str_md5,omitempty"`                                 //文件的MD5
	StrFileName      *string `protobuf:"bytes,3,opt,name=str_file_name,json=strFileName,proto3,oneof" json:"str_file_name,omitempty"`                //文件名
	Uint32FileSize   *uint32 `protobuf:"varint,4,opt,name=uint32_file_size,json=uint32FileSize,proto3,oneof" json:"uint32_file_size,omitempty"`      //文件大小
}

func (x *FileResource) Reset() {
	*x = FileResource{}
	if protoimpl.UnsafeEnabled {
		mi := &file_channel_resource_convert_svr_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FileResource) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileResource) ProtoMessage() {}

func (x *FileResource) ProtoReflect() protoreflect.Message {
	mi := &file_channel_resource_convert_svr_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileResource.ProtoReflect.Descriptor instead.
func (*FileResource) Descriptor() ([]byte, []int) {
	return file_channel_resource_convert_svr_proto_rawDescGZIP(), []int{9}
}

func (x *FileResource) GetBytesFileContext() []byte {
	if x != nil {
		return x.BytesFileContext
	}
	return nil
}

func (x *FileResource) GetStrMd5() string {
	if x != nil && x.StrMd5 != nil {
		return *x.StrMd5
	}
	return ""
}

func (x *FileResource) GetStrFileName() string {
	if x != nil && x.StrFileName != nil {
		return *x.StrFileName
	}
	return ""
}

func (x *FileResource) GetUint32FileSize() uint32 {
	if x != nil && x.Uint32FileSize != nil {
		return *x.Uint32FileSize
	}
	return 0
}

// 资源下载返回
type ResourceDownloadRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ComRsp      *ResourceRspComm `protobuf:"bytes,1,opt,name=com_rsp,json=comRsp,proto3,oneof" json:"com_rsp,omitempty"`                  //资源服务返回通用字段
	MsgLongText *LongText        `protobuf:"bytes,2,opt,name=msg_long_text,json=msgLongText,proto3,oneof" json:"msg_long_text,omitempty"` //下载的长文本内容
}

func (x *ResourceDownloadRsp) Reset() {
	*x = ResourceDownloadRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_channel_resource_convert_svr_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResourceDownloadRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResourceDownloadRsp) ProtoMessage() {}

func (x *ResourceDownloadRsp) ProtoReflect() protoreflect.Message {
	mi := &file_channel_resource_convert_svr_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResourceDownloadRsp.ProtoReflect.Descriptor instead.
func (*ResourceDownloadRsp) Descriptor() ([]byte, []int) {
	return file_channel_resource_convert_svr_proto_rawDescGZIP(), []int{10}
}

func (x *ResourceDownloadRsp) GetComRsp() *ResourceRspComm {
	if x != nil {
		return x.ComRsp
	}
	return nil
}

func (x *ResourceDownloadRsp) GetMsgLongText() *LongText {
	if x != nil {
		return x.MsgLongText
	}
	return nil
}

// 上传接口
type ResourceUploadReqExt struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StrAccessToken *string `protobuf:"bytes,1,opt,name=str_access_token,json=strAccessToken,proto3,oneof" json:"str_access_token,omitempty"` //微信需要传token,不用再去调用rpc获取
	StrText        *string `protobuf:"bytes,2,opt,name=str_text,json=strText,proto3,oneof" json:"str_text,omitempty"`                        //用于上传长文本/小程序卡片的时候
	BytesText      []byte  `protobuf:"bytes,3,opt,name=bytes_text,json=bytesText,proto3,oneof" json:"bytes_text,omitempty"`                  //pc消息 要封成pb
}

func (x *ResourceUploadReqExt) Reset() {
	*x = ResourceUploadReqExt{}
	if protoimpl.UnsafeEnabled {
		mi := &file_channel_resource_convert_svr_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResourceUploadReqExt) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResourceUploadReqExt) ProtoMessage() {}

func (x *ResourceUploadReqExt) ProtoReflect() protoreflect.Message {
	mi := &file_channel_resource_convert_svr_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResourceUploadReqExt.ProtoReflect.Descriptor instead.
func (*ResourceUploadReqExt) Descriptor() ([]byte, []int) {
	return file_channel_resource_convert_svr_proto_rawDescGZIP(), []int{11}
}

func (x *ResourceUploadReqExt) GetStrAccessToken() string {
	if x != nil && x.StrAccessToken != nil {
		return *x.StrAccessToken
	}
	return ""
}

func (x *ResourceUploadReqExt) GetStrText() string {
	if x != nil && x.StrText != nil {
		return *x.StrText
	}
	return ""
}

func (x *ResourceUploadReqExt) GetBytesText() []byte {
	if x != nil {
		return x.BytesText
	}
	return nil
}

// 资源上传请求
type ResourceUploadReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ComReq *ResourceReqComm      `protobuf:"bytes,1,opt,name=com_req,json=comReq,proto3,oneof" json:"com_req,omitempty"` //资源服务请求常用字段
	ExtReq *ResourceUploadReqExt `protobuf:"bytes,2,opt,name=ext_req,json=extReq,proto3,oneof" json:"ext_req,omitempty"` //资源服务请求额外需要的字段，通常是某些场景需要
}

func (x *ResourceUploadReq) Reset() {
	*x = ResourceUploadReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_channel_resource_convert_svr_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResourceUploadReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResourceUploadReq) ProtoMessage() {}

func (x *ResourceUploadReq) ProtoReflect() protoreflect.Message {
	mi := &file_channel_resource_convert_svr_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResourceUploadReq.ProtoReflect.Descriptor instead.
func (*ResourceUploadReq) Descriptor() ([]byte, []int) {
	return file_channel_resource_convert_svr_proto_rawDescGZIP(), []int{12}
}

func (x *ResourceUploadReq) GetComReq() *ResourceReqComm {
	if x != nil {
		return x.ComReq
	}
	return nil
}

func (x *ResourceUploadReq) GetExtReq() *ResourceUploadReqExt {
	if x != nil {
		return x.ExtReq
	}
	return nil
}

// 资源上传返回
type ResourceUploadRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ComRsp *ResourceRspComm `protobuf:"bytes,1,opt,name=com_rsp,json=comRsp,proto3,oneof" json:"com_rsp,omitempty"` //资源服务返回通用字段
}

func (x *ResourceUploadRsp) Reset() {
	*x = ResourceUploadRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_channel_resource_convert_svr_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResourceUploadRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResourceUploadRsp) ProtoMessage() {}

func (x *ResourceUploadRsp) ProtoReflect() protoreflect.Message {
	mi := &file_channel_resource_convert_svr_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResourceUploadRsp.ProtoReflect.Descriptor instead.
func (*ResourceUploadRsp) Descriptor() ([]byte, []int) {
	return file_channel_resource_convert_svr_proto_rawDescGZIP(), []int{13}
}

func (x *ResourceUploadRsp) GetComRsp() *ResourceRspComm {
	if x != nil {
		return x.ComRsp
	}
	return nil
}

var File_channel_resource_convert_svr_proto protoreflect.FileDescriptor

var file_channel_resource_convert_svr_proto_rawDesc = []byte{
	0x0a, 0x22, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x2d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x2d, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x74, 0x2d, 0x73, 0x76, 0x72, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x25, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x63,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f,
	0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x74, 0x5f, 0x73, 0x76, 0x72, 0x22, 0xd7, 0x03, 0x0a, 0x0f,
	0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x65, 0x71, 0x43, 0x6f, 0x6d, 0x6d, 0x12,
	0x28, 0x0a, 0x0d, 0x63, 0x32, 0x62, 0x5f, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x0c, 0x63, 0x32, 0x62, 0x44, 0x69, 0x72,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x28, 0x0a, 0x0d, 0x62, 0x32, 0x63,
	0x5f, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08,
	0x48, 0x01, 0x52, 0x0c, 0x62, 0x32, 0x63, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x88, 0x01, 0x01, 0x12, 0x2b, 0x0a, 0x0f, 0x73, 0x74, 0x72, 0x5f, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x48, 0x02, 0x52, 0x0d,
	0x73, 0x74, 0x72, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x88, 0x01, 0x01,
	0x12, 0x35, 0x0a, 0x14, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x5f, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x48, 0x03,
	0x52, 0x12, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x26, 0x0a, 0x0c, 0x63, 0x68, 0x61, 0x6e, 0x6e,
	0x65, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x48, 0x04, 0x52,
	0x0b, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12,
	0x1c, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x48, 0x05, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x21, 0x0a,
	0x0a, 0x61, 0x70, 0x70, 0x5f, 0x62, 0x69, 0x7a, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x04, 0x48, 0x06, 0x52, 0x08, 0x61, 0x70, 0x70, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x88, 0x01, 0x01,
	0x12, 0x1b, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09,
	0x48, 0x07, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x88, 0x01, 0x01, 0x42, 0x10, 0x0a,
	0x0e, 0x5f, 0x63, 0x32, 0x62, 0x5f, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x42,
	0x10, 0x0a, 0x0e, 0x5f, 0x62, 0x32, 0x63, 0x5f, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x73, 0x74, 0x72, 0x5f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x5f, 0x69, 0x64, 0x42, 0x17, 0x0a, 0x15, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32,
	0x5f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x42, 0x0f,
	0x0a, 0x0d, 0x5f, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x42,
	0x0a, 0x0a, 0x08, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x42, 0x0d, 0x0a, 0x0b, 0x5f,
	0x61, 0x70, 0x70, 0x5f, 0x62, 0x69, 0x7a, 0x5f, 0x69, 0x64, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x64,
	0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x22, 0x5d, 0x0a, 0x17, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x74, 0x52, 0x65, 0x71, 0x57, 0x78, 0x45, 0x78, 0x74,
	0x12, 0x2d, 0x0a, 0x10, 0x73, 0x74, 0x72, 0x5f, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x74,
	0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0e, 0x73, 0x74,
	0x72, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x88, 0x01, 0x01, 0x42,
	0x13, 0x0a, 0x11, 0x5f, 0x73, 0x74, 0x72, 0x5f, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x74,
	0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x7c, 0x0a, 0x15, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x74, 0x52, 0x65, 0x71, 0x45, 0x78, 0x74, 0x12, 0x63, 0x0a,
	0x0e, 0x6d, 0x73, 0x67, 0x5f, 0x72, 0x65, 0x71, 0x5f, 0x77, 0x78, 0x5f, 0x65, 0x78, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3e, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50,
	0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x74, 0x5f, 0x73, 0x76, 0x72, 0x2e, 0x52, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x74, 0x52, 0x65, 0x71,
	0x57, 0x78, 0x45, 0x78, 0x74, 0x52, 0x0b, 0x6d, 0x73, 0x67, 0x52, 0x65, 0x71, 0x57, 0x78, 0x45,
	0x78, 0x74, 0x22, 0xde, 0x01, 0x0a, 0x12, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x43,
	0x6f, 0x6e, 0x76, 0x65, 0x72, 0x74, 0x52, 0x65, 0x71, 0x12, 0x54, 0x0a, 0x07, 0x63, 0x6f, 0x6d,
	0x5f, 0x72, 0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x74, 0x72, 0x70,
	0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x74, 0x5f, 0x73,
	0x76, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x65, 0x71, 0x43, 0x6f,
	0x6d, 0x6d, 0x48, 0x00, 0x52, 0x06, 0x63, 0x6f, 0x6d, 0x52, 0x65, 0x71, 0x88, 0x01, 0x01, 0x12,
	0x5a, 0x0a, 0x07, 0x65, 0x78, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x3c, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x63, 0x68, 0x61, 0x6e,
	0x6e, 0x65, 0x6c, 0x5f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x6e,
	0x76, 0x65, 0x72, 0x74, 0x5f, 0x73, 0x76, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x74, 0x52, 0x65, 0x71, 0x45, 0x78, 0x74, 0x48, 0x01,
	0x52, 0x06, 0x65, 0x78, 0x74, 0x52, 0x65, 0x71, 0x88, 0x01, 0x01, 0x42, 0x0a, 0x0a, 0x08, 0x5f,
	0x63, 0x6f, 0x6d, 0x5f, 0x72, 0x65, 0x71, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x65, 0x78, 0x74, 0x5f,
	0x72, 0x65, 0x71, 0x22, 0xf1, 0x02, 0x0a, 0x0f, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x52, 0x73, 0x70, 0x43, 0x6f, 0x6d, 0x6d, 0x12, 0x32, 0x0a, 0x13, 0x73, 0x74, 0x72, 0x5f, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x5f, 0x72, 0x73, 0x70, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x10, 0x73, 0x74, 0x72, 0x52, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x49, 0x64, 0x52, 0x73, 0x70, 0x88, 0x01, 0x01, 0x12, 0x1c, 0x0a, 0x07, 0x73,
	0x74, 0x72, 0x5f, 0x6d, 0x64, 0x35, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x06,
	0x73, 0x74, 0x72, 0x4d, 0x64, 0x35, 0x88, 0x01, 0x01, 0x12, 0x27, 0x0a, 0x0d, 0x73, 0x74, 0x72,
	0x5f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x48, 0x02, 0x52, 0x0b, 0x73, 0x74, 0x72, 0x46, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x88,
	0x01, 0x01, 0x12, 0x2d, 0x0a, 0x10, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x5f, 0x66, 0x69, 0x6c,
	0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x48, 0x03, 0x52, 0x0e,
	0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x46, 0x69, 0x6c, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x88, 0x01,
	0x01, 0x12, 0x1c, 0x0a, 0x07, 0x73, 0x74, 0x72, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x48, 0x04, 0x52, 0x06, 0x73, 0x74, 0x72, 0x55, 0x72, 0x6c, 0x88, 0x01, 0x01, 0x12,
	0x2b, 0x0a, 0x0f, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x5f, 0x72, 0x65, 0x73, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x48, 0x05, 0x52, 0x0d, 0x75, 0x69, 0x6e, 0x74,
	0x33, 0x32, 0x52, 0x65, 0x73, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x42, 0x16, 0x0a, 0x14,
	0x5f, 0x73, 0x74, 0x72, 0x5f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64,
	0x5f, 0x72, 0x73, 0x70, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x73, 0x74, 0x72, 0x5f, 0x6d, 0x64, 0x35,
	0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x73, 0x74, 0x72, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x42, 0x13, 0x0a, 0x11, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x5f, 0x66, 0x69,
	0x6c, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x73, 0x74, 0x72, 0x5f,
	0x75, 0x72, 0x6c, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x5f, 0x72,
	0x65, 0x73, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x22, 0x76, 0x0a, 0x12, 0x52, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x74, 0x52, 0x73, 0x70, 0x12, 0x54, 0x0a,
	0x07, 0x63, 0x6f, 0x6d, 0x5f, 0x72, 0x73, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36,
	0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x5f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x76, 0x65,
	0x72, 0x74, 0x5f, 0x73, 0x76, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52,
	0x73, 0x70, 0x43, 0x6f, 0x6d, 0x6d, 0x48, 0x00, 0x52, 0x06, 0x63, 0x6f, 0x6d, 0x52, 0x73, 0x70,
	0x88, 0x01, 0x01, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x63, 0x6f, 0x6d, 0x5f, 0x72, 0x73, 0x70, 0x22,
	0x5c, 0x0a, 0x16, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x44, 0x6f, 0x77, 0x6e, 0x6c,
	0x6f, 0x61, 0x64, 0x52, 0x65, 0x71, 0x45, 0x78, 0x74, 0x12, 0x2d, 0x0a, 0x10, 0x73, 0x74, 0x72,
	0x5f, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0e, 0x73, 0x74, 0x72, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x88, 0x01, 0x01, 0x42, 0x13, 0x0a, 0x11, 0x5f, 0x73, 0x74, 0x72,
	0x5f, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0xe0, 0x01,
	0x0a, 0x13, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f,
	0x61, 0x64, 0x52, 0x65, 0x71, 0x12, 0x54, 0x0a, 0x07, 0x63, 0x6f, 0x6d, 0x5f, 0x72, 0x65, 0x71,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45,
	0x50, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x74, 0x5f, 0x73, 0x76, 0x72, 0x2e, 0x52,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x65, 0x71, 0x43, 0x6f, 0x6d, 0x6d, 0x48, 0x00,
	0x52, 0x06, 0x63, 0x6f, 0x6d, 0x52, 0x65, 0x71, 0x88, 0x01, 0x01, 0x12, 0x5b, 0x0a, 0x07, 0x65,
	0x78, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x74,
	0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x74,
	0x5f, 0x73, 0x76, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x44, 0x6f, 0x77,
	0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x52, 0x65, 0x71, 0x45, 0x78, 0x74, 0x48, 0x01, 0x52, 0x06, 0x65,
	0x78, 0x74, 0x52, 0x65, 0x71, 0x88, 0x01, 0x01, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x63, 0x6f, 0x6d,
	0x5f, 0x72, 0x65, 0x71, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x65, 0x78, 0x74, 0x5f, 0x72, 0x65, 0x71,
	0x22, 0x3d, 0x0a, 0x08, 0x4c, 0x6f, 0x6e, 0x67, 0x54, 0x65, 0x78, 0x74, 0x12, 0x22, 0x0a, 0x0a,
	0x62, 0x79, 0x74, 0x65, 0x73, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c,
	0x48, 0x00, 0x52, 0x09, 0x62, 0x79, 0x74, 0x65, 0x73, 0x54, 0x65, 0x78, 0x74, 0x88, 0x01, 0x01,
	0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x22,
	0x81, 0x02, 0x0a, 0x0c, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x12, 0x31, 0x0a, 0x12, 0x62, 0x79, 0x74, 0x65, 0x73, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x63,
	0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x48, 0x00, 0x52, 0x10,
	0x62, 0x79, 0x74, 0x65, 0x73, 0x46, 0x69, 0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74,
	0x88, 0x01, 0x01, 0x12, 0x1c, 0x0a, 0x07, 0x73, 0x74, 0x72, 0x5f, 0x6d, 0x64, 0x35, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x06, 0x73, 0x74, 0x72, 0x4d, 0x64, 0x35, 0x88, 0x01,
	0x01, 0x12, 0x27, 0x0a, 0x0d, 0x73, 0x74, 0x72, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x48, 0x02, 0x52, 0x0b, 0x73, 0x74, 0x72, 0x46,
	0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x2d, 0x0a, 0x10, 0x75, 0x69,
	0x6e, 0x74, 0x33, 0x32, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0d, 0x48, 0x03, 0x52, 0x0e, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x46, 0x69,
	0x6c, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x88, 0x01, 0x01, 0x42, 0x15, 0x0a, 0x13, 0x5f, 0x62, 0x79,
	0x74, 0x65, 0x73, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74,
	0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x73, 0x74, 0x72, 0x5f, 0x6d, 0x64, 0x35, 0x42, 0x10, 0x0a, 0x0e,
	0x5f, 0x73, 0x74, 0x72, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x13,
	0x0a, 0x11, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73,
	0x69, 0x7a, 0x65, 0x22, 0xe3, 0x01, 0x0a, 0x13, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x52, 0x73, 0x70, 0x12, 0x54, 0x0a, 0x07, 0x63,
	0x6f, 0x6d, 0x5f, 0x72, 0x73, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x74,
	0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x74,
	0x5f, 0x73, 0x76, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x73, 0x70,
	0x43, 0x6f, 0x6d, 0x6d, 0x48, 0x00, 0x52, 0x06, 0x63, 0x6f, 0x6d, 0x52, 0x73, 0x70, 0x88, 0x01,
	0x01, 0x12, 0x58, 0x0a, 0x0d, 0x6d, 0x73, 0x67, 0x5f, 0x6c, 0x6f, 0x6e, 0x67, 0x5f, 0x74, 0x65,
	0x78, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e,
	0x4b, 0x45, 0x50, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x74, 0x5f, 0x73, 0x76, 0x72,
	0x2e, 0x4c, 0x6f, 0x6e, 0x67, 0x54, 0x65, 0x78, 0x74, 0x48, 0x01, 0x52, 0x0b, 0x6d, 0x73, 0x67,
	0x4c, 0x6f, 0x6e, 0x67, 0x54, 0x65, 0x78, 0x74, 0x88, 0x01, 0x01, 0x42, 0x0a, 0x0a, 0x08, 0x5f,
	0x63, 0x6f, 0x6d, 0x5f, 0x72, 0x73, 0x70, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x6d, 0x73, 0x67, 0x5f,
	0x6c, 0x6f, 0x6e, 0x67, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x22, 0xba, 0x01, 0x0a, 0x14, 0x52, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x52, 0x65, 0x71, 0x45,
	0x78, 0x74, 0x12, 0x2d, 0x0a, 0x10, 0x73, 0x74, 0x72, 0x5f, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0e,
	0x73, 0x74, 0x72, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x88, 0x01,
	0x01, 0x12, 0x1e, 0x0a, 0x08, 0x73, 0x74, 0x72, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x07, 0x73, 0x74, 0x72, 0x54, 0x65, 0x78, 0x74, 0x88, 0x01,
	0x01, 0x12, 0x22, 0x0a, 0x0a, 0x62, 0x79, 0x74, 0x65, 0x73, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0c, 0x48, 0x02, 0x52, 0x09, 0x62, 0x79, 0x74, 0x65, 0x73, 0x54, 0x65,
	0x78, 0x74, 0x88, 0x01, 0x01, 0x42, 0x13, 0x0a, 0x11, 0x5f, 0x73, 0x74, 0x72, 0x5f, 0x61, 0x63,
	0x63, 0x65, 0x73, 0x73, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x73,
	0x74, 0x72, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x62, 0x79, 0x74, 0x65,
	0x73, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x22, 0xdc, 0x01, 0x0a, 0x11, 0x52, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x52, 0x65, 0x71, 0x12, 0x54, 0x0a, 0x07,
	0x63, 0x6f, 0x6d, 0x5f, 0x72, 0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e,
	0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x5f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72,
	0x74, 0x5f, 0x73, 0x76, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x65,
	0x71, 0x43, 0x6f, 0x6d, 0x6d, 0x48, 0x00, 0x52, 0x06, 0x63, 0x6f, 0x6d, 0x52, 0x65, 0x71, 0x88,
	0x01, 0x01, 0x12, 0x59, 0x0a, 0x07, 0x65, 0x78, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x63,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f,
	0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x74, 0x5f, 0x73, 0x76, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x52, 0x65, 0x71, 0x45, 0x78, 0x74,
	0x48, 0x01, 0x52, 0x06, 0x65, 0x78, 0x74, 0x52, 0x65, 0x71, 0x88, 0x01, 0x01, 0x42, 0x0a, 0x0a,
	0x08, 0x5f, 0x63, 0x6f, 0x6d, 0x5f, 0x72, 0x65, 0x71, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x65, 0x78,
	0x74, 0x5f, 0x72, 0x65, 0x71, 0x22, 0x75, 0x0a, 0x11, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x52, 0x73, 0x70, 0x12, 0x54, 0x0a, 0x07, 0x63, 0x6f,
	0x6d, 0x5f, 0x72, 0x73, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x74, 0x72,
	0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x74, 0x5f,
	0x73, 0x76, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x73, 0x70, 0x43,
	0x6f, 0x6d, 0x6d, 0x48, 0x00, 0x52, 0x06, 0x63, 0x6f, 0x6d, 0x52, 0x73, 0x70, 0x88, 0x01, 0x01,
	0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x63, 0x6f, 0x6d, 0x5f, 0x72, 0x73, 0x70, 0x2a, 0x67, 0x0a, 0x0c,
	0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0e, 0x0a, 0x0a,
	0x52, 0x54, 0x5f, 0x44, 0x45, 0x46, 0x41, 0x55, 0x4c, 0x54, 0x10, 0x00, 0x12, 0x10, 0x0a, 0x0c,
	0x52, 0x54, 0x5f, 0x4c, 0x4f, 0x4e, 0x47, 0x5f, 0x54, 0x45, 0x58, 0x54, 0x10, 0x01, 0x12, 0x0c,
	0x0a, 0x08, 0x52, 0x54, 0x5f, 0x49, 0x4d, 0x41, 0x47, 0x45, 0x10, 0x02, 0x12, 0x0c, 0x0a, 0x08,
	0x52, 0x54, 0x5f, 0x56, 0x49, 0x44, 0x45, 0x4f, 0x10, 0x03, 0x12, 0x0b, 0x0a, 0x07, 0x52, 0x54,
	0x5f, 0x46, 0x49, 0x4c, 0x45, 0x10, 0x04, 0x12, 0x0c, 0x0a, 0x08, 0x52, 0x54, 0x5f, 0x56, 0x4f,
	0x49, 0x43, 0x45, 0x10, 0x05, 0x32, 0xbc, 0x03, 0x0a, 0x16, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x12, 0x89, 0x01, 0x0a, 0x0f, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x43, 0x6f, 0x6e,
	0x76, 0x65, 0x72, 0x74, 0x12, 0x39, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e,
	0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x5f, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x74, 0x5f, 0x73, 0x76, 0x72, 0x2e, 0x52, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x74, 0x52, 0x65, 0x71, 0x1a,
	0x39, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e,
	0x65, 0x6c, 0x5f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x76,
	0x65, 0x72, 0x74, 0x5f, 0x73, 0x76, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x74, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x86, 0x01, 0x0a,
	0x0e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x12,
	0x38, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e,
	0x65, 0x6c, 0x5f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x76,
	0x65, 0x72, 0x74, 0x5f, 0x73, 0x76, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x38, 0x2e, 0x74, 0x72, 0x70, 0x63,
	0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x74, 0x5f, 0x73, 0x76,
	0x72, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64,
	0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x8c, 0x01, 0x0a, 0x10, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x12, 0x3a, 0x2e, 0x74, 0x72, 0x70,
	0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x74, 0x5f, 0x73,
	0x76, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x44, 0x6f, 0x77, 0x6e, 0x6c,
	0x6f, 0x61, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x3a, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45,
	0x50, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x74, 0x5f, 0x73, 0x76, 0x72, 0x2e, 0x52,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x52,
	0x73, 0x70, 0x22, 0x00, 0x42, 0x52, 0x5a, 0x50, 0x67, 0x69, 0x74, 0x2e, 0x77, 0x6f, 0x61, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x64, 0x69, 0x61, 0x6c, 0x6f, 0x67, 0x75, 0x65, 0x2d, 0x70, 0x6c, 0x61,
	0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x6c, 0x6b, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f,
	0x70, 0x62, 0x2d, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2f, 0x63, 0x68, 0x61, 0x6e,
	0x6e, 0x65, 0x6c, 0x5f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x6e,
	0x76, 0x65, 0x72, 0x74, 0x5f, 0x73, 0x76, 0x72, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_channel_resource_convert_svr_proto_rawDescOnce sync.Once
	file_channel_resource_convert_svr_proto_rawDescData = file_channel_resource_convert_svr_proto_rawDesc
)

func file_channel_resource_convert_svr_proto_rawDescGZIP() []byte {
	file_channel_resource_convert_svr_proto_rawDescOnce.Do(func() {
		file_channel_resource_convert_svr_proto_rawDescData = protoimpl.X.CompressGZIP(file_channel_resource_convert_svr_proto_rawDescData)
	})
	return file_channel_resource_convert_svr_proto_rawDescData
}

var file_channel_resource_convert_svr_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_channel_resource_convert_svr_proto_msgTypes = make([]protoimpl.MessageInfo, 14)
var file_channel_resource_convert_svr_proto_goTypes = []interface{}{
	(ResourceType)(0),               // 0: trpc.KEP.channel_resource_convert_svr.ResourceType
	(*ResourceReqComm)(nil),         // 1: trpc.KEP.channel_resource_convert_svr.ResourceReqComm
	(*ResourceConvertReqWxExt)(nil), // 2: trpc.KEP.channel_resource_convert_svr.ResourceConvertReqWxExt
	(*ResourceConvertReqExt)(nil),   // 3: trpc.KEP.channel_resource_convert_svr.ResourceConvertReqExt
	(*ResourceConvertReq)(nil),      // 4: trpc.KEP.channel_resource_convert_svr.ResourceConvertReq
	(*ResourceRspComm)(nil),         // 5: trpc.KEP.channel_resource_convert_svr.ResourceRspComm
	(*ResourceConvertRsp)(nil),      // 6: trpc.KEP.channel_resource_convert_svr.ResourceConvertRsp
	(*ResourceDownloadReqExt)(nil),  // 7: trpc.KEP.channel_resource_convert_svr.ResourceDownloadReqExt
	(*ResourceDownloadReq)(nil),     // 8: trpc.KEP.channel_resource_convert_svr.ResourceDownloadReq
	(*LongText)(nil),                // 9: trpc.KEP.channel_resource_convert_svr.LongText
	(*FileResource)(nil),            // 10: trpc.KEP.channel_resource_convert_svr.FileResource
	(*ResourceDownloadRsp)(nil),     // 11: trpc.KEP.channel_resource_convert_svr.ResourceDownloadRsp
	(*ResourceUploadReqExt)(nil),    // 12: trpc.KEP.channel_resource_convert_svr.ResourceUploadReqExt
	(*ResourceUploadReq)(nil),       // 13: trpc.KEP.channel_resource_convert_svr.ResourceUploadReq
	(*ResourceUploadRsp)(nil),       // 14: trpc.KEP.channel_resource_convert_svr.ResourceUploadRsp
}
var file_channel_resource_convert_svr_proto_depIdxs = []int32{
	2,  // 0: trpc.KEP.channel_resource_convert_svr.ResourceConvertReqExt.msg_req_wx_ext:type_name -> trpc.KEP.channel_resource_convert_svr.ResourceConvertReqWxExt
	1,  // 1: trpc.KEP.channel_resource_convert_svr.ResourceConvertReq.com_req:type_name -> trpc.KEP.channel_resource_convert_svr.ResourceReqComm
	3,  // 2: trpc.KEP.channel_resource_convert_svr.ResourceConvertReq.ext_req:type_name -> trpc.KEP.channel_resource_convert_svr.ResourceConvertReqExt
	5,  // 3: trpc.KEP.channel_resource_convert_svr.ResourceConvertRsp.com_rsp:type_name -> trpc.KEP.channel_resource_convert_svr.ResourceRspComm
	1,  // 4: trpc.KEP.channel_resource_convert_svr.ResourceDownloadReq.com_req:type_name -> trpc.KEP.channel_resource_convert_svr.ResourceReqComm
	7,  // 5: trpc.KEP.channel_resource_convert_svr.ResourceDownloadReq.ext_req:type_name -> trpc.KEP.channel_resource_convert_svr.ResourceDownloadReqExt
	5,  // 6: trpc.KEP.channel_resource_convert_svr.ResourceDownloadRsp.com_rsp:type_name -> trpc.KEP.channel_resource_convert_svr.ResourceRspComm
	9,  // 7: trpc.KEP.channel_resource_convert_svr.ResourceDownloadRsp.msg_long_text:type_name -> trpc.KEP.channel_resource_convert_svr.LongText
	1,  // 8: trpc.KEP.channel_resource_convert_svr.ResourceUploadReq.com_req:type_name -> trpc.KEP.channel_resource_convert_svr.ResourceReqComm
	12, // 9: trpc.KEP.channel_resource_convert_svr.ResourceUploadReq.ext_req:type_name -> trpc.KEP.channel_resource_convert_svr.ResourceUploadReqExt
	5,  // 10: trpc.KEP.channel_resource_convert_svr.ResourceUploadRsp.com_rsp:type_name -> trpc.KEP.channel_resource_convert_svr.ResourceRspComm
	4,  // 11: trpc.KEP.channel_resource_convert_svr.ResourceManagerService.ResourceConvert:input_type -> trpc.KEP.channel_resource_convert_svr.ResourceConvertReq
	13, // 12: trpc.KEP.channel_resource_convert_svr.ResourceManagerService.ResourceUpload:input_type -> trpc.KEP.channel_resource_convert_svr.ResourceUploadReq
	8,  // 13: trpc.KEP.channel_resource_convert_svr.ResourceManagerService.ResourceDownload:input_type -> trpc.KEP.channel_resource_convert_svr.ResourceDownloadReq
	6,  // 14: trpc.KEP.channel_resource_convert_svr.ResourceManagerService.ResourceConvert:output_type -> trpc.KEP.channel_resource_convert_svr.ResourceConvertRsp
	14, // 15: trpc.KEP.channel_resource_convert_svr.ResourceManagerService.ResourceUpload:output_type -> trpc.KEP.channel_resource_convert_svr.ResourceUploadRsp
	11, // 16: trpc.KEP.channel_resource_convert_svr.ResourceManagerService.ResourceDownload:output_type -> trpc.KEP.channel_resource_convert_svr.ResourceDownloadRsp
	14, // [14:17] is the sub-list for method output_type
	11, // [11:14] is the sub-list for method input_type
	11, // [11:11] is the sub-list for extension type_name
	11, // [11:11] is the sub-list for extension extendee
	0,  // [0:11] is the sub-list for field type_name
}

func init() { file_channel_resource_convert_svr_proto_init() }
func file_channel_resource_convert_svr_proto_init() {
	if File_channel_resource_convert_svr_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_channel_resource_convert_svr_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResourceReqComm); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_channel_resource_convert_svr_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResourceConvertReqWxExt); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_channel_resource_convert_svr_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResourceConvertReqExt); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_channel_resource_convert_svr_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResourceConvertReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_channel_resource_convert_svr_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResourceRspComm); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_channel_resource_convert_svr_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResourceConvertRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_channel_resource_convert_svr_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResourceDownloadReqExt); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_channel_resource_convert_svr_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResourceDownloadReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_channel_resource_convert_svr_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LongText); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_channel_resource_convert_svr_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FileResource); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_channel_resource_convert_svr_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResourceDownloadRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_channel_resource_convert_svr_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResourceUploadReqExt); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_channel_resource_convert_svr_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResourceUploadReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_channel_resource_convert_svr_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResourceUploadRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_channel_resource_convert_svr_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_channel_resource_convert_svr_proto_msgTypes[1].OneofWrappers = []interface{}{}
	file_channel_resource_convert_svr_proto_msgTypes[3].OneofWrappers = []interface{}{}
	file_channel_resource_convert_svr_proto_msgTypes[4].OneofWrappers = []interface{}{}
	file_channel_resource_convert_svr_proto_msgTypes[5].OneofWrappers = []interface{}{}
	file_channel_resource_convert_svr_proto_msgTypes[6].OneofWrappers = []interface{}{}
	file_channel_resource_convert_svr_proto_msgTypes[7].OneofWrappers = []interface{}{}
	file_channel_resource_convert_svr_proto_msgTypes[8].OneofWrappers = []interface{}{}
	file_channel_resource_convert_svr_proto_msgTypes[9].OneofWrappers = []interface{}{}
	file_channel_resource_convert_svr_proto_msgTypes[10].OneofWrappers = []interface{}{}
	file_channel_resource_convert_svr_proto_msgTypes[11].OneofWrappers = []interface{}{}
	file_channel_resource_convert_svr_proto_msgTypes[12].OneofWrappers = []interface{}{}
	file_channel_resource_convert_svr_proto_msgTypes[13].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_channel_resource_convert_svr_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   14,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_channel_resource_convert_svr_proto_goTypes,
		DependencyIndexes: file_channel_resource_convert_svr_proto_depIdxs,
		EnumInfos:         file_channel_resource_convert_svr_proto_enumTypes,
		MessageInfos:      file_channel_resource_convert_svr_proto_msgTypes,
	}.Build()
	File_channel_resource_convert_svr_proto = out.File
	file_channel_resource_convert_svr_proto_rawDesc = nil
	file_channel_resource_convert_svr_proto_goTypes = nil
	file_channel_resource_convert_svr_proto_depIdxs = nil
}
