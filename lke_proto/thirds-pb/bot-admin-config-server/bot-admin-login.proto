syntax = "proto3";

package trpc.KEP.bot_admin_config_server;
option go_package = "git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_admin_config_server";

import "validate.proto";


// 登录信息
service Login {
  // 登录操作
  rpc Login(LoginReq) returns (LoginRsp);
  // 登出操作
  rpc Logout(LogoutReq) returns (LogoutRsp);
  // 检查session
  rpc CheckSession(CheckSessionReq) returns (CheckSessionRsp);
  // 检查permission
  rpc CheckPermission(CheckPermissionReq) returns (CheckPermissionRsp);

  // Deprecated 以下接口待删除
  // 获取验证码
  rpc SendVerifyCode(SendVerifyCodeReq) returns (SendVerifyCodeRsp);
  // 获取验证码
  rpc SendVerifyCodeNew(SendVerifyCodeNewReq) returns (SendVerifyCodeNewRsp);
  // 注册企业
  rpc RegisterCorp(RegisterCorpReq) returns (RegisterCorpRsp);
  // 校验企业与员工是否有效
  rpc CheckCorpAndStaff(CheckCorpAndStaffReq) returns (CheckCorpAndStaffRsp);
  // 加入企业
  rpc JoinCorp(JoinCorpReq) returns (JoinCorpRsp);
  // 退出企业
  rpc ExitCorp(ExitCorpReq) returns (ExitCorpRsp);
}

// 登录请求
message LoginReq {
  // 手机号
  string telephone = 1;
  // 验证码
  string code = 2;
  // 是否同意用户协议/隐私政策
  bool is_agreed = 3;
  // 登录方式 0（默认手机号登录），1（账户密码登录），2（邮箱登录）
  uint32 login_type = 4 [(validate.rules).uint32 = {in: [0, 1, 2]}];
  // 用户账户
  string account = 5 [(validate.rules).string = {max_len: 20}];
  // 用户密码 md5加密
  string password = 6 [(validate.rules).string = {max_len: 32}];
  // 用户类型：0（正常用户），1（体验用户）
  uint32 user_type = 7;
  // 邮箱
  string email = 8;
}

// 登录响应
message LoginRsp {
  // cookie映射值
  string token = 1;
  // 用户名
  string user_name = 2;
  // 用户头像
  string user_avatar = 3;
  // 手机号
  string telephone = 4;
  // 用户ID
  string staff_biz_id = 6;
  // 邮箱
  string email = 7;
}

// 登出请求
message LogoutReq {}

// 登出响应
message LogoutRsp {}

// 检查session请求
message CheckSessionReq {}

// 检查session响应
message CheckSessionRsp {
  uint64 staff_id = 1;
  uint64 staff_biz_id = 2;
  uint64 corp_id = 3;
  uint64 s_id = 4;
  uint32 user_type = 5;
  uint64 corp_biz_id = 6;
}

// 校验权限请求
message CheckPermissionReq {
  string action = 1;
  string app_type = 2;
}

// 校验权限响应
message CheckPermissionRsp {
  bool has_permission = 1;
}

// 获取验证码请求
message SendVerifyCodeReq {
  // 手机号
  string telephone = 1 [(validate.rules).string.min_len = 1];
}

// 获取验证码响应
message SendVerifyCodeRsp {}

// 获取验证码请求
message SendVerifyCodeNewReq {
  // 手机号
  string telephone = 1;
  // 发送短信类型：cloud-腾讯云；qidian-企点
  string sms_type = 2;
  // 图片验证码验证信息
  VerifyCaptchaTicket captcha_ticket = 3;

  // 发送类型：0 默认 短信; 1 邮件
  uint32 message_type = 4;
  string email = 5;
}

// VerifyCaptchaTicket 验证ticket信息
message VerifyCaptchaTicket {
  // 验证码客户端验证回调的票据
  string ticket = 1;
  // 验证码客户端验证回调的随机串
  string rand_str = 2;
}

// 获取验证码响应
message SendVerifyCodeNewRsp {}

// 注册企业请求
message RegisterCorpReq {
  // 企业全称
  string corp_full_name = 1 [(validate.rules).string.min_len = 1];
  // 联系人名称
  string contact_name = 2 [(validate.rules).string.min_len = 1];
  // 邮箱地址
  string email = 3 [(validate.rules).string.min_len = 1];
  // 手机号
  string telephone = 4 [(validate.rules).string.min_len = 1];
  // 验证码
  string code = 5 [(validate.rules).string.min_len = 1];
}

// 注册企业响应
message RegisterCorpRsp {}

// 校验企业与员工 请求
message CheckCorpAndStaffReq {
  // 企业ID
  uint64 corp_biz_id = 1 [(validate.rules).uint64.gt = 0];
  // 员工ID
  uint64 staff_biz_id = 2 [(validate.rules).uint64.gt = 0];
}

// 校验企业与员工 响应
message CheckCorpAndStaffRsp {}

// 加入企业请求
message JoinCorpReq {
  // 企业ID
  uint64 corp_biz_id = 1 [(validate.rules).uint64.gt = 0];
  // 员工ID
  uint64 staff_biz_id = 2 [(validate.rules).uint64.gt = 0];
  // 受邀人姓名
  string name = 3 [(validate.rules).string.min_len = 1];
  // 受邀人手机号
  string telephone = 4 [(validate.rules).string.min_len = 1];
  // 验证码
  string code = 5 [(validate.rules).string.min_len = 1];
}

// 加入企业响应
message JoinCorpRsp {
  // cookie映射值
  string token = 1;
  // 用户名
  string user_name = 2;
  // 用户头像
  string user_avatar = 3;
  // 手机号
  string telephone = 4;
}

// 退出企业
message ExitCorpReq {}

// 退出企业响应
message ExitCorpRsp {}
