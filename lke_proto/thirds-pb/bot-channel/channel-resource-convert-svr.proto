syntax = "proto3";

package trpc.KEP.channel_resource_convert_svr;
option go_package = "git.woa.com/dialogue-platform/lke_proto/pb-protocol/channel_resource_convert_svr";

//import "validate.proto";
// 资源管理服务
service ResourceManagerService {
  // 资源转换接口
  rpc ResourceConvert(ResourceConvertReq) returns (ResourceConvertRsp) {}
  // 资源上传接口
  rpc ResourceUpload(ResourceUploadReq) returns (ResourceUploadRsp) {}
  // 资源下载接口
  rpc ResourceDownload(ResourceDownloadReq) returns (ResourceDownloadRsp) {}
}

enum ResourceType //资源类型
{
  RT_DEFAULT = 0;                                     //默认
  RT_LONG_TEXT = 1;     				                //长文本
  RT_IMAGE = 2;              		                    //图片
  RT_VIDEO = 3;         		                        //视频
  RT_FILE = 4;         		                        //文件
  RT_VOICE = 5;         		                        //语音
}

message ResourceReqComm
{
  optional bool c2b_direction = 1;               //c2b方向标识
  optional bool b2c_direction = 2;             //b2c方向标识
  optional string str_resource_id = 3;            //资源id，可能是微信的media-id
  optional uint32 uint32_resource_type = 4;           //参考ResourceType
  optional uint32 channel_type = 5;               //渠道id  10001为公众号 10002为企微应用 10003为网页组件 10004为微信客服 10005为小程序
  optional string user_id = 6;               // 第三方用户id  上传cos生成uuid用 格式为wx/{用户ID}/{应用ID}/test.png
  optional uint64 app_biz_id = 7;           // 应用id       上传cos生成uuid用
  optional string domain = 8;               // 企微用户自己配置的host,资源转换的时候要通过用户的ng请求企微下载资源
}

message ResourceConvertReqWxExt
{
  optional string str_access_token = 1;               //微信需要传token,不用再去调用rpc获取
}

message ResourceConvertReqExt
{
  ResourceConvertReqWxExt msg_req_wx_ext = 1;         //微信相关ext字段
}

//资源转换请求
message ResourceConvertReq
{
  optional ResourceReqComm com_req = 1;               //资源服务请求通用字段
  optional ResourceConvertReqExt ext_req = 2;         //资源服务请求额外需要的字段，通常是某些场景需要
}

message ResourceRspComm
{
  optional string str_resource_id_rsp = 1;            //转换之后的资源id
  optional string str_md5 = 2;                        //转换之后的资源md5值
  optional string str_file_name = 3;                  //资源的文件名
  optional uint32 uint32_file_size = 4;               //资源大小
  optional string str_url = 5;                        //转换之后的资源url，有些资源需要填
  optional uint32 uint32_res_type = 6;                //资源type
}

//资源转换返回
message ResourceConvertRsp
{
  optional ResourceRspComm com_rsp = 1;               //资源服务返回通用字段
}

// 下载接口
message ResourceDownloadReqExt
{
  optional string str_access_token = 1;               //微信需要传token,不用再去调用rpc获取
}

//资源下载请求
message ResourceDownloadReq
{
  optional ResourceReqComm com_req = 1;               //资源服务请求常用字段
  optional ResourceDownloadReqExt ext_req = 2;        //资源服务请求额外需要的字段，通常是某些场景需要
}

//下载的长文本内容
message LongText
{
  optional bytes bytes_text = 1;                       //下载的长文本内容
}

message FileResource
{
  optional bytes bytes_file_context = 1;              //文件内容
  optional string str_md5 = 2;                        //文件的MD5
  optional string str_file_name = 3;                  //文件名
  optional uint32 uint32_file_size = 4;               //文件大小
}

//资源下载返回
message ResourceDownloadRsp
{
  optional ResourceRspComm com_rsp = 1;               //资源服务返回通用字段
  optional LongText msg_long_text = 2;                //下载的长文本内容
}

// 上传接口
message ResourceUploadReqExt
{
  optional string str_access_token = 1;               //微信需要传token,不用再去调用rpc获取
  optional string str_text = 2;                       //用于上传长文本/小程序卡片的时候
  optional bytes  bytes_text = 3;                     //pc消息 要封成pb
}

//资源上传请求
message ResourceUploadReq
{
  optional ResourceReqComm com_req = 1;               //资源服务请求常用字段
  optional ResourceUploadReqExt ext_req = 2;          //资源服务请求额外需要的字段，通常是某些场景需要
}

//资源上传返回
message ResourceUploadRsp
{
  optional ResourceRspComm com_rsp = 1;               //资源服务返回通用字段
}