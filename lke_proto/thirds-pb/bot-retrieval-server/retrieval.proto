syntax = "proto3";

package trpc.KEP.bot_retrieval_server;
option go_package = "git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_retrieval_server";

import "validate.proto";

// 知识问答检索服务(用户端)
service Retrieval {
  // 检索
  rpc Search(SearchReq) returns (SearchRsp);
  // 检索多个知识库
  rpc SearchMultiKnowledgeRelease(SearchMultiKnowledgeReq) returns (SearchRsp);
  // 发布版本
  rpc Publish(PublishReq) returns (PublishRsp);
  // 计算相似度
  rpc Similarity(SimilarityReq) returns (SimilarityRsp);
  // 索引库重建
  rpc IndexRebuild(IndexRebuildReq) returns (IndexRebuildRsp);
  // 继续已经终止的任务
  rpc ContinueTerminatedTask(ContinueTerminatedTaskReq) returns (ContinueTerminatedTaskRsp);
  // 线上库embedding升级任务
  rpc UpgradeEmbedding(UpgradeEmbeddingReq) returns (UpgradeEmbeddingRsp);
  // vector发布后校验版本号是否有效
  rpc CheckVersion(CheckVersionReq) returns (CheckVersionRsp);
  // 批量删除发布库的所有知识（包括QA/文档/混合检索/text2sql等）
  rpc BatchDeleteAllKnowledgeProd(BatchDeleteAllKnowledgeProdReq) returns (BatchDeleteAllKnowledgeProdRsp);

  // 按照引用维度删除数据[es,vector,db]
  rpc ClearAppVectorResource(ClearAppVectorResourceReq) returns (ClearAppVectorResourceRsp);

}


// 知识问答检索服务(评测端)
service DirectIndex {
  // 增加检索库
  rpc CreateIndex(CreateIndexReq) returns (CreateIndexRsp);
  // 删除检索库
  rpc DeleteIndex(DeleteIndexReq) returns (DeleteIndexRsp);
  // 增加特征
  rpc AddVector(AddVectorReq) returns (AddVectorRsp);
  // 删除特征
  rpc DeleteVector(DeleteVectorReq) returns (DeleteVectorRsp);
  // 修改特征
  rpc UpdateVector(UpdateVectorReq) returns (UpdateVectorRsp);
  // 检索特征
  rpc SearchVector(SearchVectorReq) returns (SearchVectorRsp);
  // 检索多个知识库(评测端）
  rpc SearchMultiKnowledgePreview(SearchMultiKnowledgeReq) returns (SearchVectorRsp);
  // 增加检索库(无用户关系绑定)
  rpc DirectCreateIndex(DirectCreateIndexReq) returns (DirectCreateIndexRsp);
  // 删除检索库(无用户关系绑定)
  rpc DirectDeleteIndex(DirectDeleteIndexReq) returns (DirectDeleteIndexRsp);
  // 增加特征(无用户关系绑定)
  rpc DirectAddVector(DirectAddVectorReq) returns (DirectAddVectorRsp);
  // 删除特征(无用户关系绑定)
  rpc DirectDeleteVector(DirectDeleteVectorReq) returns (DirectDeleteVectorRsp);
  // 修改特征(无用户关系绑定)
  rpc DirectUpdateVector(DirectUpdateVectorReq) returns (DirectUpdateVectorRsp);
  // 检索特征(无用户关系绑定)
  rpc DirectSearchVector(DirectSearchVectorReq) returns (DirectSearchVectorRsp);
  // 新建或更新BigData数据到ES
  rpc AddBigDataElastic(AddBigDataElasticReq) returns (AddBigDataElasticRsp);
  // 从ES里删除BigData
  rpc DeleteBigDataElastic(DeleteBigDataElasticReq) returns (DeleteBigDataElasticRsp);
  // 从ES恢复离线知识库的BigData
  rpc RecoverBigDataElastic(RecoverBigDataElasticReq) returns (RecoverBigDataElasticRsp);
  // 根据robot_id, big_data_id 从bigDataEs中批量查找数据
  rpc BatchGetBigDataESByRobotBigDataID(BatchGetBigDataESByRobotBigDataIDReq) returns (BatchGetBigDataESByRobotBigDataIDResp);
  // 增加知识
  rpc AddKnowledge(AddKnowledgeReq) returns (AddKnowledgeRsp);
  // 批量增加知识
  rpc BatchAddKnowledge(BatchAddKnowledgeReq) returns (BatchAddKnowledgeRsp);
  // 删除知识
  rpc DeleteKnowledge(DeleteKnowledgeReq) returns (DeleteKnowledgeRsp);
  // 批量删除知识
  rpc BatchDeleteKnowledge(BatchDeleteKnowledgeReq) returns (BatchDeleteKnowledgeRsp);
  // 修改知识
  rpc UpdateKnowledge(UpdateKnowledgeReq) returns (UpdateKnowledgeRsp);
  // 批量增加或修改text2sql
  rpc AddText2SQL(AddText2SQLReq) returns (AddText2SQLRsp);
  // 批量删除text2sql
  rpc DeleteText2SQL(DeleteText2SQLReq) returns (DeleteText2SQLRsp);
  // 实时文档添加切片
  rpc AddRealTimeKnowledge(AddRealTimeKnowledgeReq) returns(AddRealTimeKnowledgeRsp);
  // 实时文档删除切片
  rpc DeleteRealTimeKnowledge(DeleteRealTimeKnowledgeReq) returns(DeleteRealTimeKnowledgeRsp);
  // 实时文档检索接口
  rpc RetrievalRealTime(RetrievalRealTimeReq) returns(RetrievalRealTimeRsp);
  // 批量增加或修改外部数据库的Text2SQL信息
  rpc AddDBText2SQL(AddDBText2SQLReq) returns (AddDBText2SQLRsp);
  // 批量删除外部数据库的Text2SQL信息
  rpc DeleteDBText2SQL(DeleteDBText2SQLReq) returns (DeleteDBText2SQLRsp);

  // 更新向量和ES的标签
  rpc UpdateLabel(UpdateLabelReq) returns (UpdateLabelRsp);
}

message UpdateLabelReq{
  // 机器人ID
  uint64 robot_id = 1 [(validate.rules).uint64 = {gte: 1}];
  uint64 app_biz_id = 2;
  EnvType env_type = 3;
  // 索引库ID, 评测端构建group-id，env_type为prod时不需要
  uint64 index_id = 4;
  // ID 文档切片、标准问自增ID或者相似问business id
  repeated uint64 ids = 5 [(validate.rules).repeated .min_items = 1];;
  // 文档类型 (1 QA, 2 文档段)
  uint32 doc_type = 6;
  // 问答类型 (0 标准问, 1 相似问)
  uint32 qa_type = 7;
  // embedding 版本
  uint64 embedding_version = 8;
  // labels 标签
  repeated VectorLabel labels = 9;
  // segment类型，取值： segment  table text2sql_content，注：text2sql_meta不需要
  string segment_type = 10;
  // 当传text2sql_content的时候需要传入表格的列数
  uint32 columns_count = 11;
}

message UpdateLabelRsp {
}

// 数据库类型
enum DBType {
  MYSQL = 0;
  SQL_SERVER= 1;
  Text_To_SQL = 2; // 文本转SQL
}

enum EnvType {
  Test = 0;
  Prod = 1;
}

message AddDBText2SQLReq {
  // 机器人ID
  uint64 robot_id = 1 [(validate.rules).uint64 = {gte: 1}];
  uint64 db_source_biz_id = 2 [(validate.rules).uint64 = {gte: 1}];
  DBType db_type = 3;
  // 库描述信息，格式：库名|库别名|库描述
  string db_desc = 4;
  uint64 db_table_biz_id = 5 [(validate.rules).uint64 = {gte: 1}];
  // 表描述信息，格式：表名|表别名|表描述
  string table_desc = 6;
  repeated DBRowData rows = 7 [(validate.rules).repeated .min_items = 1];
  EnvType env_type = 8;
  repeated VectorLabel labels = 9;
}

message DBRowData {
  repeated DBCell cells = 1 [(validate.rules).repeated .min_items = 1];
}

message DBCell {
  string column_name = 1;
  string column_alias_name = 2;
  string column_desc = 3;
  string data_type = 4;
  string value = 5;
}

message AddDBText2SQLRsp {
}

message DeleteDBText2SQLReq {
  // 机器人ID
  uint64 robot_id = 1 [(validate.rules).uint64 = {gte: 1}];
  repeated uint64 db_table_biz_id = 2 [(validate.rules).repeated .min_items = 1];
  EnvType env_type = 3;
}

message DeleteDBText2SQLRsp {
}

// 检索策略类型
enum SearchStrategyTypeEnum {
  Mixing = 0; // 混合检索: 向量 + ES
  Semantics = 1; // 语义检索： 向量
  // 没有语义和向量，只能通过table_enhancement和doc_type 2去控制excel的text2sql，通过doc_type为数据库控制数据库的text2sql
  NoneSearch = 2;
}

// 检索策略配置
message SearchStrategy {
  // 检索策略类型 0:混合检索，1：语义检索
  SearchStrategyTypeEnum strategy_type = 1;
  // excel检索增强，默认关闭
  bool table_enhancement = 2;
}

// 知识库类型，0：离线知识库，1：C端实时文档
enum KnowledgeType {
  KNOWLEDGE = 0;   // 离线知识库
  REALTIME = 1;   // C端实时文档
}

// 检索结果类型
enum RetrievalResultType {
  RETRIEVAL = 0;    // 向量/混合检索的结果
  TEXT2SQL = 1;    // text2sql的结果
  IMAGE_SEARCH_IMAGE = 2; // 图搜图
  TEXT_SEARCH_IMAGE = 3;  // 文搜图
}

//标签表达式
message LabelExpression {
  // 逻辑运算符
  enum LogicOpr {
    NOOP = 0;
    AND = 1;
    OR = 2;
  }
  // 定义基本条件表达式
  message Condition {
    // 数据类型，general_vector标签为string类型，用户配置的标签默认为array类型，转换的表达式不同
    enum DataType {
      ARRAY = 0;
      STRING = 1;
    }
    DataType type = 1;
    // 标签名
    string name = 2;
    // 标签值
    repeated string values = 3;
  }
  // AND或OR
  LogicOpr operator = 1;
  // 嵌套逻辑表达式
  repeated LabelExpression expressions = 2;
  // 基本条件表达式
  Condition condition = 3;
}

// 特征标签
message VectorLabel{
  // 标签名
  string name = 1;
  // 标签值
  string value = 2;
}

// 检索的特征标签
message SearchVectorLabel {
  // 标签名
  string name = 1;
  // 标签值，一个标签多个标签值
  repeated string values = 2;
}

// 特征标签表达式
message VectorLabelExpr{
  // 运算符
  enum Operator {
    NOOP = 0;
    SCALE = 1;
    AND = 2;
    OR = 3;
    NOT = 4;
    IN = 5;
    NOT_IN = 6;
    ALL_IN = 7;
    EQUAL = 8;
    NOT_EQUAL = 9;
    GREATER = 10;
    GREATER_EQUAL = 11;
    LESS = 12;
    LESS_EQUAL = 13;
  }
  enum Type {
    INT = 0;
    STRING = 1;
  }
  // 运算符
  Operator op = 1;
  // 表达式
  repeated VectorLabelExpr expressions = 2;
  // 类型, 0 int, 1 string
  Type type = 3;
  // 值
  string value = 4;
}

// 检索的额外信息，如排序和分数等字段
message RetrievalExtra  {
  int32 emb_rank = 1;
  float es_score = 2;
  int32 es_rank  = 3;
  float rerank_score = 4;
  int32 rerank_rank = 5;
  float rrf_score = 6;
  int32 rrf_rank  = 7;
}

// 问题查询请求
message SearchReq {
  // 机器人ID
  uint64 robot_id = 1 [(validate.rules).uint64 = {gte: 1}];
  // 问答库版本
  uint64 version_id = 2 [(validate.rules).uint64 = {gte: 1}];
  // 问题
  string question = 3 [(validate.rules).string = {min_len: 1}];
  message Filter {
    // 文档类型 (1 QA, 2 文档段, 3 拒答问题, 4 搜索引擎)
    uint32 doc_type = 1;
    // 置信度
    float confidence = 2;
    // 取 top_n
    uint32 top_n = 3;
    // 废弃字段，以LabelExpression为准
    // 标签表达式字符串 key1="value1" and key2="value2"
    // 优先使用表达式字符串, 表达式字符串无值时使用表达式
    string label_expr_string = 4;
    // 标签表达式
    VectorLabelExpr label_expr = 5;
  }
  // 筛选器
  repeated Filter filters = 4;
  // 取前 n 条 (默认3)
  uint32 top_n = 5;
  // 重排模型
  message Rerank {
    string model = 1; // 模型名称
    uint32 top_n = 2; // top_n
    bool enable = 3; // 启用
  }
  Rerank rerank = 7;
  // 过滤器名称
  string filter_key = 8;
  // 废弃字段，以LabelExpression为准
  // labels 标签
  repeated SearchVectorLabel labels = 9;
  // 请求query带图片URL，用于图搜图
  repeated string image_urls = 10;
  // 应用id
  uint64 bot_biz_id = 11;
  // 标签表达式
  LabelExpression label_expression = 12;
  // 拆解的子问题，为空表示没有子问题
  repeated string sub_questions = 13;
  // 知识检索策略配置
  SearchStrategy search_strategy = 14;
  // 模型名称
  string model_name = 15;
}

// 问题查询响应
message SearchRsp {
  message Doc {
    // 文档类型 (1 QA, 2 文档段, 3 拒答问题, 4 搜索引擎)
    uint32 doc_type = 1;
    // 文档ID
    uint64 doc_id = 2;
    // 对应文档类型关联的数据ID, 类型为 QA 时为 QAID, 类型为 文档段 时为 文档段ID
    uint64 related_id = 3;
    // 文档段, 当文档类型为 文档段(2) 时有效
    string page_content = 4;
    // 问题, 当文档类型为 QA(1) 时有效
    string question = 5;
    // 答案, 当文档类型为 QA(1) 时有效
    string answer = 6;
    // 置信度
    float confidence = 7;
    // 文档段原文, 当文档类型为 文档段(2) 时有效
    string org_data = 8;
    // 自定义参数
    string custom_param = 9;
    // 是否big_data true-表示org_data是由big_data填充
    bool is_big_data = 10;
    // 算法扩展信息
    RetrievalExtra extra = 11;
    // 检索结果类型
    RetrievalResultType result_type = 12;
    // 检索命中的图片URL
    repeated string image_urls = 13;
    // 相似问相关的额外信息
    message SimilarQuestionExtra {
      // 当检索到的是相似问时，返回该相似问ID
      uint64 similar_id = 1;
      // 当检索到的是相似问时，返回该相似问的问题
      string similar_question = 2;
    }
    SimilarQuestionExtra similar_question_extra = 14;
    // text2sql检索结果带上sheet名称，参考来源溯源用到
    message Text2SQLExtra{
      message TableInfo {
        string table_name = 1;
        uint64 doc_id = 2;
      }
      repeated TableInfo table_infos = 1;
    }
    Text2SQLExtra text2sql_extra = 15;
    // 问题描述 qa意图的描述
    string question_desc = 16;
    // 过滤器名称
    string filter_key = 17;
  }
  // 文档数据
  repeated Doc docs = 1;
}

// 发布版本请求
message PublishReq {
  // 机器人ID
  uint64 robot_id = 1 [(validate.rules).uint64 = {gte: 1}];
  // 版本
  uint64 version_id = 2 [(validate.rules).uint64 = {gte: 1}];
  // 版本名称
  string version_name = 3 [(validate.rules).string = {min_len: 1, max_len: 1024}];
  // embedding 版本
  uint64 embedding_version = 4;
  // 应用id
  uint64 bot_biz_id = 5;
  // 上一个版本(发布前版本号)
  uint64 last_qa_version = 6;
}

// 发布版本响应
message PublishRsp {}

// 获取相似度
message SimilarityReq {
  string ori = 1 [(validate.rules).string = {min_len: 1, max_len: 2000}];
  message Doc {
    // 文档类型 (1 QA, 2 文档段, 3 拒答问题, 4 搜索引擎)
    uint32 doc_type = 1;
    // 文本内容
    string content = 2;
  }
  repeated Doc docs = 2;
  // 机器人ID
  uint64 robot_id = 3 [(validate.rules).uint64 = {gte: 1}];
  // 版本
  uint64 version_id = 4;
  // embedding 版本(相似/评测 直接指定 embedding_version, 否则使用 robot_id + version_id 获取发布时使用的 embedding)
  uint64 embedding_version = 5;
  // 应用id
  uint64 bot_biz_id = 6;
}

message SimilarityRsp {
  repeated float similarities = 1;
}

// 索引库重建请求
message IndexRebuildReq {
  // 机器人ID
  uint64 robot_id = 1 [(validate.rules).uint64 = {gte: 1}];
  // 机器人版本
  uint64 version_id = 2 [(validate.rules).uint64 = {gte: 1}];
  // 应用id
  uint64 bot_biz_id = 3;
}

// 索引库重建响应
message IndexRebuildRsp {}

// 继续已经终止的任务请求
message ContinueTerminatedTaskReq {
  // 任务ID
  uint64 task_id = 1 [(validate.rules).uint64 = {gte: 1}];
  // 重试次数
  uint64 retry_times = 2 [(validate.rules).uint64 = {gte: 1}];
  // 等待发起重试的时间(ms)
  uint64 wait_to_start = 3;
}

// 继续已经终止的任务响应
message ContinueTerminatedTaskRsp {}

// 线上库embedding升级请求
message UpgradeEmbeddingReq {
  // 机器人ID
  uint64 robot_id = 1 [(validate.rules).uint64 = {gte: 1}];
  // 升级的embedding版本id
  uint64 embedding_version_id = 2 [(validate.rules).uint64 = {gte: 1}];
  // 应用id
  uint64 bot_biz_id = 3;
}

// 线上库embedding升级响应
message UpgradeEmbeddingRsp {}

// 校验vector版本号是否有效的请求
message CheckVersionReq{
  // 机器人ID
  uint64 robot_id = 1 [(validate.rules).uint64 = {gte: 1}];
  // 版本号
  uint64 version_id = 2 [(validate.rules).uint64 = {gte: 1}];
}

// 校验vector版本号是否有效的响应
message CheckVersionRsp{
  // 版本号是否有效
  bool is_valid = 1;
}

// 增加检索库请求
message CreateIndexReq {
  // 机器人ID
  uint64 robot_id = 1 [(validate.rules).uint64 = {gte: 1}];
  // 索引库ID, 业务自己定义保存
  uint64 index_id = 2 [(validate.rules).uint64 = {gte: 1}];
  // embedding 版本
  uint64 embedding_version = 3;
  // 文档类型 (1 QA, 2 文档段, 3 拒答问题, 4 搜索引擎)
  uint32 doc_type = 4;
  // 应用id
  uint64 bot_biz_id = 5;
}

// 增加检索库响应
message CreateIndexRsp {}

// 删除检索库请求
message DeleteIndexReq {
  // 机器人ID
  uint64 robot_id = 1 [(validate.rules).uint64 = {gte: 1}];
  // 索引库ID, 业务自己定义保存
  uint64 index_id = 2 [(validate.rules).uint64 = {gte: 1}];
  // embedding 版本
  uint64 embedding_version = 3;
  // 应用id
  uint64 bot_biz_id = 4;
}

// 删除检索库响应
message DeleteIndexRsp{}

// 增加特征请求
message AddVectorReq{
  // 机器人ID
  uint64 robot_id = 1 [(validate.rules).uint64 = {gte: 1}];
  // 索引库ID, 业务自己定义保存
  uint64 index_id = 2 [(validate.rules).uint64 = {gte: 1}];
  // ID
  uint64 id = 3 [(validate.rules).uint64 = {gte: 1}];
  // 文档内容
  string page_content = 4 [(validate.rules).string = {min_len: 1, max_len: 2000}];
  // 文档类型 (1 QA, 2 文档段, 3 拒答问题, 4 搜索引擎)
  uint32 doc_type = 5;
  // embedding 版本
  uint64 embedding_version = 6;
  // labels 标签
  repeated VectorLabel labels = 7;
  // 有效期，时间戳，秒。填0时不过期
  int64 expire_time = 8;
  // 应用id
  uint64 bot_biz_id = 9;
  // 知识库类型，区分离线知识库还是实时文档; embedding升级时调用AddVector需要区分
  KnowledgeType type = 10;
}

// 增加特征响应
message AddVectorRsp{}

// 删除特征请求
message DeleteVectorReq{
  // 机器人ID
  uint64 robot_id = 1 [(validate.rules).uint64 = {gte: 1}];
  // 索引库ID, 业务自己定义保存
  uint64 index_id = 2 [(validate.rules).uint64 = {gte: 1}];
  // ID
  uint64 id = 3 [(validate.rules).uint64 = {gte: 1}];
  // embedding 版本
  uint64 embedding_version = 4;
  // 应用id
  uint64 bot_biz_id = 5;
}

// 删除特征响应
message DeleteVectorRsp{}

// 修改特征请求
message UpdateVectorReq{
  // 机器人ID
  uint64 robot_id = 1 [(validate.rules).uint64 = {gte: 1}];
  // 索引库ID, 业务自己定义保存
  uint64 index_id = 2 [(validate.rules).uint64 = {gte: 1}];
  // ID
  uint64 id = 3 [(validate.rules).uint64 = {gte: 1}];
  // 文档内容
  string page_content = 4 [(validate.rules).string = {min_len: 1, max_len: 2000}];
  // 文档类型 (1 QA, 2 文档段, 3 拒答问题, 4 搜索引擎)
  uint32 doc_type = 5;
  // embedding 版本
  uint64 embedding_version = 6;
  // labels 标签
  repeated VectorLabel labels = 7;
  // 有效期，时间戳，秒。填0时不过期
  int64 expire_time = 8;
  // 应用id
  uint64 bot_biz_id = 9;
}

// 修改特征响应
message UpdateVectorRsp{}

// 搜索数据
message SearchData {
    // 知识库自增ID(需与入库的RobotID保持一致)
    uint64 knowledge_id = 1 [(validate.rules).uint64 = {gte: 1}];
    // 知识库业务ID
    uint64 knowledge_biz_id = 2;
    repeated SearchFilter filters = 3;
    // Embedding版本
    uint64 embedding_version = 4;
    // labels 标签
    repeated SearchVectorLabel labels = 5;
    // 标签表达式
    LabelExpression label_expression = 6;
    // 问答库版本
    uint64 qa_version = 7;
    // 过滤器名称
    string filter_key = 8;
}

message SearchFilter {
  // 索引库ID, 业务自己定义保存
  uint64 index_id = 1;
  // 置信度
  float confidence = 2;
  // 取 top_n
  uint32 top_n = 3;
  // 文档类型 (1 QA, 2 文档段, 3 拒答问题, 4 搜索引擎)
  uint32 doc_type = 4;
  // 标签表达式字符串 key1="value1" and key2="value2"
  // 优先使用表达式字符串, 表达式字符串无值时使用表达式
  string label_expr_string = 5;
  // 标签表达式
  VectorLabelExpr label_expr = 6;
}

// 重排模型
message Rerank {
  string model = 1; // 模型名称
  uint32 top_n = 2; // top_n
  bool enable = 3; // 启用
}

// 多库检索请求
message SearchMultiKnowledgeReq {
  // 应用自增ID
  uint64 robot_id = 1 [(validate.rules).uint64 = {gte: 1}];
  // 应用业务id
  uint64 bot_biz_id = 2;
  // 问题
  string question = 3 [(validate.rules).string = {min_len: 1}];
  // 请求query带图片URL，用于图搜图
  repeated string image_urls = 4;
  // 拆解的子问题，为空表示没有子问题
  repeated string sub_questions = 5;
  // 取前 n 条 (默认3)
  uint32 top_n = 6;
  // 重排模型
  Rerank rerank = 7;
  // 知识检索策略配置
  SearchStrategy search_strategy = 9;
  // 模型名称
  string model_name = 10;
  // 搜索数据
  repeated SearchData search_data = 11;
  // 全局Filter
  repeated SearchFilter filters = 12;
}

// 问题查询请求
message SearchVectorReq {
  // 机器人ID
  uint64 robot_id = 1 [(validate.rules).uint64 = {gte: 1}];
  // 问题
  string question = 2 [(validate.rules).string = {min_len: 1}];
  message Filter {
    // 索引库ID, 业务自己定义保存
    uint64 index_id = 1;
    // 置信度
    float confidence = 2;
    // 取 top_n
    uint32 top_n = 3;
    // 文档类型 (1 QA, 2 文档段, 3 拒答问题, 4 搜索引擎)
    uint32 doc_type = 4;
    // 标签表达式字符串 key1="value1" and key2="value2"
    // 优先使用表达式字符串, 表达式字符串无值时使用表达式
    string label_expr_string = 5;
    // 标签表达式
    VectorLabelExpr label_expr = 6;
  }
  repeated Filter filters = 3;
  // 取前 n 条 (默认3)
  uint32 top_n = 4;
  // embedding 版本
  uint64 embedding_version = 5;
  // 重排模型
  message Rerank {
    string model = 1; // 模型名称
    uint32 top_n = 2; // top_n
    bool enable = 3; // 启用
  }
  Rerank rerank = 7;
  // 过滤器名称
  string filter_key = 8;
  // labels 标签
  repeated SearchVectorLabel labels = 9;
  // 请求query带图片URL，用于图搜图
  repeated string image_urls = 10;
  // 应用id
  uint64 bot_biz_id = 11;
  // 标签表达式
  LabelExpression label_expression = 12;
  // 拆解的子问题，为空表示没有子问题
  repeated string sub_questions = 13;
  // 知识检索策略配置
  SearchStrategy search_strategy = 14;
  // 模型名称
  string model_name = 15;
}

// 问题查询响应
message SearchVectorRsp {
  message Doc {
    // 索引库ID, 业务自己定义保存
    uint64 index_id = 1;
    // 对应文档类型关联的数据ID, 类型为 QA 时为 QAID, 类型为 文档段 时为 文档段ID
    uint64 id = 2;
    // 置信度
    float confidence = 3;
    // question
    string question = 4;
    // answer
    string answer = 5;
    // 内容
    string page_content = 6;
    // 原始内容
    string org_data = 7;
    // 文档类型 (1 QA, 2 文档段, 3 拒答问题, 4 搜索引擎)
    uint32 doc_type = 8;
    // 是否big_data true-表示org_data是由big_data填充
    bool is_big_data = 9;
    // 算法扩展信息
    RetrievalExtra extra = 10;
    // 检索结果类型
    RetrievalResultType result_type = 11;
    // 文档ID
    uint64 doc_id = 12;
    // 检索命中的图片URL
    repeated string image_urls = 13;
    // 相似问相关的额外信息
    message SimilarQuestionExtra {
      // 当检索到的是相似问时，返回该相似问ID
      uint64 similar_id = 1;
      // 当检索到的是相似问时，返回该相似问的问题
      string similar_question = 2;
    }
    SimilarQuestionExtra similar_question_extra = 14;
    // text2sql检索结果带上sheet名称，参考来源溯源用到
    message Text2SQLExtra{
      message TableInfo {
        string table_name = 1;
        uint64 doc_id = 2;
      }
      repeated TableInfo table_infos = 1;
    }
    Text2SQLExtra text2sql_extra = 15;
    // 问题描述 qa意图的描述
    string question_desc = 16;
    // 过滤器名称
    string filter_key = 17;
  }
  // 文档数据
  repeated Doc docs = 1;
}

// 增加检索库请求
message DirectCreateIndexReq {
  // 检索库名称
  string name = 1 [(validate.rules).string = {min_len: 1}];
  // 索引库ID, 业务自己定义保存
  uint64 index_id = 2 [(validate.rules).uint64 = {gte: 1}];
  // embedding 版本
  uint64 embedding_version = 3;
  // 文档类型 (1 QA, 2 文档段, 3 拒答问题, 4 搜索引擎)
  uint32 doc_type = 4;
}

// 增加检索库响应
message DirectCreateIndexRsp {}

// 删除检索库请求
message DirectDeleteIndexReq {
  // 检索库名称
  string name = 1 [(validate.rules).string = {min_len: 1}];
  // 索引库ID, 业务自己定义保存
  uint64 index_id = 2 [(validate.rules).uint64 = {gte: 1}];
  // embedding 版本
  uint64 embedding_version = 3;
}

// 删除检索库响应
message DirectDeleteIndexRsp{}

// 增加特征请求
message DirectAddVectorReq{
  // 检索库名称
  string name = 1 [(validate.rules).string = {min_len: 1}];
  // 索引库ID, 业务自己定义保存
  uint64 index_id = 2 [(validate.rules).uint64 = {gte: 1}];
  // ID
  uint64 id = 3 [(validate.rules).uint64 = {gte: 1}];
  // 文档内容
  string page_content = 4 [(validate.rules).string = {min_len: 1, max_len: 2000}];
  // 文档类型 (1 QA, 2 文档段, 3 拒答问题, 4 搜索引擎)
  uint32 doc_type = 5;
  // embedding 版本
  uint64 embedding_version = 6;
  // labels 标签
  repeated VectorLabel labels = 7;
  // 有效期，时间戳，秒。填0时不过期
  int64 expire_time = 8;
}

// 增加特征响应
message DirectAddVectorRsp{}

// 删除特征请求
message DirectDeleteVectorReq{
  // 检索库名称
  string name = 1 [(validate.rules).string = {min_len: 1}];
  // 索引库ID, 业务自己定义保存
  uint64 index_id = 2 [(validate.rules).uint64 = {gte: 1}];
  // ID
  uint64 id = 3 [(validate.rules).uint64 = {gte: 1}];
  // embedding 版本
  uint64 embedding_version = 4;
}

// 删除特征响应
message DirectDeleteVectorRsp{}

// 修改特征请求
message DirectUpdateVectorReq{
  // 检索库名称
  string name = 1 [(validate.rules).string = {min_len: 1}];
  // 索引库ID, 业务自己定义保存
  uint64 index_id = 2 [(validate.rules).uint64 = {gte: 1}];
  // ID
  uint64 id = 3 [(validate.rules).uint64 = {gte: 1}];
  // 文档内容
  string page_content = 4 [(validate.rules).string = {min_len: 1, max_len: 2000}];
  // 文档类型 (1 QA, 2 文档段, 3 拒答问题, 4 搜索引擎)
  uint32 doc_type = 5;
  // embedding 版本
  uint64 embedding_version = 6;
  // labels 标签
  repeated VectorLabel labels = 7;
  // 有效期，时间戳，秒。填0时不过期
  int64 expire_time = 8;
}

// 修改特征响应
message DirectUpdateVectorRsp{}

// 问题查询请求
message DirectSearchVectorReq {
  // 检索库名称
  string name = 1 [(validate.rules).string = {min_len: 1}];
  // 问题
  string question = 2 [(validate.rules).string = {min_len: 1}];
  message Filter {
    // 索引库ID, 业务自己定义保存
    uint64 index_id = 1;
    // 置信度
    float confidence = 2;
    // 取 top_n
    uint32 top_n = 3;
    // 文档类型 (1 QA, 2 文档段, 3 拒答问题, 4 搜索引擎)
    uint32 doc_type = 4;
    // 标签表达式字符串 key1="value1" and key2="value2"
    // 优先使用表达式字符串, 表达式字符串无值时使用表达式
    string label_expr_string = 5;
    // 标签表达式
    VectorLabelExpr label_expr = 6;
  }
  repeated Filter filters = 3;
  // 取前 n 条 (默认3)
  uint32 top_n = 4;
  // embedding 版本
  uint64 embedding_version = 5;
  // 重排模型
  message Rerank {
    string model = 1; // 模型名称
    uint32 top_n = 2; // top_n
    bool enable = 3; // 启用
  }
  Rerank rerank = 7;
}

// 问题查询响应
message DirectSearchVectorRsp {
  message Doc {
    // 索引库ID, 业务自己定义保存
    uint64 index_id = 1;
    // 对应文档类型关联的数据ID, 类型为 QA 时为 QAID, 类型为 文档段 时为 文档段ID
    uint64 id = 2;
    // 置信度
    float confidence = 3;
    // question
    string question = 4;
    // answer
    string answer = 5;
    // 内容
    string page_content = 6;
    // 原始内容
    string org_data = 7;
    // 文档类型 (1 QA, 2 文档段, 3 拒答问题, 4 搜索引擎)
    uint32 doc_type = 8;
  }
  // 文档数据
  repeated Doc docs = 1;
}

// 添加Bigdata到ES的请求
// es的index 在vector维护
message AddBigDataElasticReq{
  repeated BigData data = 1;
  // 知识库类型，区分离线知识库还是实时文档
  KnowledgeType type = 2;
}


message BigData{
  // 机器人ID
  uint64 robot_id = 1 [(validate.rules).uint64 = {gte: 1}];
  // 业务的文档ID
  uint64 doc_id = 2 [(validate.rules).uint64 = {gte: 1}];
  // BigData的ID
  string big_data_id = 3 [(validate.rules).string = {min_len: 1}];
  // BigData 分片起始索引
  int32 BigStart = 4;
  // BigData 分片结束索引
  int32 BigEnd = 5;
  // BigData的内容
  string BigString = 6 [(validate.rules).string = {min_len: 1}];
}


// BatchGetBigDataESByRobotBigDataIDReq 根据robot_id, big_data_id 从bigDataEs中查找数据
message BatchGetBigDataESByRobotBigDataIDReq{
  // 机器人ID
  uint64 robot_id = 1 [(validate.rules).uint64 = {gte: 1}];
  // BigData的ID
  repeated string big_data_ids = 2 ;
  // 知识库类型，区分离线知识库还是实时文档
  KnowledgeType type = 3;
}

// BatchGetBigDataESByRobotBigDataIDResp 根据robot_id,  big_data_id 从bigDataEs中查找数据响应
message BatchGetBigDataESByRobotBigDataIDResp{
  repeated BigData data = 1;
}

// 添加Bigdata到ES的响应
message AddBigDataElasticRsp{}

// 从ES删除Bigdata的请求
message DeleteBigDataElasticReq{
  // 机器人ID
  uint64 robot_id = 1 [(validate.rules).uint64 = {gte: 1}];
  // 业务的文档ID
  uint64 doc_id = 2 [(validate.rules).uint64 = {gte: 1}];
  // 知识库类型，区分离线知识库还是实时文档
  KnowledgeType type = 3;
  // 是否硬删除，默认否，只有在文档删除场景才硬删除
  bool hard_delete = 4;
}

// 从ES删除Bigdata响应
message DeleteBigDataElasticRsp{}


// 从ES恢复离线知识库的BigData的请求
message RecoverBigDataElasticReq{
  // 机器人ID
  uint64 robot_id = 1 [(validate.rules).uint64 = {gte: 1}];
  // 业务的文档ID
  uint64 doc_id = 2 [(validate.rules).uint64 = {gte: 1}];
}

// 从ES恢复离线知识库的BigData的响应
message RecoverBigDataElasticRsp{}

// 增加知识请求
message AddKnowledgeReq{
  // 机器人ID
  uint64 robot_id = 1 [(validate.rules).uint64 = {gte: 1}];
  // 索引库ID, 业务自己定义保存
  uint64 index_id = 2 [(validate.rules).uint64 = {gte: 1}];
  // ID
  uint64 id = 3 [(validate.rules).uint64 = {gte: 1}];
  // 文档类型 (1 QA, 2 文档或者表格, 3 拒答问题, 4 搜索引擎)
  uint32 doc_type = 4;
  // 文档切片类型，segment-文档切片 table-表格，当doc_type=2时需要填写
  string segment_type = 5;
  // 文档id，如果是文档则是对应的文档id，如果是qa，则用关联的文档id，即t_doc_qa表里的doc_id字段，没有则为0
  uint64 doc_id = 6;
  // 文档内容
  string page_content = 7;
  // embedding 版本
  uint64 embedding_version = 8;
  // labels 标签
  repeated VectorLabel labels = 9;
  // 有效期，时间戳，秒。填0时不过期
  int64 expire_time = 10;
  // 应用id
  uint64 bot_biz_id = 11;
}

// 增加知识响应
message AddKnowledgeRsp{}

// 批量增加知识请求
message BatchAddKnowledgeReq{
  // 机器人ID
  uint64 robot_id = 1 [(validate.rules).uint64 = {gte: 1}];
  // 索引库ID, 业务自己定义保存
  uint64 index_id = 2 [(validate.rules).uint64 = {gte: 1}];
  // 文档类型 (1 QA, 2 文档或者表格, 3 拒答问题, 4 搜索引擎)
  uint32 doc_type = 3;
  // embedding 版本
  uint64 embedding_version = 4;
  // 文档切片数组 支持批量
  repeated KnowledgeData knowledge = 5;
  // 应用id
  uint64 bot_biz_id = 6;
}

// 批量增加知识响应
message BatchAddKnowledgeRsp{}

// 删除知识请求
message DeleteKnowledgeReq{
  // 机器人ID
  uint64 robot_id = 1 [(validate.rules).uint64 = {gte: 1}];
  // 索引库ID, 业务自己定义保存
  uint64 index_id = 2 [(validate.rules).uint64 = {gte: 1}];
  // ID
  uint64 id = 3 [(validate.rules).uint64 = {gte: 1}];
  // 文档类型 (1 QA, 2 文档或者表格, 3 拒答问题, 4 搜索引擎)
  uint32 doc_type = 4;
  // 文档切片类型，segment-文档切片 table-表格，当doc_type=2时需要填写
  string segment_type = 5;
  // embedding 版本
  uint64 embedding_version = 6;
  // 应用id
  uint64 bot_biz_id = 7;

}

// 删除知识响应
message DeleteKnowledgeRsp{}

// 批量删除知识请求
message BatchDeleteKnowledgeReq{
  // 机器人ID
  uint64 robot_id = 1 [(validate.rules).uint64 = {gte: 1}];
  // 索引库ID, 业务自己定义保存
  uint64 index_id = 2 [(validate.rules).uint64 = {gte: 1}];
  // 要删除的数据，文档片段ID和对应的切片类型
  repeated KnowledgeIDType data = 3;
  // 文档类型 (1 QA, 2 文档或者表格, 3 拒答问题, 4 搜索引擎)
  uint32 doc_type = 4;
  // embedding 版本
  uint64 embedding_version = 5;
  // 应用id
  uint64 bot_biz_id = 6;
}

// 批量删除知识响应
message BatchDeleteKnowledgeRsp{}

// 批量删除发布库的所有知识（包括QA/文档/混合检索/text2sql等）请求
message BatchDeleteAllKnowledgeProdReq{
  // 机器人ID
  uint64 robot_id = 1 [(validate.rules).uint64 = {gte: 1}];
  // 文档库版本
  uint64 version_id = 2 [(validate.rules).uint64 = {gte: 1}];
  // 要删除的数据，文档片段ID和对应的切片类型
  repeated KnowledgeIDType data = 3;
  // 文档类型 (1 QA, 2 文档或者表格, 3 拒答问题, 4 搜索引擎)
  uint32 doc_type = 4;
  // 应用id
  uint64 bot_biz_id = 5;
  // 文档id，如果是文档则是对应的文档id，如果是qa，则用关联的文档id，即t_doc_qa表里的doc_id字段，没有则为0
  uint64 doc_id = 6;
}

// 批量删除发布库的知识响应
message BatchDeleteAllKnowledgeProdRsp{}

// 修改知识请求
message UpdateKnowledgeReq{
  // 机器人ID
  uint64 robot_id = 1 [(validate.rules).uint64 = {gte: 1}];
  // 索引库ID, 业务自己定义保存
  uint64 index_id = 2 [(validate.rules).uint64 = {gte: 1}];
  // ID
  uint64 id = 3 [(validate.rules).uint64 = {gte: 1}];
  // 文档类型 (1 QA, 2 文档或者表格, 3 拒答问题, 4 搜索引擎)
  uint32 doc_type = 4;
  // 文档切片类型，segment-文档切片 table-表格，当doc_type=2时需要填写
  string segment_type = 5;
  // 文档内容
  string page_content = 6;
  // embedding 版本
  uint64 embedding_version = 7;
  // labels 标签
  repeated VectorLabel labels = 8;
  // 有效期，时间戳，秒。填0时不过期
  int64 expire_time = 9;
  // 文档id，如果是文档则是对应的文档id，如果是qa，则用关联的文档id，即t_doc_qa表里的doc_id字段，没有则为0
  uint64 doc_id = 10;
  // 应用id
  uint64 bot_biz_id = 11;
}

// 修改知识响应
message UpdateKnowledgeRsp{}


// 数据类型枚举
enum CellDataType {
  DATA_TYPE_STRING = 0;      // 字符串类型
  DATA_TYPE_INTEGER = 1;     // 整数类型
  DATA_TYPE_FLOAT = 2;       // 浮点数类型
  DATA_TYPE_DATE = 3;        // 日期类型
  DATA_TYPE_TIME = 4;        // 时间类型
  DATA_TYPE_DATETIME = 5;    // 日期时间类型
  DATA_TYPE_BOOLEAN = 6;     // 布尔类型
}

// 单元格
message Cell {
  string value = 1;
  CellDataType  cell_data_type = 2;
}
// 行
message Row {
  repeated Cell cells = 1;
}

// text2sql meta信息
message Text2SQLMeta {
  // 表头
  message Header {
    // 表头类型
    enum HeaderType {
      HEADER_TYPE_UNKNOWN = 0;
      HEADER_TYPE_COLUMN = 1;  // 列表头
      HEADER_TYPE_ROW = 2;     // 行表头
    }
    HeaderType type = 1;         // 表头类型
    repeated Row rows = 2;       // 表头数据，可能有多行，取index 0
  }
  // table_id：对于excel表格，就是sheet id
  string table_id = 1;
  // table_name：对于excel表格，就是sheet名
  string table_name = 2;
  // 文档类型 (2 文档或者表格)
  uint32 doc_type = 3;
  // 表头
  repeated Header headers = 4;// header取index 0
}

// test2sql的表格某一行的data
message Text2SQLRowData {
  // 分片ID
  uint64 id = 1 [(validate.rules).uint64 = {gte: 1}];
  // 文档切片类型，"text2sql_content"
  string segment_type = 2;
  // 某个row对应的所有单元格
  Row row = 3;
}

// text2sql更新的请求
message AddText2SQLReq{
  // 机器人ID
  uint64 robot_id = 1 [(validate.rules).uint64 = {gte: 1}];
  // 整个文档ID
  uint64 doc_id = 2 [(validate.rules).uint64 = {gte: 1}];
  // meta信息
  Text2SQLMeta meta = 3;
  // 文档内容
  repeated Text2SQLRowData rows = 4;
  // labels 标签
  repeated VectorLabel labels = 5;
  // 有效期，时间戳，秒。填0时不过期
  int64 expire_time = 6;
  // 文档的名称
  string file_name = 7;
  // 企业ID
  uint64 corp_id = 8 [(validate.rules).uint64 = {gte: 1}];
  // 是否添加到es，如果是关闭了结构化部分的文档，不添加到es，只添加到数据库
  bool disable_es = 9;
}

// text2sql更新的响应
message AddText2SQLRsp{}


// text2sql删除的请求
message DeleteText2SQLReq{
  // 机器人ID
  uint64 robot_id = 1 [(validate.rules).uint64 = {gte: 1}];
  // 文档ID
  uint64 doc_id = 2 [(validate.rules).uint64 = {gte: 1}];
  // 文档切片类型，"text2sql_content"
  string segment_type = 3;
}

// text2sql删除的响应
message DeleteText2SQLRsp{}


// 文档切片等知识数据
message KnowledgeData {
  // ID
  uint64 id = 1 [(validate.rules).uint64 = {gte: 1}];
  // 文档切片类型，segment-文档切片 table-表格，当doc_type=2时需要填写
  string segment_type = 2;
  // 文档id，如果是文档则是对应的文档id，如果是qa，则用关联的文档id，即t_doc_qa表里的doc_id字段，没有则为0
  uint64 doc_id = 3;
  // 文档内容
  string page_content = 4;
  // labels 标签
  repeated VectorLabel labels = 5;
  // 有效期，时间戳，秒。填0时不过期
  int64 expire_time = 6;
}

// 实时文档添加切片请求
message AddRealTimeKnowledgeReq {
  // 机器人ID
  uint64 robot_id = 1 [(validate.rules).uint64 = {gte: 1}];
  // 索引库ID, 业务自己定义保存
  uint64 index_id = 2 [(validate.rules).uint64 = {gte: 1}];
  // 文档类型 (1 QA, 2 文档或者表格, 3 拒答问题, 4 搜索引擎)
  uint32 doc_type = 3;
  // embedding 版本
  uint64 embedding_version = 4;
  // 文档切片数组 支持批量
  repeated KnowledgeData knowledge = 5;
  // 应用id
  uint64 bot_biz_id = 6;
}

// 实时文档添加切片响应
message AddRealTimeKnowledgeRsp{}

message KnowledgeIDType {
  // 文档片段ID
  uint64 id = 1 [(validate.rules).uint64 = {gte: 1}];
  // 文档切片类型，segment-文档切片 table-表格，当doc_type=2时需要填写
  string segment_type = 2;
}

// 实时文档删除切片请求
message DeleteRealTimeKnowledgeReq {
  // 机器人ID
  uint64 robot_id = 1 [(validate.rules).uint64 = {gte: 1}];
  // 索引库ID, 业务自己定义保存
  uint64 index_id = 2 [(validate.rules).uint64 = {gte: 1}];
  // embedding 版本
  uint64 embedding_version = 3;
  // 文档类型 (1 QA, 2 文档或者表格, 3 拒答问题, 4 搜索引擎)
  uint32 doc_type = 4;
  // 要删除的数据，文档片段ID和对应的切片类型
  repeated KnowledgeIDType data = 5;
  // 应用id
  uint64 bot_biz_id = 6;
}

// 实时文档删除切片响应
message DeleteRealTimeKnowledgeRsp {}

// 实时文档检索接口请求
message RetrievalRealTimeReq {
  // 机器人ID
  uint64 robot_id = 1 [(validate.rules).uint64 = {gte: 1}];
  // 问题
  string question = 2 [(validate.rules).string = {min_len: 1}];
  message Filter {
    // 索引库ID, 业务自己定义保存
    uint64 index_id = 1;
    // 置信度
    float confidence = 2;
    // 取 top_n
    uint32 top_n = 3;
    // 文档类型 (1 QA, 2 文档段, 3 拒答问题, 4 搜索引擎)
    uint32 doc_type = 4;
  }
  repeated Filter filters = 3;
  // 取前 n 条 (默认3)
  uint32 top_n = 4;
  // embedding 版本
  uint64 embedding_version = 5;
  // 重排模型
  message Rerank {
    string model = 1; // 模型名称
    uint32 top_n = 2; // top_n
    bool enable = 3; // 启用
  }
  Rerank rerank = 7;
  // 过滤器名称
  string filter_key = 8;
  // labels 标签
  repeated SearchVectorLabel labels = 9;
  // 请求query带图片URL，用于图搜图
  repeated string image_urls = 10;
  // 应用id
  uint64 bot_biz_id = 11;
  // 标签表达式
  LabelExpression label_expression = 12;
  // 拆解的子问题，为空表示没有子问题
  repeated string sub_questions = 13;
  // 知识检索策略配置
  SearchStrategy search_strategy = 14;
  // 模型名称
  string model_name = 15;
}

// 实时文档检索接口响应
message RetrievalRealTimeRsp {
  message Doc {
    // 索引库ID, 业务自己定义保存
    uint64 index_id = 1;
    // 对应文档类型关联的数据ID, 类型为 QA 时为 QAID, 类型为 文档段 时为 文档段ID
    uint64 id = 2;
    // 置信度
    float confidence = 3;
    // question
    string question = 4;
    // answer
    string answer = 5;
    // 内容
    string page_content = 6;
    // 原始内容
    string org_data = 7;
    // 文档类型 (1 QA, 2 文档段, 3 拒答问题, 4 搜索引擎)
    uint32 doc_type = 8;
    // 是否big_data true-表示org_data是由big_data填充
    bool is_big_data = 9;
    // 算法扩展信息
    RetrievalExtra extra = 10;
    // 文档ID
    uint64 doc_id = 11;
    // 检索结果类型
    RetrievalResultType result_type = 12;
    // 检索命中的图片URL
    repeated string image_urls = 13;
  }
  // 文档数据
  repeated Doc docs = 1;
}

// 清理应用向量库资源请求
message ClearAppVectorResourceReq {
  // 应用ID[表中的主键ID]
  uint64  robot_id= 1;
  // 应用业务ID
  string bot_biz_id = 2;
  // 任务ID
  uint64 task_id = 3;

}

message ClearAppVectorResourceRsp {}