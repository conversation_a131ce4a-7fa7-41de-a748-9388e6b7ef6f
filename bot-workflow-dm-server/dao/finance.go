package dao

import (
	"context"
	"errors"
	"fmt"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/entity"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/entity/tconst"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/util"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/util/config"
	admin "git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_admin_config_server"
	"git.woa.com/ivy/protobuf/trpc-go/qbot/finance/finance"
)

func isFinanceEnable() bool {
	return !config.IsPrivate() || (config.IsPrivate() && config.GetMainConfig().Privatization.EnableFinance)
}

// isModelFreeOfCharge 判断模型是否免费
func (d *dao) isModelFreeOfCharge(ctx context.Context, modelName string, isRAGRelated bool) bool {
	req := &admin.GetModelFinanceInfoReq{
		ModelNames: []string{modelName},
	}
	opts := []client.Option{WithTrpcSelector()}
	rsp, err := d.adminAPICli.GetModelFinanceInfo(ctx, req, opts...)
	if err != nil {
		LogAPI(ctx).Warnf("isModelFreeOfCharge invoke admin.GetModelFinanceInfo failed, model: %+v, err: %+v", modelName, err)
		return false
	}
	if info, ok := rsp.GetModelFinanceInfo()[modelName]; ok {
		if info.GetIsFree() {
			return true
		}
		if info.GetIsCustomModel() && !isRAGRelated {
			// 需求链接：https://tapd.woa.com/tapd_fe/70080800/task/detail/1070080800075810251
			// 自定义模型收费只针对RAG相关的场景，比如大模型知识问答、知识检索等；其他场景免费
			return true
		}
	}
	return false
}

// isModelFinanceStatusOK 判断模型计费是否可用
func (d *dao) isModelFinanceStatusOK(ctx context.Context, session *entity.Session, modelName string, isRAGRelated bool) (bool, error) {
	// 判断模型是否在白名单内
	if d.IsModelInWhiteList(session.AppID, modelName) {
		return true, nil
	}
	if d.isModelFreeOfCharge(ctx, modelName, isRAGRelated) {
		LogAPI(ctx).Infof("isModelStatusOK model:%s is free, model status ok", modelName)
		return true, nil
	}
	req := &finance.DescribeAccountStatusReq{
		Biz: &finance.Biz{
			BizType:    finance.BizType_BIZ_TYPE_LKE,
			SubBizType: tconst.FinanceSubBizTypeKnowledgeQA,
		},
		Account: &finance.Account{
			Sid: finance.SID(session.SID),
			Uin: fmt.Sprintf("%d", session.Uin),
		},
		ModelName: modelName,
	}
	opts := []client.Option{WithTrpcSelector()}
	rsp, err := d.financeCli.DescribeAccountStatus(ctx, req, opts...)
	if err != nil {
		LogAPI(ctx).Warnf("isModelStatusOK invoke finance.DescribeAccountStatus failed, "+
			"uin: %d, sid: %d, modelName: %s, err: %v", session.Uin, session.SID, modelName, err)
		var err0 *errs.Error
		if ok := errors.As(err, &err0); ok { // 当前模型无计费配置
			if err0.Code == 400 {
				LogAPI(ctx).Warnf("isModelStatusOK model:%s has no finance configuration", modelName)
				return true, nil
			}
		}
		return false, err
	}
	return rsp.GetStatus() == 0, nil
}

// reportDosage 上报用量
func (d *dao) reportDosage(ctx context.Context, req *finance.ReportDosageReq) error {
	// 没有上报用量数据，不调用计费接口，避免上报空数据导致计费接口报错
	if len(req.GetList()) == 0 {
		return nil
	}
	opts := []client.Option{WithTrpcSelector()}
	_, err := d.financeCli.ReportDosage(ctx, req, opts...)
	if err != nil {
		LogAPI(ctx).Warnf("reportDosage invoke finance.ReportDosage failed, req: %+v, err: %+v", req, err)
		return err
	}
	return nil
}

// BaseDosage 基础上报数据结构，包含共用字段
type BaseDosage struct {
	ModelName  string    // 模型标识
	DosageID   string    // 每个节点每次上报都需要用不同的ID
	StartTime  time.Time // 用量实际发生开始时间 业务侧定义
	EndTime    time.Time // 用量实际发生结束时间 业务侧定义
	RAGRelated bool      // 是否和RAG相关，若与RAG无关则**自定义模型**不计费
}

// ConcurrencyDosage 并发上报数据
type ConcurrencyDosage struct {
	BaseDosage
	Dosage int // 用量详情信息
}

// ReportConcurrencyDosage 上报并发用量
func (d *dao) ReportConcurrencyDosage(ctx context.Context, session *entity.Session, dosage ConcurrencyDosage) error {
	// 判断是否开启计费
	if !isFinanceEnable() {
		LogAPI(ctx).Infof("ReportConcurrencyDosage finance is not enable, skip")
		return nil
	}
	// 判断模型是否在白名单内
	if d.IsModelInWhiteList(session.AppID, dosage.ModelName) {
		return nil
	}
	// 判断模型是否免费
	if d.isModelFreeOfCharge(ctx, dosage.ModelName, dosage.RAGRelated) {
		LogAPI(ctx).Infof("ReportConcurrencyDosage model %s is free, skip", dosage.ModelName)
		return nil
	}
	payload := fmt.Sprintf(`{"AppID":"%v","AppType":"%v"}`, session.AppID, session.AppType)
	if session.BillingTag != nil {
		payload = fmt.Sprintf(`{"AppID":"%v","AppType":"%v","BillingTag":%v}`, session.AppID, session.AppType,
			util.ToJsonString(session.BillingTag))
	}
	req := &finance.ReportDosageReq{
		ModelName: dosage.ModelName,
		DosageId:  dosage.DosageID,
		Payload:   payload,
		Biz: &finance.Biz{
			BizType:    finance.BizType_BIZ_TYPE_LKE,
			SubBizType: tconst.FinanceSubBizTypeConcurrency,
		},
		Account: &finance.Account{
			Sid: finance.SID(session.SID),
			Uin: fmt.Sprintf("%d", session.Uin),
		},
	}
	if !dosage.StartTime.IsZero() {
		req.StartTime = uint64(dosage.StartTime.Unix()) // 单位s
	}
	if !dosage.EndTime.IsZero() {
		req.EndTime = uint64(dosage.EndTime.Unix()) // 单位s
	}
	req.List = append(req.List, &finance.ReportDosageReq_Detail{
		Dosage: float64(dosage.Dosage),
	})
	return d.reportDosage(ctx, req)
}

// TokenDosage token上报详情数据
type TokenDosage struct {
	BaseDosage
	InputDosages  []uint32 // 输入用量详情信息
	OutputDosages []uint32 // 输出用量详情信息
}

// ReportTokenDosage 上报token用量
func (d *dao) ReportTokenDosage(ctx context.Context, session *entity.Session, dosage TokenDosage) error {
	// 判断是否开启计费
	if !isFinanceEnable() {
		LogAPI(ctx).Infof("ReportTokenDosage finance is not enable, skip")
		return nil
	}
	// 判断是否在白名单内
	if d.IsModelInWhiteList(session.AppID, dosage.ModelName) {
		return nil
	}
	// 判断模型是否计费可用
	if ok, err := d.isModelFinanceStatusOK(ctx, session, dosage.ModelName, dosage.RAGRelated); !ok {
		log.InfoContextf(ctx, "ReportTokenDosage model:%s, status not ok, err: %v, skip", dosage.ModelName, err)
		return nil
	}
	// 判断模型是否免费
	if d.isModelFreeOfCharge(ctx, dosage.ModelName, dosage.RAGRelated) {
		log.InfoContextf(ctx, "ReportTokenDosage model:%s, is free, skip", dosage.ModelName)
		return nil
	}
	subBizType := session.FinanceSubBizType
	if subBizType == "" {
		subBizType = tconst.FinanceSubBizTypeKnowledgeQA
	}
	payload := fmt.Sprintf(`{"AppID":"%v","AppType":"%v"}`, session.AppID, session.AppType)
	if session.BillingTag != nil {
		payload = fmt.Sprintf(`{"AppID":"%v","AppType":"%v","BillingTag":%v}`, session.AppID, session.AppType,
			util.ToJsonString(session.BillingTag))
	}
	req := &finance.ReportDosageReq{
		ModelName: dosage.ModelName,
		DosageId:  dosage.DosageID,
		Payload:   payload,
		Biz: &finance.Biz{
			BizType:    finance.BizType_BIZ_TYPE_LKE,
			SubBizType: subBizType,
		},
		Account: &finance.Account{
			Sid: finance.SID(session.SID),
			Uin: fmt.Sprintf("%d", session.Uin),
		},
	}
	if !dosage.StartTime.IsZero() {
		req.StartTime = uint64(dosage.StartTime.Unix()) // 单位s
	}
	if !dosage.EndTime.IsZero() {
		req.EndTime = uint64(dosage.EndTime.Unix()) // 单位s
	}
	for i := range dosage.InputDosages {
		req.List = append(req.List, &finance.ReportDosageReq_Detail{
			Dosage:  float64(dosage.InputDosages[i]),
			Payload: `{"type":"input"}`,
		})
	}
	for i := range dosage.OutputDosages {
		req.List = append(req.List, &finance.ReportDosageReq_Detail{
			Dosage:  float64(dosage.OutputDosages[i]),
			Payload: `{"type":"output"}`,
		})
	}
	return d.reportDosage(ctx, req)
}

// OverloadDosage 超用量dosage
type OverloadDosage struct {
	ModelName string    // 模型标识
	UsageTime time.Time // 用量实际发生开始时间 业务侧定义
	// 注意：这里没有使用BaseDosage，因为OverloadDosage结构与其他两个不同
}

// ReportOverConcurrencyDosage 上报超并发dosage
func (d *dao) ReportOverConcurrencyDosage(ctx context.Context, session *entity.Session, dosage OverloadDosage) error {
	// 判断是否开启计费
	if !isFinanceEnable() {
		LogAPI(ctx).Infof("ReportOverConcurrencyDosage finance is not enable, skip")
		return nil
	}
	// 判断是否在白名单内
	if d.IsModelInWhiteList(session.AppID, dosage.ModelName) {
		return nil
	}
	if len(session.Query) == 0 {
		LogAPI(ctx).Infof("ReportOverConcurrencyDosage query is empty, skip")
		return nil
	}
	req := &finance.ReportOverConcurrencyDosageReq{
		Biz: &finance.Biz{BizType: finance.BizType_BIZ_TYPE_LKE, SubBizType: tconst.FinanceSubBizTypeConcurrency},
		Account: &finance.Account{
			Sid: finance.SID(session.SID),
			Uin: fmt.Sprintf("%d", session.Uin),
		},
		ModelName: dosage.ModelName,
		UsageTime: uint64(dosage.UsageTime.Unix()),
		Query:     session.Query,
		AppId:     session.AppID,
	}
	opts := []client.Option{WithTrpcSelector()}
	_, err := d.financeCli.ReportOverConcurrencyDosage(ctx, req, opts...)
	if err != nil {
		LogAPI(ctx).Warnf("ReportOverConcurrencyDosage invoke finance.ReportOverConcurrencyDosage failed, req: %+v, err: %+v", req, err)
		return err
	}
	return nil
}

// ReportOverMinuteDosage 上报超TPM/QPMdosage
func (d *dao) ReportOverMinuteDosage(ctx context.Context, session *entity.Session, dosage OverloadDosage) error {
	// 判断是否开启计费
	if !isFinanceEnable() {
		LogAPI(ctx).Infof("ReportOverMinuteDosage finance is not enable, skip")
		return nil
	}
	// 判断是否在白名单内
	if d.IsModelInWhiteList(session.AppID, dosage.ModelName) {
		return nil
	}
	if len(session.Query) == 0 {
		LogAPI(ctx).Infof("ReportOverMinuteDosage query is empty, skip")
		return nil
	}
	req := &finance.ReportOverMinuteDosageReq{
		Biz: &finance.Biz{BizType: finance.BizType_BIZ_TYPE_LKE, SubBizType: tconst.FinanceSubBizTypeConcurrency},
		Account: &finance.Account{
			Sid: finance.SID(session.SID),
			Uin: fmt.Sprintf("%d", session.Uin),
		},
		ModelName: dosage.ModelName,
		UsageTime: uint64(dosage.UsageTime.Unix()),
		Query:     session.Query,
		BizAppId:  session.AppID,
	}
	opts := []client.Option{WithTrpcSelector()}
	_, err := d.financeCli.ReportOverMinuteDosage(ctx, req, opts...)
	if err != nil {
		LogAPI(ctx).Warnf("ReportOverMinuteDosage invoke finance.ReportOverMinuteDosage failed, req: %+v, err: %+v", req, err)
		return err
	}
	return nil
}
