package workflow

import (
	"context"

	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/entity"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/util"
)

func executeVarAggregationNode(ctx context.Context, session *entity.Session, nodeTask *entity.NodeTask,
	nodeResultQueue chan entity.NodeResult) {
	node := nodeTask.Node
	LogWorkflow(ctx).Infof("executeVarAggregationNode start, nodeName: %v, nodeID: %v", node.NodeName, node.NodeID)
	outputResult := make(map[string]any)
	nodeData := node.GetVarAggregationNodeData()
	if nodeData == nil {
		LogWorkflow(ctx).Errorf("invalid node data")
		sendFailedResult(nodeResultQueue, nodeTask.BelongNodeID, node.NodeID, entity.ErrMissingParam)
		return
	}
	for _, group := range nodeData.GetGroups() {
		var value any
		for _, item := range group.GetItems() {
			input := item.GetInput()
			value = getInputValue(session, nodeTask.BelongNodeID, input, nil)
			if !isValueEmpty(value) {
				break
			}
		}
		outputResult[group.Name] = value
	}

	result := entity.NodeResult{
		BelongNodeID:     nodeTask.BelongNodeID,
		NodeID:           node.NodeID,
		Status:           entity.NodeStatusSuccess,
		Input:            "",
		Output:           util.ToJsonString(outputResult),
		TaskOutput:       "",
		Reply:            "",
		FailMessage:      "",
		ErrorCode:        "",
		ReferencesMap:    nil,
		CostMilliSeconds: 0,
		StatisticInfo:    nil,
	}
	nodeResultQueue <- result
	LogWorkflow(ctx).Infof("executeVarAggregationNode done, nodeName: %v", node.NodeName)
}
