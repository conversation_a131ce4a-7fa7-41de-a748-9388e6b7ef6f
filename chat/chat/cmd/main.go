// Package main 服务入口
package main

import (
	_ "git.code.oa.com/trpc-go/trpc-config-rainbow"
	_ "git.code.oa.com/trpc-go/trpc-filter/recovery"
	_ "git.code.oa.com/trpc-go/trpc-filter/validation"
	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	_ "git.code.oa.com/trpc-go/trpc-metrics-prometheus"
	_ "git.code.oa.com/trpc-go/trpc-metrics-runtime"
	_ "git.code.oa.com/trpc-go/trpc-naming-polaris"
	_ "git.woa.com/dialogue-platform/common/v3/filters/i18n"
	"git.woa.com/dialogue-platform/go-comm/clues"
	"git.woa.com/dialogue-platform/go-comm/panicprinter"
	"git.woa.com/dialogue-platform/go-comm/plugin"
	"git.woa.com/dialogue-platform/go-comm/runtime0"
	_ "git.woa.com/galileo/trpc-go-galileo"
	"git.woa.com/ivy/qbot/qbot/chat/pkg/agentlogger"
	_ "git.woa.com/ivy/qbot/qbot/chat/pkg/filters"
	"git.woa.com/ivy/qbot/qbot/chat/pkg/registry"
)

const (
	// 编译版本号, 流水线会注入相关信息
	buildVersion = "服务编译版本号, 勿动~~"
)

func init() {
	// rand.Seed(time.Now().UnixNano())
	// rand.New(rand.NewSource(time.Now().UnixNano()))
	clues.Init()
	agentlogger.Init()

}

func main() {
	defer panicprinter.PrintPanic()
	runtime0.SetServerVersion(buildVersion)
	runtime0.PrintVersion()

	srv := trpc.NewServer() // 启动一个服务实例
	plugin.Init()           // 需要放在 trpc.NewServer() 之后
	reg := registry.New()

	err := reg.Register(srv)
	if err != nil {
		log.Fatal(err)
	}

	if err = srv.Serve(); err != nil {
		log.Fatal(err)
	}
}
