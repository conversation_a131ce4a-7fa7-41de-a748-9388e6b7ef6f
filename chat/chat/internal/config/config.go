// Package config 写配置定义和热加载等配置相关逻辑
package config

import (
	"context"
	"strings"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/config"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/common/v3/errors"
	"git.woa.com/dialogue-platform/common/v3/plugins/i18n"
	"gopkg.in/yaml.v2"
)

const (
	providerName   = "rainbow"          // 对应yaml文件中的providers.name
	applicationKey = "application.yaml" // 对应我们具体想要获取的配置的键位key
)

// Application 业务配置
type Application struct {
	DisableContentCheck bool     `yaml:"disable_content_check"` // 是否禁用内容审核
	SkipCheckEvil       []uint64 `yaml:"skip_check"`
	Websocket           struct {
		TokenTTL         uint64 `yaml:"token_ttl"`          // Token 有效期 (秒)
		ClientTTL        uint64 `yaml:"client_ttl"`         // 客户端有效期 (秒)
		EmitMaxGoroutine int    `yaml:"emit_max_goroutine"` // 最大并发推送数
	} `yaml:"websocket"` // Websocket 配置
	Reconnect struct {
		ReconnectDataTTL uint64   `yaml:"reconnect_data_ttl"` // 重连数据过期时间 (秒) 10分钟
		OutputRate       uint64   `yaml:"output_rate"`        // 重连恢复的输出频率 (毫秒) 10ms
		RecoveryMaxTime  uint64   `yaml:"recovery_max_time"`  // 允许重连恢复最长时间 (秒) 15分钟
		SupportEvents    []string `yaml:"support_events"`     // 支持的重连事件
		MaxTimes         int      `yaml:"max_times"`          // 最大重连次数
	} `yaml:"reconnect"` // 重连配置
	Apex struct {
		Namespace     string `yaml:"namespace"`
		Service       string `yaml:"service"`
		DefaultTarget string `yaml:"default_target"`
		UseGrpc       bool   `yaml:"use_grpc"` // 是否使用grpc直连
	} `yaml:"apex"`
	EventBus struct {
		WorkerNum     int `yaml:"worker_num"`      // 事件处理协程数
		EventPoolSize int `yaml:"event_pool_size"` // 事件池大小
	} `yaml:"event_bus"` // 事件总线配置
	Session struct {
		DownvoteExpire        int    `yaml:"downvote_expire"`         // 点踩过期时间 (秒)
		GreetingInterval      int    `yaml:"greeting_interval"`       // 下发欢迎语间隔 (秒)
		IntentTransferKeyword string `yaml:"intent_transfer_keyword"` // 语义转人工关键词
		SilentInterval        int    `yaml:"silent_interval"`         // 静默间隔 (秒)
	} `yaml:"session"` // 会话配置
	Bot struct {
		Timeout                    uint                `yaml:"timeout"`                       // 首字超时 (秒)
		QueueTimeout               uint                `yaml:"queue_timeout"`                 // 请求排队超时 (秒)
		IntentThreshold            int                 `yaml:"intent_threshold"`              // 意图阈值，超过阈值不做意图识别
		SearchThreshold            int                 `yaml:"search_threshold"`              // 搜索阈值，超过阈值不检索
		BusyReplies                map[uint8]string    `yaml:"busy_replies"`                  // 并发超限回复
		EvilReply                  string              `yaml:"evil_reply"`                    // 对话, 敏感回复
		TagEvilReply               string              `yaml:"tag_evil_reply"`                // 标签提取, 敏感回复
		SummaryEvilReply           string              `yaml:"summary_evil_reply"`            // 摘要, 敏感回复
		RejectedReply              string              `yaml:"rejected_reply"`                // 拒答回复
		SummaryReject              string              `yaml:"summary_reject"`                // 摘要拒答回复
		EmptyReply                 string              `yaml:"empty_reply"`                   // 为空回复
		MultiIntentReply           string              `yaml:"multi_intent_reply"`            // 多意图回复
		ClarifyConfirmReply        string              `yaml:"clarify_confirm_reply"`         // 澄清确认回复
		ResponseChannelSize        uint                `yaml:"response_channel_size"`         // 响应 Channel 大小
		AnswerFromKnowledgeTimeout int                 `yaml:"answer_from_knowledge_timeout"` // 从知识库获取答案超时时间
		IntentAchievements         map[uint64][]string `yaml:"intent_achievements"`           // 意图达成方式优先级配置
		StopGeneration             struct {
			KeyExpire     uint `yaml:"key_expire"`     // Key 过期时间 (秒)
			CheckInterval uint `yaml:"check_interval"` // 检查时间间隔 (毫秒)
		} `yaml:"stop_generation"` // 停止生成配置
		Throttles struct {
			Check     int `yaml:"check"`     // 内容审核
			Streaming int `yaml:"streaming"` // 流式输出
		} `yaml:"throttles"` // 节流配置
		MaxConcurrence struct {
			Standard  uint            `yaml:"standard"`   // 标准版
			WhiteList map[uint64]uint `yaml:"white_list"` // 白名单
		} `yaml:"max_concurrence"` // 最大并发数
		Rewrite struct {
			RewriteThreshold      int      `yaml:"rewrite_threshold"`        // 改写阈值
			ComplexQueryWhiteList []uint64 `yaml:"complex_query_white_list"` // 复杂query改写白名单
		} `yaml:"rewrite"` // Query改写配置
		HistoryLimit       uint32            `yaml:"history_limit"`        // 历史记录对话轮数限制
		SingleMessageLimit uint32            `yaml:"single_message_limit"` // 单条记录长度限制
		RoleDescription    string            `yaml:"role_description"`     // 角色描述
		BotRoleDescription map[string]string `yaml:"bot_role_description"` // 角色描述
		VisitorLabels      struct {
			LabelCount       int `yaml:"label_count"`        // 标签数量
			LabelNameLength  int `yaml:"label_name_length"`  // 标签名长度
			LabelValueCount  int `yaml:"label_value_count"`  // 标签值数量
			LabelValueLength int `yaml:"label_value_length"` // 标签值长度
		} `yaml:"visitor_labels"` // 标签配置
		ContentLimit    int    `yaml:"content_limit"` // 内容长度限制
		SessionRegx     string `yaml:"session_regx"`  // sessionID格式校验
		RoleCommandRegx struct {
			RoleNameRegex        string `yaml:"role_name_regex"`        // 角色名正则表达式
			SpeechStyleRegex     string `yaml:"speech_style_regex"`     // 言谈风格正则表达式
			PersonalityRegex     string `yaml:"personality_regex"`      // 性格特点正则表达式
			ExperienceRegex      string `yaml:"experience_regex"`       // 经历和背景正则表达式
			CapabilityLimitRegex string `yaml:"capability_limit_regex"` // 能力限制正则表达式
			SkillsRegex          string `yaml:"skills_regex"`           // 技能正则表达式
		} `yaml:"role_command_regx"` // 角色命令正则配置
		RecommendRegex  string `yaml:"recommend_regex"` // 推荐问正则表达式
		PlaceholderConf struct {
			StartPlaceholders map[string]int `yaml:"start_placeholders"` // 起始占位符对应的等待步长
		} `yaml:"placeholder_conf"` // 占位符配置
		// 渠道类型列表，0:前端未携带渠道信息 10000:微信订阅号 10001:微信服务号 10002:企微应用
		ChannelTypeList []int `yaml:"channel_type_list"`
	} `yaml:"bot"` // 机器人配置
	UnsatisfiedReply struct {
		ContextLength struct {
			Above uint32 `yaml:"above"`
			Below uint32 `yaml:"below"`
		} `yaml:"context_length"` // 上下文长度
	} `yaml:"unsatisfied_reply"` // 不满意回复
	EvilWords               []string `yaml:"evil_words"`                 // 敏感词
	RejectAnswerJudgeLength int      `yaml:"reject_answer_judge_length"` // 拒答判断长度
	UsePlaceholderModels    []string `yaml:"use_placeholder_models"`     // 使用占位符的模型名称
	App                     struct { // 应用配置
		KnowledgeSummary struct {
			DefaultQueryMaxLen      int      `yaml:"default_query_max_len"`     // 兜底请求最大长度
			AllowReplaceMethod      []uint32 `yaml:"allow_replace_method"`      // 允许替换的method
			AllowReplaceRequirement []uint32 `yaml:"allow_replace_requirement"` // 允许替换的requirement
		} `yaml:"knowledge_summary"` // 知识摘要
		TagExtraction struct {
			AllowMaxTags         int    `yaml:"allow_max_tags"`         // 允许传入的最大标签数量
			DefaultQueryMaxLen   int    `yaml:"default_query_max_len"`  // 兜底请求最大长度
			DefaultExtractAnswer string `yaml:"default_extract_answer"` // 默认标签提取答案 没有提取出有效标签时展示
		} `yaml:"tag_extraction"` // 标签抽签
		BlackList     []uint64 `yaml:"black_list"`      // 黑名单
		ShareCodeLen  int      `yaml:"share_code_len"`  // 分享码长度
		ForceCloseLLM []uint64 `yaml:"force_close_llm"` // 强制关闭模型润色回复 todo 2.8迭代后去掉该配置项
	} `yaml:"app"` // 应用配置
	// 关闭金融行业大模型知识包（FAQ手册）
	DisableFinancialKnowledge []uint64          `yaml:"disable_financial_knowledge"`
	FinancialBotBizID         uint64            `yaml:"financial_bot_biz_id"`      // 金融行业大模型知识包（FAQ手册）的bot_biz_id
	MedicalBotBizID           uint64            `yaml:"medical_bot_biz_id"`        // 医疗行业大模型知识包（FAQ手册）的bot_biz_id
	DisableMedicalKnowledge   []uint64          `yaml:"disable_medical_knowledge"` // 关闭医疗行业大模型知识包（FAQ手册）
	StreamOutputBatchSize     int               `yaml:"stream_output_batch_size"`  // 流式输出批量大小
	Procedure                 map[string]string `yaml:"procedure"`                 // 调用过程
	MultiModal                struct {
		ModelNameRewrite       string            `yaml:"model_name_rewrite"`         // 多模态模型名称
		ModelNameDialog        string            `yaml:"model_name_dialog"`          // 多模态模型名称
		WhiteList              map[uint64]string `yaml:"white_list"`                 // 白名单
		HistoryLimit           uint32            `yaml:"history_limit"`              // 历史记录对话轮数限制
		PromptWordsLimit       uint32            `yaml:"prompt_words_limit"`         // 提示词长度限制, 多模态问答Prompt限制2K
		GetCaptionPrompt       string            `yaml:"get_caption_prompt"`         // 获取图片描述提示
		GetMultiPrompt         string            `yaml:"get_multi_prompt"`           // 获取多图片描述Prompt
		GetRewritePrompt       string            `yaml:"get_rewrite_prompt"`         // 获取多模态改写prompt
		RewritePrefix          string            `yaml:"rewrite_prefix"`             // 改写前缀
		Placeholder            string            `yaml:"placeholder"`                // 占位符
		PlaceholderV2          string            `yaml:"placeholderv2"`              // 占位符
		MaxImageCount          int               `yaml:"max_image_count"`            // 最大图片数量
		OneImageTokenLength    int               `yaml:"one_image_token_length"`     // 单张图片token
		CanUseMultiModalSearch []uint64          `yaml:"can_use_multi_modal_search"` // 可以使用多模态搜索的bot_biz_id
		Prompt                 string            `yaml:"prompt"`                     // 提示
		Disabled               bool              `yaml:"disabled"`                   // 多模态开关
		OnlyCosURL             []string          `yaml:"only_cos_url"`               // 只使用cos的url的模型
		MultiModalRewrite      struct {
			ModelName string            `yaml:"model_name"` // 多模态改写模型名称
			WhiteList map[uint64]string `yaml:"white_list"` // 白名单
		}
		MultiModalComprehension struct {
			ModelName string            `yaml:"model_name"` // 多模态理解模型名称
			WhiteList map[uint64]string `yaml:"white_list"` // 白名单
		} `yaml:"multi_modal_comprehension"` // 多模态阅读理解理解模型配置
	} `yaml:"multi_modal"` // 多模态问答配置
	SSE struct {
		EnableDeferClientClose bool          `yaml:"enable_defer_client_close"` // 是否开启客户端延迟关闭时间
		DeferClientCloseTime   time.Duration `yaml:"defer_client_close_time"`   // 客户端关闭时间
	} `yaml:"sse"` // SSE配置
	RateLimiter struct {
		Enable          bool          `yaml:"enable"`            // 是否启用
		RedisAddress    string        `yaml:"redis_address"`     // redis 地址
		Period          time.Duration `yaml:"period"`            // 间隔时间
		CleanUpInterval time.Duration `yaml:"clean_up_interval"` // 清理间隔
		Limit           int64         `yaml:"limit"`             // 限制
	} `yaml:"rate_limiter"`
	Limiter struct {
		Enable            bool           `yaml:"enable"`              // 是否启用
		LimitBuffer       int            `yaml:"limit_buffer"`        // 限制缓冲
		TimePeriod        int            `yaml:"time_period"`         // 时间间隔，单位秒
		CorpLimit         map[uint64]int `yaml:"corp_limit"`          // 企业限频
		DefaultCorpLimit  int            `yaml:"default_corp_limit"`  // 企业默认限频
		ReportJudgeLength int            `yaml:"report_judge_length"` // 上报判定长度
	} `yaml:"limiter"`
	HunYuanConfig struct {
		HunYuanStandard       string            `yaml:"hunyuan_standard"`        // 混元标准版模型名称
		HunYuanSearch         string            `yaml:"hunyuan_search"`          // 混元搜索模型名称
		Whitelist             map[uint64]string `yaml:"whitelist"`               // 混元搜索白名单
		EnableInstructionList []uint64          `yaml:"enable_instruction_list"` // 混元启用指令遵循白名单
	} `yaml:"hunyuan_config"` // 混元配置
	POT struct {
		Math struct {
			ModelName           string `yaml:"model_name"`
			POTSystemPrompt     string `yaml:"pot_system_prompt"`
			NonePOTSystemPrompt string `yaml:"non_pot_system_prompt"`
			Prompt              string `yaml:"prompt"`
		} `yaml:"math"`
	} `yaml:"pot"`
	PipelineDebugging struct {
		Keys []string `yaml:"keys"`
	} `yaml:"pipeline_debugging"`
	DebugMessage struct {
		Salt string `yaml:"salt"`
	} `yaml:"debug_message"`
	Model struct {
		LocalCacheTTL int64             `yaml:"local_cache_ttl"` // 本地缓存过期时间, 单位秒
		TokenLimit    int64             `yaml:"token_limit"`     // 模型token限制
		PromptLength  map[uint64]int    `yaml:"prompt_length"`   // 模型阅读理解prompt长度白名单
		WhiteList     []uint64          `yaml:"white_list"`      // 使用定制Prompt的白名单  应用维度
		PromptList    map[string]string `yaml:"prompt_list"`     // Prompt列表
	} `yaml:"model"`
	Finance struct { // 计费开关
		Disabled       bool   `yaml:"disabled"`        // token上报是否关闭
		SearchDisabled bool   `yaml:"search_disabled"` // 搜索引擎上报是否关闭
		KeyExpire      uint64 `yaml:"key_expire"`      // redis缓存过期时间
		ReportDisabled bool   `yaml:"report_disabled"` // 是否关闭用量上报
		DefaultQPM     uint64 `yaml:"default_qpm"`     // 默认QPM
		DefaultTPM     uint64 `yaml:"default_tpm"`     // 默认TPM
		DsDefaultQPM   uint64 `yaml:"ds_default_qpm"`  // ds默认QPM
		DsDefaultTPM   uint64 `yaml:"ds_default_tpm"`  // ds默认TPM
	} `yaml:"finance"`
	TaskFlowPreviewConfig struct {
		ModelName        string `yaml:"model_name"`         // 模型名称
		RequestNodeQuery string `yaml:"request_node_query"` // 询问节点prompt
	} `yaml:"task_flow_preview_config"` // 预览配置
	WorkflowConfig struct {
		ModelName               string `yaml:"model_name"`                 // 运行时模型名称
		WorkflowRoleModelName   string `yaml:"workflow_role_model_name"`   // 工作流一键描述通用模型名称
		WorkflowIntentModelName string `yaml:"workflow_intent_model_name"` // 工作流意图模型名称
		RoleIntentModelName     string `yaml:"role_intent_model_name"`     // 角色描述意图模型名称
		ParamRegexp             string `yaml:"param_regexp"`               // 变量正则
		RoleQuery               string `yaml:"role_query"`                 // 角色描述prompt
		RoleQueryNoIntention    string `yaml:"role_query_no_intention"`    // 角色描述无意图prompt
		DescQuery               string `yaml:"desc_query"`                 // 工作流描述prompt
		ParameterExtractor      string `yaml:"parameter_extractor"`        // 工作流参数提取节点提示词
		LLMPrompt               string `yaml:"llm_prompt"`                 // 工作流大模型节点大模型提示词
		LLMTagDesc              string `yaml:"llm_tag_desc"`               // 工作流大模型标签提取节点标签描述
		IntentDesc              string `yaml:"intent_desc"`                // 工作流意图识别节点描述
		CloseTokenReport        bool   `yaml:"close_token_report"`         // 是否关闭token上报，true:关闭，false:不关闭
	} `yaml:"workflow_config"` // 新工作流配置
	ClarifyConfirm struct {
		TopN      int      `yaml:"top_n"`      // 澄清确认
		WhiteList []uint64 `yaml:"white_list"` // 应用ID维度，白名单开通澄清确认
	} `yaml:"clarify_confirm"` // 澄清配置返回结果数量
	IntentModel struct {
		WhiteList             []uint64            `yaml:"white_list"`               // 白名单
		SystemIntent          map[int]string      `yaml:"system_intent"`            // 系统意图
		SystemIntentMap       map[string]string   `yaml:"system_intent_map"`        // 系统意图映射
		SystemIntentWhiteList map[string][]uint64 `yaml:"system_intent_white_list"` // 白名单干预，主要针对数学计算和逻辑推理
		CloseDoc              []uint64            `yaml:"close_doc"`                // 关闭文档
		IsQANotFirst          []uint64            `yaml:"is_qa_not_first"`          // QA不优先
		IsWorkflowNotFirst    []uint64            `yaml:"is_workflow_not_first"`    // 工作流不优先
		NoFaq                 []uint64            `yaml:"no_faq"`                   // 意图识别不加入FAQ
		FaqDef                string              `yaml:"faq_def"`                  // FAQ定义模板
		DocDef                string              `yaml:"doc_def"`                  // doc定义模板
		RejectPrompt          string              `yaml:"reject_prompt"`            // 拒绝prompt
		SysPrompt             string              `yaml:"sys_prompt"`               // 系统prompt
		DsFAQRejectModel      string              `yaml:"ds_faq_reject_model"`      // ds的faq拒答模型
		ExplicitIntent        map[uint64]string   `yaml:"explicit_intent"`          // 明确意图，不再走意图识别<appid,IntentName>
		RerankScore           float32             `yaml:"rerank_score"`             // 重新排序阈值
	} `yaml:"intent_model"` // 意图模型配置
	Reference struct {
		Patterns        []string `yaml:"patterns"`         // 引用正则
		HunYuanSwitch   []uint64 `yaml:"hunyuan_switch"`   // 混元开关, 哪些bot_biz_id生效
		HunYuanPatterns []string `yaml:"hunyuan_patterns"` // 混元引用正则 key: bot_biz_id
	} `yaml:"reference"` // 引用配置
	Mock struct {
		EnableLLM      bool `yaml:"enable_llm"`      // 是否开启LLM mock
		EnableSearch   bool `yaml:"enable_search"`   // 是否开启检索 mock
		EnableWorkflow bool `yaml:"enable_workflow"` // 是否开启工作流 mock
	} `yaml:"mock"` // Mock配置
	RetrievalConfig struct {
		ClosePriority []uint64 `yaml:"close_priority"` // 关闭FAQ的高阈值优先
		NERThreshold  int      `yaml:"ner_threshold"`  // NER阈值，大于阈值不做NER
	} `yaml:"retrieval_config"` // 检索配置
	NeedSearchGlobalKnowledge []uint64 `yaml:"need_search_global_knowledge"` // 需要走全局知识检索的应用id
	ThoughtConf               struct {
		ThoughtIcon string            `yaml:"thought_icon"` // 思考图标
		StatusTitle map[string]string `yaml:"status_title"` // 状态标题
	} `yaml:"thought_conf"` // 思维链配置
	StageTaskName struct {
		DialogPPL          string `yaml:"dialog_ppl"`         // 对话PPL
		CheckEvil          string `yaml:"check_evil"`         // 安全审核
		BasicSearch        string `yaml:"basic_search"`       // 基础检索
		QueryRewrite       string `yaml:"query_rewrite"`      // 问题改写
		IntentionRecognize string `yaml:"intent_recognize"`   // 意图识别
		SearchKnowledge    string `yaml:"search_knowledge"`   // 知识检索
		Taskflow           string `yaml:"taskflow"`           // 任务流
		Workflow           string `yaml:"workflow"`           // 工作流
		LLMReply           string `yaml:"llm_reply"`          // 大模型回复
		SearchProForDS     string `yaml:"search_pro_for_ds"`  // deepseek + 搜索增强链路
		FirstThoughtCost   string `yaml:"first_thought_cost"` //
		TotalThoughtCost   string `yaml:"total_thought_cost"` //
	} `yaml:"stage_task_name"` // 阶段任务名称
	PluginCfg    map[string]string `yaml:"plugin_cfg"` // 插件配置, key: intent_name, value: plugin_id
	DeepSeekConf struct {
		EnableDSMathCalculate bool              `yaml:"enable_ds_math_calculate"` // 是否开启deepseek数学计算
		ModelName             []string          `yaml:"model_name"`               // 模型名称
		ModelHasThink         []string          `yaml:"model_has_think"`          // 深度搜索有思维链的模型名称
		ImageQuizWhiteList    []string          `yaml:"image_quiz_white_list"`    // 图文问答白名单列表
		RewriteModel          string            `yaml:"rewrite_model"`            // 问题改写模型名称
		IntentModel           string            `yaml:"intent_model"`             // 意图识别模型名称
		SearchVersion         map[uint64]string `yaml:"search_version"`           // 是否开启深度搜索 map[app_id]bool
		SearchSecretID        string            `yaml:"search_secret_id"`         // 搜索引擎密钥
		SearchSecretKey       string            `yaml:"search_secret_key"`        // 搜索引擎密钥
		SearchResultSize      int               `yaml:"search_result_size"`       // 搜索引擎结果数量
		SearchModel           map[string]string `yaml:"search_model"`             // 搜索引擎模型
		SummaryPromptTpl      string            `yaml:"summary_prompt_tpl"`       // 摘要模板
		NormalMsgTpl          string            `yaml:"normal_msg_tpl"`           // 阅读理解prompt
		NonGeneralMsgTpl      string            `yaml:"non_general_msg_tpl"`      // 阅读理解拒答prompt
		SearchEnableFree      bool              `yaml:"search_enable_free"`       // 搜索是否开启免费
		ExcludeSearchEngine   []string          `yaml:"exclude_search_engine"`    // 排除搜索引擎， mp:微信公众号
		NoThinkPrompt         string            `yaml:"no_think_prompt"`          // 不带思维链的prompt
		NoThinkModelParams    string            `yaml:"no_think_model_params"`    // 不带思维链的模型参数
		QDDsStartTime         string            `yaml:"qd_ds_start_time"`         // 启用QD_DS审核策略的开始时间
	} `yaml:"deep_seek_conf"` // 深度搜索配置
	ExperimentConfig   ExperimentConfig `yaml:"experiment_config"`    // 实验配置
	EventDowngradeConf map[string]bool  `yaml:"event_downgrade_conf"` // 事件降级不报错配置
	ErrorDowngradeConf []string         `yaml:"error_downgrade_conf"` // 错误降级不报错配置
	CleanDataConf      struct {
		RetentionDay    int `yaml:"retention_day"`     // 清理数据保留天数
		SelectBatchSize int `yaml:"select_batch_size"` // 清理数据批量大小
		CleanInterval   int `yaml:"clean_interval"`    // 清理间隔时间，单位ms
	} `yaml:"clean_data_conf"` // 清理数据配置
	GetMsgCountLimit   int  `yaml:"get_msg_count_limit"`   // 获取消息数量限制
	GetMsgDefaultCount int  `yaml:"get_msg_default_count"` // 获取消息默认数量
	IsPrivate          bool `yaml:"is_private"`            // 是否私有化，默认false
	RTC                struct {
		UserSigTTL       uint64            `yaml:"user_sig_ttl"`       // userSig 有效期（单位：秒）
		RobotUserSuffix  string            `yaml:"robot_user_suffix"`  // 机器人user后缀
		AvatarUserSuffix string            `yaml:"avatar_user_suffix"` // 数智人user后缀
		TokenTTL         uint64            `yaml:"token_ttl"`          // Token 有效期 (秒)
		ClientTTL        uint64            `yaml:"client_ttl"`         // 客户端有效期 (秒)
		TaskTTL          uint64            `yaml:"task_ttl"`           // 任务有效期 (秒)
		RoomTTL          uint64            `yaml:"room_ttl"`           // 房间有效期 (秒)
		RoomRetry        uint              `yaml:"room_retry"`         // 房间重试次数
		RoomMap          map[uint64]uint32 `yaml:"room_map"`           // 应用指定某个房间
		STTLanguages     []string          `yaml:"stt_languages"`      // 语音转文本支持语言（zh-中文，en-英文）
		EnableTaskValid  bool              `yaml:"enable_task_valid"`  // 启用任务检查
		AgentConfig      struct {
			Enable                  bool   `yaml:"enable"`                    // 是否启用
			MaxIdleTime             uint64 `yaml:"max_idle_time"`             // 房间内超过MaxIdleTime 没有推流，后台自动关闭任务，默认值是60s。
			InterruptSpeechDuration uint64 `yaml:"interrupt_speech_duration"` // InterruptMode为0时使用，单位为毫秒，默认为500ms。
			AllowOneWord            bool   `yaml:"allow_one_word"`            // 是否不过滤掉用户只说了一个字的句子
			TurnDetectionMode       uint64 `yaml:"turn_detection_mode"`       // 控制新一轮对话的触发方式，默认为0
			FilterBracketsContent   uint64 `yaml:"filter_brackets_content"`   // 用于过滤LLM返回内容，不播放括号中的内容。
		} `yaml:"agent_config"`
		STTConfig struct {
			Enable         bool   `yaml:"enable"`           // 是否启用
			Language       string `yaml:"language"`         // 语音转文本支持语言（zh-中文，en-英文）
			VadSilenceTime uint64 `yaml:"vad_silence_time"` // 语音识别vad的时间，范围为240-2000，默认为1000，单位为ms。更小的值会让语音识别分句更快。
			HotWordList    string `yaml:"hot_word_list"`    // 热词表：该参数用于提升识别准确率。
		} `yaml:"stt_config"` // 语音转文本配置
		Limiter struct {
			Enable      bool `yaml:"enable"`       // 是否启用
			LimitBuffer int  `yaml:"limit_buffer"` // 限制缓冲
			TimePeriod  int  `yaml:"time_period"`  // 时间间隔，单位秒
		} `yaml:"limiter"`
		MaxConcurrence struct {
			Standard  uint            `yaml:"standard"`   // 标准版
			WhiteList map[uint64]uint `yaml:"white_list"` // 白名单
		} `yaml:"max_concurrence"` // 最大并发数
		Callback struct {
			Enable            bool              `yaml:"enable"`              // 是否启用
			EnableDebug       bool              `yaml:"enable_debug"`        // 是否启用debug
			PanicErrorMap     map[string]bool   `yaml:"panic_error_map"`     // 异常错误
			EnableMetricLabel bool              `yaml:"enable_metric_label"` // 监控指标带标签
			ReportMetricMap   map[string]string `yaml:"report_metric_map"`   // 监控指标
		} `yaml:"callback"` // 回调配置
	} `yaml:"rtc"` // RTC配置
	TRTC struct {
		Endpoint    string `yaml:"endpoint"`     // TRTC 服务器地址
		Region      string `yaml:"region"`       // TRTC 服务器区域
		SecretID    string `yaml:"secret_id"`    // TRTC secret id
		SecretKey   string `yaml:"secret_key"`   // TRTC secret key
		SdkAppID    uint64 `yaml:"sdk_app_id"`   // TRTC 应用ID
		UserKey     string `yaml:"user_key"`     // TRTC 密钥key
		CallbackKey string `yaml:"callback_key"` // TRTC 回调密钥
	} `yaml:"trtc"` // TRTC配置
	LLM struct {
		APIURL            string `yaml:"api_url"`              // LLM 发布API地址
		APIExpURL         string `yaml:"api_exp_url"`          // LLM 体验API地址
		Timeout           uint64 `yaml:"timeout"`              // LLM 超时时间
		IsRetThoughtEvent bool   `yaml:"is_ret_thought_event"` // LLM 是否返回思考事件
	} `yaml:"llm"` // LLM配置
	TTS struct {
		AppID           uint32 `yaml:"app_id"`           // TTS appid
		SecretID        string `yaml:"secret_id"`        // TTS secret id
		SecretKey       string `yaml:"secret_key"`       // TTS secret key
		SignatureAction string `yaml:"signature_action"` // TTS action
		SignatureURL    string `yaml:"signature_url"`    // TTS wss地址
		SignatureTTL    uint64 `yaml:"signature_ttl"`    // TTS签名过期时间
	} `yaml:"tts"` // TTS配置
	DigitalHuman struct {
		AppKey       string `yaml:"app_key"`       // 数智人app key
		AccessToken  string `yaml:"access_token"`  // 数智人token
		SecretKey    string `yaml:"secret_key"`    // 数智人secret key
		Platform     string `yaml:"platform"`      // 数智人平台code
		SignatureTTL uint64 `yaml:"signature_ttl"` // 数智人签名ttl
		DriverType   uint64 `yaml:"driver_type"`   // 数智人driver_type
	} `yaml:"digital_human"` // 数智人配置
	STS struct {
		Endpoint      string                      `yaml:"endpoint"`       // STS 服务器地址
		Region        string                      `yaml:"region"`         // STS  服务器区域
		AppID         uint32                      `yaml:"app_id"`         // STS app_id
		SecretID      string                      `yaml:"secret_id"`      // STS  secret id
		SecretKey     string                      `yaml:"secret_key"`     // STS  secret key
		CredentialMap map[string]CredentialConfig `yaml:"credential_map"` // 临时秘钥配置
	} `yaml:"sts"` // STS配置
	SupportCustomAuditSwitch bool `yaml:"support_custom_audit_switch"` // 是否支持自定义审核
}

// ExperimentConfig 实验配置
type ExperimentConfig struct {
	KnowledgeServiceName string                    `yaml:"knowledge_service_name"`
	Namespace            string                    `yaml:"namespace"`
	EnvName              string                    `yaml:"env_name"`
	ReplaceAppMap        map[uint64]ReplaceAppInfo `yaml:"replace_app_map"` // 替换应用列表
}

// ReplaceAppInfo 替换应用信息
type ReplaceAppInfo struct {
	ReplaceAppID    uint64 `yaml:"replace_app_id"`
	CalleeNamespace string `yaml:"callee_namespace"`
}

// CredentialConfig 临时秘钥配置
type CredentialConfig struct {
	Name   string `yaml:"name"`
	Policy string `yaml:"policy"`
	TTL    uint64 `yaml:"ttl"`
}

// ApplicationConfig 应用配置
var ApplicationConfig = &Application{}

// Init 初始化配置
func Init() {
	log.Info("init config")
	// 拿到整个application.yaml文件中的内容
	resp, err := config.Get(providerName).Get(context.Background(), applicationKey)
	if err != nil {
		log.ErrorContextf(context.Background(), "get config failed. error:%v", err)
	}
	if err = initConfig(ApplicationConfig, resp); err != nil {
		log.ErrorContextf(context.Background(), "init config failed. error:%v", err)
	}
	log.Info("init config success, cfg：", ApplicationConfig)
	watchConfigUpdateEvent(ApplicationConfig)
}

func initConfig(configInstance interface{}, response config.Response) error {
	err := yaml.Unmarshal([]byte(response.Value()), configInstance)
	if err != nil {
		log.ErrorContextf(context.Background(), "unmarshal config failed. error:%v", err)
		return err
	}
	return nil
}

func watchConfigUpdateEvent(configInstance interface{}) {
	resp, err := config.Get(providerName).Watch(context.Background(), applicationKey)
	if err != nil {
		log.ErrorContextf(context.Background(), "watch config failed. error:%v", err)
	}
	go func() {
		defer errors.PanicHandler()
		for r := range resp {
			_ = initConfig(configInstance, r)
		}
	}()
}

// App 获取应用配置
func App() *Application {
	return ApplicationConfig
}

// GetLabelCount 获取标签数量
func GetLabelCount() int {
	return App().Bot.VisitorLabels.LabelCount
}

// GetLabelNameLength 获取标签名长度
func GetLabelNameLength() int {
	return App().Bot.VisitorLabels.LabelNameLength
}

// GetLabelValueCount 获取标签数量
func GetLabelValueCount() int {
	return App().Bot.VisitorLabels.LabelValueCount
}

// GetLabelValueLength 获取标签名长度
func GetLabelValueLength() int {
	return App().Bot.VisitorLabels.LabelValueLength
}

// GetMultiModalComprehensionModelName 获取多模态阅读理解模型名称
func GetMultiModalComprehensionModelName(botBizID uint64) string {
	if model, ok := App().MultiModal.MultiModalComprehension.WhiteList[botBizID]; ok {
		return model
	}
	return App().MultiModal.MultiModalComprehension.ModelName
}

// GetDialogMLLMModelName 获取多模态问答模型名称
func GetDialogMLLMModelName(botBizID uint64) string {
	if model, ok := App().MultiModal.WhiteList[botBizID]; ok {
		return model
	}
	return App().MultiModal.ModelNameDialog
}

// GetRewriteMLLMModelName 获取默认多模态模型名称，Query改写的时候用小模型
func GetRewriteMLLMModelName() string {
	return App().MultiModal.ModelNameRewrite
}

// GetContentLimit 获取内容长度限制
func GetContentLimit() int {
	if App().Bot.ContentLimit == 0 {
		return 12000
	}
	return App().Bot.ContentLimit
}

// GetMaxImageCount 获取最大图片数量
func GetMaxImageCount() int {
	if App().MultiModal.MaxImageCount == 0 {
		return 5
	}
	return App().MultiModal.MaxImageCount
}

// GetDefaultTokenLimit 获取默认的模型token限制
func GetDefaultTokenLimit() int {
	if App().Model.TokenLimit == 0 {
		return 8000
	}
	return int(App().Model.TokenLimit)
}

// CanUseMultiModalSearch 是否可以使用多模态搜索
func CanUseMultiModalSearch(botBizID uint64) bool {
	for _, bizID := range App().MultiModal.CanUseMultiModalSearch {
		if bizID == botBizID {
			return true
		}
	}
	return false
}

// IsInIntentRecognizeWhitelist 是否在意图识别白名单中
func IsInIntentRecognizeWhitelist(botBizID uint64) bool {
	for _, bizID := range App().IntentModel.WhiteList {
		if bizID == botBizID {
			return true
		}
	}
	return false
}

// GetReferencePattern 获取引用正则
func GetReferencePattern(botBizID uint64) []string {
	if len(App().Reference.Patterns) == 0 {
		return []string{`【passage (\d+)】`}
	}
	patterns := App().Reference.Patterns
	for _, bizID := range App().Reference.HunYuanSwitch {
		if bizID == botBizID {
			patterns = append(patterns, App().Reference.HunYuanPatterns...)
			break
		}
	}

	return patterns
}

// GetSessionRegx 获取session校验正则表达式
func GetSessionRegx() string {
	return App().Bot.SessionRegx
}

// GetSysIntent 获取系统意图路由
func GetSysIntent(systemIntentName string, appID uint64) string {
	for _, item := range App().IntentModel.SystemIntentWhiteList[systemIntentName] {
		if item == appID {
			return "knowledge_qa"
		}
	}
	if res, ok := App().IntentModel.SystemIntentMap[systemIntentName]; ok {
		return res
	}
	return "knowledge_qa"
}

// GetModelPromptLength 获取模型token长度 - 白名单
func GetModelPromptLength(botBizID uint64, promptLimit int) int {
	if length, ok := App().Model.PromptLength[botBizID]; ok {
		return length
	}
	return promptLimit
}

// IsCloseDoc 是否关闭文档
func IsCloseDoc(botBizID uint64) bool {
	for _, id := range App().IntentModel.CloseDoc {
		if id == botBizID {
			return true
		}
	}
	return false
}

// GetSearchModelName 获取hunyuan搜索模型名称
func GetSearchModelName(botBizID uint64) string {
	if model, ok := App().HunYuanConfig.Whitelist[botBizID]; ok {
		return model
	}
	if App().HunYuanConfig.HunYuanSearch != "" {
		return App().HunYuanConfig.HunYuanSearch
	}
	return "hunyuan-search"
}

// IsQANotFirst QA是否优先
func IsQANotFirst(botBizID uint64) bool {
	for _, id := range App().IntentModel.IsQANotFirst {
		if id == botBizID {
			return true
		}
	}
	return false
}

// IsWorkflowNotFirst 工作流是否优先
func IsWorkflowNotFirst(botBizID uint64) bool {
	for _, id := range App().IntentModel.IsWorkflowNotFirst {
		if id == botBizID {
			return true
		}
	}
	return false
}

// GetIntentThreshold 获取Intent阈值
func GetIntentThreshold() int {
	if App().Bot.IntentThreshold < 1 { // 主要是考虑没有配置的情况
		return 160000
	}
	return App().Bot.IntentThreshold
}

// GetSearchThreshold 获取搜索阈值
func GetSearchThreshold() int {
	if App().Bot.SearchThreshold < 1 { // 主要是考虑没有配置的情况
		return 160000
	}
	return App().Bot.SearchThreshold
}

// IsNoFaq 是否不加入FAQ
func IsNoFaq(botBizID uint64) bool {
	for _, id := range App().IntentModel.NoFaq {
		if id == botBizID {
			return true
		}
	}
	return false
}

// IsClosePriority 是否关闭FAQ的高阈值优先
func IsClosePriority(botBizID uint64) bool {
	for _, id := range App().RetrievalConfig.ClosePriority {
		if id == botBizID {
			return true
		}
	}
	return false
}

// NeedSearchGlobalKnowledge 需要检索全局知识库
func NeedSearchGlobalKnowledge(botBizID uint64) bool {
	for _, id := range App().NeedSearchGlobalKnowledge {
		if id == botBizID {
			return true
		}
	}
	return false
}

// IsExplicitIntent 是否是明确意图
func IsExplicitIntent(botBizID uint64) bool {
	if _, ok := App().IntentModel.ExplicitIntent[botBizID]; ok {
		return true
	}
	return false
}

// IsEnableInstructionSearch 是否开启指令搜索
func IsEnableInstructionSearch(botBizID uint64) bool {
	for _, id := range App().HunYuanConfig.EnableInstructionList {
		if id == botBizID {
			return true
		}
	}
	return false
}

// SupportComplexQueryRewrite 支持复杂query改写
func SupportComplexQueryRewrite(botBizID uint64) bool {
	for _, id := range App().Bot.Rewrite.ComplexQueryWhiteList {
		if id == botBizID {
			return true
		}
	}
	return false
}

// DeepSeekSearchVersion deepseek搜索版本
func DeepSeekSearchVersion(botBizID uint64) string {
	if ver, ok := App().DeepSeekConf.SearchVersion[botBizID]; ok {
		return ver
	}
	if ver, ok := App().DeepSeekConf.SearchVersion[0]; ok { // 0 为默认版本
		return ver
	}
	return ""
}

// GetBotRoleDescription 获取bot角色描述
func GetBotRoleDescription(ctx context.Context, modelName string) string {
	if role, ok := App().Bot.BotRoleDescription[modelName]; ok {
		return i18n.Translate(ctx, role)
	}
	return i18n.Translate(ctx, App().Bot.RoleDescription)
}

// IsFinanceDisabled 是否关闭计费
func IsFinanceDisabled() bool {
	return App().Finance.Disabled
}

// IsReportDosageDisabled 是否关闭用量上报
func IsReportDosageDisabled() bool {
	return App().Finance.ReportDisabled
}

// IsForceCloseLLM 是否强制关掉模型润色
func IsForceCloseLLM(botBizID uint64) bool {
	for _, id := range App().App.ForceCloseLLM {
		if id == botBizID {
			return true
		}
	}
	return false
}

// GetIntentAchievements 未设置意图优先级，用默认配置
func GetIntentAchievements(botBizID uint64) []string {
	if ver, ok := App().Bot.IntentAchievements[botBizID]; ok {
		return ver
	}
	if ver, ok := App().Bot.IntentAchievements[0]; ok { // 0 为默认版本
		return ver
	}
	return []string{}
}

// ErrorDowngrade 错误消息是否降级
func ErrorDowngrade(msg string) bool {
	for _, val := range App().ErrorDowngradeConf {
		if strings.Contains(msg, val) {
			return true
		}
	}
	return false
}

// GetCorpReqLimit 获取企业请求限制
func GetCorpReqLimit(corpID uint64) int {
	limit := App().Limiter.CorpLimit[corpID]
	if limit > 0 {
		return limit
	}
	return App().Limiter.DefaultCorpLimit
}

// GetTRTCSdkAppID 获取trtc sdk app id
func GetTRTCSdkAppID() uint64 {
	return App().TRTC.SdkAppID
}

// GetTRTCUserKey 获取trtc sdk user key
func GetTRTCUserKey() string {
	return App().TRTC.UserKey
}

// GetRTCUserSigTTL 获取trtc用户签名有效期
func GetRTCUserSigTTL() uint64 {
	if App().RTC.UserSigTTL < 1 { // 主要是考虑没有配置的情况
		return 7200
	}
	return App().RTC.UserSigTTL
}

// GetRTCRoomTTL 获取trtc房间号有效期
func GetRTCRoomTTL() uint64 {
	if App().RTC.RoomTTL <= 0 {
		return 7200
	}
	return App().RTC.RoomTTL
}

// GetRTCTaskTTL 获取trtc任务有效期
func GetRTCTaskTTL() uint64 {
	if App().RTC.TaskTTL <= 0 {
		return 7200
	}
	return App().RTC.TaskTTL
}

// GetRTCTokenTTL 获取trtc token有效期
func GetRTCTokenTTL() uint64 {
	if App().RTC.TokenTTL <= 0 {
		return 180
	}
	return App().RTC.TokenTTL
}

// GetRTCClientTTL 获取trtc 客户端有效期
func GetRTCClientTTL() uint64 {
	if App().RTC.ClientTTL <= 0 {
		return 7200
	}
	return App().RTC.ClientTTL
}

// GetRTCRobotUserSuffix 获取trtc机器人用户id后缀
func GetRTCRobotUserSuffix() string {
	if strings.TrimSpace(App().RTC.RobotUserSuffix) == "" { // 主要是考虑没有配置的情况
		return "_robot"
	}
	return strings.TrimSpace(App().RTC.RobotUserSuffix)
}

// GetRTCAvatarUserSuffix 获取trtc数字人人用户id后缀
func GetRTCAvatarUserSuffix() string {
	if strings.TrimSpace(App().RTC.AvatarUserSuffix) == "" { // 主要是考虑没有配置的情况
		return "_avatar"
	}
	return strings.TrimSpace(App().RTC.AvatarUserSuffix)
}

// GetSTTLanguages 获取STT语言
func GetSTTLanguages() []string {
	if len(App().RTC.STTLanguages) == 0 { // 主要是考虑没有配置的情况
		return []string{"zh"}
	}
	return App().RTC.STTLanguages
}

// GetSTTLanguage 获取STT语言
func GetSTTLanguage() string {
	if len(App().RTC.STTConfig.Language) == 0 { // 主要是考虑没有配置的情况
		return "zh"
	}
	return App().RTC.STTConfig.Language
}

// GetLLMTimout 获取LLM超时时间
func GetLLMTimout() float64 {
	if App().LLM.Timeout < 1 { // 主要是考虑没有配置的情况
		return float64(3)
	}
	return float64(App().LLM.Timeout)
}

// GetDigitalHumanSignatureTTL 获取数字人签名有效期
func GetDigitalHumanSignatureTTL() uint64 {
	if App().DigitalHuman.SignatureTTL == 0 { // 主要是考虑没有配置的情况
		return 300
	}
	return App().DigitalHuman.SignatureTTL
}

// GetDigitalHumanDriverType 获取数字人驱动类型
func GetDigitalHumanDriverType() uint64 {
	if App().DigitalHuman.DriverType == 0 { // 主要是考虑没有配置的情况
		return 1
	}
	return App().DigitalHuman.DriverType
}

// GetTTSSignatureAction 获取TTS 签名action
func GetTTSSignatureAction() string {
	if App().TTS.SignatureAction == "" {
		return "TextToStreamAudioWSv2"
	}
	return App().TTS.SignatureAction
}

// GetTTSSignatureURL 获取TTS 签名URL
func GetTTSSignatureURL() string {
	if App().TTS.SignatureURL == "" {
		return "wss://tts.cloud.tencent.com/stream_wsv2"
	}
	return App().TTS.SignatureURL
}

// GetTTSSignatureTTL 获取TTS签名有效期
func GetTTSSignatureTTL() uint64 {
	if App().TTS.SignatureTTL == 0 { // 主要是考虑没有配置的情况
		return 3600
	}
	return App().TTS.SignatureTTL
}

// OnlyCosURLModel 只允许使用cos链接的模型
func OnlyCosURLModel(model string) bool {
	for _, val := range App().MultiModal.OnlyCosURL {
		if val == model {
			return true
		}
	}
	return false
}

// IsSkipCheckEvil 是否跳过安全审核
func IsSkipCheckEvil(botBizID uint64) bool {
	for _, id := range App().SkipCheckEvil {
		if id == botBizID {
			return true
		}
	}
	return false
}
