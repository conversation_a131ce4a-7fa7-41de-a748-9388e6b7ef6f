// Package KBAgent TODO
package KBAgent

import (
	"context"
	_ "embed" // 用于嵌入文件

	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
)

// Prompt TODO
//
//go:embed kb_agent_prompt.txt
var Prompt string

// InfoExtra 暂时没有用到
//
//go:embed info_extra.txt
var InfoExtra string

// PromptData 用于渲染模板的数据模型
type PromptData struct {
	CurrentDate             string
	DatabaseInfo            string
	KBAgentToolsDescription string
	ProjectRequests         string
	Examples                string
	UserQuery               string
	HistoryMessages         string
}

// RenderPrompt 接收数据模型并返回渲染后的字符串
func RenderPrompt(data PromptData, tpl string) (string, error) {
	return model.Render(context.Background(), tpl, data)
}
