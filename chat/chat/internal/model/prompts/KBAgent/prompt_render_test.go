package KBAgent

import (
	"strings"
	"testing"
)

// TestRenderPrompt 验证 renderPrompt 是否正确渲染模板内容
func TestRenderPrompt(t *testing.T) {
	// 准备测试数据
	data := PromptData{
		CurrentDate:             "2025-05-17",
		DatabaseInfo:            "- users 表\n- orders 表",
		KBAgentToolsDescription: "- SearchTool\n- FilterTool",
		ProjectRequests:         "规划检索流程并输出工具调用",
		Examples:                "示例一：…\n示例二：…",
		UserQuery:               "测试用户查询",
		HistoryMessages:         "机器人：…\n用户：…",
	}

	out, err := RenderPrompt(data, Prompt)
	if err != nil {
		t.Fatalf("renderPrompt 返回错误: %v", err)
	}

	// 检查输出是否包含各字段值
	tests := []struct {
		name     string
		expected string
	}{
		{"包含当前时间", "### 当前时间: 2025-05-17"},
		{"包含数据库信息", "### 数据库中的信息包括：\n- users 表\n- orders 表"},
		{"包含工具描述", "### 你可以使用的工具包括\n- SearchTool\n- FilterTool"},
		{"包含项目要求", "### 对你的要求如下：\n规划检索流程并输出工具调用"},
		{"包含示例", "### 给你举几个例子\n示例一：…\n示例二：…"},
		{"包含用户查询", "<用户问题>\n测试用户查询"},
		{"包含历史记录", "<历史记录>\n机器人：…\n用户：…"},
	}

	for _, tc := range tests {
		if !strings.Contains(out, tc.expected) {
			t.Errorf("未在渲染结果中找到 %q: 输出 =\n%v", tc.expected, out)
		}
	}
}
