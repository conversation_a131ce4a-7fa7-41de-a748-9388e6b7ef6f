package event

// experience send事件都对应dialog事件

import (
	"context"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"slices"
	"time"
	"unicode/utf8"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/agent_config_server"
	"git.woa.com/dialogue-platform/proto/pb-stub/openapi"
	pb "git.woa.com/ivy/protobuf/trpc-go/qbot/qbot/chat"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
)

// EventDialog 对话事件
const EventDialog = "dialog"

// EventSend send事件
const EventSend = "send"

// EventExperience experience事件
const EventExperience = "experience"

// DialogEvent 对话事件消息体
type DialogEvent struct {
	RequestID         string            `json:"request_id"`
	OriginContent     string            `json:"origin_content"`
	Content           string            `json:"content"`
	SessionID         string            `json:"session_id"`
	StreamingThrottle int               `json:"streaming_throttle"`
	SystemInfo        map[string]string `json:"system_info"` // 仅用于任务型接口调用场景
	FileInfos         []*model.FileInfo `json:"file_infos"`
	SystemRole        string            `json:"system_role"`
	CustomVariables   map[string]string `json:"custom_variables"` // 自定义参数
	BotAppKey         string            `json:"bot_app_key"`
	VisitorBizID      string            `json:"visitor_biz_id"`
	VisitorLabels     []model.Label     `json:"visitor_labels"`
	StartTime         time.Time         `json:"start_time"`
	IsEvaluateTest    bool              `json:"is_evaluate_test"` // 是否应用评测；内部调用，不暴露到API文档
	Environment       string            `json:"environment"`      // experience:对话测试 send：用户端
	WorkflowID        string            `json:"workflow_id"`      // 工作流ID
	WorkflowInput     map[string]string `json:"workflow_input"`   // 工作流输入参数
	PdlID             string            `json:"pdl_id"`           // PDL ID
	GenerateAgain     bool              `json:"generate_again"`   // 是否重新生成
	DebugMessage      *DebugMessage     `json:"debug_message,omitempty"`
	Plugins           []*PluginInfo     `json:"plugins,omitempty"`

	ModelName          string         `json:"model_name"`   // 强制指定模型
	ChatHistoryFromAPI []*ChatHistory `json:"chat_message"` // 历史上下文
	Verbose            int            `json:"verbose"`      // 是否返回详细信息
	Incremental        bool           `json:"incremental"`  // content是否是增量输出的内容，默认否，输出全量
	// 是否联网搜索，只有应用配置开了联网才有意义，"" -> 跟随应用配置， enable/disable 开关
	SearchNetwork     model.SearchNetwork  `json:"search_network"`
	EnableMultiIntent bool                 `json:"enable_multi_intent"` // 是否支持下发多意图，支持的话会下发多意图选项卡
	Stream            model.ChatStream     `json:"stream"`              // 是否开启流式输出
	WorkflowStatus    model.WorkflowStatus `json:"workflow_status"`     // 是否开启工作流
	LKEUserID         string               `json:"tcadp_user_id"`       // 应用角色id

	// 用于端上sdk的参数
	ToolOuputs  []ToolOuput `json:"tool_ouputs"`  // 端上调用工具的输出提交到云上
	AgentConfig AgentConfig `json:"agent_config"` // agent配置
	ChannelType int         `json:"channel_type"` // 渠道类型
}

// ToolOuput 本地工具的输出结果
type ToolOuput struct {
	ToolName string `json:"tool_name"`
	Output   string `json:"output"`
}

// AgentConfig 对话的 agent 配置
type AgentConfig struct {
	StartAgentName   string                                               `json:"start_agent_name"`   // 入口 agent 的名字，如果不填默认从主 agent 开始执行
	Agents           []*agent_config_server.DescribeAppAgentListRsp_Agent `json:"agents"`             // 每次对话的动态新增 agent
	DisableSystemOpt bool                                                 `json:"disable_system_opt"` // 是否关闭系统内置优化
	Handoffs         []Handoff                                            `json:"handoffs"`           // 每次对话的动态新增工具
	AgentTools       []AgentTool                                          `json:"agent_tools"`        // 每次对话的动态新增工具
}

// Handoff 转交关系
type Handoff struct {
	SourceAgentName string `json:"source_agent_name"`
	TargetAgentName string `json:"target_agent_name"`
}

// AgentTool Agent 的工具
type AgentTool struct {
	AgentName string          `json:"agent_name"` // agent 的名字
	Tools     []*openapi.Tool `json:"tools"`      // 工具列表
}

// DebugMessage 调试信息
type DebugMessage struct {
	Timestamp int64          `json:"timestamp"` // 时间戳
	Histories []*ChatHistory `json:"histories"` // 历史上下文
	Sign      string         `json:"sign"`      // 签名
}

// ChatHistory 会话历史
type ChatHistory struct {
	Role           pb.LLMRole `json:"role"`                      // 角色
	Content        string     `json:"content"`                   // 内容
	IntentCategory string     `json:"intent_category,omitempty"` // 意图分类
}

// PluginInfo 插件信息
type PluginInfo struct {
	PluginID string `json:"plugin_id"`
	ToolID   string `json:"tool_id"`
}

// Name 事件名称
func (e DialogEvent) Name() string {
	return EventDialog
}

// IsValid 判断事件是否合法
func (e *DialogEvent) IsValid(ctx context.Context) bool {
	if !helper.CheckSessionID(e.SessionID) {
		return false
	}
	if len(e.VisitorBizID) > 128 {
		log.ErrorContextf(ctx, "[param invalid] VisitorBizID len:%d > 128", len(e.VisitorBizID))
		return false
	}
	if utf8.RuneCountInString(e.SystemRole) > 20000 {
		log.ErrorContextf(ctx, "[param invalid] SystemRole len:%d > 4000", utf8.RuneCountInString(e.SystemRole))
		return false
	}
	// 多模态开关
	if config.App().MultiModal.Disabled && helper.IsQueryContainsImage(e.Content) {
		log.ErrorContextf(ctx, "[param invalid] MultiModal disabled, but Content has images")
		return false
	}
	if !e.isValidDebugReq() {
		log.ErrorContextf(ctx, "[param invalid] invalid debug request")
		return false
	}
	if len(e.FileInfos) != 0 { // 传文档场景，校验文档内容
		for _, fileInfo := range e.FileInfos {
			if fileInfo == nil {
				log.ErrorContextf(ctx, "[param invalid] FileInfos has nil element")
				e.FileInfos = nil
				break
			}
			if fileInfo != nil && fileInfo.DocID == "" && fileInfo.FileName == "" {
				log.ErrorContextf(ctx, "[param invalid] FileInfos DocID and FileName are empty")
				e.FileInfos = nil
				break
			}
		}
	}
	if e.Content != "" || len(e.FileInfos) != 0 { // 有一个不为空即可
		return true
	}
	if len(e.ToolOuputs) > 0 {
		return true
	}
	log.ErrorContextf(ctx, "[param invalid] Content and FileInfos are both empty")
	return false
}

// func checkSessionID(sessionID string) bool {
// 	sessionRegx := config.GetSessionRegx()
// 	if len(sessionRegx) == 0 {
// 		return len(sessionID) != 0 && len(sessionID) < 65
// 	}
// 	regx, err := regexp.Compile(sessionRegx)
// 	if err != nil {
// 		log.Errorf("CheckSessionID sessionRegx:%s,err:%+v", sessionRegx, err)
// 		return false
// 	}
// 	return regx.MatchString(sessionID)
// }

func (e *DialogEvent) isValidDebugReq() bool {
	if e.DebugMessage == nil {
		return true
	}
	sign := generateSign(e.BotAppKey,
		e.VisitorBizID,
		e.SessionID,
		e.DebugMessage.Timestamp,
		config.App().DebugMessage.Salt)
	return sign == e.DebugMessage.Sign
}

func generateSign(appKey string, botBizID string, sessionID string, timestamp int64, salt string) string {
	content := fmt.Sprintf("%s.%s.%s.%d.%s", appKey, botBizID, sessionID, timestamp, salt)
	log.DebugContextf(context.Background(), "generateSign content:%s", content)
	hash := sha256.Sum256([]byte(content))
	return hex.EncodeToString(hash[:])
}

// GetChannelType TODO
func (e *DialogEvent) GetChannelType() int {
	if slices.Contains(config.App().Bot.ChannelTypeList, e.ChannelType) {
		return e.ChannelType
	} else {
		return 0
	}
}
