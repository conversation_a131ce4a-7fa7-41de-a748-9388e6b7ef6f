package model

import (
	_ "embed" // embed 用于导入外部文档
	"fmt"
	"regexp"
	"strings"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/agent_config_server"
	plugin "git.woa.com/dialogue-platform/lke_proto/pb-protocol/plugin_config_server"
	"git.woa.com/dialogue-platform/proto/pb-stub/openapi"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	jsoniter "github.com/json-iterator/go"
)

// ToolType 工具类型
const (
	ToolTypeFunction string = "function"
)

// DataType 数据类型
const (
	DataTypeObject  string = "object"
	DataTypeNumber  string = "number"
	DataTypeInteger string = "integer"
	DataTypeString  string = "string"
	DataTypeArray   string = "array"
	DataTypeNull    string = "null"
	DataTypeBoolean string = "boolean"
)

const (
	// TypeEnumString TODO
	TypeEnumString plugin.TypeEnum = 0 // 默认值是string，如果不填就按string处理
	// TypeEnumInt TODO
	TypeEnumInt plugin.TypeEnum = 1
	// TypeEnumFloat TODO
	TypeEnumFloat plugin.TypeEnum = 2
	// TypeEnumBool TODO
	TypeEnumBool plugin.TypeEnum = 3
	// TypeEnumObject TODO
	TypeEnumObject plugin.TypeEnum = 4
	// TypeEnumArrayString TODO
	TypeEnumArrayString plugin.TypeEnum = 5
	// TypeEnumArrayInt TODO
	TypeEnumArrayInt plugin.TypeEnum = 6
	// TypeEnumArrayFloat TODO
	TypeEnumArrayFloat plugin.TypeEnum = 7
	// TypeEnumArrayBool TODO
	TypeEnumArrayBool plugin.TypeEnum = 8
	// TypeEnumArrayObject TODO
	TypeEnumArrayObject plugin.TypeEnum = 9
)

const (
	// WorkflowTypeEnumString TODO
	WorkflowTypeEnumString KEP_WF.TypeEnum = 0 // 默认值是string，如果不填就按string处理
	// WorkflowTypeEnumInt TODO
	WorkflowTypeEnumInt KEP_WF.TypeEnum = 1
	// WorkflowTypeEnumFloat TODO
	WorkflowTypeEnumFloat KEP_WF.TypeEnum = 2
	// WorkflowTypeEnumBool TODO
	WorkflowTypeEnumBool KEP_WF.TypeEnum = 3
	// WorkflowTypeEnumObject TODO
	WorkflowTypeEnumObject KEP_WF.TypeEnum = 4
	// WorkflowTypeEnumArrayString TODO
	WorkflowTypeEnumArrayString KEP_WF.TypeEnum = 5
	// WorkflowTypeEnumArrayInt TODO
	WorkflowTypeEnumArrayInt KEP_WF.TypeEnum = 6
	// WorkflowTypeEnumArrayFloat TODO
	WorkflowTypeEnumArrayFloat KEP_WF.TypeEnum = 7
	// WorkflowTypeEnumArrayBool TODO
	WorkflowTypeEnumArrayBool KEP_WF.TypeEnum = 8
	// WorkflowTypeEnumArrayObject TODO
	WorkflowTypeEnumArrayObject KEP_WF.TypeEnum = 9
	// WorkflowTypeEnumFile TODO
	WorkflowTypeEnumFile KEP_WF.TypeEnum = 10
	// WorkflowTypeEnumDocument TODO
	WorkflowTypeEnumDocument KEP_WF.TypeEnum = 11
	// WorkflowTypeEnumImage TODO
	WorkflowTypeEnumImage KEP_WF.TypeEnum = 12
	// WorkflowTypeEnumAudio TODO
	WorkflowTypeEnumAudio KEP_WF.TypeEnum = 13
)

// TypeEnum2DataType 类型转换
var TypeEnum2DataType = map[plugin.TypeEnum]string{
	TypeEnumString:      DataTypeString,
	TypeEnumInt:         DataTypeInteger,
	TypeEnumFloat:       DataTypeNumber,
	TypeEnumBool:        DataTypeBoolean,
	TypeEnumObject:      DataTypeObject,
	TypeEnumArrayString: DataTypeArray,
	TypeEnumArrayInt:    DataTypeArray,
	TypeEnumArrayFloat:  DataTypeArray,
	TypeEnumArrayBool:   DataTypeArray,
	TypeEnumArrayObject: DataTypeArray,
}

// WorkflowTypeEnum2DataType 类型转换
var WorkflowTypeEnum2DataType = map[KEP_WF.TypeEnum]string{
	WorkflowTypeEnumString:      DataTypeString,
	WorkflowTypeEnumInt:         DataTypeInteger,
	WorkflowTypeEnumFloat:       DataTypeNumber,
	WorkflowTypeEnumBool:        DataTypeBoolean,
	WorkflowTypeEnumObject:      DataTypeObject,
	WorkflowTypeEnumArrayString: DataTypeArray,
	WorkflowTypeEnumArrayInt:    DataTypeArray,
	WorkflowTypeEnumArrayFloat:  DataTypeArray,
	WorkflowTypeEnumArrayBool:   DataTypeArray,
	WorkflowTypeEnumArrayObject: DataTypeArray,
	WorkflowTypeEnumFile:        DataTypeString,
	WorkflowTypeEnumDocument:    DataTypeString,
	WorkflowTypeEnumImage:       DataTypeString,
	WorkflowTypeEnumAudio:       DataTypeString,
}

//go:embed tools/ask_user.json
var askUser string

//go:embed tools/answer_directly.json
var answerDirectly string

//go:embed tools/task_completed.json
var taskCompleted string

//go:embed tools/search.json
var search string

//go:embed tools/qb_tools.json
var qbTools string

// AskUseRegexp 匹配ask_user的结果
var AskUseRegexp = regexp.MustCompile(`"question"\s*:\s*"((?:\\.|[^"\\])*)(?:"|$)`)

// AnswerDirectlyRegexp 匹配answer_directly的结果
var AnswerDirectlyRegexp = regexp.MustCompile(`"answer"\s*:\s*"((?:\\.|[^"\\])*)(?:"|$)`)

// TaskCompletedRegexp 匹配task_completed的结果
var TaskCompletedRegexp = regexp.MustCompile(`"final_answer"\s*:\s*"((?:\\.|[^"\\])*)(?:"|$)`)

// ResponseToUserRegexp 匹配response_to_user的结果
var ResponseToUserRegexp = regexp.MustCompile(`"content"\s*:\s*"((?:\\.|[^"\\])*)(?:"|$)`)

// SysToolSwitch2Main 切换到主工作流
const SysToolSwitch2Main = "switch_workflow"

// SysToolSwitch2Parent 切换到主工作流
const SysToolSwitch2Parent = "task_done"

// GetAskUserTool 获取询问用户工具
func GetAskUserTool() *openapi.Tool {
	askUserTool := &openapi.Tool{}
	data := []byte(config.AgentConfig.MainAgentTools["ask_user"])
	log.DebugContextf(trpc.BackgroundContext(), "ask_user data: %s", string(data))
	err := jsoniter.Unmarshal(data, askUserTool)
	if err != nil {
		fmt.Printf("err: %v\n", err)
	}
	return askUserTool
}

// GetAnswerDirectlyTool 获取直接回答工具
func GetAnswerDirectlyTool() *openapi.Tool {
	answerDirectlyTool := &openapi.Tool{}
	data := []byte(config.AgentConfig.MainAgentTools["answer_directly"])
	log.DebugContextf(trpc.BackgroundContext(), "answer_directly data: %s", string(data))
	err := jsoniter.Unmarshal(data, answerDirectlyTool)
	if err != nil {
		fmt.Printf("err: %v\n", err)
	}
	return answerDirectlyTool
}

// GetTaskCompletedTool 获取任务完成工具
func GetTaskCompletedTool() *openapi.Tool {
	taskCompletedTool := &openapi.Tool{}
	data := []byte(config.AgentConfig.MainAgentTools["task_completed"])
	log.DebugContextf(trpc.BackgroundContext(), "task_completed data: %s", string(data))
	err := jsoniter.Unmarshal(data, taskCompletedTool)
	if err != nil {
		fmt.Printf("err: %v\n", err)
	}
	return taskCompletedTool

}

// GetTaskDoneTool 获取任务完成工具。当用户没有配置handoff时，增加默认工具task_done. 当调用该工具时，默认退回到父节点
func GetTaskDoneTool() *openapi.Tool {
	taskDoneTool := &openapi.Tool{}
	data := []byte(config.AgentConfig.MainAgentTools["task_done"])
	log.DebugContextf(trpc.BackgroundContext(), "task_done data: %s", string(data))
	err := jsoniter.Unmarshal(data, taskDoneTool)
	if err != nil {
		fmt.Printf("err: %v\n", err)
	}
	return taskDoneTool

}

// GetHunYuanSearchTool 获取混沌搜索工具
func GetHunYuanSearchTool() *openapi.Tool {
	hunYuanSearchTool := &openapi.Tool{}
	data := []byte(search)
	err := jsoniter.Unmarshal(data, hunYuanSearchTool)
	if err != nil {
		fmt.Printf("err: %v\n", err)
	}
	return hunYuanSearchTool
}

// GetQbTools 获取Qb工具
func GetQbTools() []*openapi.Tool {
	qbToolList := make([]*openapi.Tool, 0)
	data := []byte(qbTools)
	err := jsoniter.Unmarshal(data, &qbToolList)
	if err != nil {
		fmt.Printf("err: %v\n", err)
	}
	return qbToolList
}

// GetDefaultTools 获取默认工具
func GetDefaultTools() (tools []*openapi.Tool) {
	tools = append(tools, GetAskUserTool())
	tools = append(tools, GetAnswerDirectlyTool())
	// tools = append(tools, GetTaskCompletedTool())
	// tools = append(tools, GetHunYuanSearchTool()) for test
	return tools
}

// IsDefaultTools 是否默认工具
func IsDefaultTools(toolName string) bool {
	if toolName == "ask_user" || toolName == "answer_directly" || toolName == "task_completed" ||
		toolName == "response_to_user" {
		return true
	}
	return false
}

// MatchAskUserReply 匹配询问用户回复
func MatchAskUserReply(content string) string {
	match := AskUseRegexp.FindStringSubmatch(content)
	if len(match) > 1 {
		ans := strings.TrimSuffix(match[1], "\"")
		ans = strings.TrimSuffix(ans, "\"}")
		ans = strings.TrimSuffix(ans, "\" }")
		return ans
	}
	return ""
}

// MatchAnswerDirectlyReply 匹配直接回答回复
func MatchAnswerDirectlyReply(content string) string {
	match := AnswerDirectlyRegexp.FindStringSubmatch(content)
	if len(match) > 1 {
		ans := strings.TrimSuffix(match[1], "\"")
		ans = strings.TrimSuffix(ans, "\"}")
		ans = strings.TrimSuffix(ans, "\" }")
		return ans
	}
	return ""
}

// MatchTaskCompletedReply 匹配任务完成回复
func MatchTaskCompletedReply(content string) string {
	match := TaskCompletedRegexp.FindStringSubmatch(content)
	if len(match) > 1 {
		ans := strings.TrimSuffix(match[1], "\"")
		ans = strings.TrimSuffix(ans, "\"}")
		ans = strings.TrimSuffix(ans, "\" }")
		return ans
	}
	return ""
}

// MatchResponseToUserReply 匹配回复用户回复
func MatchResponseToUserReply(content string) string {
	match := ResponseToUserRegexp.FindStringSubmatch(content)
	if len(match) > 1 {
		ans := strings.TrimSuffix(match[1], "\"")
		ans = strings.TrimSuffix(ans, "\"}")
		ans = strings.TrimSuffix(ans, "\" }")
		return ans
	}
	return ""
}

// ToolResponse 工具响应
type ToolResponse struct {
	Code int
	Msg  string
	Data interface{}
}

// HunYuanResponse HunYuan响应
type HunYuanResponse struct {
	Code int
	Msg  string
	Data HunYuanData
}

// HunYuanData HunYuan数据
type HunYuanData struct {
	Answer     string
	QuoteInfos []QuoteInfo
	References []PluginReference
}

// AgentReference Agent参考来源
type AgentReference struct {
	ID       uint64 `json:"id,omitempty,string"`
	Type     uint32 `json:"type,omitempty"`
	URL      string `json:"url,omitempty"`
	Name     string `json:"name,omitempty"`
	DocID    uint64 `json:"doc_id,omitempty,string"`
	DocBizID uint64 `json:"doc_biz_id,omitempty,string"` // 前端需要biz id用于反馈
	DocName  string `json:"doc_name,omitempty"`
	QABizID  uint64 `json:"qa_biz_id,omitempty,string"`
	Index    uint32 `json:"index,omitempty"`
	Title    string `json:"title,omitempty"`
}

// PluginReference Agent参考来源
type PluginReference struct {
	ID       uint64 `json:"ID,omitempty"`
	Type     uint32 `json:"Type,omitempty"`
	URL      string `json:"Url,omitempty"`
	Name     string `json:"Name,omitempty"`
	DocID    uint64 `json:"DocID,omitempty"`
	DocBizID uint64 `json:"DocBizID,omitempty"` // 前端需要biz id用于反馈
	DocName  string `json:"DocName,omitempty"`
	QABizID  uint64 `json:"QABizID,omitempty"`
	Index    uint32 `json:"Index,omitempty"`
	Title    string `json:"Title,omitempty"`
}

// PluginReferenceConvert Agent参考来源转换
func PluginReferenceConvert(references []PluginReference) []AgentReference {
	agentReferences := make([]AgentReference, 0)
	for _, reference := range references {
		agentReferences = append(agentReferences, AgentReference(reference))
	}
	return agentReferences
}

// KnowledgeResponse 知识问答响应
type KnowledgeResponse struct {
	Code int
	MsG  string
	Data KnowledgeData
}

// KnowledgeData 知识问答数据
type KnowledgeData struct {
	Thought    string
	Answer     string
	References []PluginReference
}

// AgentTool 智能体工具
type AgentTool struct {
	*agent_config_server.AgentToolInfo
	Plugin                    *agent_config_server.DescribeAppAgentListRsp_AgentPluginInfo
	IsWorkflow                bool
	WorkflowID                string
	WorkflowName              string
	PDLContent                string
	APIInfo                   string
	Parameters                string
	UserAdditionalConstraints string
}

// EChartsResponse ECharts响应
type EChartsResponse struct {
	Code int         `json:"Code"`
	Msg  string      `json:"Msg"`
	Data EChartsData `json:"Data"`
}

// EChartsData ECharts数据
type EChartsData struct {
	ExecResult string           `json:"ExecResult"`
	ExecMsg    string           `json:"ExecMsg"`
	ExecState  string           `json:"ExecState"`
	Images     []interface{}    `json:"Images"`
	Files      []CodeFileObject `json:"Files"`
}

// CodeFileObject 代码解释器返回文件
type CodeFileObject struct {
	URL      string `json:"Url"`
	FileName string `json:"FileName"`
	Size     int    `json:"Size"`
}

// MCPResponse 部署HTML响应
type MCPResponse struct {
	Content []MCPToolContent `json:"content"`
}

// MCPToolContent TODO
type MCPToolContent struct {
	Type string `json:"type"`
	Text string `json:"text"`
}

// DeployResponse 对应 text 中的最外层对象
type DeployResponse struct {
	Status string `json:"status"`
	Files  []File `json:"files"`
}

// File 对应 files 数组里的每一项
type File struct {
	Name      string `json:"name"`
	Size      string `json:"size"`      // 原始数据是字符串，如 "3"
	Timestamp int64  `json:"timestamp"` // 毫秒级时间戳
}

// TextToImageResponse 文本转图片响应
type TextToImageResponse struct {
	Code int    `json:"Code"`
	Msg  string `json:"Msg"`
	Data struct {
		Results []struct {
			URL string `json:"Url"`
		} `json:"Results"`
	} `json:"Data"`
}

// BrowserResponse 浏览器工具返回
type BrowserResponse struct {
	ActionResult   string `json:"action_result"`
	PageScreenshot string `json:"page_screenshot"`
}
