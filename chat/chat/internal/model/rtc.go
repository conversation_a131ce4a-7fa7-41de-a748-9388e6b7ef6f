package model

const (
	// LLMTypeLKE 腾讯LKE
	LLMTypeLKE = "tencent_lke" // LKE
	// TTSTypeTencent 腾讯TTS
	TTSTypeTencent = "tencent" // 腾讯TTS
	// AvatarTypeTencent 腾讯数字人
	AvatarTypeTencent = "tencent" // 腾讯数字人
)

// RTCType 实时通话类型
type RTCType string

const (
	// RTCTypeVoice 实时通话-声音
	RTCTypeVoice RTCType = "voice"
	// RTCTypeDigitalHuman 实时通话-数字人
	RTCTypeDigitalHuman RTCType = "digital_human"
)

// RTCEventType 实时通话事件类型
type RTCEventType int

const (
	// EventTypeAiServiceStart AI任务开始状态回调
	EventTypeAiServiceStart RTCEventType = 901

	// EventTypeAiServiceStop AI任务结束状态回调
	EventTypeAiServiceStop RTCEventType = 902

	// EventTypeAiServiceMsg 回调ASR识别的完整一句话或LLM返回的完整内容
	// EventTypeAiServiceMsg RTCEventType = 903

	// EventTypeAiStartOfSpeech ASR识别一句话的开始回调
	EventTypeAiStartOfSpeech RTCEventType = 904

	// EventTypeAiSpeakingFinished 在一轮对话中，AI说完话的回调
	// EventTypeAiSpeakingFinished RTCEventType = 905

	// EventTypeAiMetricMessage AI服务中调用LLM/TTS的指标回调
	EventTypeAiMetricMessage RTCEventType = 906

	// EventTypeAiErrorMetricCallback AI服务的调用指标错误回调
	EventTypeAiErrorMetricCallback RTCEventType = 908
)

// RTCToken 实时通话Token
type RTCToken struct {
	Token         string `json:"token"`           // 实时通话Token
	ConnType      uint32 `json:"conn_type"`       // 连接类型，1-坐席，2-访客，3-体验 5-API
	BotBizID      uint64 `json:"bot_biz_id"`      // 应用ID
	CorpID        uint64 `json:"corp_id"`         // 企业ID
	SdkAppID      uint64 `json:"sdk_app_id"`      // 应用ID
	UserID        string `json:"user_id"`         // 用户ID
	UserSig       string `json:"user_sig"`        // 用户签名
	RobotUserID   string `json:"robot_user_id"`   // 机器人用户ID
	RobotUserSig  string `json:"robot_user_sig"`  // 机器人用户签名
	AvatarUserID  string `json:"avatar_user_id"`  // 数字人用户ID
	AvatarUserSig string `json:"avatar_user_sig"` // 数字人用户签名
	RoomID        uint32 `json:"room_id"`         // 房间ID
	Timestamp     uint64 `json:"timestamp"`       // 时间戳
	RTCType       string `json:"rtc_type"`        // 实时通话类型
}

// RTCClient 实时通话Client
type RTCClient struct {
	Token     string `json:"token"`      // 实时通话Token
	SdkAppID  uint64 `json:"sdk_app_id"` // 应用ID
	RoomID    uint32 `json:"room_id"`    // 房间ID
	ConnType  uint32 `json:"conn_type"`  // 连接类型，1-坐席，2-访客，3-体验 5-API
	BotBizID  uint64 `json:"bot_biz_id"` // 应用ID
	CorpID    uint64 `json:"corp_id"`    // 企业ID
	Timestamp uint64 `json:"timestamp"`  // 时间戳
	RTCType   string `json:"rtc_type"`   // 实时通话类型
}

// LLMConfig LLM配置
type LLMConfig struct {
	LLMType string  `json:"LLMType"`           // LLM类型，必填
	APIUrl  string  `json:"APIUrl,omitempty"`  // API地址
	APIKey  string  `json:"APIKey"`            // API密钥，必填
	Timeout float64 `json:"Timeout,omitempty"` // 超时时间(秒)
	// FileInfos         []interface{}          `json:"FileInfos,omitempty"`         // 文件信息列表
	// VisitorLabels     []interface{}          `json:"VisitorLabels,omitempty"`     // 访问者标签
	// StreamingThrottle int                    `json:"StreamingThrottle,omitempty"` // 流控阈值
	// SystemRole        string                 `json:"SystemRole,omitempty"`        // 系统角色
	SessionID         string            `json:"SessionId,omitempty"`         // 会话ID
	VisitorBizID      string            `json:"VisitorBizId,omitempty"`      // 访客ID
	Streaming         bool              `json:"Streaming,omitempty"`         // 是否流式处理
	IsRetThoughtEvent bool              `json:"IsRetThoughtEvent,omitempty"` // 是否返回思考事件
	CustomVariables   map[string]string `json:"CustomVariables,omitempty"`   // 自定义变量
}

// TTSConfig 语音合成(TTS)配置
type TTSConfig struct {
	TTSType   string `json:"TTSType"`   // TTS类型，目前支持"tencent"，必填
	AppID     uint64 `json:"AppId"`     // 应用ID，必填
	SecretID  string `json:"SecretId"`  // 密钥ID，必填
	SecretKey string `json:"SecretKey"` // 密钥Key，必填
	VoiceType uint32 `json:"VoiceType"` // 音色ID，必填
	// Speed     float64 `json:"Speed"`     // 语速，范围[-2,6]，非必填
	// Volume    float64 `json:"Volume"`    // 音量大小，范围[0,10]，非必填
	// PrimaryLanguage  uint32  `json:"PrimaryLanguage"`  // 主要语言，1-中文(默认) 2-英文 3-日文
	// FastVoiceType    string  `json:"FastVoiceType"`    // 快速声音复刻参数，可选
	// EmotionCategory  string  `json:"EmotionCategory"`  // 情感类型，如"neutral"(中性)、"sad"(悲伤)等
	// EmotionIntensity uint32  `json:"EmotionIntensity"` // 情感强度，范围[50,200]，默认为100
}

// AvatarConfig 数字人配置
type AvatarConfig struct {
	AvatarType         string      `json:"AvatarType"`         // 数字人类型，目前只支持"tencent"，必填
	Appkey             string      `json:"Appkey"`             // 数字人的appkey，必填
	AccessToken        string      `json:"AccessToken"`        // 访问token，必填
	AssetVirtualmanKey string      `json:"AssetVirtualmanKey"` // 数字人资产key，必填
	DriverType         uint64      `json:"DriverType"`         // 驱动类型 1-文本驱动，必填
	AvatarUserID       string      `json:"AvatarUserID"`       // 数字人用户ID，必填
	AvatarUserSig      string      `json:"AvatarUserSig"`      // 数字人用户签名，必填
	SpeechParam        SpeechParam `json:"SpeechParam"`        // 声音参数，必填
}

// SpeechParam 语音参数
type SpeechParam struct {
	TimbreKey string `json:"TimbreKey,omitempty"` // 音色
	// Speed     float64 `json:"Speed,omitempty"`     // 语速
	// Volume    float64 `json:"Volume,omitempty"`    // 音量
}

// RTCEventRequest 实时通话事件请求
type RTCEventRequest struct {
	EventGroupID int          `json:"EventGroupId"` // 事件组ID
	EventType    int          `json:"EventType"`    // 事件类型
	CallbackTs   int64        `json:"CallbackTs"`   // 回调时间戳
	EventInfo    RTCEventInfo `json:"EventInfo"`    // 事件信息
}

// RTCEventResponse 实时通话事件响应
type RTCEventResponse struct {
	Code    int    `json:"Code"`    // 状态码
	Message string `json:"Message"` // 信息
}

// RTCEventInfo 实时通话事件信息
type RTCEventInfo struct {
	EventMsTs  int64       `json:"EventMsTs"`  // 事件毫秒时间戳
	TaskID     string      `json:"TaskId"`     // 任务ID
	RoomID     string      `json:"RoomId"`     // 房间ID
	RoomIDType int         `json:"RoomIdType"` // 房间ID类型
	Payload    interface{} `json:"Payload"`    // 负载数据
}

// RTCStartEventPayload 实时通话事件开启负载数据
type RTCStartEventPayload struct {
	Status int `json:"Status"`
}

// RTCStopEventPayload 实时通话事件关闭负载数据
type RTCStopEventPayload struct {
	LeaveCode int `json:"LeaveCode"`
}

// RTCErrorEventPayload 实时通话事件错误负载数据
type RTCErrorEventPayload struct {
	Metric string `json:"Metric"`
	Tag    struct {
		RoundID string `json:"RoundId"`
		Code    int    `json:"Code"`
		Message string `json:"Message"`
	} `json:"Tag"`
}

// RTCMetricEventPayload 实时通话事件指标负载数据
type RTCMetricEventPayload struct {
	Metric string `json:"Metric"`
	Value  int64  `json:"Value"`
	Tag    struct {
		RoundID string `json:"RoundId"`
	} `json:"Tag"`
}
