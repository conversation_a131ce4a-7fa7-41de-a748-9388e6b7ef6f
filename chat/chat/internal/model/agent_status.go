package model

import (
	llmm "git.woa.com/dialogue-platform/proto/pb-stub/llm-manager-server"
)

// AgentStatus Agent状态，当前在哪个Agent
type AgentStatus struct {
	AgentType      string
	AgentSwitch    bool // 是否切换了Agent
	WorkflowID     string
	WorkflowName   string
	Memory         []*AgentMemory
	WorkflowMemory []*AgentMemory // 工作流记忆，这里可能保存的历史轮数不一样。如果开了PDL，两个Memory应该同时写
	UsePDL         bool
	CurrentStep    int      // agent 当前的思考轮数
	CurrentAgent   string   // 当前执行所在的 agent 的 name
	ParentAgents   []string // 当前执行所在的 agent 的 父 agent 栈
}

// GetLastParentAgent 获取最后一个 parent agent
func (a *AgentStatus) GetLastParentAgent() string {
	if len(a.ParentAgents) == 0 {
		return ""
	}
	return a.ParentAgents[len(a.ParentAgents)-1]
}

// AgentBacktrace agent 转交回溯，回到父节点
func (a *AgentStatus) AgentBacktrace() {
	if len(a.ParentAgents) == 0 {
		return
	}
	a.AgentSwitch = true
	// 更新当前 agent
	a.CurrentAgent = a.ParentAgents[len(a.ParentAgents)-1]
	// 更新父 agent 栈
	a.ParentAgents = a.ParentAgents[:len(a.ParentAgents)-1]
	a.WorkflowID = ""
	a.WorkflowName = ""
	a.AgentType = AgentTypeMain
}

// Handoff 执行状态转交
func (a *AgentStatus) Handoff(newAgent string) {
	if a.CurrentAgent == newAgent {
		// 可重入性
		return
	}
	a.AgentSwitch = true
	a.ParentAgents = append(a.ParentAgents, a.CurrentAgent)
	a.CurrentAgent = newAgent
}

// AgentMemory 智能体记忆
type AgentMemory struct {
	*llmm.Message
	From     string // 来源 "MAIN" or  具体工作流
	CallType string // 调用类型 "API" or "workflow"
}

// KBAgentMemory KBAgent记忆
type KBAgentMemory struct {
	Thought      string
	Action       string
	ActionInput  string
	ActionOutput string
}

// AddWorkflowMemory 添加Memory追加写入工作流记忆
func (a *AgentStatus) AddWorkflowMemory(memory *AgentMemory) {
	if a.UsePDL {
		a.WorkflowMemory = append(a.WorkflowMemory, memory)
	}
}

// AddMemory 添加Memory, 这里memory已经是全集了
func (a *AgentStatus) AddMemory(memory []*AgentMemory) {
	a.Memory = memory
	// // 保留 N 轮，传入的 memory 也算一轮，所以只需要再往前找 N-1 轮
	// round := config.GetAgentThoughtRound() - 1
	// // 倒序遍历
	// index := len(a.Memory) - 1
	// for ; index >= 0 && round != 0; index-- {
	//	if a.Memory[index].Role == llmm.Role_USER { //把当前找到的那条用户消息也一起保留
	//		round--
	//		if round == 0 {
	//			break
	//		}
	//	}
	// }
	// if index < 0 {
	//	a.Memory = memory
	//	return
	// }
	//
	// a.Memory = a.Memory[index:]
	// a.Memory = append(a.Memory, memory...)
}

const (
	// AgentTypeMain 主Agent
	AgentTypeMain string = "main"
	// AgentTypeWorkflow 工作流Agent
	AgentTypeWorkflow string = "workflow"
	// AgentTypeSubAgent 子 agent
	AgentTypeSubAgent string = "sub_agent"
	// AgentTypeKBAgent 知识库 agent
	AgentTypeKBAgent string = "kb_agent"
)

// ChatAgentImplCache session 中断 chat agent impl 的缓存数据
type ChatAgentImplCache struct {
	AgentMemory []*AgentMemory  `json:"agent_memory"`
	History     [][2]HisMessage `json:"history"`
	EChartsInfo []string        `json:"e_charts_info"`
	Thought     string          `json:"thought"`
	Container   string          `json:"agent_map"`
}

// SessionInterruptStatus  session 中断状态数据记录
type SessionInterruptStatus struct {
	RequestID          string              `json:"request_id"`
	RecordID           string              `json:"record_id"`
	Interrupt          bool                `json:"interrupt"`
	LastLLmRsp         *llmm.Response      `json:"last_llm_rsp"`         // 缓存上一次的 llm rsp
	AgentStatus        *AgentStatus        `json:"agent_status"`         // 缓存当前的 agent_status
	ChatAgentImplCache *ChatAgentImplCache `json:"chat_agent_iml_cache"` // 当前 imp 的缓存
}

// CheckVaild 检查中断状态是否是合法的
func (status SessionInterruptStatus) CheckVaild() bool {
	if status.LastLLmRsp == nil || len(status.LastLLmRsp.Message.ToolCalls) == 0 ||
		status.AgentStatus == nil || status.ChatAgentImplCache == nil {
		return false
	}
	return true
}
