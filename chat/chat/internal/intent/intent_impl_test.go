package intent

import (
	"bytes"
	"context"
	"fmt"
	"reflect"
	"sort"
	"testing"
	"text/template"

	"git.woa.com/ivy/qbot/qbot/chat/internal/dao"
	"git.woa.com/ivy/qbot/qbot/chat/internal/eventbus/botsession"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
)

/**
    @author：cooper
    @date：2025/4/7
    @note：
**/

func TestIntent_filerFinalIntents(t *testing.T) {
	type fields struct {
		dao dao.Dao
	}
	type args struct {
		ctx        context.Context
		resultList []int
		intentPri  []string
		intentCtx  botsession.ChatIntentCtx
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   []int
	}{
		{
			name: "test",
		},
	}
	for _, tt := range tests {
		type DD struct {
			UseSingleIntent bool
		}
		b := &bytes.Buffer{}
		info := DD{UseSingleIntent: true}
		tpl := "{{if .UseSingleIntent}}a\n{{else}}b\\n----\\n{{end}}\nc"
		template.Must(template.New("").Parse(tpl)).Execute(b, info)
		fmt.Println(b.String())

		t.Run(tt.name, func(t *testing.T) {
			var list []model.IntentAchieveMethod
			list = append(list, model.IntentAchieveMethod{Priority: 1, IntentCate: "test1", Index: 1})
			list = append(list, model.IntentAchieveMethod{Priority: 0, IntentCate: "test2", Index: 2})
			// 按优先级升序
			sort.Slice(list, func(i, j int) bool {
				return list[i].Priority < list[j].Priority
			})
			fmt.Println(list)
			i := &Intent{
				dao: tt.fields.dao,
			}
			if got := i.filerFinalIntents(tt.args.ctx, tt.args.resultList, tt.args.intentPri, tt.args.intentCtx); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("filerFinalIntents() = %v, want %v", got, tt.want)
			}
		})
	}
}
