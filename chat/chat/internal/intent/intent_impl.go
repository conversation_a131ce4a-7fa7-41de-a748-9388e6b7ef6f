package intent

import (
	"bytes"
	"context"
	"sort"
	"strconv"
	"strings"
	"text/template"
	"time"
	"unicode/utf8"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/common/v3/utils"
	"git.woa.com/dialogue-platform/go-comm/encode"
	"git.woa.com/dialogue-platform/go-comm/pf"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF_DM"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/knowledge"
	llmm "git.woa.com/dialogue-platform/proto/pb-stub/llm-manager-server"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	"git.woa.com/ivy/qbot/qbot/chat/internal/eventbus/botsession"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
	"git.woa.com/ivy/qbot/qbot/chat/pkg"
)

// QATpl QA模板
type QATpl struct {
	Question string
	Answer   string
}

// Recognize 意图识别
func (i *Intent) Recognize(ctx context.Context, bs *botsession.BotSession) (intentRsp model.IntentRsp) {
	tik := time.Now()
	pf.StartElapsed(ctx, "preview.intent.Recognize")                             // 意图识别 耗时打点
	pf.StartElapsedAsMetrics(ctx, config.App().StageTaskName.IntentionRecognize) // 意图识别阶段耗时统计
	defer func() {
		pf.AppendSpanElapsed(ctx, "preview.intent.Recognize")
		log.InfoContextf(ctx, "IntentRecognize rsp:%s,cost:%d", utils.Any2String(intentRsp),
			time.Since(tik).Milliseconds())
	}()
	if bs.WorkflowID != "" { // 工作流调试，或者单工作流模式
		log.InfoContextf(ctx, "bs.WorkflowID is not empty, IntentCate:%s", model.IntentTypeWorkflow)
		intentRsp = model.IntentRsp{CandidateIntent: model.CandidateIntent{WorkflowID: bs.WorkflowID}}
		intentRsp.IntentCate = model.IntentTypeWorkflow
		return intentRsp
	}
	workflowID := i.dao.GetWorkflowUnchanged(ctx, bs.App.GetAppBizId(), bs.SessionID)
	if workflowID != "" { // 工作流保持
		intentRsp = model.IntentRsp{CandidateIntent: model.CandidateIntent{WorkflowID: workflowID}}
		intentRsp.IntentCate = model.IntentTypeWorkflow
		return intentRsp
	}
	if config.IsExplicitIntent(bs.App.GetAppBizId()) { // 配置了明确意图，不再走意图识别逻辑
		return i.dealExplicitIntent(ctx, bs)
	}
	if utf8.RuneCountInString(bs.OriginContent) > config.GetIntentThreshold() { // 超长
		log.WarnContextf(ctx, "IntentRecognize content too long")
		intentRsp.CandidateIntent.Type = model.AppTypeKnowledgeQA
		return intentRsp
	}
	m := bs.App.GetModel(ctx, model.AppTypeKnowledgeQA, model.ModelTypeIntentRecognize)
	m.PromptLimit = i.dao.GetModelPromptLimit(ctx, m.GetModelName()) // 获取截断长度
	intentCtx := i.GetIntentContext(ctx, bs)                         // 组装意图上下文
	prompt, err := i.dao.TextTruncate(ctx, m, intentCtx)             // 截断
	if err != nil {
		log.ErrorContextf(ctx, "IntentRecognize render prompt error: %+v", err)
		return intentRsp
	}
	requestID := model.RequestID(ctx, "Recognize.SessionID", encode.GenerateUUID()) // 非流式调用
	message := m.WrapMessages("", nil, prompt)
	req := m.NewLLMRequest(requestID, message)
	rsp, err := i.dao.SimpleChat(ctx, req) // 意图识别
	if err != nil {
		log.ErrorContextf(ctx, "IntentRecognize simple chat error: %+v prompt:%s", err, prompt)
		return intentRsp
	}
	intentRsp.LLMRspStr = rsp.GetMessage().GetContent()
	// 更新意图prompt
	go i.dao.UpdateIntentPrompt(ctx, bs.RelatedRecordID, helper.Object2StringEscapeHTML(req), intentRsp.LLMRspStr)
	if helper.ResponseFormatNew(intentRsp.LLMRspStr) { // 多意图版本
		i.parseIntentResponseV2(ctx, bs, intentCtx, &intentRsp)
	} else { // 单意图版本
		intentRsp.IntentName, intentRsp.IntentCate, intentRsp.CandidateIntent, intentRsp.Related =
			i.parseIntentResponse(ctx, bs, rsp, intentCtx)
	}
	if len(intentRsp.MultiIntent.FinalList) > 1 {
		bs.Flags.IsMultiIntentReply = true
	}
	intentRsp.IsDocPriorityByRecognize = i.isDocPriorityByRecognize(ctx, bs, intentRsp) // 是否文档优先，候选意图<文档
	// 处理结果
	go i.SetLastWorkflow(ctx, bs, intentRsp.CandidateIntent, intentRsp.Related) // 设置上一轮工作流
	uin := pkg.Uin(ctx)
	pf.AppendFullSpanElapsed(ctx, config.App().StageTaskName.IntentionRecognize, m.ModelName,
		intentRsp.IntentCate, uin, -1)
	return intentRsp
}

// GetIntentContext 获取意图上下文
func (i *Intent) GetIntentContext(ctx context.Context, bs *botsession.BotSession) (intentCtx botsession.ChatIntentCtx) {
	intentCtx = botsession.ChatIntentCtx{
		// Docs:      i.GetIntentDocs(ctx, bs.App.GetAppBizId(), bs.Knowledge, bs.Flags.IsRealTimeDocument),
		Question:  bs.PromptCtx.Question,
		Intents:   make([]model.CandidateIntent, 0),
		Histories: botsession.TruncateIntentHistories(i.makeIntentHistories(ctx, bs), 2000),
	}
	startIndex := len(config.App().IntentModel.SystemIntent) - 1 // 里面有个-1 其他的，忽略
	// 如果最后一轮是工作流，需要把工作流是否结束的状态更新进去
	if len(intentCtx.Histories) > 0 &&
		strings.Contains(intentCtx.Histories[len(intentCtx.Histories)-1].Intention, "工作流") {
		lastIntent := intentCtx.Histories[len(intentCtx.Histories)-1].Intention
		lastWorkflow, _ := i.dao.GetLastWorkflow(ctx, bs.App.GetAppBizId(), bs.SessionID)
		if lastWorkflow == nil { // 为nil，说明工作流已经结束
			lastIntent = strings.Replace(lastIntent, "工作流", "工作流-结束", 1)
			intentCtx.Histories[len(intentCtx.Histories)-1].Intention = lastIntent
		}
	}
	// 工作流意图
	currIndex := startIndex
	if bs.IsWorkflowStatusEnabled() {
		intentCtx.Intents = i.GetIntentWorkflow(ctx, currIndex, bs)
	}
	// FAQ意图
	currIndex = startIndex + len(intentCtx.Intents)
	intentCtx.Intents = append(intentCtx.Intents, i.GetIntentQa(ctx, currIndex, bs)...)
	// 自定义意图
	currIndex = currIndex + len(intentCtx.Intents)
	intentCtx.Intents = append(intentCtx.Intents, i.GetCustomIntent(ctx, currIndex, bs)...)
	// 用户没有设置过意图优先级，就默认走单意图prompt
	if len(bs.App.GetKnowledgeQa().GetIntentAchievements()) == 0 {
		intentCtx.UseSingleIntent = true
	}
	return intentCtx
}

// GetIntentDocs 获取意图文档
func (i *Intent) GetIntentDocs(ctx context.Context, appID uint64, docs []*knowledge.SearchKnowledgeRsp_SearchRsp_Doc,
	isRealtime bool) (newDocs []*knowledge.SearchKnowledgeRsp_SearchRsp_Doc) {
	newDocs = make([]*knowledge.SearchKnowledgeRsp_SearchRsp_Doc, 0, len(docs))
	// 2024.11.06 修改，在白名单中的appID，才返回docs.默认不返回docs
	// 2024.11.15 修改，在白名单中的appID，不返回docs.默认返回docs
	// 2024.11.19 修改，在白名单中的appID，才返回docs.默认不返回docs
	// 2024.11.26 修改，在白名单中的appID，不返回docs.默认返回docs
	// 2025.03.11 修改，在白名单中的appID，才返回docs.默认不返回docs
	if !config.IsInIntentRecognizeWhitelist(appID) {
		return newDocs
	}
	log.InfoContextf(ctx, "GetIntentDocs appID:%d is in whitelist, return docs", appID)
	for _, doc := range docs {
		if doc.DocType != 2 { // 只要文档
			continue
		}
		newDoc := &knowledge.SearchKnowledgeRsp_SearchRsp_Doc{
			DocId:   doc.GetDocId(),
			DocType: doc.GetDocType(),
			OrgData: strings.ReplaceAll(doc.GetOrgData(), "\n", ""),
		}
		newDocs = append(newDocs, newDoc)
	}
	return newDocs
}

// GetIntentWorkflow 获取意图工作流
func (i *Intent) GetIntentWorkflow(ctx context.Context,
	startIndex int, bs *botsession.BotSession) (workflows []model.CandidateIntent) {
	workflows = make([]model.CandidateIntent, 0)
	lastWorkflow, _ := i.dao.GetLastWorkflow(ctx, bs.App.GetAppBizId(), bs.SessionID)
	has := false
	for j, workflow := range bs.Workflows.GetWorkflows() {
		exp := ""
		for k, item := range workflow.GetExamples() {
			if k == 0 {
				exp = item
			} else {
				exp = exp + "\n" + item
			}
		}
		if len(exp) > 0 {
			exp = "{" + exp + "}"
		}
		if lastWorkflow != nil && lastWorkflow.WorkflowID == workflow.WorkflowID {
			has = true
		}
		workflows = append(workflows, model.CandidateIntent{
			Index:      startIndex + j + 1,
			Name:       workflow.WorkflowName,
			Def:        workflow.WorkflowDesc,
			Example:    exp,
			Type:       model.IntentTypeWorkflow,
			WorkflowID: workflow.WorkflowID,
		})
	}
	// 拼上上一轮的工作流
	if !has && lastWorkflow != nil && len(lastWorkflow.WorkflowID) > 0 {
		workflowStatus, _ := i.dao.GetWorkflowStatus(ctx, bs.App.GetAppBizId(), lastWorkflow.WorkflowID, bs.Scene)
		if workflowStatus != nil && !workflowStatus.GetIsEnable() { // 如果上一轮工作流状态未启用，不拼上一轮的工作流
			return workflows
		}

		exp := ""
		for _, item := range lastWorkflow.GetExamples() {
			exp = item // 只有一个
			break
		}
		workflows = append(workflows, model.CandidateIntent{
			Index:      startIndex + len(workflows) + 1,
			Name:       lastWorkflow.WorkflowName,
			Def:        lastWorkflow.WorkflowDesc,
			Example:    exp,
			Type:       model.IntentTypeWorkflow,
			WorkflowID: lastWorkflow.WorkflowID,
		})
	}
	return workflows
}

// GetIntentQa 获取意图问答
func (i *Intent) GetIntentQa(ctx context.Context, startIndex int,
	bs *botsession.BotSession) (qas []model.CandidateIntent) {
	qas = make([]model.CandidateIntent, 0)
	if !bs.App.GetKnowledgeQa().GetIntentWithFaq() { // FAQ不加入意图识别
		return qas
	}
	count := 1
	for _, qa := range bs.Knowledge {
		if qa.DocType != 1 {
			continue
		}
		if qa.GetExtra().GetRerankScore() < config.App().IntentModel.RerankScore {
			log.WarnContextf(ctx, "qa %s rerank score:%f < threshold:%f", utils.Any2String(qa),
				qa.GetExtra().GetRerankScore(), config.App().IntentModel.RerankScore)
			continue
		}
		question := qa.GetQuestion()
		exp := qa.GetQuestion()
		// 处理相似问逻辑
		if qa.GetSimilarQuestionExtra() != nil && qa.GetSimilarQuestionExtra().GetSimilarQuestion() != "" {
			question = qa.GetSimilarQuestionExtra().GetSimilarQuestion()
			exp = exp + "\n" + question
		}
		def := "用户的问题跟\"" + question + "\"是一个意思"
		if qa.QuestionDesc != "" { // 有描述 就用问题描述
			def = qa.QuestionDesc
		}
		var info QATpl
		info.Question = question
		info.Answer = qa.GetAnswer()
		b := &bytes.Buffer{}
		m := bs.App.GetModel(ctx, model.AppTypeKnowledgeQA, model.ModelTypeFaqDef)
		log.DebugContextf(ctx, "get faq_def, finally model_name:%s, prompt:%s", m.GetModelName(), m.GetPrompt())
		tpl := m.GetPrompt()
		if tpl != "" {
			err := template.Must(template.New("").Parse(tpl)).Execute(b, info)
			if err != nil {
				log.WarnContextf(ctx, "qa tpl render err:%+v", err)
			} else {
				def = b.String()
			}
		}
		qas = append(qas, model.CandidateIntent{
			Index:   startIndex + count,
			Name:    "FAQ-" + strconv.Itoa(count),
			Def:     def,
			Example: "{" + exp + "}",
			Type:    model.IntentTypeFAQ,
			DocID:   qa.GetRelatedId(),
		})
		count++
	}
	return qas
}

// GetCustomIntent 获取自定义意图
func (i *Intent) GetCustomIntent(ctx context.Context,
	startIndex int, bs *botsession.BotSession) (customs []model.CandidateIntent) {
	customs = make([]model.CandidateIntent, 0)
	for j, custom := range bs.CustomizeIntent.Skills {
		customs = append(customs, model.CandidateIntent{
			Index:   startIndex + j + 1,
			Name:    custom.Name,
			Def:     custom.Description,
			Example: custom.Implementation,
			Type:    model.IntentTypeCustom,
		})
	}
	return customs

}

// parseIntentResponse 解析意图大模型的返回
func (i *Intent) parseIntentResponse(ctx context.Context, bs *botsession.BotSession, rsp *llmm.Response,
	intentCtx botsession.ChatIntentCtx) (intentName, intentCategory string,
	hitIntent model.CandidateIntent, isRelated bool) {
	if helper.ResponseFormat(rsp.GetMessage().GetContent()) { // 简单校验一下格式
		index := helper.ParseIntent(rsp.GetMessage().GetContent())
		if index <= len(config.App().IntentModel.SystemIntent)-1 { // 系统意图
			intentName = config.App().IntentModel.SystemIntent[index]
		} else {
			hitIntent = i.GetIntentByIndex(ctx, index, intentCtx)
			if hitIntent.Type == model.IntentTypeFAQ { // FAQ
				hitIntent.Name = i.FindFaqQuestion(ctx, bs.Knowledge, hitIntent.DocID)
			}
			bs.IntentCate = hitIntent.Type
			bs.Intent = hitIntent.Name
			intentName = hitIntent.Name
			if !config.IsQANotFirst(bs.App.GetAppBizId()) && hitIntent.Type == model.IntentTypeFAQ { // FAQ 不判断相关性
				return hitIntent.Name, hitIntent.Type, hitIntent, false
			}
			// 工作流 不判断相关性
			if !config.IsWorkflowNotFirst(bs.App.GetAppBizId()) && hitIntent.Type == model.IntentTypeWorkflow {
				i.setWorkflowUnchanged(ctx, bs, hitIntent)
				return hitIntent.Name, hitIntent.Type, hitIntent, false
			}
		}
		related := helper.ParseRelated(rsp.GetMessage().GetContent())
		if related { // 文档相关
			return intentName, config.GetSysIntent(intentName, bs.App.GetAppBizId()), hitIntent, true
		}
		if len(hitIntent.Type) > 0 { // // 工作流、自定义意图 不相关直接返回
			i.setWorkflowUnchanged(ctx, bs, hitIntent)
			return hitIntent.Name, hitIntent.Type, hitIntent, false
		}
	} else { // 可能存在格式非法的情况，兜底一下
		intentName = strings.TrimSpace(rsp.GetMessage().GetContent())
	}
	// 非系统路由的场景
	intentRsp, err := i.dao.GetIntent(ctx, bs.App.GetKnowledgeQa().GetIntentPolicyId(), intentName) // 获取自定义路由
	if err == nil && intentRsp != nil {
		return intentName, intentRsp.GetCategory(), hitIntent, false
	}
	return intentName, model.KnowledgeQAIntent, hitIntent, false
}

// parseIntentResponseV2 解析意图大模型的返回
func (i *Intent) parseIntentResponseV2(ctx context.Context, bs *botsession.BotSession,
	intentCtx botsession.ChatIntentCtx, rsp *model.IntentRsp) {
	rsp.MultiIntent.ResultList = helper.ParseMultiIntent(rsp.LLMRspStr)
	if len(rsp.MultiIntent.ResultList) == 0 {
		log.WarnContextf(ctx, "intent result_list empty")
	} else if len(rsp.MultiIntent.ResultList) == 1 { // 单意图结果
		rsp.MultiIntent.FinalList = []int{rsp.MultiIntent.ResultList[0]}
	} else { // 模型返回多意图情况【只有faq支持吐出多意图，其他需要根据优先级选择一个】
		intentPri := bs.App.GetAppInfoRsp.GetKnowledgeQa().GetIntentAchievements()
		if len(intentPri) == 0 { // 没有配置意图优先级，返回文档非优先
			intentPri = []string{"qa", "workflow", "doc", "llm"} // 默认优先级
			log.WarnContextf(ctx, "GetIntentAchievements empty")
		}
		finalList := i.filerFinalIntents(ctx, rsp.MultiIntent.ResultList, intentPri, intentCtx)
		if len(finalList) > 1 && !bs.App.IsSupportMultiIntent(bs.EnableMultiIntent) { // 不支持多意图，默认取第一个
			log.InfoContextf(ctx, "no support multi intent")
			finalList = finalList[:1]
		}
		rsp.MultiIntent.FinalList = finalList
	}
	log.InfoContextf(ctx, "intent final_list:%s", utils.Any2String(rsp.MultiIntent.FinalList))
	if len(rsp.MultiIntent.FinalList) <= 0 {
		rsp.MultiIntent.FinalList = []int{-1} // 兜底处理
		log.WarnContextf(ctx, "intent final_list empty, set default intent")
	} else if len(rsp.MultiIntent.FinalList) > 1 { // 多意图，只能是FAQ
		for _, intentIdx := range rsp.MultiIntent.FinalList {
			hitIntent := i.GetIntentByIndex(ctx, intentIdx, intentCtx)
			if hitIntent.Type == model.IntentTypeFAQ { // FAQ
				hitIntent.Name = i.FindFaqQuestion(ctx, bs.Knowledge, hitIntent.DocID)
				rsp.MultiIntent.OptionCards = append(rsp.MultiIntent.OptionCards, hitIntent.Name)
				rsp.IntentCate = model.IntentTypeFAQ
			}
		}
		return
	}
	// 单意图结果
	index := rsp.MultiIntent.FinalList[0]
	if index <= len(config.App().IntentModel.SystemIntent)-1 { // 系统意图
		if index == model.SysIntentKnowledgeQAIdx || index == model.SysIntentDocSummaryIdx {
			index = -1 // 新版本全文处理和知识问答，需要重置为-1
		}
		rsp.IntentName = config.App().IntentModel.SystemIntent[index]
	} else { // 非系统意图
		rsp.CandidateIntent = i.GetIntentByIndex(ctx, index, intentCtx)
		if rsp.CandidateIntent.Type == model.IntentTypeFAQ { // FAQ
			rsp.CandidateIntent.Name = i.FindFaqQuestion(ctx, bs.Knowledge, rsp.CandidateIntent.DocID)
		}
		rsp.IntentName = rsp.CandidateIntent.Name
		rsp.IntentCate = rsp.CandidateIntent.Type
		if len(rsp.IntentCate) > 0 { // // 工作流、自定义意图 不相关直接返回
			i.setWorkflowUnchanged(ctx, bs, rsp.CandidateIntent)
			return
		}
	}
	// 非系统路由的场景,获取自定义路由
	intentRsp, err := i.dao.GetIntent(ctx, bs.App.GetKnowledgeQa().GetIntentPolicyId(), rsp.IntentName)
	if err == nil {
		rsp.IntentCate = intentRsp.GetCategory()
		return
	}
	return
}

// filerFinalIntents 基于优先级，过滤出最终的意图
func (i *Intent) filerFinalIntents(ctx context.Context, resultList []int, intentPri []string,
	intentCtx botsession.ChatIntentCtx) []int {
	var finalList []int
	priorityMap := make(map[string]int)
	for idx, intentAch := range intentPri {
		priorityMap[intentAch] = idx
	}
	var methodList []model.IntentAchieveMethod
	for _, intentIdx := range resultList { // TODO 这里是否需要获取自定义的意图分类
		hitIntent := i.GetIntentByIndex(ctx, intentIdx, intentCtx)
		item := model.IntentAchieveMethod{Index: intentIdx}
		if hitIntent.Type == model.IntentTypeFAQ { // FAQ
			item.IntentCate = "qa"
			item.Priority = priorityMap["qa"]
		} else if hitIntent.Type == model.IntentTypeWorkflow { // 工作流
			item.IntentCate = "workflow"
			item.Priority = priorityMap["workflow"]
		} else {
			item.IntentCate = "llm"
			item.Priority = priorityMap["llm"]
		}
		methodList = append(methodList, item)
	}
	// 按优先级升序
	sort.Slice(methodList, func(i, j int) bool {
		return methodList[i].Priority < methodList[j].Priority
	})
	log.InfoContextf(ctx, "filerFinalIntents:%s", utils.Any2String(methodList))
	top := methodList[0]        // 取优先级最高的达成方式
	if top.IntentCate != "qa" { // 如果优先级最高的不是faq，则直接返回top1的结果
		log.WarnContextf(ctx, "top intent is:%s, no support multi intent", top.IntentCate)
		finalList = []int{top.Index}
		return finalList
	}
	// 如果优先级最高的是faq,则需要判断是否有多个，有多个的话，需要返回多意图的结果
	for _, item := range methodList {
		if item.IntentCate == "qa" {
			finalList = append(finalList, item.Index)
		}
	}
	return finalList
}

// GetIntentByIndex 根据索引获取意图
func (i *Intent) GetIntentByIndex(ctx context.Context, index int,
	intentCtx botsession.ChatIntentCtx) (intent model.CandidateIntent) {
	for _, intent := range intentCtx.Intents {
		if index == intent.Index {
			log.InfoContextf(ctx, "GetIntentByIndex index:%d intent:%s", index, helper.Object2String(intent))
			return intent
		}
	}
	return model.CandidateIntent{}
}

// SetLastWorkflow 设置上一轮工作流
func (i *Intent) SetLastWorkflow(ctx context.Context, bs *botsession.BotSession,
	hitIntent model.CandidateIntent, related bool) {
	if related || len(hitIntent.WorkflowID) < 1 {
		_ = i.dao.SetLastWorkflow(ctx, bs.App.GetAppBizId(), bs.SessionID, nil)
	} else {
		workflow := &KEP_WF_DM.RetrieveWorkflow{
			WorkflowID:   hitIntent.WorkflowID,
			WorkflowName: hitIntent.Name,
			WorkflowDesc: hitIntent.Def,
			Examples:     []string{hitIntent.Example},
		}
		_ = i.dao.SetLastWorkflow(ctx, bs.App.GetAppBizId(), bs.SessionID, workflow)
	}
}

// FindFaqQuestion 查找FAQ问题
func (i *Intent) FindFaqQuestion(ctx context.Context,
	docs []*knowledge.SearchKnowledgeRsp_SearchRsp_Doc, docID uint64) (question string) {
	for _, d := range docs {
		if d.DocType != 1 {
			continue
		}
		if d.GetRelatedId() == docID {
			question = d.GetQuestion()
			if d.GetSimilarQuestionExtra() != nil && d.GetSimilarQuestionExtra().GetSimilarQuestion() != "" {
				question = d.GetSimilarQuestionExtra().GetSimilarQuestion()
			}
			break
		}
	}
	log.InfoContextf(ctx, "FindFaqQuestion of [docID:%d] question is: %s", docID, question)
	return question
}

// dealExplicitIntent 明确意图处理
func (i *Intent) dealExplicitIntent(ctx context.Context,
	bs *botsession.BotSession) (hitIntent model.IntentRsp) {
	hitIntent = model.IntentRsp{
		IntentName: config.App().IntentModel.ExplicitIntent[bs.App.GetAppBizId()],
	}
	hitIntent.IntentCate = config.App().IntentModel.SystemIntentMap[hitIntent.IntentName]
	log.InfoContextf(ctx, "dealExplicitIntent:%s", utils.Any2String(hitIntent))
	return hitIntent
}

func (i *Intent) makeIntentHistories(ctx context.Context,
	bs *botsession.BotSession) (histories []botsession.ChatHistory) {
	m := bs.App.GetModel(ctx, model.AppTypeKnowledgeQA, model.ModelTypeMessage)
	limit := int(m.GetHistoryLimit())
	num := helper.When(limit > len(bs.ChatHistoriesV2.ChatStack), len(bs.ChatHistoriesV2.ChatStack), limit)
	start := len(bs.ChatHistoriesV2.ChatStack) - num // 指定截取的起始位置
	histories = make([]botsession.ChatHistory, 0, num)
	for i := start; i < len(bs.ChatHistoriesV2.ChatStack); i++ {
		item := bs.ChatHistoriesV2.ChatStack[i]
		question := helper.When(item.RewriteQuery != "", item.RewriteQuery, item.OriginQuery) // 使用改写后的query
		histories = append(histories, botsession.ChatHistory{
			User:      question,
			Assistant: strings.ReplaceAll(item.GetAssistantContent(), "\n", "") + item.OptionCardsStr,
			Intention: item.Intent + "(" + model.GetRealCategory(item.IntentCategory) + ")",
		})
	}
	return histories
}

// setWorkflowUnchanged 设置在工作流保持信息
func (i *Intent) setWorkflowUnchanged(ctx context.Context, bs *botsession.BotSession,
	hitIntent model.CandidateIntent) {
	if config.IsWorkflowUnchanged(bs.App.GetAppBizId()) && hitIntent.Type == model.IntentTypeWorkflow {
		i.dao.SetWorkflowUnchanged(ctx, bs.App.GetAppBizId(), bs.SessionID, hitIntent.WorkflowID)
	} else {
		log.DebugContextf(ctx, "setWorkflowUnchanged not set, appID:%d, sessionID:%s, workflowID:%s",
			bs.App.GetAppBizId(), bs.SessionID, hitIntent.WorkflowID)
	}
}

// isDocPriorityByRecognize 判断是否文档优先
func (i *Intent) isDocPriorityByRecognize(ctx context.Context, bs *botsession.BotSession,
	intentRsp model.IntentRsp) (isDocPriority bool) {
	defer func() {
		log.InfoContextf(ctx, "isDocPriorityByRecognize: %v", isDocPriority)
	}()
	intentPri := bs.App.GetAppInfoRsp.GetKnowledgeQa().GetIntentAchievements()
	if len(intentPri) == 0 { // 说明用户没有设置优先级,使用默认配置
		intentPri = config.GetIntentAchievements(bs.App.GetAppBizId())
	}
	priorityMap := make(map[string]int)
	for idx, intentAch := range intentPri {
		priorityMap[intentAch] = idx
	}
	docPri := priorityMap["doc"]
	if len(intentPri) == 0 { // 没有配置意图优先级，返回文档非优先
		log.WarnContextf(ctx, "GetIntentAchievements empty")
		return isDocPriority
	}
	if len(intentRsp.MultiIntent.FinalList) > 1 {
		log.WarnContextf(ctx, "MultiIntent.FinalList not equal 1")
		return isDocPriority
	}
	comparePri := 0
	switch intentRsp.IntentCate {
	case model.IntentTypeFAQ:
		comparePri = priorityMap["qa"]
	case model.IntentTypeWorkflow:
		comparePri = priorityMap["workflow"]
	default:
		cfgPri, cfgOK := priorityMap[intentRsp.IntentCate]
		log.InfoContextf(ctx, "intentCate:%s, cfgOK:%t, cfgPri:%d", intentRsp.IntentCate, cfgOK, cfgPri)
		if len(bs.App.GetAppInfoRsp.GetKnowledgeQa().GetIntentAchievements()) == 0 && cfgOK { // 未设置且有配置，从配置取
			comparePri = cfgPri
		} else {
			comparePri = priorityMap["llm"]
		}
	}
	log.InfoContextf(ctx, "docPri:%d, comparePri:%d", docPri, comparePri)
	if docPri < comparePri { // 文档优先级高于当前意图优先级，返回文档优先
		isDocPriority = true
	}
	return isDocPriority
}
