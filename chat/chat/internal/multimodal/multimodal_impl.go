package multimodal

import (
	"context"
	"fmt"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/common/v3/plugins/i18n"
	"git.woa.com/dialogue-platform/go-comm/clues"
	llmm "git.woa.com/dialogue-platform/proto/pb-stub/llm-manager-server"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	"git.woa.com/ivy/qbot/qbot/chat/internal/eventbus/botsession"
	"git.woa.com/ivy/qbot/qbot/chat/pkg"
)

// GetCaption 获取图片描述
func (m *MultiModal) GetCaption(ctx context.Context, imageURLs []string, req *llmm.Request,
	query string) (*llmm.Response, error) {
	var placeholder string
	for i := 0; i < len(imageURLs); i++ {
		placeholder = placeholder + fmt.Sprintf(config.App().MultiModal.Placeholder, i)
	}
	prompt := i18n.Translate(ctx, config.App().MultiModal.GetCaptionPrompt)
	if len(imageURLs) > 1 {
		prompt = i18n.Translate(ctx, config.App().MultiModal.GetMultiPrompt)
	}
	if query != "" {
		prompt = query
	}
	message := &llmm.Message{
		Role:    llmm.Role_USER,
		Content: placeholder + prompt, // 算法同学说 占位符放前面
		Images:  imageURLs,
	}
	if config.OnlyCosURLModel(req.GetModelName()) {
		message.Images = helper.ReplaceDomain(imageURLs)
		message.Content = prompt
	}
	req.Messages = append(req.Messages, message)
	resp, err := m.dao.SimpleChat(ctx, req) // 获取Caption
	clues.AddTrackDataWithError(ctx, "GetCaption.SimpleChat", map[string]any{"req": req, "resp": resp}, err)
	if err != nil || resp == nil || resp.Message == nil { // 下游不正经报错，返回空，需要注意。
		log.ErrorContextf(ctx, "E|MultiModalEvent|GetCaption rsp:%s, Error %v", helper.Object2String(resp), err)
		return nil, pkg.ErrImageRecognitionFailed
	}
	return resp, err
}

// QueryWithImage 带图片的请求
func (m *MultiModal) QueryWithImage(ctx context.Context, bs *botsession.BotSession) (err error) {
	// req := &llmm.Request{
	//	RequestId:   model.RequestID(ctx, bs.RequestID, bs.RecordID),
	//	ModelName:   config.GetMultiModalModelName(bs.Session.BotBizID),
	//	AppKey:      fmt.Sprintf("%d", bs.Session.BotBizID),
	//	Messages:    make([]*llmm.Message, 0),
	//	PromptType:  llmm.PromptType_TEXT,
	//	RequestType: llmm.RequestType_ONLINE,
	//	Biz:         "cs",
	// }
	// bs.TokenStat.UpdateProcedure(event.NewProcessingTSProcedure(ctx, event.ProcedureImage))
	// SendTokenStat(ctx, bs, bs.TokenStat)
	//
	// var placeholder string
	// for i := 0; i < len(bs.Images); i++ {
	//	placeholder = placeholder + fmt.Sprintf(config.App().MultiModal.Placeholder, i)
	// }
	// prompt :=i18n.Translate(ctx, config.App().MultiModal.GetCaptionPrompt)
	// if len(bs.Images) > 1 {
	//	prompt = i18n.Translate(ctx, config.App().MultiModal.GetMultiPrompt)
	// }
	// message := &llmm.Message{
	//	Role:    llmm.Role_USER,
	//	Content: placeholder + prompt, // 算法同学说 占位符放前面
	//	Images:  bs.Images,
	// }
	// req.Messages = append(req.Messages, message)
	// bs.Flags.IsJudgeModelReject = false // 这种场景不判断拒答
	// bs.Intent = "纯图片输入"
	// bs.IntentCate = "图像描述"
	// startTime, last, lastEvil, isModelRejected, err := streamReply(ctx, bs, req,
	//	event.ProcedureImage, model.ReplyMethodImage, nil)
	// if err != nil {
	//	return err
	// }
	// if isModelRejected {
	//	pf.AddNode(ctx, pf.PipelineNode{Key: "processMultiModalReply.HitReject"})
	// }
	// isEvil := false
	// if !isModelRejected && last != nil && last.Message != nil {
	//	bs.Caption = last.Message.Content
	//	isEvil, _ = checkLastAndCreateRecord(ctx, bs, model.ReplyMethodImage, last, lastEvil, req, startTime)
	// }
	// if !isEvil { // 安全审核通过，处理图片历史
	//	go processImageHistory(ctx, bs)
	// }
	return nil
}
