package dao

import (
	"context"

	kpb "git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_knowledge_config_server"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/knowledge"
)

// IKnowledgeSearch 检索相关接口
type IKnowledgeSearch interface {
	// SearchGlobalKnowledge 检索全局知识
	// SearchGlobalKnowledge(ctx context.Context,
	//	req *knowledge_server.GlobalKnowledgeReq) ([]*knowledge_server.GlobalKnowledgeRsp_Doc, error)
	// // SearchRelease 正式库索引
	// SearchRelease(ctx context.Context, req *knowledge_server.SearchReq) (
	//	[]*knowledge_server.SearchRsp_Doc, error)
	// // SearchPreview 测评库索引
	// SearchPreview(ctx context.Context, req *knowledge_server.SearchPreviewReq) (
	//	[]*knowledge_server.SearchPreviewRsp_Doc, error)
	// // SearchPreviewRejectedQuestion 检索评测库拒答问题
	// SearchPreviewRejectedQuestion(
	//	context.Context, string, uint64,
	// ) ([]*knowledge_server.SearchPreviewRejectedQuestionRsp_RejectedQuestions, error)
	// // SearchReleaseRejectedQuestion 检索正式库拒答问题
	// SearchReleaseRejectedQuestion(
	//	context.Context, string, uint64,
	// ) ([]*knowledge_server.SearchReleaseRejectedQuestionRsp_RejectedQuestions, error)

}

// IKnowledgeSearchNew 检索相关接口-V2.6.0新版
type IKnowledgeSearchNew interface {
	// SearchRejectedQuestion 检索拒答问题
	SearchRejectedQuestion(context.Context, *knowledge.SearchKnowledgeReq_SearchReq,
		knowledge.SceneType) ([]*knowledge.SearchKnowledgeRsp_SearchRsp_Doc, error)
	// SearchGlobalKnowledge 检索全局知识
	SearchGlobalKnowledge(context.Context,
		*knowledge.SearchKnowledgeReq_SearchReq) ([]*knowledge.SearchKnowledgeRsp_SearchRsp_Doc, error)
	// SearchKnowledge 检索知识库
	SearchKnowledge(context.Context, *knowledge.SearchKnowledgeReq_SearchReq,
		knowledge.SceneType) ([]*knowledge.SearchKnowledgeRsp_SearchRsp_Doc, error)
	// SearchWorkflow 检索工作流
	SearchWorkflow(context.Context, *knowledge.SearchKnowledgeReq_SearchReq,
		knowledge.SceneType) ([]*knowledge.SearchKnowledgeRsp_SearchRsp_Doc, error)
	// SearchBatchKnowledge 检索多个知识库
	SearchBatchKnowledge(context.Context,
		*knowledge.SearchKnowledgeBatchReq) ([]*knowledge.SearchKnowledgeRsp_SearchRsp_Doc, error)
	// SearchRealtimeKnowledge 检索实时知识库
	SearchRealtimeKnowledge(ctx context.Context, r *knowledge.SearchKnowledgeReq_SearchReq,
		scene knowledge.SceneType) ([]*knowledge.SearchKnowledgeRsp_SearchRsp_Doc, error)

	// SearchFinancialKnowledge 检索金融行业FAQ
	SearchFinancialKnowledge(context.Context,
		*knowledge.SearchKnowledgeReq_SearchReq) ([]*knowledge.SearchKnowledgeRsp_SearchRsp_Doc, error)
	// SearchMedicalKnowledge 检索医疗行业FAQ
	SearchMedicalKnowledge(context.Context, *knowledge.SearchKnowledgeReq_SearchReq) (
		[]*knowledge.SearchKnowledgeRsp_SearchRsp_Doc, error)

	// MatchRefer 匹配参考来源
	MatchRefer(ctx context.Context, req *kpb.MatchReferReq) (
		[]*kpb.MatchReferRsp_Refer, error)

	// GetKnowledgeSchema 检索知识 Schema 列表 废弃 使用上游返回的
	// GetKnowledgeSchema(ctx context.Context, appID uint64, env string) ([]*kpb.GetKnowledgeSchemaRsp_SchemaItem, error)
}
