package dao

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"git.code.oa.com/trpc-go/trpc-database/redis"
	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/go-comm/clues"
	"git.woa.com/dialogue-platform/go-comm/pf"
	admin "git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_admin_config_server"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
	"git.woa.com/ivy/qbot/qbot/chat/pkg"
	jsoniter "github.com/json-iterator/go"
)

// CheckSession 根据登录态获取用户信息
func (d *dao) CheckSession(ctx context.Context) (*model.CorpStaff, error) {
	req := &admin.CheckSessionReq{}
	rsp, err := d.loginCli.CheckSession(ctx, req)
	if err != nil {
		if errs.Code(err) == errs.Code(pkg.ErrUserNotLogin) {
			log.WarnContextf(ctx, "Get staff by botsession error: %+v, req: %+v", err, req)
			return nil, err
		}
		log.ErrorContextf(ctx, "Get staff by botsession error: %+v, req: %+v", err, req)
		return nil, err
	}
	return &model.CorpStaff{ID: rsp.GetStaffId(), BusinessID: rsp.GetStaffBizId(), UserType: rsp.GetUserType()}, nil
}

// GetCorpStaffByBizID 根据业务 ID 获取企业员工信息
func (d *dao) GetCorpStaffByBizID(ctx context.Context, id uint64) (*model.CorpStaff, error) {
	staffs, err := d.GetCorpStaffsByBizID(ctx, []uint64{id})
	log.InfoContextf(ctx, "R|GetCorpStaffByBizID id: %d, %+v, ERR: %+v", id, staffs, err)
	if err != nil {
		return nil, err
	}
	if len(staffs) == 0 {
		return nil, nil
	}
	return &staffs[0], nil
}

// GetCorpStaffsByBizID 根据业务 ID 批量获取企业员工信息
func (d *dao) GetCorpStaffsByBizID(ctx context.Context, ids []uint64) ([]model.CorpStaff, error) {
	if len(ids) == 0 {
		return nil, nil
	}
	t0 := time.Now()
	req := &admin.GetCorpStaffsByBizIDsReq{StaffBizIds: ids}
	log.InfoContextf(ctx, "REQ|GetCorpStaffsByBizIDsReq %+v", req)
	rsp, err := d.apiCli.GetCorpStaffsByBizIDs(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "Get corp staffs by bizID error: %+v, req: %+v", err, req)
		return nil, err
	}
	staffs := make([]model.CorpStaff, 0, len(rsp.GetStaffs()))
	for _, staff := range rsp.GetStaffs() {
		staffs = append(staffs, model.CorpStaff{
			ID:         staff.GetStaffId(),
			Avatar:     staff.GetAvatar(),
			NickName:   staff.GetNickName(),
			BusinessID: staff.GetStaffBizId(),
		})
	}
	log.InfoContextf(ctx, "RESP|GetCorpStaffsByBizIDsRsp %+v, %s", staffs, time.Since(t0))
	return staffs, nil
}

// GetUserInfo 获取用户信息
func (d *dao) GetUserInfo(ctx context.Context, fromType model.SourceType, fromID uint64) (*model.UserInfo, error) {
	rsp, err := d.apiCli.GetUserInfo(ctx, &admin.GetUserInfoReq{
		FromType: uint32(fromType),
		FromId:   fromID,
	})
	if err != nil {
		return nil, err
	}
	return &model.UserInfo{
		UserBizID: rsp.GetUserBizId(),
		NickName:  rsp.GetNickName(),
		Avatar:    rsp.GetAvatar(),
	}, nil
}

func keyCorpIDUIN(corpID uint64) string {
	return fmt.Sprintf("qbot:chat:corp_id:%d", corpID)
}

// GetUinByCorpID 根据企业 ID 获取 Uin
func (d *dao) GetUinByCorpID(ctx context.Context, corpID uint64) (uin uint64, sid int, err error) {
	// Acc 账号临时结构
	type Acc struct {
		UIN uint64 `db:"uin" json:"uin"`
		SID int    `db:"sid" json:"sid"`
	}
	var acc Acc
	// 从缓存中取
	accCache, err := redis.String(d.redis.Do(ctx, "GET", keyCorpIDUIN(corpID)))
	if err == nil && len(accCache) != 0 {
		if err := jsoniter.UnmarshalFromString(accCache, &acc); err == nil {
			return acc.UIN, acc.SID, nil
		}
	}
	// 未命中从DB中取
	sql := "SELECT uin, sid FROM t_corp WHERE id = ?"
	err = d.db.Get(ctx, &acc, sql, corpID)
	clues.AddTrackE(ctx, "GetUinByCorpID.db.Get", clues.M{"sql": sql, "corpID": corpID, "acc": acc}, err)
	if err != nil {
		log.ErrorContextf(ctx, "Get uin by corpID error: %+v", err)
		return 0, 0, err
	}
	_, err = d.redis.Do(ctx, "SET", keyCorpIDUIN(corpID), helper.Object2String(acc))
	if err != nil {
		log.WarnContextf(ctx, "Set corp uin cache info  error: %+v, corpID: %d", err, corpID)
	}
	log.DebugContextf(ctx, "Get uin by corpID success, corpID: %d, uin: %d", corpID, uin)
	return acc.UIN, acc.SID, nil
}

// QueryCorpIDByStaffID 按 staff id 查询 corp id
func (d *dao) QueryCorpIDByStaffID(ctx context.Context, staffID uint64) (uint64, error) {
	var corpID uint64
	sql := "SELECT corp_id from t_corp_staff WHERE id = ? and status = 1 LIMIT 1"
	err := d.db.Get(ctx, &corpID, sql, staffID)
	clues.AddTrackE(ctx, "QueryCorpIDByAppID.db.Get", clues.M{"sql": sql, "staffID": staffID, "corpID": corpID}, err)
	if err != nil {
		log.ErrorContextf(ctx, "Get corpID by staffID error: %+v", err)
		return 0, err
	}
	return corpID, nil
}

// QueryCorpIDByAppID 按 app id 查询 corp id
func (d *dao) QueryCorpIDByAppID(ctx context.Context, appID uint64) (uint64, error) {
	var corpID uint64
	sql := "SELECT corp_id from t_robot WHERE business_id = ? and is_deleted = 0 LIMIT 1"
	err := d.db.Get(ctx, &corpID, sql, appID)
	clues.AddTrackE(ctx, "QueryCorpIDByAppID.db.Get", clues.M{"sql": sql, "appID": appID, "corpID": corpID}, err)
	if err != nil {
		log.ErrorContextf(ctx, "Get corpID by appID error: %+v", err)
		return 0, err
	}
	return corpID, nil
}

type m0 map[string]pf.PipelineNode

// QueryPipelineDebugging 查询 pipeline debug 信息
func (d *dao) QueryPipelineDebugging(ctx context.Context, appID uint64, recordID string) ([]pf.PipelineNode, error) {
	var logs []string
	sql := "SELECT content from t_pipeline_detail WHERE app_id = ? and record_id = ?"
	err := d.pipelineDB.Select(ctx, &logs, sql, appID, recordID)
	clues.AddTrackE(ctx, "QueryPipelineDebugging.db.Select",
		clues.M{"sql": sql, "appID": appID, "recordID": recordID, "logs": logs}, err)
	if err != nil {
		log.ErrorContextf(ctx, "QueryPipelineDebugging by appID error: %+v", err)
		return nil, err
	}
	var p0 []pf.PipelineNode
	for i := range logs {
		var m m0
		err = json.Unmarshal([]byte(logs[i]), &m)
		if err != nil {
			clues.AddTrackE(ctx, "QueryPipelineDebugging.Unmarshal", logs[i], err)
			continue
		}
		for k := range m {
			if hasKey(m[k].Key) {
				p0 = append(p0, m[k])
			}
		}
	}
	clues.AddT(ctx, "QueryPipelineDebugging.p0", p0)
	return p0, nil
}

func hasKey(key string) bool {
	keys := config.App().PipelineDebugging.Keys
	for k := range keys {
		if key == keys[k] {
			return true
		}
	}
	return false
}
