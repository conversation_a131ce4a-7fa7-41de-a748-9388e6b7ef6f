package dao

import (
	"context"
	"encoding/hex"
	"fmt"
	"sort"
	"strings"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
	"git.woa.com/ivy/qbot/qbot/chat/internal/utils"
	"git.woa.com/ivy/qbot/qbot/chat/pkg"
)

// GenDigitalHumanSignature 生成签名
func (d *dao) GenDigitalHumanSignature(ctx context.Context, param map[string]string) (*model.DigitalHumanSign, error) {
	cfg := config.App().DigitalHuman
	// 参数校验
	if cfg.AppKey == "" || cfg.AccessToken == "" || cfg.SecretKey == "" {
		log.ErrorContext(ctx, "DigitalHuman appKey or accessToken or secretKey is empty")
		return nil, pkg.ErrInternalServerError
	}
	timestamp := uint64(time.Now().Unix())
	expired := timestamp + config.GetDigitalHumanSignatureTTL()
	// 添加签名参数
	if param == nil {
		param = make(map[string]string)
	}
	param["virtualmanKey"] = cfg.AppKey
	param["token"] = cfg.AccessToken
	param["timeStamp"] = fmt.Sprintf("%v", timestamp)
	param["expired"] = fmt.Sprintf("%v", expired)
	param["platform"] = helper.When(cfg.Platform != "", cfg.Platform, "lke")
	// 生成签名内容
	content := genSignatureContent(param)
	// 加密签名内容
	realKey, err := hex.DecodeString(cfg.SecretKey)
	if err != nil {
		log.ErrorContextf(ctx, "DigitalHuman decode secretKey error: %v, secretKey: %s", err, cfg.SecretKey)
		return nil, pkg.ErrInternalServerError
	}
	signature, err := utils.AESECBEncrypt(content, realKey)
	if err != nil {
		log.ErrorContextf(ctx, "DigitalHuman encrypt error: %v, content: %s", err, content)
		return nil, pkg.ErrInternalServerError
	}
	log.InfoContextf(ctx, "DigitalHuman encrypt content: %s, signature: %s", content, signature)
	// 返回签名对象
	sign := &model.DigitalHumanSign{
		Appkey:    cfg.AppKey,
		Signature: signature,
		Timestamp: timestamp,
		Expired:   expired,
	}
	return sign, nil
}

func genSignatureContent(parameter map[string]string) string {
	// 将参数按字典序排序
	keys := make([]string, 0, len(parameter))
	for k := range parameter {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	// 构造签名字符串
	var signingContent strings.Builder
	for i, k := range keys {
		if i > 0 {
			signingContent.WriteString("&")
		}
		signingContent.WriteString(fmt.Sprintf("%s=%s", k, parameter[k]))
	}
	return signingContent.String()
}
