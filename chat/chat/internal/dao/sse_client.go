package dao

import (
	"context"
	"time"

	"git.code.oa.com/trpc-go/trpc-database/redis"
	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/baicaoyuan/apex/proto/service"
	"git.woa.com/dialogue-platform/common/v3/errors"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
)

// PrefixSseClient SSE-client Redis前缀
const PrefixSseClient = "qbot:chat:sse_client"

// keySseClient  key
func keySseClient(clientID string) string {
	return PrefixSseClient + ":" + clientID
}

// SetSseClient 设置SSE client
func (d *dao) SetSseClient(ctx context.Context, clientID string, flag bool) error {
	_, err := d.redis.Do(ctx, "SET", keySseClient(clientID), flag, "EX", 24*60*60)
	if err != nil {
		log.ErrorContextf(ctx, "SetSseClient error: %+v, clientID: %s", err, clientID)
		return err
	}
	log.InfoContextf(ctx, "SetSseClient success, clientID: %s, flag: %t", clientID, flag)
	return nil
}

// DelSseClient 删除SSE client
func (d *dao) DelSseClient(ctx context.Context, clientID string) error {
	_, err := d.redis.Do(ctx, "DEL", keySseClient(clientID))
	if err != nil {
		log.ErrorContextf(ctx, "DelSseClient error: %+v, clientID: %s", err, clientID)
		return err
	}
	log.InfoContextf(ctx, "DelSseClient success, clientID: %s,", clientID)

	if config.App().SSE.EnableDeferClientClose {
		log.DebugContextf(ctx, "clientID: %s SSE.DeferClientCloseTime: %+v",
			clientID, config.App().SSE.DeferClientCloseTime)
		time.Sleep(config.App().SSE.DeferClientCloseTime)
	}
	for i := 0; i < 3; i++ { // 关闭最多重试 3 次
		if config.App().Apex.UseGrpc {
			err = d.closeApex(ctx, clientID)
		} else {
			_, err = d.wsCli.Close(ctx, &service.CloseReq{ClientId: clientID})
		}
		if err == nil || model.IsClientNotFoundError(err) {
			go func() {
				defer errors.PanicHandler()
				_ = d.DeleteWsClient(trpc.BackgroundContext(), clientID)
			}()
			log.InfoContextf(ctx, "Delete SSE client ok: %s", clientID)
			return nil
		}
		log.ErrorContextf(ctx, "Delete SSE client error: %+v, clientID: %s", err, clientID)
	}
	log.InfoContextf(ctx, "Delete SSE client ok: %s", clientID)
	return err

}

// IsSseClientValid client是否有效
func (d *dao) IsSseClientValid(ctx context.Context, clientID string) bool {
	rsp, err := redis.Bool(d.redis.Do(ctx, "GET", keySseClient(clientID)))
	if err != nil {
		return false
	}
	log.InfoContextf(ctx, "IsSseClientValid success, clientID: %s, flag: %t", clientID, rsp)
	return rsp
}
