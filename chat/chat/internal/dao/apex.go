package dao

import (
	"context"
	"fmt"
	"sync/atomic"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/baicaoyuan/apex/proto/message"
	"git.woa.com/baicaoyuan/apex/proto/service"
	"git.woa.com/dialogue-platform/common/v3/errors"
	"git.woa.com/dialogue-platform/common/v3/utils"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	polarisApi "git.woa.com/polaris/polaris-go/v2/api"
	"git.woa.com/polaris/polaris-go/v2/pkg/model"
	"github.com/google/uuid"
	"go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"
	"google.golang.org/protobuf/types/known/structpb"
)

// sendApex 推消息给apex
func (d *dao) sendApex(ctx context.Context, clientID, event string, payload interface{}) (err error) {
	defer errors.PanicHandler()
	log.InfoContextf(ctx, "send apex: %s", utils.Any2String(payload))
	address, err := d.getTarget(ctx, clientID)
	if err != nil || address == "" {
		address = config.App().Apex.DefaultTarget
	}
	// 建立grpc连接
	conn, err := grpc.Dial(address, grpc.WithInsecure())
	if err != nil {
		log.ErrorContextf(ctx, "grpc.Dial err: %v", err)
		return err
	}
	defer conn.Close()
	md := metadata.MD{}
	otelgrpc.Inject(ctx, &md)
	ctx = metadata.NewOutgoingContext(ctx, md)
	c := service.NewInternalClient(conn)
	e, err := newEvent(clientID, event, payload)
	if err != nil {
		log.ErrorContextf(ctx, "apex emit new event failed: %v", err)
		return err
	}
	_, err = c.Send(ctx, e)
	return err
}

// closeApex 关闭连接
func (d *dao) closeApex(ctx context.Context, clientID string) (err error) {
	defer errors.PanicHandler()
	log.InfoContextf(ctx, "close apex clientID: %s", clientID)
	address, err := d.getTarget(ctx, clientID)
	if err != nil || address == "" {
		address = config.App().Apex.DefaultTarget
	}
	// 建立grpc连接
	conn, err := grpc.Dial(address, grpc.WithInsecure())
	if err != nil {
		log.ErrorContextf(ctx, "grpc.Dial err: %v", err)
		return err
	}
	defer conn.Close()
	md := metadata.MD{}
	otelgrpc.Inject(ctx, &md)
	ctx = metadata.NewOutgoingContext(ctx, md)
	c := service.NewInternalClient(conn)
	_, err = c.Close(ctx, &service.CloseReq{ClientId: clientID})
	return err
}

// newEvent 创建一个普通事件
func newEvent(client, event string, payload interface{}) (*message.Event, error) {
	v, err := structpb.NewValue(payload)
	if err != nil {
		return nil, err
	}
	return &message.Event{
		MessageId: uuid.New().String(),
		ClientId:  client,
		Type:      event,
		Payload:   v,
	}, nil
}

func (d *dao) getTarget(ctx context.Context, clientID string) (string, error) {
	var flowID uint64
	getInstancesReq := &polarisApi.GetOneInstanceRequest{}
	getInstancesReq.FlowID = atomic.AddUint64(&flowID, 1)
	getInstancesReq.Namespace = config.App().Apex.Namespace
	getInstancesReq.Service = config.App().Apex.Service
	getInstancesReq.HashKey = []byte(clientID)
	startTime := time.Now()
	getInstResp, err := d.consumerCli.GetOneInstance(getInstancesReq)
	if err != nil {
		log.ErrorContextf(ctx, "GetOneInstance err: %v", err)
		return "", err
	}
	var targetInstance model.Instance
	if len(getInstResp.Instances) != 1 {
		log.ErrorContext(ctx, "instances len not valid: ", len(getInstResp.Instances))
		return "", fmt.Errorf("polaris instances len not valid: %d", len(getInstResp.Instances))
	}
	targetInstance = getInstResp.Instances[0]
	address := targetInstance.GetHost() + ":" + utils.Uint32ToString(targetInstance.GetPort())
	log.InfoContextf(ctx, "get target:%s, cost:%d", address, time.Since(startTime).Milliseconds())
	return address, nil
}
