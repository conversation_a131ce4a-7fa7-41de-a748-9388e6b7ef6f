package dao

import (
	"context"
	"testing"

	"git.code.oa.com/trpc-go/trpc-database/redis"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// setupRedisClient 创建Redis连接用于测试
func setupRedisClient(t *testing.T) redis.Client {
	// 创建Redis客户端代理，用于测试
	redisCli := redis.NewClientProxy("trpc.server.service", client.WithTarget("ip://127.0.0.1:6379"))

	// 测试连接是否可用
	ctx := context.Background()
	_, err := redisCli.Do(ctx, "PING")
	require.NoError(t, err, "Redis connection failed. Please ensure Redis is running on 127.0.0.1:6379")

	return redisCli
}

// cleanupRedisKeys 清理测试用的Redis键
func cleanupRedisKeys(t *testing.T, redisCli redis.Client, keys []string) {
	ctx := context.Background()
	for _, key := range keys {
		_, err := redisCli.Do(ctx, "DEL", key)
		if err != nil {
			log.ErrorContextf(ctx, "Failed to cleanup Redis key %s: %v", key, err)
		}
	}
}

func TestDao_SetRequestInterruptTraceID(t *testing.T) {
	redisCli := setupRedisClient(t)
	d := &dao{
		redis: redisCli,
	}

	tests := []struct {
		name      string
		requestID string
		traceID   string
	}{
		{
			name:      "success case",
			requestID: "test_request_123",
			traceID:   "trace_abc_456",
		},
		{
			name:      "empty requestID",
			requestID: "",
			traceID:   "trace_abc_456",
		},
		{
			name:      "empty traceID",
			requestID: "test_request_123_empty",
			traceID:   "",
		},
		{
			name:      "both empty",
			requestID: "",
			traceID:   "",
		},
	}

	var testKeys []string
	defer func() {
		// 清理测试数据
		cleanupRedisKeys(t, redisCli, testKeys)
	}()

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 记录要清理的key
			testKey := keyRequestInterruptTraceID(tt.requestID)
			testKeys = append(testKeys, testKey)

			ctx := context.Background()
			// SetRequestInterruptTraceID 没有返回值
			d.SetRequestInterruptTraceID(ctx, tt.requestID, tt.traceID)

			// 验证Redis中的数据
			value, err := redisCli.Do(ctx, "GET", testKey)
			assert.NoError(t, err)
			if value != nil {
				assert.Equal(t, tt.traceID, string(value.([]byte)))
			} else if tt.traceID != "" {
				t.Errorf("Expected value %s but got nil", tt.traceID)
			}
		})
	}
}

func TestDao_GetRequestInterruptTraceID(t *testing.T) {
	redisCli := setupRedisClient(t)
	d := &dao{
		redis: redisCli,
	}

	tests := []struct {
		name      string
		requestID string
		setupData func(string) string // 返回设置的key用于清理
		expected  string
	}{
		{
			name:      "success case with existing data",
			requestID: "test_request_123",
			setupData: func(requestID string) string {
				key := keyRequestInterruptTraceID(requestID)
				ctx := context.Background()
				_, err := redisCli.Do(ctx, "SET", key, "trace_abc_456", "EX", 60)
				require.NoError(t, err)
				return key
			},
			expected: "trace_abc_456",
		},
		{
			name:      "request not exists",
			requestID: "nonexistent_request_456",
			setupData: func(requestID string) string {
				// 不设置任何数据
				return keyRequestInterruptTraceID(requestID)
			},
			expected: "",
		},
		{
			name:      "empty requestID",
			requestID: "",
			setupData: func(requestID string) string {
				key := keyRequestInterruptTraceID(requestID)
				ctx := context.Background()
				_, err := redisCli.Do(ctx, "SET", key, "some_trace_id", "EX", 60)
				require.NoError(t, err)
				return key
			},
			expected: "some_trace_id",
		},
		{
			name:      "empty trace value",
			requestID: "test_request_empty_789",
			setupData: func(requestID string) string {
				key := keyRequestInterruptTraceID(requestID)
				ctx := context.Background()
				_, err := redisCli.Do(ctx, "SET", key, "", "EX", 60)
				require.NoError(t, err)
				return key
			},
			expected: "",
		},
	}

	var testKeys []string
	defer func() {
		// 清理测试数据
		cleanupRedisKeys(t, redisCli, testKeys)
	}()

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 设置测试数据并记录key
			testKey := tt.setupData(tt.requestID)
			testKeys = append(testKeys, testKey)

			ctx := context.Background()
			// GetRequestInterruptTraceID 只返回string，没有error
			result := d.GetRequestInterruptTraceID(ctx, tt.requestID)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestDao_RequestInterruptTraceID_Integration(t *testing.T) {
	redisCli := setupRedisClient(t)
	d := &dao{
		redis: redisCli,
	}

	ctx := context.Background()
	requestID := "integration_test_request_999"
	traceID := "integration_trace_id_12345"

	// 记录要清理的key
	testKey := keyRequestInterruptTraceID(requestID)
	defer cleanupRedisKeys(t, redisCli, []string{testKey})

	// 测试设置和获取的完整流程
	t.Run("set and get flow", func(t *testing.T) {
		// 先获取不存在的数据
		result := d.GetRequestInterruptTraceID(ctx, requestID)
		assert.Equal(t, "", result)

		// 设置数据
		d.SetRequestInterruptTraceID(ctx, requestID, traceID)

		// 获取设置的数据
		result = d.GetRequestInterruptTraceID(ctx, requestID)
		assert.Equal(t, traceID, result)

		// 覆盖设置新的数据
		newTraceID := "new_trace_id_67890"
		d.SetRequestInterruptTraceID(ctx, requestID, newTraceID)

		// 获取新的数据
		result = d.GetRequestInterruptTraceID(ctx, requestID)
		assert.Equal(t, newTraceID, result)
	})
}

func Test_keyRequestInterruptTraceID(t *testing.T) {
	tests := []struct {
		name      string
		requestID string
		expected  string
	}{
		{
			name:      "normal requestID",
			requestID: "request_123",
			expected:  "qbot:chat:agent:status:request_interrupt_trace_id:request_123",
		},
		{
			name:      "empty requestID",
			requestID: "",
			expected:  "qbot:chat:agent:status:request_interrupt_trace_id:",
		},
		{
			name:      "requestID with special characters",
			requestID: "request:123_abc-def",
			expected:  "qbot:chat:agent:status:request_interrupt_trace_id:request:123_abc-def",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := keyRequestInterruptTraceID(tt.requestID)
			assert.Equal(t, tt.expected, result)
		})
	}
}
