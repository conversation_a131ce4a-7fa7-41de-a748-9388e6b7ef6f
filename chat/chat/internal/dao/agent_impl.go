package dao

import (
	"context"
	"errors"
	"fmt"
	"io"
	"strings"
	"time"

	"git.code.oa.com/trpc-go/trpc-database/redis"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/agent_config_server"
	codeRun "git.woa.com/dialogue-platform/lke_proto/pb-protocol/code_interpreter_dispatcher"
	plugin "git.woa.com/dialogue-platform/lke_proto/pb-protocol/plugin_config_server"
	pluginRun "git.woa.com/dialogue-platform/lke_proto/pb-protocol/plugin_exec_server"
	resourceSchedule "git.woa.com/dialogue-platform/lke_proto/pb-protocol/resource_schedule_server"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
	"git.woa.com/ivy/qbot/qbot/chat/pkg"
	json "github.com/json-iterator/go"
)

// PrefixAgentStatus agent status Redis前缀
const PrefixAgentStatus = "qbot:chat:agent:status"

// keyAgentStatus agent status key
func keyAgentStatus(botBizID, sessionID string) string {
	return PrefixAgentStatus + ":" + botBizID + ":" + sessionID
}

// keyRequestInterruptTraceID request interrupt trace id key
func keyRequestInterruptTraceID(requestID string) string {
	return PrefixAgentStatus + ":request_interrupt_trace_id:" + requestID
}

// ListAppTools 批量获取工具详情：描述、参数这些
func (d *dao) ListAppTools(ctx context.Context, req *plugin.ListAppToolsInfoReq) []*plugin.AppToolInfo {
	tik := time.Now()
	defer func() {
		log.InfoContextf(ctx, "ListTools cost: %s", time.Since(tik))
	}()
	log.InfoContextf(ctx, "ListTools req: %s", helper.Object2String(req))
	if len(req.AppBizId) == 0 {
		return make([]*plugin.AppToolInfo, 0)
	}

	appTools, err := d.pluginCli.ListAppToolsInfo(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "ListAppTools error: %v", err)
		return make([]*plugin.AppToolInfo, 0) // 调用上游忽略错误
	}
	log.InfoContextf(ctx, "ListTools result: %s", helper.Object2String(appTools.GetTools()))
	return appTools.GetTools()
}

// ListTools 批量获取工具详情：描述、参数这些
// func (d *dao) ListTools(ctx context.Context, req *plugin.ListToolsReq) []*plugin.ToolInfo {
//	tik := time.Now()
//	defer func() {
//		log.InfoContextf(ctx, "ListTools cost: %s", time.Since(tik))
//	}()
//	log.InfoContextf(ctx, "ListTools req: %s", helper.Object2String(req))
//	if len(req.ToolIds) == 0 {
//		return make([]*plugin.ToolInfo, 0)
//	}
//
//	tools, err := d.pluginCli.ListTools(ctx, req) // todo: 接口调整，改为从配置端获取 工具id和插件id打平
//	if err != nil {
//		log.ErrorContextf(ctx, "ListTools error: %v", err)
//		return make([]*plugin.ToolInfo, 0) // 调用上游忽略错误
//	}
//	log.InfoContextf(ctx, "ListTools result: %s", helper.Object2String(tools.GetTools()))
//	return tools.GetTools()

// ListPlugins 批量获取插件详情：主要用于获取icon。
func (d *dao) ListPlugins(ctx context.Context, req *plugin.ListPluginsReq) []*plugin.PluginInfo {
	tik := time.Now()
	defer func() {
		log.InfoContextf(ctx, "ListPlugins cost: %s", time.Since(tik))
	}()
	log.InfoContextf(ctx, "ListPlugins req: %s", helper.Object2String(req))
	if len(req.PluginIds) == 0 {
		return make([]*plugin.PluginInfo, 0)
	}

	plugins, err := d.pluginCli.ListPlugins(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "ListPlugins error: %v", err)
		return make([]*plugin.PluginInfo, 0) // 调用上游忽略错误
	}
	log.InfoContextf(ctx, "ListPlugins result: %s", helper.Object2String(plugins.GetPlugins()))
	return plugins.GetPlugins()
}

// RunTool 运行工具
func (d *dao) RunTool(ctx context.Context, req *pluginRun.RunToolReq) (rsp *pluginRun.RunToolRsp, err error) {
	tik := time.Now()
	defer func() {
		log.InfoContextf(ctx, "RunTool cost: %s", time.Since(tik))
	}()
	log.InfoContextf(ctx, "RunTool req: %s", helper.Object2StringEscapeHTML(req))
	rsp, err = d.pluginRunCli.RunTool(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "RunTool error: %v", err)
		return nil, err
	}
	log.InfoContextf(ctx, "RunTool result: %s", helper.Object2StringEscapeHTML(rsp))
	return rsp, nil
}

// RunStreamTool 执行流式工具调用，负责客户端创建、初始请求发送及流关闭
func (d *dao) RunStreamTool(ctx context.Context, req *pluginRun.StreamRunToolReq,
	ch chan *pluginRun.StreamRunToolRsp, signal chan int) (err error) {
	var last *pluginRun.StreamRunToolRsp
	defer close(ch)
	// 若最后一条消息未设置为最终状态，则补充推送一条最终响应
	defer func() {
		if last != nil && !last.IsFinal {
			last.IsFinal = true
			ch <- last
		}
	}()

	tik := time.Now()
	log.InfoContextf(ctx, "RunStreamTool req: %s", helper.Object2String(req))

	// 创建流式客户端
	cli, err := d.pluginRunCli.StreamRunTool(ctx)
	if err != nil {
		log.ErrorContextf(ctx, "New StreamRunTool client error: %+v", err)
		return err
	}
	// 关闭发送流
	defer func() {
		if err := cli.CloseSend(); err != nil {
			log.ErrorContextf(ctx, "CloseSend error: %+v", err)
		}
		log.InfoContextf(ctx, "CloseSend done. Total cost: %s", time.Since(tik))
	}()

	// 发送初始请求
	if err = cli.Send(req); err != nil {
		log.ErrorContextf(ctx, "Send StreamRunTool request error: %+v, req: %s", err, helper.Object2String(req))
		return err
	}

	// 调用独立的循环处理函数
	last, err = d.runStreamToolLoop(ctx, req, cli, ch, signal, tik)
	return err
}

// runStreamToolLoop 独立处理流式接收逻辑
func (d *dao) runStreamToolLoop(ctx context.Context, req *pluginRun.StreamRunToolReq,
	cli pluginRun.PluginExec_StreamRunToolClient, ch chan *pluginRun.StreamRunToolRsp,
	signal chan int, tik time.Time) (last *pluginRun.StreamRunToolRsp, err error) {
	idx, logSpeed := 0, 0
	timeout := time.NewTimer(time.Duration(config.GetAgentGlobalTimeout()) * time.Second)
	for {
		select {
		case <-timeout.C:
			// 超时退出
			req.Type = pluginRun.StreamRunToolReq_CANCEL
			_ = cli.Send(req)
			log.WarnContextf(ctx, "RunStreamTool timeout: %+v, req: %s", err, helper.Object2String(req))
			return last, errors.New("RunStreamTool timeout")
		case <-ctx.Done():
			// 上下文取消时发送停止请求
			req.Type = pluginRun.StreamRunToolReq_CANCEL
			_ = cli.Send(req)
			log.WarnContextf(ctx, "RunStreamTool send cancel: %+v, req: %s", err, helper.Object2String(req))
			return last, nil
		case <-signal:
			log.InfoContextf(ctx, "RunStreamTool receive signal: %+v", signal)
			close(signal)
			req.Type = pluginRun.StreamRunToolReq_CANCEL
			err = cli.Send(req)
			log.WarnContextf(ctx, "RunStreamTool send cancel: %+v, req: %s", err, helper.Object2String(req))
			return last, nil
		default:
			last, err = cli.Recv()
			// 定期打印日志
			if idx%(10*logOutput[logSpeed]) == 0 || (last != nil && last.IsFinal) {
				log.InfoContextf(ctx, "RunStreamTool[%d] rsp: %s, cost: %s",
					idx, helper.Object2String(last), time.Since(tik))
				if logSpeed < len(logOutput)-1 {
					logSpeed++
				}
			}
			// 正常响应则推送至通道
			if err == nil && last != nil && last.Code == 0 {
				ch <- last
			}
			// 收到最终响应或遇到 EOF，则结束流
			if (last != nil && last.GetIsFinal()) || err == io.EOF {
				if last != nil && last.GetCode() != 0 {
					return last, errors.New("RunStreamTool run error")
				}
				return last, nil
			}
			// 错误处理：若因上下文取消，则发送停止请求后退出
			if err != nil {
				if errors.Is(err, context.Canceled) || strings.Contains(err.Error(), "context canceled") {
					req.Type = pluginRun.StreamRunToolReq_CANCEL
					_ = cli.Send(req)
					log.WarnContextf(ctx, "RunStreamTool send cancel: %+v, req: %s", err, helper.Object2String(req))
					return last, nil
				}
				log.ErrorContextf(ctx, "RunStreamTool error: %+v, req: %s", err, helper.Object2String(req))
				return last, err
			}
			idx++
		}
	}
}

// GetAgentWorkflowInfo 获取Agent工作流信息
func (d *dao) GetAgentWorkflowInfo(ctx context.Context,
	req *KEP_WF.GetAgentWorkflowInfoReq) []*KEP_WF.GetAgentWorkflowInfoRsp_AgentWorkflowInfo {
	tik := time.Now()
	defer func() {
		log.InfoContextf(ctx, "GetAgentWorkflowInfo cost: %s", time.Since(tik))
	}()
	log.InfoContextf(ctx, "GetAgentWorkflowInfo req: %s", helper.Object2String(req))
	agentWorkflowInfo, err := d.configCli.GetAgentWorkflowInfo(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "GetAgentWorkflowInfo error: %v", err)
		return nil
	}
	log.InfoContextf(ctx, "GetAgentWorkflowInfo result: %s", helper.Object2String(agentWorkflowInfo))
	return agentWorkflowInfo.GetAgentWorkflowInfoList()
}

// SetAgentStatus 设置Agent状态，model.AgentStatus是一个结构体
func (d *dao) SetAgentStatus(ctx context.Context, botBizID, sessionID string, status *model.AgentStatus) error {
	b, _ := json.Marshal(status)
	_, err := d.redis.Do(ctx, "SET", keyAgentStatus(botBizID, sessionID), b, "EX", 24*60*60)
	if err != nil {
		log.ErrorContextf(ctx, "SetAgentStatus error: %+v, botBizID: %s, sessionID: %s",
			err, botBizID, sessionID)
		return err
	}
	log.InfoContextf(ctx, "SetAgentStatus success, botBizID: %s, sessionID: %s, status: %s",
		botBizID, sessionID, string(b))
	return nil
}

// GetAgentStatus 获取Agent状态，model.AgentStatus是一个结构体
func (d *dao) GetAgentStatus(ctx context.Context, botBizID, sessionID string) (*model.AgentStatus, error) {
	b, err := redis.Bytes(d.redis.Do(ctx, "GET", keyAgentStatus(botBizID, sessionID)))
	// 如果是redis.ErrNil，说明没有找到对应的key
	if errors.Is(err, redis.ErrNil) {
		return nil, pkg.ErrRedisNotFound
	}
	if err != nil {
		log.ErrorContextf(ctx, "GetAgentStatus error: %+v, botBizID: %s, sessionID: %s",
			err, botBizID, sessionID)
		return nil, err
	}
	status := &model.AgentStatus{}
	err = json.Unmarshal(b, status)
	if err != nil {
		log.ErrorContextf(ctx, "GetAgentStatus Unmarshal error: %+v, botBizID: %s, sessionID: %s",
			err, botBizID, sessionID)
		return nil, err
	}
	log.InfoContextf(ctx, "GetAgentStatus success, botBizID: %s, sessionID: %s, status: %s",
		botBizID, sessionID, string(b))
	return status, nil
}

// ClearAgentStatus 清除Agent状态
func (d *dao) ClearAgentStatus(ctx context.Context, botBizID, sessionID string) error {
	_, err := d.redis.Do(ctx, "DEL", keyAgentStatus(botBizID, sessionID))
	if err != nil {
		log.ErrorContextf(ctx, "ClearAgentStatus error: %+v, botBizID: %s, sessionID: %s",
			err, botBizID, sessionID)
		return err
	}
	log.InfoContextf(ctx, "ClearAgentStatus success, botBizID: %s, sessionID: %s", botBizID, sessionID)
	return nil
}

// GetVarList 获取变量列表
func (d *dao) GetVarList(ctx context.Context, req *KEP.GetVarListReq) []*KEP.GetVarListRsp_Var {
	tik := time.Now()
	defer func() {
		log.InfoContextf(ctx, "GetVarList cost: %s", time.Since(tik))
	}()

	log.InfoContextf(ctx, "GetVarList req: %s", helper.Object2String(req))
	rsp, err := d.configCli.GetVarList(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "GetVarList error: %v", err)
		return nil
	}
	log.InfoContextf(ctx, "GetVarList result: %s", helper.Object2String(rsp))
	return rsp.List
}

// GetSystemVarList 	获取系统变量列表
func (d *dao) GetSystemVarList(ctx context.Context, req *KEP.GetSystemVarListReq) []*KEP.SystemVar {
	tik := time.Now()
	defer func() {
		log.InfoContextf(ctx, "GetSystemVarList cost: %s", time.Since(tik))
	}()
	log.InfoContextf(ctx, "GetSystemVarList req: %s", helper.Object2String(req))
	rsp, err := d.configCli.GetSystemVarList(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "GetSystemVarList error: %v", err)
		return nil
	}
	log.InfoContextf(ctx, "GetSystemVarList result: %s", helper.Object2String(rsp))
	return rsp.List
}

// RunCode 运行代码
func (d *dao) RunCode(ctx context.Context, req *codeRun.ExecuteReq) (*codeRun.ExecuteRsp, error) {
	tik := time.Now()
	defer func() {
		log.InfoContextf(ctx, "RunCode cost: %s", time.Since(tik))
	}()
	log.InfoContextf(ctx, "RunCode req: %s", helper.Object2String(req))
	rsp, err := d.codeExcCli.Execute(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "RunCode error: %v", err)
		return nil, err
	}
	log.InfoContextf(ctx, "RunCode result: %s", helper.Object2String(rsp))
	return rsp, nil

}

// CodeRelease 释放资源
func (d *dao) CodeRelease(ctx context.Context, sessionID string) {
	tik := time.Now()
	defer func() {
		log.InfoContextf(ctx, "CodeRelease cost: %s", time.Since(tik))
	}()

	req := &codeRun.ReleaseReq{
		SessionID: sessionID,
	}

	log.InfoContextf(ctx, "CodeRelease req: %s", helper.Object2String(req))
	rsp, err := d.codeExcCli.Release(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "CodeRelease error: %v", err)
		return
	}
	log.InfoContextf(ctx, "CodeRelease result: %s", helper.Object2String(rsp))
}

// keyAgentInterrupt interrupt agent status key
func keyAgentInterrupt(botBizID, sessionID string) string {
	return PrefixAgentStatus + ":interrupt:" + botBizID + ":" + sessionID
}

// SetSessionInterrupt 设置对话为中断状态
func (d *dao) SetSessionInterrupt(ctx context.Context, botBizID, sessionID string,
	status *model.SessionInterruptStatus) error {
	redisKey := keyAgentInterrupt(botBizID, sessionID)
	timeoutHour := config.GetInterruptTimeoutHour()
	if _, err := d.redis.Do(ctx, "SET", redisKey, helper.Object2String(status),
		"EX", timeoutHour*60*60); err != nil {
		err = fmt.Errorf("SetSessionInterrupt redis set error: %v", err)
		log.ErrorContextf(ctx, "%v", err)
		return err
	}
	return nil
}

// SetSessionUnInterrupt 取消对话临时中断状态
func (d *dao) SetSessionUnInterrupt(ctx context.Context, botBizID, sessionID string) error {
	redisKey := keyAgentInterrupt(botBizID, sessionID)
	if _, err := d.redis.Do(ctx, "DEL", redisKey); err != nil {
		err = fmt.Errorf("SetSessionUnInterrupt redis del error: %v", err)
		log.ErrorContextf(ctx, "%v", err)
		return err
	}
	return nil
}

// GetSessionInterruptStatus 获取对话的临时中断状态
func (d *dao) GetSessionInterruptStatus(ctx context.Context, botBizID, sessionID string) (
	status *model.SessionInterruptStatus, err error) {
	redisKey := keyAgentInterrupt(botBizID, sessionID)
	value, err := d.redis.Do(ctx, "GET", redisKey)
	if err != nil {
		err = fmt.Errorf("GetSessionInterruptStatus redis get error: %v", err)
		log.ErrorContextf(ctx, "%v", err)
		return nil, err
	}
	if value == nil {
		// 没有 key，正常, status 为 nil, 不返回 error
		return nil, nil
	}
	if bys, err := redis.Bytes(value, err); err != nil {
		err = fmt.Errorf("redis.Bytes error: %v", err)
		log.ErrorContextf(ctx, "%v", err)
		return nil, err
	} else {
		if len(bys) == 0 {
			return nil, nil
		}
		status = &model.SessionInterruptStatus{}
		if err := json.Unmarshal(bys, status); err != nil {
			log.ErrorContextf(ctx, "json Unmarshal error: %v, data: %v", err, string(bys))
			return nil, fmt.Errorf("GetSessionInterruptStatus json Unmarshal error: %v", err)
		}
		return status, nil
	}
}

// DescribeTool 获取工具详情
func (d *dao) DescribeTool(ctx context.Context, pluginID, toolID string) (*plugin.DescribeToolRsp, error) {
	tik := time.Now()
	defer func() {
		log.InfoContextf(ctx, "DescribeTool cost: %s", time.Since(tik))
	}()

	req := &plugin.DescribeToolReq{
		PluginId: pluginID,
		ToolId:   toolID,
	}

	log.InfoContextf(ctx, "DescribeTool req: %s", helper.Object2String(req))
	rsp, err := d.pluginCli.DescribeTool(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "DescribeTool error: %v", err)
		return nil, err
	}
	log.InfoContextf(ctx, "DescribeTool result: %s", helper.Object2String(rsp))
	return rsp, nil
}

func (d *dao) DescribeAppAgentList(
	ctx context.Context, appBizID string, env agent_config_server.EnvType) (
	*agent_config_server.DescribeAppAgentListRsp, error) {
	tik := time.Now()
	defer func() {
		log.InfoContextf(ctx, "DescribeAppAgentList cost: %s", time.Since(tik))
	}()

	req := &agent_config_server.DescribeAppAgentListReq{
		AppBizId: appBizID,
		EnvType:  env,
	}

	log.InfoContextf(ctx, "DescribeAppAgentList req: %s", helper.Object2String(req))
	rsp, err := d.agentConfigCli.DescribeAppAgentList(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "DescribeAppAgentList error: %v", err)
		return nil, err
	}
	log.InfoContextf(ctx, "DescribeAppAgentList result: %s", helper.Object2String(rsp))
	return rsp, nil
}

// ReleasePod 释放 pod
func (d *dao) ReleasePod(ctx context.Context, uin, appBizID, sessionID string) error {
	tik := time.Now()
	defer func() {
		log.InfoContextf(ctx, "ReleasePod cost: %s", time.Since(tik))
	}()

	req := &resourceSchedule.CancelPodReq{
		Uin:       uin,
		AppBizId:  appBizID,
		SessionId: sessionID,
	}

	log.InfoContextf(ctx, "ReleasePod req: %s", helper.Object2String(req))
	rsp, err := d.resourceSchedule.CancelPod(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "ReleasePod error: %v", err)
		return err
	}
	log.InfoContextf(ctx, "ReleasePod result: %s", helper.Object2String(rsp))
	return nil
}

// SetRequestInterruptTraceID 设置对话RequestID中断时的TraceID
func (d *dao) SetRequestInterruptTraceID(ctx context.Context, requestID, traceID string) {
	redisKey := keyRequestInterruptTraceID(requestID)
	if _, err := d.redis.Do(ctx, "SET", redisKey, traceID, "EX", 30*60); err != nil {
		log.ErrorContextf(ctx, "SetRequestInterruptTraceID redis set error: %v", err)
	}
	return
}

// GetRequestInterruptTraceID 获取对话RequestID中断时的TraceID
func (d *dao) GetRequestInterruptTraceID(ctx context.Context, requestID string) string {
	redisKey := keyRequestInterruptTraceID(requestID)
	value, err := d.redis.Do(ctx, "GET", redisKey)
	if err != nil {
		log.ErrorContextf(ctx, "GetRequestInterruptTraceID redis get error: %v", err)
		return ""
	}
	if value == nil {
		return ""
	}
	return string(value.([]byte))
}
