// Package dao 与外界系统做数据交换，如访问 http/cache/mq/database 等
package dao

import (
	"context"
	"net"
	"strings"
	"sync"
	"time"

	"git.code.oa.com/trpc-go/trpc-database/mysql"
	redigo "git.code.oa.com/trpc-go/trpc-database/redis"
	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/baicaoyuan/apex/trpcapex"
	limiter2 "git.woa.com/dialogue-platform/common/v3/limiter"
	"git.woa.com/dialogue-platform/go-comm/pf"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_DM"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF_DM"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/agent_config_server"
	admin "git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_admin_config_server"
	knowledge "git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_knowledge_config_server"
	kpb "git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_knowledge_config_server"
	botllm "git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_llm_server"
	codeRun "git.woa.com/dialogue-platform/lke_proto/pb-protocol/code_interpreter_dispatcher"
	parse "git.woa.com/dialogue-platform/lke_proto/pb-protocol/knowledge"
	plugin "git.woa.com/dialogue-platform/lke_proto/pb-protocol/plugin_config_server"
	pluginRun "git.woa.com/dialogue-platform/lke_proto/pb-protocol/plugin_exec_server"
	resourceSchedule "git.woa.com/dialogue-platform/lke_proto/pb-protocol/resource_schedule_server"
	llmm "git.woa.com/dialogue-platform/proto/pb-stub/llm-manager-server"
	"git.woa.com/ivy/protobuf/trpc-go/qbot/finance/finance"
	pb "git.woa.com/ivy/protobuf/trpc-go/qbot/qbot/chat"
	"git.woa.com/ivy/protobuf/trpc-go/qbot/qbot/infosec"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
	"git.woa.com/ivy/qbot/qbot/chat/pkg/limiter"
	polarisApi "git.woa.com/polaris/polaris-go/v2/api"
	"github.com/bwmarrin/snowflake"
)

var (
	onceNew sync.Once
	handle  *dao
)

// Dao is Dao interface
type Dao interface {
	// Limiter 并发控制器
	// -------------------------------------------------------------------------
	Limiter() limiter.Limiter

	// SaveWsToken TODO
	// -------------------------------------------------------------------------
	// SaveWsToken 保存 WS Token
	SaveWsToken(context.Context, string, *model.Conn) error
	// RenewWsClient 续期 WS 客户端
	RenewWsClient(ctx context.Context, clientID string) error
	// AuthWsClient 认证 WS 客户端
	AuthWsClient(ctx context.Context, token, clientID string) error
	// DeleteWsClient 删除 WS 客户端
	DeleteWsClient(ctx context.Context, clientID string) error
	// EmitWsUser 推送事件给用户
	EmitWsUser(ctx context.Context, cli *model.Conn, userID uint64, e model.WsEvent) error
	// GetWsClient 推送事件给客户端
	// EmitWsClient(ctx context.Context, cli *model.Conn, clientIDs []string, e model.WsEvent) error
	// GetWsClient 获取已认证的 WS 客户端信息
	GetWsClient(ctx context.Context, clientID string) (*model.Conn, error)
	// GetWsClientIDs 获取 UserID 对应的 WS ClientID
	GetWsClientIDs(ctx context.Context, userIDs []uint64, typ model.ConnType) ([]string, error)
	// GetReconnectData 获取重连恢复数据
	GetReconnectData(ctx context.Context, recordID string) (*model.WsMessage, error)
	// SetReconnectTimes 设置重连次数
	SetReconnectTimes(ctx context.Context, recordID string, times int) error
	// GetReconnectTimes 获取重连次数
	GetReconnectTimes(ctx context.Context, recordID string) int
	// CreateMsgRecord 创建消息记录
	// -------------------------------------------------------------------------
	CreateMsgRecord(ctx context.Context, record model.MsgRecord, stat *model.MsgRecordTokenStat) (int64, error)
	// CreateChatPrompt 创建对话prompt
	CreateChatPrompt(ctx context.Context, record model.ChatPrompt)
	// UpdateMsgRecordRef 更新消息记录参考来源
	UpdateMsgRecordRef(ctx context.Context, reocrdID string, refs []model.Reference, botBizID uint64) error
	// UpdateMsgRecordCheckResult 更新消息记录安全检测结果
	UpdateMsgRecordCheckResult(ctx context.Context, recordID string, checkCode, checkType uint32, botBizID uint64) error
	// GetMsgRecord 获取消息记录
	GetMsgRecord(ctx context.Context, p model.GetMsgRecordParam) ([]model.MsgRecord, error)
	// GetMsgRecordByRecordID 根据消息记录 ID 获取消息记录
	GetMsgRecordByRecordID(ctx context.Context, recordID string, botBizID uint64) (*model.MsgRecord, error)
	// GetMsgRecordsByRecordID 根据消息记录 ID 批量获取消息记录
	GetMsgRecordsByRecordID(ctx context.Context, recordIDs []string, botBizID uint64) ([]model.MsgRecord, error)
	// GetMsgRecordIDByRelatedRecordID 根据关联消息记录 ID 获取消息记录 ID
	GetMsgRecordIDByRelatedRecordID(ctx context.Context, relatedRecordID string) (string, error)
	// GetMsgRecordToClean 获取需要清理的消息记录
	GetMsgRecordToClean(ctx context.Context, clearCreateTime time.Time, limit int) ([]string, error)
	// DeleteMsgRecord 删除消息记录【只允许定时任务调用】
	DeleteMsgRecord(ctx context.Context, recordIDs []string) error
	// CreateOrUpdateUserDialogConfig 更新用户应用配置，如果不存在，则创建
	CreateOrUpdateUserDialogConfig(ctx context.Context, req *pb.ModifyUserDialogConfigReq) error
	// GetUserDialogConfig 获取用户会话配置
	GetUserDialogConfig(ctx context.Context, params *model.UserDialogConfig) (model.UserDialogConfig, bool, error)
	// GetLastNBotRecord 获取最后 N 轮机器人回复
	GetLastNBotRecord(context.Context, uint64, *model.Session, uint, []model.RecordType) ([]model.MsgRecord, error)
	// UpdateRewroteContent 更新消息改写内容
	UpdateRewroteContent(ctx context.Context, id uint64, prompt, content string, botBizID uint64) error
	// UpdateIntentPrompt 更新意图Prompt
	UpdateIntentPrompt(ctx context.Context, recordID, prompt, result string)
	// UpdateFaqReject 更新FAQ拒答
	UpdateFaqReject(ctx context.Context, recordID string)
	// UpdateWorkflowPrompt 更新工作流Prompt
	UpdateWorkflowPrompt(ctx context.Context, appID uint64, sessionID, prompt string)
	// UpdateWorkflowResult 更新工作流检索结果
	UpdateWorkflowResult(ctx context.Context, recordID, result string)
	// AddUnsatisfiedReply 增加不满意回复
	AddUnsatisfiedReply(ctx context.Context, req *knowledge.AddUnsatisfiedReplyReq) error
	// Rating 机器人回复评价
	Rating(ctx context.Context, id uint64, score model.ScoreType, reasons []string, botBizID uint64) error
	// CountScore 统计已评价机器人回复
	// CountScore(ctx context.Context, sessionID string, t time.Time, score model.ScoreType) (uint32, error)
	// GetStatisticInfosByBizID 统计token用量信息，临时方案
	// GetStatisticInfosByBizID(ctx context.Context, botBizIDs []uint64) (res []model.MsgStatisticInfo, err error)
	// GetBotRecordWithTimestamp 获取有会话信息的应用
	// GetBotRecordWithTimestamp(ctx context.Context, startTime time.Time) ([]model.MsgRecord, error)

	// GetSession TODO
	// -------------------------------------------------------------------------
	// GetSession 获取会话
	GetSession(ctx context.Context, typ model.SessionType, sessionID string) (*model.Session, error)
	// GetSessionsBySeat 坐席侧获取会话列表
	GetSessionsBySeat(ctx context.Context, seatID uint64) ([]model.Session, error)
	// ResetSession 重置会话
	ResetSession(ctx context.Context, sessionID string, isOnlyEmptyTheDialog bool) (bool, error)
	// StopGeneration 停止消息生成
	StopGeneration(ctx context.Context, userID uint64, recordID string) error
	// IsGenerationStopped 判断消息是否被停止生成
	IsGenerationStopped(ctx context.Context, userID uint64, recordID string) (bool, error)

	// TransferSession 会话转人工
	// TransferSession(ctx context.Context, sessionID string) error
	// UntransferSession 重置会话转人工
	// UntransferSession(ctx context.Context, sessionID string) error

	// RefreshSessionVisitTime 刷新会话访问时间
	RefreshSessionVisitTime(ctx context.Context, id uint64, sessionID string) error
	// CreateSession 创建会话
	CreateSession(ctx context.Context, session *model.Session) error
	// MustGetLastSession 获取最后一次会话（如果没有则创建）
	MustGetLastSession(
		ctx context.Context, typ model.SessionType, visitor, bot, seat model.ID,
	) (*model.Session, bool, error)

	// GetRecommendQuestion 获取推荐问题
	GetRecommendQuestion(ctx context.Context) ([]model.RecommendQuestion, error)
	// GetExperienceRecommendQuestion 获取体验中心推荐问题
	GetExperienceRecommendQuestion(ctx context.Context, robotIDs []uint64) ([]model.ExperienceRecommendQuestion, error)
	// GetCorpStaffByBizID 根据业务 ID 获取企业员工信息
	GetCorpStaffByBizID(ctx context.Context, id uint64) (*model.CorpStaff, error)
	// GetCorpStaffsByBizID 根据业务 ID 批量获取企业员工信息
	GetCorpStaffsByBizID(ctx context.Context, ids []uint64) ([]model.CorpStaff, error)
	// GetUserInfo 获取用户信息
	GetUserInfo(ctx context.Context, fromType model.SourceType, fromID uint64) (*model.UserInfo, error)
	// CheckSession 根据登录态获取用户信息
	CheckSession(ctx context.Context) (*model.CorpStaff, error)
	// MustGetVisitor 获取访客（如果没有则创建）
	MustGetVisitor(ctx context.Context, botID, botBizID uint64, visitorBizID string) (*model.Visitor, error)
	// GetVisitorByID 获取访客
	GetVisitorByID(ctx context.Context, botBizID uint64, id uint64) (*model.Visitor, error)

	// QueryCorpIDByStaffID 按 staff id 查询 corp id
	QueryCorpIDByStaffID(ctx context.Context, staffID uint64) (uint64, error)
	// QueryCorpIDByAppID 按 app id 查询 corp id
	QueryCorpIDByAppID(ctx context.Context, appID uint64) (uint64, error)
	// QueryPipelineDebugging 查询 pipeline debug 信息
	QueryPipelineDebugging(ctx context.Context, appID uint64, recordID string) ([]pf.PipelineNode, error)

	// GetIntent TODO
	// -------------------------------------------------------------------------
	// GetIntent 获取意图
	GetIntent(ctx context.Context, policyID uint32, content string) (*admin.GetIntentRsp, error)
	// GetUinByCorpID 获取Uin
	GetUinByCorpID(ctx context.Context, corpID uint64) (uin uint64, sid int, err error)
	// DescribeAccountBalance 获取账户余额
	DescribeAccountBalance(ctx context.Context, corpID uint64, accountTypeList []model.AccountType) (
		*admin.DescribeAccountBalanceRsp, error)

	// GetSynonymsNER 获取同义词NER
	GetSynonymsNER(ctx context.Context, envType AppScene, robotID uint64, question string) (*kpb.SynonymsNERRsp, error)

	// GetUserCellphoneByCorpStaffBizID TODO
	GetUserCellphoneByCorpStaffBizID(ctx context.Context, corpStaffBizID uint64) string

	ILanguageModel
	IDialogManagement
	IApp
	IMultiModal
	IKnowledgeSearchNew
	IKnowledgeDocParse
	Calculator
	Financer
	ICommon
	INewSseConn
	ITokenStat
	IBotLLM
	IConfigInfo
	IWorkflow
	IInfoCheck
	IModelLimit
	IAgent
	IRTC
	IDigitalHuman
	ICredential
	ITTS
}

// ILanguageModel 大语言模型接口
type ILanguageModel interface {
	// Chat 流式调用 LLM
	Chat(ctx context.Context, req *llmm.Request, ch chan *llmm.Response, startTime time.Time, signal chan int) error
	// SimpleChat 非流式调用 LLM
	SimpleChat(ctx context.Context, req *llmm.Request) (*llmm.Response, error)
	// GetModelPromptLimit 获取模型Prompt长度限制
	GetModelPromptLimit(ctx context.Context, modelName string) int
}

// IDialogManagement 对话管理接口
type IDialogManagement interface {
	// GetSemantic 调用 DM 服务, 获取语义结果
	GetSemantic(ctx context.Context, req *KEP_DM.GetSemanticRequest, ch chan *KEP_DM.GetSemanticReply) (err error)
	// ClearSession 清空 botsession 关联
	ClearSession(ctx context.Context, req *KEP_DM.ClearSessionRequest) error
	// IsTaskRelease 是否任务流发布
	IsTaskRelease(ctx context.Context, botBizID uint64, env uint32) (bool, error)
	// SetTaskFlowStatus 设置任务流状态
	SetTaskFlowStatus(ctx context.Context, sessionID string, flag bool) error
	// IsInTaskFlow 是否在任务流中
	IsInTaskFlow(ctx context.Context, sessionID string) bool
	// SetWhiteList 设置白名单
	SetWhiteList(ctx context.Context, corpID, botBizID uint64, flag int, timeout int) error
	// IsInWhiteList 是否在白名单中
	IsInWhiteList(ctx context.Context, corpID, botBizID uint64) int
	// GetAnswerFromDocs 调用 DM 服务, 获取文档检索答案
	// GetAnswerFromDocs(ctx context.Context, req *KEP_DM.GetAnswerFromDocsRequest,
	//	ch chan *KEP_DM.GetAnswerFromDocsReply, signal chan int) (err error)
}

// IApp app接口 AppTestScence = 1 测试场 AppReleaseScence = 2 正式场
type IApp interface {
	// GetAppByAppKey 根据 AppKey 获取APP
	GetAppByAppKey(ctx context.Context, scene AppScene, appKey string) (*model.App, error)
	// GetAppByBizID 根据业务 ID 获取App
	GetAppByBizID(ctx context.Context, scene AppScene, id uint64) (*model.App, error)
	// GetAppsByBizID 根据业务 ID 批量获取App
	GetAppsByBizID(ctx context.Context, scene AppScene, ids []uint64) ([]*model.AppListInfo, error)
	// GetRobotIDByShareCode 获取机器人/应用ID
	GetRobotIDByShareCode(ctx context.Context, shareCode string) (uint64, error)
	// GetCorp 获取企业信息
	GetCorp(ctx context.Context, requestID string, corpID uint64) (*admin.GetCorpRsp, error)
	// GetAppChatInputLimit 通过App信息获取对话输入字符限制
	GetAppChatInputLimit(ctx context.Context, req *admin.GetAppChatInputNumReq) int32
	// GetModelFinanceInfo 获取模型计费信息
	GetModelFinanceInfo(ctx context.Context, mainModel string) *admin.GetModelFinanceInfoRsp_ModelFinance
}

// IMultiModal 多模态相关
type IMultiModal interface {
	// SetMultiModalStatus 设置在多模态中状态标记 一天过期
	SetMultiModalStatus(ctx context.Context, sessionID string, flag bool) error
	// SetMultiModalCount 设置多模态次数
	SetMultiModalCount(ctx context.Context, sessionID string) error
	// GetMultiModalCount 获取多模态次数
	GetMultiModalCount(ctx context.Context, sessionID string) (int, error)
	// IsInMultiModal 是否在多模态中
	IsInMultiModal(ctx context.Context, sessionID string, historyLimit uint32) bool
	// SetMultiModalHistory 设置多模态历史记录
	SetMultiModalHistory(ctx context.Context, sessionID string, history string) error
	// GetMultiModalHistory 获取多模态历史记录
	GetMultiModalHistory(ctx context.Context, sessionID string) (string, error)
}

// IKnowledgeDocParse 文档解析相关接口
type IKnowledgeDocParse interface {
	// CreateMsgDocRecord 创建消息文档记录
	CreateMsgDocRecord(ctx context.Context, record model.MsgDocRecord) (int64, error)
	// UpdateDocInfo 更新Doc信息
	UpdateDocInfo(ctx context.Context, docID, botBizID, cosID uint64, summary string) error
	// GetDistinctBotBizID 获取不同的机器人业务ID
	GetDistinctBotBizID(ctx context.Context) ([]uint64, error)
	// GetSessionID 获取会话ID
	GetSessionID(ctx context.Context, botBizID uint64) ([]string, error)
	// GetDocIDBySessionID 获取文档ID
	GetDocIDBySessionID(ctx context.Context, botBizID uint64, sessionID string) ([]uint64, error)

	// UpdateDeleteStatus 更新删除状态
	UpdateDeleteStatus(ctx context.Context, botBizID uint64, sessionID string) error
	// StreamSaveDoc 流式保存文档
	StreamSaveDoc(ctx context.Context, req *parse.StreamSaveDocReq,
		ch chan *parse.StreamSaveDocRsp, signal chan int) (err error)
	// GetDocFullText 获取文档全文
	GetDocFullText(ctx context.Context, req *parse.GetDocFullTextReq) (rsp *parse.GetDocFullTextRsp, err error)
	// SearchRealtime 检索实时库
	SearchRealtime(ctx context.Context, req *parse.SearchRealtimeReq) (docs []*parse.SearchRealtimeRsp_Doc, err error)
	// DeleteRealtimeDoc 删除实时库文档
	DeleteRealtimeDoc(ctx context.Context, req *parse.DeleteRealtimeDocReq) (rsp *parse.DeleteRealtimeDocRsp, err error)

	// GetDocSummary 获取文档摘要
	GetDocSummary(ctx context.Context, req *parse.GetDocSummaryReq, chReply chan *parse.GetDocSummaryRsp) (err error)

	// SetFileKnowledgeStatus 设置在文件知识问答中状态标记 一天过期（todo 也可以查聊天记录 重新进入该状态）
	SetFileKnowledgeStatus(ctx context.Context, sessionID string, flag bool) error
	// SetFileKnowledgeCount 设置文件知识问答次数
	SetFileKnowledgeCount(ctx context.Context, sessionID string) error
	// GetFileKnowledgeCount 获取文件知识问答次数
	GetFileKnowledgeCount(ctx context.Context, sessionID string) (int, error)
	// IsInFileKnowledge 是否在文件知识问答中
	IsInFileKnowledge(ctx context.Context, sessionID string, historyLimit uint32) bool
	// SetRealTimeFileHistory 设置实时文档历史记录
	SetRealTimeFileHistory(ctx context.Context, sessionID string, history string) error
	// GetRealTimeFileHistory 获取实时文档历史记录
	GetRealTimeFileHistory(ctx context.Context, sessionID string) (string, error)
	// GetMsgDocRecord 获取文档解析记录
	GetMsgDocRecord(ctx context.Context, docIDs []uint64, modelName string) ([]*model.MsgDocRecord, error)
	// GetMsgDocSummary 获取文档摘要
	GetMsgDocSummary(ctx context.Context, docIDs []uint64) (map[string]string, error)
	// DescribeAttributeLabel 获取标签信息
	DescribeAttributeLabel(ctx context.Context, appID string, attrBizIds []uint64,
	) (map[uint64]*kpb.GetAttributeInfoRsp_AttrLabelInfo, error)
	// InnerDescribeDocs 批量获取文档详情（内部接口）
	InnerDescribeDocs(ctx context.Context, req *kpb.InnerDescribeDocsReq) (
		*kpb.InnerDescribeDocsRsp, error)
}

// Calculator 计算器
type Calculator interface {
	CalcCall(ctx context.Context, text string) (string, error)
}

// Financer 计费相关接口
type Financer interface {
	// DescribeAccountStatus 获取账户状态
	// DescribeAccountStatus(ctx context.Context, req *finance.DescribeAccountStatusReq)
	// (*finance.DescribeAccountStatusRsp, error)

	// GetModelStatus 获取模型状态, 账户状态 0可用, 1不可用, -1不可用
	GetModelStatus(ctx context.Context, corpID uint64, modelName string) int

	// GetModelConcurrency 获取模型并发数, 小于0则说明获取异常
	GetModelConcurrency(ctx context.Context, corpID uint64, modelName string) (int, bool)

	// GetTotalConcurrency 获取总并发数
	GetTotalConcurrency(ctx context.Context, appID uint64, modelName string) (int, error)

	// GetModelConcurrencyUseCache 获取模型并发数, 小于0则说明获取异常
	// GetModelConcurrencyUseCache(ctx context.Context, corpID uint64, modelName string) (int, bool)

	// DescribeAccountInfo 获取企业账户信息
	// DescribeAccountInfo(ctx context.Context, req *finance.DescribeAccountInfoReq)
	// (*finance.DescribeAccountInfoRsp, error)

	// GetModelToken 获取模型 token, 返回 用量, 剩余量, -1 表示内部错误
	GetModelToken(ctx context.Context, corpID uint64, modelName string) (int, int)

	// ReportTokenDosage 上报用量, 原始接口
	ReportTokenDosage(ctx context.Context, corpID uint64, req *finance.ReportDosageReq, subBizType string) error
	// ReportTokenDosage2 上报用量, 业务封装
	ReportTokenDosage2(ctx context.Context, corpID uint64, dosage TokenDosage, subBizType string) error
	// ReportSearchEngineDosage 上报搜索引擎，业务封装
	ReportSearchEngineDosage(ctx context.Context, corpID uint64, dosage SearchEngineDosage) error
	// GetSearchEngineStatus 获取搜索引擎资源状态, 账户状态 0可用, 1不可用, -1不可用
	GetSearchEngineStatus(ctx context.Context, corpID uint64) int
	// ReportConcurrencyDosage 上报并发 业务封装
	ReportConcurrencyDosage(ctx context.Context, corpID uint64, dosage ConcurrencyDosage) error
	// ReportOverConcurrencyDosage 上报超并发dosage
	ReportOverConcurrencyDosage(ctx context.Context, app *model.App, dosage OverConcurrencyDosage) error

	// GetRobotKeywords 获取应用关键词信息
	GetRobotKeywords(ctx context.Context, robotID uint64, interval, limit int) ([]*model.RobotKeywords, error)
	// UpdateRobotKeywordsCache 更新应用关键词缓存
	UpdateRobotKeywordsCache(ctx context.Context, keywords []*model.RobotKeywords) error
	// ReplaceRobotKeywords 替换应用关键词
	ReplaceRobotKeywords(ctx context.Context, robotID uint64, content string) string
}

// ICommon 通用接口
type ICommon interface {
	// GenerateSeqID 生成唯一ID
	GenerateSeqID() uint64
	// GetRedis TODO
	// GetRedis() redigo.Client
}

// INewSseConn 新的SSE连接
type INewSseConn interface {
	// SetSseClient 设置 SSE 客户端
	SetSseClient(ctx context.Context, clientID string, flag bool) error
	// DelSseClient 删除 SSE 客户端
	DelSseClient(ctx context.Context, clientID string) error
	// IsSseClientValid 判断 SSE 客户端是否有效
	IsSseClientValid(ctx context.Context, clientID string) bool
	// DoEmitWsClient 推送消息到单个客户端
	DoEmitWsClient(ctx context.Context, clientID string, e model.WsEvent, cancel context.CancelFunc) (err error)
}

// ITokenStat 调试信息接口
type ITokenStat interface {
	// GetMsgRecordTokenStatByStatID 通过ID获取消息记录统计信息
	GetMsgRecordTokenStatByStatID(ctx context.Context, recordID string) (
		*model.MsgRecordTokenStat, error)
	// DeleteMsgRecordTokenStat 删除消息记录统计信息【【只允许定时任务调用】】
	DeleteMsgRecordTokenStat(ctx context.Context, recordIDs []string) error
}

// IBotLLM python服务接口
type IBotLLM interface {
	// TextTruncateCommon 文本截断
	TextTruncateCommon(ctx context.Context, promptTmpl string, req any, limit int) (string, error)
	// TextTruncate 文本截断
	TextTruncate(ctx context.Context, appModel *model.AppModel, req any) (string, error)
	// GetTextTokenLen 获取文本token长度
	GetTextTokenLen(ctx context.Context, text string) int
	// TextTruncateTemp 文本截断 for V2.6.1 临时解决
	TextTruncateTemp(ctx context.Context, appModel *model.AppModel, req any) (string, error)
}

type dao struct {
	once         sync.Once
	db           mysql.Client
	tdsqlDB      mysql.Client
	pipelineDB   mysql.Client
	uniIDNode    *snowflake.Node
	redis        redigo.Client
	limiterRedis redigo.Client
	// goRedis         gorediscli.UniversalClient
	wsCli               trpcapex.Client
	apiCli              admin.ApiClientProxy
	loginCli            admin.LoginClientProxy
	infosecCli          infosec.InfosecClientProxy
	llmmCli             llmm.ChatClientProxy
	limiter             limiter.Limiter
	limiterV2           limiter2.LimiterInterface // 公共库版本，支持tpm和qpm
	dmCli               KEP_DM.BotDmClientProxy
	configCli           KEP.TaskConfigClientProxy
	knowledgeAPICli     knowledge.ApiClientProxy
	knowledgeAPITestCli knowledge.ApiClientProxy
	financeCli          finance.FinanceClientProxy // 计费服务, @stanwei 提供
	botllmCli           botllm.LlmClientProxy
	workflowCli         KEP_WF_DM.WorkflowDmClientProxy
	pluginRunCli        pluginRun.PluginExecClientProxy
	pluginCli           plugin.PluginConfigApiClientProxy
	codeExcCli          codeRun.CodeExecClientProxy
	agentConfigCli      agent_config_server.AgentConfigApiClientProxy
	resourceSchedule    resourceSchedule.ScheduleServerClientProxy
	consumerCli         polarisApi.ConsumerAPI // 北极星
}

// New creates Dao instance
func New() Dao {
	onceNew.Do(func() {
		var err error
		rdb := redigo.NewClientProxy("redis.qbot.qbot")
		limiterRedis := redigo.NewClientProxy("redis.lke.limiter")
		handle = &dao{
			redis:        rdb,
			limiterRedis: limiterRedis,
			// goRedis:    tRedis,
			db:         mysql.NewClientProxy("mysql.qbot.qbot"),
			tdsqlDB:    mysql.NewClientProxy("tdsql.qbot.qbot"),
			pipelineDB: mysql.NewClientProxy("pipeline-flow-detail"),
			uniIDNode:  snowFlakeNode,
			wsCli:      trpcapex.NewClientProxy("apex.router"),
			apiCli:     admin.NewApiClientProxy(),
			loginCli:   admin.NewLoginClientProxy(),
			infosecCli: infosec.NewInfosecClientProxy(),
			llmmCli: llmm.NewChatClientProxy([]client.Option{
				// https://iwiki.woa.com/p/284289117#4-%E5%AE%A2%E6%88%B7%E7%AB%AF%E9%85%8D%E7%BD%AE
				// https://iwiki.woa.com/p/4008319150#b-%E5%85%B3%E9%97%AD%E6%9C%8D%E5%8A%A1%E8%B7%AF%E7%94%B1%EF%BC%9A
				client.WithServiceName("trpc.SmartAssistant.LLMManagerServer.Chat"),
				// client.WithTarget("ip://**************:10001"), // fot test DEV环境写死IP
			}...),
			limiter: limiter.New(rdb),
			dmCli: KEP_DM.NewBotDmClientProxy([]client.Option{
				client.WithServiceName("trpc.KEP.bot-dm-server.bot-dm"),
			}...),
			configCli: KEP.NewTaskConfigClientProxy([]client.Option{
				client.WithServiceName("trpc.KEP.bot-task-config-server.task-config"),
			}...),
			knowledgeAPICli: knowledge.NewApiClientProxy([]client.Option{
				client.WithServiceName("trpc.KEP.bot-knowledge-config-server.Api"),
			}...),
			knowledgeAPITestCli: nil,
			financeCli:          finance.NewFinanceClientProxy(),
			botllmCli:           botllm.NewLlmClientProxy(),
			workflowCli: KEP_WF_DM.NewWorkflowDmClientProxy([]client.Option{
				client.WithServiceName("trpc.KEP.bot-workflow-dm-server.workflow-dm"),
			}...),
			pluginRunCli: pluginRun.NewPluginExecClientProxy([]client.Option{
				client.WithServiceName("trpc.KEP.plugin-exec-server.PluginExec"),
			}...),
			pluginCli: plugin.NewPluginConfigApiClientProxy([]client.Option{
				client.WithServiceName("trpc.KEP.plugin-config-server.PluginConfigApi"),
			}...),
			codeExcCli: codeRun.NewCodeExecClientProxy([]client.Option{
				client.WithServiceName("trpc.KEP.code-interpreter-dispatcher.CodeExec"),
			}...),
			agentConfigCli: agent_config_server.NewAgentConfigApiClientProxy([]client.Option{
				client.WithServiceName("trpc.KEP.agent-config-server.AgentConfigApi"),
			}...),
			resourceSchedule: resourceSchedule.NewScheduleServerClientProxy([]client.Option{
				client.WithServiceName("trpc.KEP.resource-schedule-server.ScheduleServer"),
			}...),
		}
		handle.consumerCli, err = polarisApi.NewConsumerAPI()
		if err != nil {
			log.Errorf("limiterInf.RegisterTimer err:%+v", err)
		}
		defaultFn := func() limiter2.DefaultConfig {
			return handle.GetDefaultQpmTpmLimit(context.Background())
		}
		limiterInf := limiter2.New(limiter2.LimiterConfig{
			LimiterBiz:       limiter2.LimiterBizLke,
			FinanceEnable:    helper.When(config.App().IsPrivate, false, true),
			GetDefaultConifg: defaultFn,
		}, limiter2.NewDataAccessor(handle.limiterRedis, handle.financeCli))
		// 注册定时器处理
		if err := limiterInf.RegisterTimer(); err != nil {
			log.Errorf("limiterInf.RegisterTimer err:%+v", err)
		}
		handle.limiterV2 = limiterInf
	})

	return handle
}

func placeholder(c int) string {
	if c <= 0 {
		log.Errorf("invalid placeholder count: %d", c)
		return ""
	}
	return "?" + strings.Repeat(", ?", c-1)
}

// Limiter 获取并发限制器
func (d *dao) Limiter() limiter.Limiter {
	return d.limiter
}

var snowFlakeNode *snowflake.Node

func init() {
	// "github.com/bwmarrin/snowflake" total 22 bits to share between NodeBits/StepBits
	// 机器码：使用IP二进制的后16位
	snowflake.NodeBits = 16
	// 序列号：6位，每毫秒最多生成64个ID [0,64)
	snowflake.StepBits = 6

	nodeNum := int64(0)

	// 取IP二进制的后16位
	ip := net.ParseIP(getClientIP()).To4()
	if len(ip) == 4 {
		nodeNum = (int64(ip[2]) << 8) + int64(ip[3])
	}

	node, err := snowflake.NewNode(nodeNum)
	log.Infof("GenerateSeqID ip:%s nodeNum:%d NodeBits:%d StepBits:%d",
		ip, nodeNum, snowflake.NodeBits, snowflake.StepBits)
	if err != nil {
		log.Fatalf("GenerateSeqID ip:%s nodeNum:%d NodeBits:%d StepBits:%d err:%+v",
			ip, nodeNum, snowflake.NodeBits, snowflake.StepBits, err)
	}
	snowFlakeNode = node
}

// getClientIP 获取本机IP
func getClientIP() string {
	ip := trpc.GetIP("eth1")
	if len(ip) > 0 {
		return ip
	}
	if addresses, err := net.InterfaceAddrs(); err == nil {
		for _, addr := range addresses {
			if ipNet, ok := addr.(*net.IPNet); ok {
				if !ipNet.IP.IsLoopback() && ipNet.IP.To4() != nil {
					ip = ipNet.IP.To4().String()
					// only ignore 127.0.0.1, return ip
					return ip
				}
			}
		}
	}
	panic("getClientIP failed")
}

// GenerateSeqID 生成唯一ID
func (d *dao) GenerateSeqID() uint64 {
	return uint64(d.uniIDNode.Generate())
}

func (d *dao) GetExperimentKnowledgeCli() knowledge.ApiClientProxy {
	d.once.Do(func() {
		if config.App().ExperimentConfig.KnowledgeServiceName != "" {
			d.knowledgeAPITestCli = knowledge.NewApiClientProxy([]client.Option{
				client.WithServiceName(config.App().ExperimentConfig.KnowledgeServiceName),
				client.WithNamespace(config.App().ExperimentConfig.Namespace),
				client.WithDisableServiceRouter(),
				client.WithCalleeEnvName(config.App().ExperimentConfig.EnvName),
				client.WithCalleeMetadata("ENV-SET", ""),
			}...)
		}
	})
	return d.knowledgeAPITestCli
}
