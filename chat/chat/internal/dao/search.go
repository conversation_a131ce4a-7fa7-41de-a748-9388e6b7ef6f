package dao

import (
	"context"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	kpb "git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_knowledge_config_server"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/knowledge"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
)

// SearchRejectedQuestion 检索拒答问题
func (d *dao) SearchRejectedQuestion(ctx context.Context, r *knowledge.SearchKnowledgeReq_SearchReq,
	scene knowledge.SceneType) ([]*knowledge.SearchKnowledgeRsp_SearchRsp_Doc, error) {
	log.InfoContextf(ctx, "Search rejected question req: %s", helper.Object2String(r))
	tik := time.Now()
	var calleeNamespace string
	defer func() {
		log.InfoContextf(ctx, "SearchRejectedQuestion cost: %s", time.Since(tik))
	}()
	if config.App().Mock.EnableSearch {
		return make([]*knowledge.SearchKnowledgeRsp_SearchRsp_Doc, 0), nil
	}
	if speicalApp, ok := config.App().ExperimentConfig.ReplaceAppMap[r.BotBizId]; ok {
		r.BotBizId = speicalApp.ReplaceAppID
		calleeNamespace = speicalApp.CalleeNamespace
		log.InfoContextf(ctx, "Search rejected replace app: %s", helper.Object2String(speicalApp))
	}
	req := &knowledge.SearchKnowledgeReq{
		KnowledgeType: knowledge.KnowledgeType_REJECTED_QUESTION,
		SceneType:     scene,
		Req:           r,
	}
	cli := d.knowledgeAPICli
	if d.GetExperimentKnowledgeCli() != nil && calleeNamespace == config.App().ExperimentConfig.Namespace {
		cli = d.GetExperimentKnowledgeCli()
	}
	rsp, err := cli.SearchKnowledge(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "Search rejected question error: %+v, req: %+v", err, req)
		return nil, err
	}
	log.InfoContextf(ctx, "Search rejected question rsp: %s", helper.Object2String(rsp))
	return rsp.GetRsp().GetDocs(), nil
}

// SearchGlobalKnowledge 检索全局知识
func (d *dao) SearchGlobalKnowledge(ctx context.Context,
	r *knowledge.SearchKnowledgeReq_SearchReq) ([]*knowledge.SearchKnowledgeRsp_SearchRsp_Doc, error) {
	log.InfoContextf(ctx, "Search global knowledge req: %s", helper.Object2String(r))
	tik := time.Now()
	var calleeNamespace string
	defer func() {
		log.InfoContextf(ctx, "SearchGlobalKnowledge cost: %s", time.Since(tik))
	}()
	if config.App().Mock.EnableSearch {
		return make([]*knowledge.SearchKnowledgeRsp_SearchRsp_Doc, 0), nil
	}
	if speicalApp, ok := config.App().ExperimentConfig.ReplaceAppMap[r.BotBizId]; ok {
		r.BotBizId = speicalApp.ReplaceAppID
		calleeNamespace = speicalApp.CalleeNamespace
		log.InfoContextf(ctx, "Search global replace app: %s", helper.Object2String(speicalApp))
	}

	req := &knowledge.SearchKnowledgeReq{
		KnowledgeType: knowledge.KnowledgeType_GLOBAL_KNOWLEDGE,
		SceneType:     knowledge.SceneType_UNKNOWN_SCENE,
		Req:           r,
	}
	cli := d.knowledgeAPICli
	if d.GetExperimentKnowledgeCli() != nil && calleeNamespace == config.App().ExperimentConfig.Namespace {
		cli = d.GetExperimentKnowledgeCli()
	}
	rsp, err := cli.SearchKnowledge(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "Search global knowledge error: %+v, req: %+v", err, req)
		return nil, err
	}
	log.InfoContextf(ctx, "Search global knowledge rsp: %s", helper.Object2String(rsp))
	return rsp.GetRsp().GetDocs(), nil
}

// SearchKnowledge 检索知识库
func (d *dao) SearchKnowledge(ctx context.Context, r *knowledge.SearchKnowledgeReq_SearchReq,
	scene knowledge.SceneType) ([]*knowledge.SearchKnowledgeRsp_SearchRsp_Doc, error) {
	log.InfoContextf(ctx, "Search knowledge req: %s", helper.Object2String(r))
	tik := time.Now()
	var calleeNamespace string
	defer func() {
		log.InfoContextf(ctx, "SearchKnowledge cost: %s", time.Since(tik))
	}()
	if config.App().Mock.EnableSearch {
		return make([]*knowledge.SearchKnowledgeRsp_SearchRsp_Doc, 0), nil
	}
	if speicalApp, ok := config.App().ExperimentConfig.ReplaceAppMap[r.BotBizId]; ok {
		r.BotBizId = speicalApp.ReplaceAppID
		calleeNamespace = speicalApp.CalleeNamespace
		log.InfoContextf(ctx, "Search knowledge replace app: %s", helper.Object2String(speicalApp))
	}

	req := &knowledge.SearchKnowledgeReq{
		KnowledgeType: knowledge.KnowledgeType_DOC_QA,
		SceneType:     scene,
		Req:           r,
	}
	cli := d.knowledgeAPICli
	if d.GetExperimentKnowledgeCli() != nil && calleeNamespace == config.App().ExperimentConfig.Namespace {
		cli = d.GetExperimentKnowledgeCli()
	}
	rsp, err := cli.SearchKnowledge(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "Search knowledge error: %+v, req: %+v", err, req)
		return nil, err
	}
	log.InfoContextf(ctx, "Search knowledge rsp: %s", helper.Object2String(rsp))
	return rsp.GetRsp().GetDocs(), nil
}

// SearchWorkflow 检索工作流
func (d *dao) SearchWorkflow(ctx context.Context, r *knowledge.SearchKnowledgeReq_SearchReq,
	scene knowledge.SceneType) ([]*knowledge.SearchKnowledgeRsp_SearchRsp_Doc, error) {
	log.InfoContextf(ctx, "Search workflow req: %s", helper.Object2String(r))
	tik := time.Now()
	defer func() {
		log.InfoContextf(ctx, "SearchWorkflow cost: %s", time.Since(tik))
	}()
	if config.App().Mock.EnableSearch {
		return make([]*knowledge.SearchKnowledgeRsp_SearchRsp_Doc, 0), nil
	}

	req := &knowledge.SearchKnowledgeReq{
		KnowledgeType: knowledge.KnowledgeType_WORKFLOW,
		SceneType:     scene,
		Req:           r,
	}
	rsp, err := d.knowledgeAPICli.SearchKnowledge(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "Search workflow error: %+v, req: %+v", err, req)
		return nil, err
	}
	log.InfoContextf(ctx, "Search workflow rsp: %s", helper.Object2String(rsp))
	return rsp.GetRsp().GetDocs(), nil
}

// SearchBatchKnowledge 检索多个知识库
func (d *dao) SearchBatchKnowledge(ctx context.Context,
	req *knowledge.SearchKnowledgeBatchReq) ([]*knowledge.SearchKnowledgeRsp_SearchRsp_Doc, error) {
	log.InfoContextf(ctx, "Search batch knowledge req: %s", helper.Object2String(req))
	tik := time.Now()
	defer func() {
		log.InfoContextf(ctx, "SearchBatchKnowledge cost: %s", time.Since(tik))
	}()
	if config.App().Mock.EnableSearch {
		return make([]*knowledge.SearchKnowledgeRsp_SearchRsp_Doc, 0), nil
	}

	rsp, err := d.knowledgeAPICli.SearchKnowledgeBatch(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "Search batch knowledge error: %+v, req: %+v", err, req)
		return nil, err
	}
	log.InfoContextf(ctx, "Search batch knowledge rsp: %s", helper.Object2String(rsp))
	return rsp.GetRsp().GetDocs(), nil
}

// SearchRealtimeKnowledge 检索实时文档
func (d *dao) SearchRealtimeKnowledge(ctx context.Context, r *knowledge.SearchKnowledgeReq_SearchReq,
	scene knowledge.SceneType) ([]*knowledge.SearchKnowledgeRsp_SearchRsp_Doc, error) {
	log.InfoContextf(ctx, "Search realtime knowledge req: %s", helper.Object2String(r))
	tik := time.Now()
	defer func() {
		log.InfoContextf(ctx, "SearchRealtimeKnowledge cost: %s", time.Since(tik))
	}()
	if config.App().Mock.EnableSearch {
		return make([]*knowledge.SearchKnowledgeRsp_SearchRsp_Doc, 0), nil
	}

	req := &knowledge.SearchKnowledgeReq{
		KnowledgeType: knowledge.KnowledgeType_REALTIME,
		SceneType:     scene,
		Req:           r,
	}
	rsp, err := d.knowledgeAPICli.SearchKnowledge(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "Search realtime knowledge error: %+v, req: %+v", err, req)
		return nil, err
	}
	log.InfoContextf(ctx, "Search realtime knowledge rsp: %s", helper.Object2String(rsp))
	return rsp.GetRsp().GetDocs(), nil
}

// SearchFinancialKnowledge 检索金融行业FAQ
func (d *dao) SearchFinancialKnowledge(ctx context.Context,
	r *knowledge.SearchKnowledgeReq_SearchReq) ([]*knowledge.SearchKnowledgeRsp_SearchRsp_Doc, error) {
	log.InfoContextf(ctx, "Search financial knowledge req: %s", helper.Object2String(r))
	tik := time.Now()
	defer func() {
		log.InfoContextf(ctx, "SearchFinancialKnowledge cost: %s", time.Since(tik))
	}()

	req := &knowledge.SearchKnowledgeReq{
		KnowledgeType: knowledge.KnowledgeType_DOC_QA,
		SceneType:     knowledge.SceneType_PROD,
		Req:           r,
	}
	rsp, err := d.knowledgeAPICli.SearchKnowledge(ctx, req)

	if err != nil {
		log.ErrorContextf(ctx, "Search financial knowledge error: %+v, req: %+v", err, req)
		return nil, err
	}
	log.InfoContextf(ctx, "Search financial knowledge rsp: %s", helper.Object2String(rsp))
	return rsp.GetRsp().GetDocs(), nil
}

// SearchMedicalKnowledge 检索医疗行业FAQ
func (d *dao) SearchMedicalKnowledge(ctx context.Context,
	r *knowledge.SearchKnowledgeReq_SearchReq) ([]*knowledge.SearchKnowledgeRsp_SearchRsp_Doc, error) {
	log.InfoContextf(ctx, "Search medical knowledge req: %s", helper.Object2String(r))
	tik := time.Now()
	defer func() {
		log.InfoContextf(ctx, "SearchMedicalKnowledge cost: %s", time.Since(tik))
	}()

	req := &knowledge.SearchKnowledgeReq{
		KnowledgeType: knowledge.KnowledgeType_DOC_QA,
		SceneType:     knowledge.SceneType_PROD,
		Req:           r,
	}
	rsp, err := d.knowledgeAPICli.SearchKnowledge(ctx, req)

	if err != nil {
		log.ErrorContextf(ctx, "Search medical knowledge error: %+v, req: %+v", err, req)
		return nil, err
	}
	log.InfoContextf(ctx, "Search medical knowledge rsp: %s", helper.Object2String(rsp))
	return rsp.GetRsp().GetDocs(), nil
}

// MatchRefer 匹配参考来源
func (d *dao) MatchRefer(ctx context.Context, req *kpb.MatchReferReq) ([]*kpb.MatchReferRsp_Refer, error) {
	log.InfoContextf(ctx, "Match refer req: %s", helper.Object2String(req))
	tik := time.Now()
	defer func() {
		log.InfoContextf(ctx, "MatchRefer cost: %s", time.Since(tik))
	}()

	var calleeNamespace string
	if specialApp, ok := config.App().ExperimentConfig.ReplaceAppMap[req.BotBizId]; ok {
		req.BotBizId = specialApp.ReplaceAppID
		calleeNamespace = specialApp.CalleeNamespace
		log.InfoContextf(ctx, "use replace app: %s", helper.Object2String(specialApp))
	}
	cli := d.knowledgeAPICli
	if d.GetExperimentKnowledgeCli() != nil && calleeNamespace == config.App().ExperimentConfig.Namespace {
		cli = d.GetExperimentKnowledgeCli()
	}
	rsp, err := cli.MatchRefer(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "Match refer error: %+v, req: %+v", err, req)
		return nil, err
	}
	log.InfoContextf(ctx, "Match refer rsp: %s", helper.Object2String(rsp))
	return rsp.GetRefers(), nil
}

// GetKnowledgeSchema 检索知识 Schema 列表
// func (d *dao) GetKnowledgeSchema(
//	ctx context.Context, appID uint64, env string,
// ) ([]*kpb.GetKnowledgeSchemaRsp_SchemaItem, error) {
//	// 统计耗时
//	start := time.Now()
//	defer func() {
//		log.InfoContextf(ctx, "GetKnowledgeSchema cost: %s", time.Since(start))
//	}()
//	// 构造请求
//	req := &kpb.GetKnowledgeSchemaReq{
//		AppBizId: appID,
//		EnvType:  env,
//	}
//	log.InfoContextf(ctx, "GetKnowledgeSchema req: %+v", req)
//
//	// 调用外部 API
//	rsp, err := d.knowledgeAPICli.GetKnowledgeSchema(ctx, req)
//	if err != nil {
//		log.ErrorContextf(ctx, "GetKnowledgeSchema error: %v", err)
//		return nil, err
//	}
//	log.InfoContextf(ctx, "GetKnowledgeSchema rsp: %+v", rsp)
//
//	// 返回文档列表
//	return rsp.GetSchemas(), nil
// }

// DescribeAttributeLabel 获取标签信息
func (d *dao) DescribeAttributeLabel(
	ctx context.Context, appID string, attrBizID []uint64,
) (map[uint64]*kpb.GetAttributeInfoRsp_AttrLabelInfo, error) {
	if len(attrBizID) == 0 {
		return nil, nil
	}
	res := map[uint64]*kpb.GetAttributeInfoRsp_AttrLabelInfo{}
	// 统计耗时
	start := time.Now()
	defer func() {
		log.InfoContextf(ctx, "DescribeAttributeLabel cost: %s", time.Since(start))
	}()
	pagesize := 90
	for i := 0; i < len(attrBizID); i += pagesize {
		// 构造请求
		end := i + pagesize
		if end > len(attrBizID) {
			end = len(attrBizID)
		}
		req := &kpb.GetAttributeInfoReq{
			BotBizId:   appID,
			AttrBizIds: attrBizID[i:end],
		}
		log.InfoContextf(ctx, "GetKnowledgeSchema req: %+v", req)
		// 调用外部 API
		rsp, err := d.knowledgeAPICli.GetAttributeInfo(ctx, req)
		if err != nil {
			log.ErrorContextf(ctx, "GetKnowledgeSchema error: %v", err)
			return nil, err
		}
		log.InfoContextf(ctx, "GetKnowledgeSchema rsp: %+v", rsp)
		for _, attrInfo := range rsp.GetAttrLabelInfos() {
			res[attrInfo.GetAttrBizId()] = attrInfo
		}
	}
	return res, nil
}

// InnerDescribeDocs 批量获取文档详情（内部接口）
func (d *dao) InnerDescribeDocs(ctx context.Context, req *kpb.InnerDescribeDocsReq) (
	*kpb.InnerDescribeDocsRsp, error) {
	rsp, err := d.knowledgeAPICli.InnerDescribeDocs(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "InnerDescribeDocs error: %+v, req: %+v", err, req)
		return nil, err
	}
	log.DebugContextf(ctx, "InnerDescribeDocs rsp: %s", helper.Object2String(rsp))
	return rsp, nil
}
