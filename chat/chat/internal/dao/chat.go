package dao

import (
	"context"
	"errors"
	"fmt"
	"sort"
	"strconv"
	"time"

	"git.code.oa.com/trpc-go/trpc-database/redis"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/common/v3/utils"
	"git.woa.com/dialogue-platform/go-comm/clues"
	pb "git.woa.com/ivy/protobuf/trpc-go/qbot/qbot/chat"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
	ispkg "git.woa.com/ivy/qbot/qbot/infosec/pkg"
	"github.com/jmoiron/sqlx"
	jsoniter "github.com/json-iterator/go"
	"golang.org/x/exp/slices"
)

const (
	prefixStopGeneration = "qbot:ws:stop:generation:"
)

// keyStopGeneration 停止生成的消息
func keyStopGeneration(userID uint64, recordID string) string {
	return prefixStopGeneration + strconv.FormatUint(userID, 10) + ":" + recordID
}

const (
	fieldMsgRecord = `
		id, type, bot_biz_id, record_id, session_id, to_id, to_type, from_id, from_type, content,
		caption,file_infos, labels, intent, 
		intent_category, rewrote_content, reference, prompt, statistic_info, rewrite_prompt, knowledge, has_read, 
		score, reason, is_deleted, related_record_id, result_code, result_type, rejected_question,
		reply_method, rating_time, create_time, update_time,cfg_version_id,trace_id, token_stat, option_cards,
        task_flow, quote_infos, channel_type
	`
	fieldChatPrompt = `
		id, bot_biz_id, record_id, session_id, intent_prompt, intent_result, workflow_prompt,
		workflow_result, create_time, update_time
	`
)

const (
	insertMsgRecord = `
	INSERT INTO t_msg_record (` + fieldMsgRecord + `) VALUES (
		null, :type, :bot_biz_id, :record_id, :session_id, :to_id, :to_type, :from_id, :from_type, :content, 
		:caption, :file_infos, :labels, 
		:intent, :intent_category, :rewrote_content, :reference, :prompt, :statistic_info, :rewrite_prompt, :knowledge, 
		:has_read, :score, :reason, 0, :related_record_id, :result_code, :result_type, :rejected_question,
		:reply_method, :create_time, :create_time, :create_time, :cfg_version_id,:trace_id, :token_stat, :option_cards,
        :task_flow, :quote_infos, :channel_type
	)`

	getMsgRecord = `
	SELECT ` + fieldMsgRecord + ` 
	FROM t_msg_record 
	WHERE is_deleted = 0 AND record_id IN (%s) ORDER BY create_time DESC`

	getMsgRecordWithBotBizID = `
	SELECT ` + fieldMsgRecord + ` 
	FROM t_msg_record 
	WHERE is_deleted = 0 AND record_id IN (%s) AND bot_biz_id = ? ORDER BY create_time DESC`

	getMsgRecordIDByRelatedID = `
	SELECT record_id FROM t_msg_record WHERE related_record_id = ? AND is_deleted = 0 limit 1`

	getMsgRecordsToClean = `
	SELECT record_id FROM t_msg_record WHERE create_time < ? limit ?`

	deleteMsgRecord = `
	DELETE FROM t_msg_record WHERE record_id IN (%s)`

	rating = `
	UPDATE t_msg_record SET 
		rating_time = IF(score = ?, NOW(), rating_time), score = ?, reason = ? 
	WHERE is_deleted = 0 AND id = ? AND bot_biz_id = ?`

	// countScore = `
	// SELECT COUNT(*) FROM t_msg_record WHERE is_deleted = 0 AND session_id = ? AND rating_time >= ? AND score = ?`

	getMsgRecordByFromTo = `
	SELECT ` + fieldMsgRecord + ` FROM t_msg_record 
	WHERE 
		is_deleted = 0 AND 
		(type != ? OR result_code = 0 OR result_code = ? %s) AND session_id = ? %s AND type IN (%s) %s
	ORDER BY create_time %s LIMIT ?`

	getLastNRobotRecord = `
	SELECT ` + fieldMsgRecord + ` FROM t_msg_record 
	WHERE is_deleted = 0 AND from_type = ? AND bot_biz_id = ? AND session_id = ? AND type IN (%s)
	ORDER BY create_time DESC LIMIT ?`

	getLastNRobotRecordWithResetTime = `
	SELECT ` + fieldMsgRecord + ` FROM t_msg_record 
	WHERE is_deleted = 0 AND from_type = ? AND bot_biz_id = ? AND session_id = ? AND type IN (%s) 
	AND UNIX_TIMESTAMP(create_time) > ? ORDER BY create_time DESC LIMIT ?`

	updateMsgRecordRef = `
	UPDATE t_msg_record SET reference = ? WHERE record_id = ? AND is_deleted = 0 AND bot_biz_id = ?`

	updateMsgRecordCheckResult = `
	UPDATE t_msg_record SET result_code=?,result_type = ? WHERE record_id = ? AND is_deleted = 0 AND bot_biz_id = ?`

	updateLastRecordID = `UPDATE t_session SET last_record_id = ? WHERE session_id = ?`

	updateRewroteContent = `
	UPDATE t_msg_record SET rewrite_prompt = ?, rewrote_content = ? WHERE is_deleted = 0 AND id = ? AND bot_biz_id = ?`
)

const (
	insertChatPrompt = `
	INSERT INTO t_chat_prompt (` + fieldChatPrompt + `) VALUES (
		null, :bot_biz_id, :record_id, :session_id, :intent_prompt, :intent_result, :workflow_prompt,:workflow_result,
		:create_time, :create_time
	)`

	updateIntentPrompt = `
	UPDATE t_chat_prompt SET intent_prompt = ?, intent_result = ? WHERE  record_id = ?`

	updateWorkflowResult = `
	UPDATE t_chat_prompt SET workflow_result = ? WHERE  record_id = ?`

	getStatisticInfos = `SELECT bot_biz_id, statistic_info from t_msg_record WHERE bot_biz_id IN (%s)`

	updateFaqReject = `
	UPDATE t_chat_prompt SET faq_reject = ? WHERE  record_id = ?`

	getRecommendQuestion = `SELECT id, question, create_time, update_time FROM t_recommend_question`

	getExperienceRecommendQuestion = `SELECT id, question, content, show_type, create_time, update_time 
		FROM t_exp_recommend_question WHERE is_deleted = 0 and robot_id IN (%s)`
)

const (
	createUserDialogConfig = `INSERT INTO t_user_dialog_config (business_id, bot_biz_id, user_biz_id, user_type, 
        search_network, stream, workflow_status, auto_play) VALUES (:business_id, :bot_biz_id, :user_biz_id, 
        :user_type, :search_network, :stream, :workflow_status, :auto_play)`
	updateUserDialogConfig = `update t_user_dialog_config set search_network = :search_network, 
		stream = :stream, workflow_status = :workflow_status, auto_play = :auto_play where business_id = :business_id 
		and bot_biz_id = :bot_biz_id limit 1`
	getUserDialogConfig = `select business_id, bot_biz_id, user_biz_id, user_type, search_network, 
        stream, workflow_status, auto_play from t_user_dialog_config where bot_biz_id = ? and user_biz_id = ?
        and user_type = ? limit 1`
)

// CreateMsgRecord 创建消息记录
func (d *dao) CreateMsgRecord(ctx context.Context, record model.MsgRecord, stat *model.MsgRecordTokenStat) (int64,
	error) {
	var id int64
	if err := d.tdsqlDB.Transactionx(ctx, func(tx *sqlx.Tx) error {
		sql := insertMsgRecord
		r, err := tx.NamedExec(sql, record)
		if err != nil {
			log.ErrorContextf(ctx, "Insert msg record error: %+v, sql: %s, params: %+v",
				err, sql, record)
			return err
		}

		id, _ = r.LastInsertId()
		if !record.NeedUpdateSession() {
			return nil
		}

		sql = updateLastRecordID
		params := []any{record.RecordID, record.SessionID}
		if _, err = tx.Exec(sql, params...); err != nil {
			log.ErrorContextf(ctx, "Update botsession error: %+v, sql: %s, params: %+v", err, sql, params)
			return err
		}

		// 消息记录调试信息
		_, err = d.createMsgRecordTokenStatWithTx(ctx, tx, stat)
		if err != nil {
			log.ErrorContextf(ctx, "CreateMsgRecordTokenStatWithTx error: %+v, stat: %+v", err, stat)
			return err
		}

		return nil
	}); err != nil {
		log.ErrorContextf(ctx, "Create msg record transaction error: %+v", err)
		return 0, err
	}

	return id, nil
}

// CreateChatPrompt 初始化对话中的Prompt
func (d *dao) CreateChatPrompt(ctx context.Context, prompt model.ChatPrompt) {
	sql := insertChatPrompt
	_, err := d.tdsqlDB.NamedExec(ctx, sql, prompt)
	if err != nil {
		log.ErrorContextf(ctx, "Insert chat prompt error: %+v, sql: %s, params: %+v", err, sql, prompt)
	}
	log.InfoContextf(ctx, "Create chat prompt success, prompt: %s", helper.Object2String(prompt))
}

// UpdateMsgRecordRef 更新消息记录参考来源
func (d *dao) UpdateMsgRecordRef(ctx context.Context, recordID string, refs []model.Reference, botBizID uint64) error {
	s, err := jsoniter.MarshalToString(refs)
	if err != nil {
		log.ErrorContextf(ctx, "Marshal reference error: %+v", err)
		return err
	}
	sql := updateMsgRecordRef
	params := []any{s, recordID, botBizID}
	if _, err := d.tdsqlDB.Exec(ctx, sql, params...); err != nil {
		log.ErrorContextf(ctx, "Update msg record reference error: %+v, sql: %s, params: %+v", err, sql, params)
		return err
	}
	log.DebugContextf(ctx, "Update msg record reference success, recordID: %s, refs: %s", recordID, s)
	return nil
}

// UpdateMsgRecordCheckResult 更新消息记录安全检测结果
func (d *dao) UpdateMsgRecordCheckResult(ctx context.Context, recordID string, checkCode, checkType uint32,
	botBizID uint64) error {
	sql := updateMsgRecordCheckResult
	params := []any{checkCode, checkType, recordID, botBizID}
	if _, err := d.tdsqlDB.Exec(ctx, sql, params...); err != nil {
		log.ErrorContextf(ctx, "Update msg record check error: %+v, sql: %s, params: %+v", err, sql, params)
		return err
	}
	log.DebugContextf(ctx, "Update msg record check success, recordID: %s, code: %d", recordID, checkCode)
	return nil
}

// GetMsgRecord 获取消息记录
func (d *dao) GetMsgRecord(ctx context.Context, p model.GetMsgRecordParam) ([]model.MsgRecord, error) {
	if p.SessionID == "" || len(p.Types) == 0 {
		return nil, nil
	}
	var botEvilWhere string
	params := []any{model.RecordTypeMessage, ispkg.ResultOk}
	if p.IncludeBotEvil {
		botEvilWhere = "OR from_type = ?"
		params = append(params, model.SourceTypeRobot)
	}
	params = append(params, p.SessionID)
	var bizIDWhere string
	if p.BotBizID > 0 {
		bizIDWhere = "AND bot_biz_id = ?"
		params = append(params, p.BotBizID)
	}
	for _, typ := range p.Types {
		params = append(params, typ)
	}
	order := "DESC"
	var startWhere string
	if !p.LastIDCreateTime.IsZero() { // 从当前时间往前查
		params = append(params, p.LastIDCreateTime)
		order, startWhere = "DESC", helper.When(!p.IncludeStart, "AND create_time < ?", "AND create_time <= ?")
	} else if !p.FirstIDCreateTime.IsZero() { // 从当前时间往后查
		params = append(params, p.FirstIDCreateTime)
		order, startWhere = "ASC", helper.When(!p.IncludeStart, "AND create_time > ?", "AND create_time >= ?")
	}
	params = append(params, p.Count)
	sql := fmt.Sprintf(getMsgRecordByFromTo, botEvilWhere, bizIDWhere, placeholder(len(p.Types)), startWhere, order)
	log.InfoContextf(ctx, "Get msg record sql: %s, params: %+v", sql, utils.Any2String(params))
	var records []model.MsgRecord
	if err := d.tdsqlDB.Select(ctx, &records, sql, params...); err != nil {
		log.ErrorContextf(ctx, "Get msg record error: %+v, sql: %s, params: %+v", err, sql, params)
		return nil, err
	}
	log.InfoContextf(ctx, "Get msg record success, records: %+v", utils.Any2String(records))
	sort.Slice(records, func(i, j int) bool { return records[i].CreateTime.Before(records[j].CreateTime) })
	mAnswerRecord := make(map[string]model.MsgRecord, len(records))
	for _, msg := range records {
		if msg.RelatedRecordID != "" && msg.RecordID != "" {
			mAnswerRecord[msg.RelatedRecordID] = msg // 答案
		}
	}
	var results []model.MsgRecord
	for _, msg := range records { // 通过问题找答案，确保答案是跟在问题后面
		if msg.RelatedRecordID == "" {
			if answer, ok := mAnswerRecord[msg.RecordID]; ok { // 确保有答案，再一起写问题和答案，避免只出现问题，没有答案
				results = append(results, msg)    // 写问题
				results = append(results, answer) // 写答案
			}
		}
	}
	return results, nil
}

// GetLastNBotRecord 获取最后 N 轮机器人回复
func (d *dao) GetLastNBotRecord(
	ctx context.Context, botBizID uint64, session *model.Session, n uint, types []model.RecordType,
) ([]model.MsgRecord, error) {
	if session.SessionID == "" {
		return nil, nil
	}

	var sql string
	params := []any{model.SourceTypeRobot, botBizID, session.SessionID}
	for _, typ := range types {
		params = append(params, typ)
	}
	// 重置会话时间存在, 则只取比重置会话时间大的记录
	if !session.ResetTime.IsZero() && session.ResetTime.Unix() > 0 {
		sql = fmt.Sprintf(getLastNRobotRecordWithResetTime, placeholder(len(types)))
		params = append(params, session.ResetTime.Unix())
	} else {
		sql = fmt.Sprintf(getLastNRobotRecord, placeholder(len(types)))
	}
	params = append(params, n)

	var records []model.MsgRecord
	err := d.tdsqlDB.Select(ctx, &records, sql, params...)
	clues.AddTrackE(ctx, "GetLastNBotRecord.Select", clues.M{"sql": sql, "params": params, "records": records}, err)
	if err != nil {
		log.ErrorContextf(ctx, "Get last n bot record error: %+v, sql: %s, params: %+v", err, sql, params)
		return nil, err
	}

	slices.SortFunc(records, func(a, b model.MsgRecord) int { return int(a.ID) - int(b.ID) })
	return records, nil
}

// UpdateRewroteContent 更新消息改写内容
func (d *dao) UpdateRewroteContent(ctx context.Context, id uint64, prompt, content string, botBizID uint64) error {
	sql := updateRewroteContent
	params := []any{prompt, content, id, botBizID}
	if _, err := d.tdsqlDB.Exec(ctx, sql, params...); err != nil {
		log.ErrorContextf(ctx, "Update rewrote content error: %+v, sql: %s, params: %+v", err, sql, params)
		return err
	}
	return nil
}

// UpdateIntentPrompt 更新意图Prompt
func (d *dao) UpdateIntentPrompt(ctx context.Context, recordID, prompt, result string) {
	sql := updateIntentPrompt
	params := []any{prompt, result, recordID}
	if _, err := d.tdsqlDB.Exec(ctx, sql, params...); err != nil {
		log.ErrorContextf(ctx, "Update Intent prompt error: %+v, length of prompt(%d), result(%d), "+
			"sql: %s, params: %+v", err, len(prompt), len(result), sql, params)
	}
}

// UpdateFaqReject 更新FAQ Reject字段为1
func (d *dao) UpdateFaqReject(ctx context.Context, recordID string) {
	sql := updateFaqReject
	params := []any{1, recordID}
	if _, err := d.tdsqlDB.Exec(ctx, sql, params...); err != nil {
		log.ErrorContextf(ctx, "Update faq reject error: %+v, sql: %s, params: %+v", err, sql, params)
	}
	log.InfoContextf(ctx, "Update faq reject success, recordID: %s", recordID)
}

// UpdateWorkflowPrompt 更新工作流Prompt
func (d *dao) UpdateWorkflowPrompt(ctx context.Context, appID uint64, sessionID, prompt string) {
	if err := d.tdsqlDB.Transactionx(ctx, func(tx *sqlx.Tx) error {
		sql := "select record_id from t_chat_prompt where bot_biz_id = ? and session_id = ? " +
			"order by create_time desc limit 1"
		recordID := make([]string, 0)
		err := tx.Select(&recordID, sql, appID, sessionID)
		if err != nil {
			log.ErrorContextf(ctx, "select record_id from  t_chat_prompt error: %+v, sql: %s, sessionID: %+v",
				err, sql, sessionID)
			return err
		}
		if len(recordID) < 1 {
			log.WarnContextf(ctx, "select record_id from  t_chat_prompt no result, sql: %s, sessionID: %+v",
				err, sql, sessionID)
			return nil
		}
		updateSQL := "update t_chat_prompt set workflow_prompt = ? where record_id = ?"
		_, err = tx.Exec(updateSQL, prompt, recordID[0])
		if err != nil {
			log.ErrorContextf(ctx, "update workflow prompt error: %+v, sql: %s, prompt: %+v",
				err, updateSQL, prompt)
			return err
		}
		return nil
	}); err != nil {
		log.ErrorContextf(ctx, "Update workflow prompt transaction error: %+v", err)
	}
}

// UpdateWorkflowResult 更新工作流检索结果
func (d *dao) UpdateWorkflowResult(ctx context.Context, recordID, result string) {
	sql := updateWorkflowResult
	params := []any{result, recordID}
	if _, err := d.tdsqlDB.Exec(ctx, sql, params...); err != nil {
		log.ErrorContextf(ctx, "Update Workflow result error: %+v, sql: %s, params: %+v", err, sql, params)
	}
}

// GetMsgRecordByRecordID 根据消息记录 ID 获取消息记录
func (d *dao) GetMsgRecordByRecordID(ctx context.Context, recordID string, botBizID uint64) (*model.MsgRecord, error) {
	records, err := d.GetMsgRecordsByRecordID(ctx, []string{recordID}, botBizID)
	if err != nil {
		return nil, err
	}
	if len(records) == 0 {
		return nil, nil
	}
	return &records[0], nil
}

// GetMsgRecordsByRecordID 根据消息记录 ID 批量获取消息记录
func (d *dao) GetMsgRecordsByRecordID(ctx context.Context, recordIDs []string, botBizID uint64) ([]model.MsgRecord,
	error) {
	if len(recordIDs) == 0 {
		return nil, nil
	}
	params := make([]any, 0, len(recordIDs))
	for _, id := range recordIDs {
		params = append(params, id)
	}
	var records []model.MsgRecord
	sql := fmt.Sprintf(getMsgRecord, placeholder(len(recordIDs)))
	if botBizID > 0 { // 切到tdsql后，带上shardkey
		params = append(params, botBizID)
		sql = fmt.Sprintf(getMsgRecordWithBotBizID, placeholder(len(recordIDs)))
	}
	if err := d.tdsqlDB.Select(ctx, &records, sql, params...); err != nil {
		log.ErrorContextf(ctx, "Get msg records by recordID error: %+v, sql: %s, params: %+v", err, sql, params)
		return nil, err
	}
	return records, nil
}

// GetMsgRecordIDByRelatedRecordID 根据关联消息记录ID,获取消息记录ID
func (d *dao) GetMsgRecordIDByRelatedRecordID(ctx context.Context, relatedRecordID string) (string, error) {
	recordID := make([]string, 0)
	if err := d.tdsqlDB.Select(ctx, &recordID, getMsgRecordIDByRelatedID, relatedRecordID); err != nil {
		log.ErrorContextf(ctx, "Get msg records by recordID error: %+v, params: %+v", err, relatedRecordID)
		return "", err
	}
	if len(recordID) < 1 {
		log.WarnContextf(ctx, "Get msg records by recordID no result, params: %+v", relatedRecordID)
		return "", errors.New("no found recordID")
	}
	return recordID[0], nil
}

// Rating 机器人回复评价
func (d *dao) Rating(ctx context.Context, id uint64, score model.ScoreType, reasons []string, botBizID uint64) error {
	reason, _ := jsoniter.MarshalToString(reasons)
	sql := rating
	params := []any{model.ScoreNone, score, reason, id, botBizID}
	if _, err := d.tdsqlDB.Exec(ctx, sql, params...); err != nil {
		log.ErrorContextf(ctx, "Rating error: %+v, sql: %s, params: %+v", err, sql, params)
		return err
	}
	return nil
}

// CountScore 统计已评价机器人回复
// func (d *dao) CountScore(ctx context.Context, sessionID string, t time.Time, score model.ScoreType) (uint32, error) {
//	var c uint32
//	sql := countScore
//	params := []any{sessionID, t, score}
//	if err := d.db.Get(ctx, &c, sql, params...); err != nil {
//		log.ErrorContextf(ctx, "Count score error: %+v, sql: %s, params: %+v", err, sql, params)
//		return 0, err
//	}
//	return c, nil
// }

// StopGeneration 停止消息生成
func (d *dao) StopGeneration(ctx context.Context, userID uint64, recordID string) error {
	cfg := config.App().Bot.StopGeneration
	key := keyStopGeneration(userID, recordID)
	if _, err := redis.String(d.redis.Do(ctx, "SETEX", key, cfg.KeyExpire, 1)); err != nil {
		log.ErrorContextf(ctx, "Set stop generation sign error: %+v, key: %s", err, key)
		return err
	}
	return nil
}

// IsGenerationStopped 判断消息是否被停止生成
func (d *dao) IsGenerationStopped(ctx context.Context, userID uint64, recordID string) (bool, error) {
	key := keyStopGeneration(userID, recordID)
	v, err := redis.Int(d.redis.Do(ctx, "GET", key))
	if err != nil {
		if err == redis.ErrNil {
			return false, nil
		}
		log.ErrorContextf(ctx, "Get stop generation sign error: %+v, key: %s", err, key)
		return false, err
	}
	return v == 1, nil
}

// GetRecommendQuestion 获取推荐问题
func (d *dao) GetRecommendQuestion(ctx context.Context) ([]model.RecommendQuestion, error) {
	var questions []model.RecommendQuestion
	if err := d.db.Select(ctx, &questions, getRecommendQuestion); err != nil {
		log.ErrorContextf(ctx, "Get recommend question error: %+v, sql: %s", err, getRecommendQuestion)
		return nil, err
	}
	return questions, nil
}

// GetExperienceRecommendQuestion 获取体验中心推荐问题
func (d *dao) GetExperienceRecommendQuestion(ctx context.Context, robotIDs []uint64) (
	[]model.ExperienceRecommendQuestion, error) {
	params := make([]any, 0, len(robotIDs))
	for _, id := range robotIDs {
		params = append(params, id)
	}
	var questions []model.ExperienceRecommendQuestion
	sql := fmt.Sprintf(getExperienceRecommendQuestion, placeholder(len(robotIDs)))
	if err := d.db.Select(ctx, &questions, sql, params...); err != nil {
		log.ErrorContextf(ctx, "Get experience recommend question error: %+v, sql: %s, params: %+v", err, sql, params)
		return nil, err
	}
	return questions, nil
}

// GetMsgRecordToClean 获取需要清理的消息记录
func (d *dao) GetMsgRecordToClean(ctx context.Context, clearCreateTime time.Time, limit int) ([]string, error) {
	t1 := time.Now()
	recordIDs := make([]string, 0)
	params := []any{clearCreateTime, limit}
	if !config.App().IsPrivate && clearCreateTime.After(time.Now().AddDate(0, 0, -60)) {
		// 做个保护，避免配置配错误删除；私有化环境不作限制
		return recordIDs, errors.New("clearCreateTime should be at least 60 days ago")
	}
	if limit == 0 || limit > 10000 { // 避免limit太大，做个保护
		limit = 1000
	}
	if err := d.tdsqlDB.Select(ctx, &recordIDs, getMsgRecordsToClean, params...); err != nil {
		log.ErrorContextf(ctx, "GetMsgRecordToClean error: %+v, sql: %s, params: %+v", err,
			getMsgRecordsToClean, params)
		return recordIDs, err
	}
	log.InfoContextf(ctx, "data:%s cleanRecords len(recordIDs): %d, cost:%d", clearCreateTime, len(recordIDs),
		time.Since(t1).Milliseconds())
	return recordIDs, nil
}

// DeleteMsgRecord 删除消息记录【只允许定时任务调用】
func (d *dao) DeleteMsgRecord(ctx context.Context, recordIDs []string) error {
	sql := fmt.Sprintf(deleteMsgRecord, placeholder(len(recordIDs)))
	params := make([]any, 0, len(recordIDs))
	for _, id := range recordIDs {
		params = append(params, id)
	}
	if _, err := d.tdsqlDB.Exec(ctx, sql, params...); err != nil {
		log.ErrorContextf(ctx, "DeleteMsgRecord error: %+v, sql: %s, params: %+v", err, sql, params)
		return err
	}
	return nil
}

// CreateOrUpdateUserDialogConfig 更新用户应用配置，如果不存在，则创建
func (d *dao) CreateOrUpdateUserDialogConfig(ctx context.Context, req *pb.ModifyUserDialogConfigReq) error {
	// 先查询配置是否存在
	params := &model.UserDialogConfig{
		UserBizID:      req.UserBizId,
		UserType:       model.DialogUserType(req.UserType),
		BotBizID:       req.BotBizId,
		SearchNetwork:  model.SearchNetwork(req.SearchNetwork),
		Stream:         model.ChatStream(req.Stream),
		WorkflowStatus: model.WorkflowStatus(req.WorkflowStatus),
		AutoPlay:       model.AutoPlay(req.AutoPlay),
	}
	dialogConfig, found, err := d.GetUserDialogConfig(ctx, params)
	if err != nil {
		log.ErrorContextf(ctx, "Get user dialog config error: %+v, param:%+v", err, params)
		return err
	}

	if !found {
		// 不存在配置，则创建
		params.BusinessID = d.GenerateSeqID()
		_, err = d.tdsqlDB.NamedExec(ctx, createUserDialogConfig, params)
		if err != nil {
			log.ErrorContextf(ctx, "create user dialog config error: %+v, dialogConfig:%+v", err, params)
			return err
		}
		return nil
	}

	// 更新配置, 不更新的字段使用数据库原值
	if params.SearchNetwork == model.SearchNetworkDefault {
		params.SearchNetwork = dialogConfig.SearchNetwork
	}
	if params.Stream == model.ChatStreamDefault {
		params.Stream = dialogConfig.Stream
	}
	if params.WorkflowStatus == model.WorkflowStatusDefault {
		params.WorkflowStatus = dialogConfig.WorkflowStatus
	}
	if params.AutoPlay == model.AutoPlayDefault {
		params.AutoPlay = dialogConfig.AutoPlay
	}

	// 存在，则更新
	params.BusinessID = dialogConfig.BusinessID
	_, err = d.tdsqlDB.NamedExec(ctx, updateUserDialogConfig, params)
	if err != nil {
		log.ErrorContextf(ctx, "user dialog config error: %+v, dialogConfig:%+v", err, dialogConfig)
		return err
	}
	return nil
}

// GetUserDialogConfig 获取用户会话配置
func (d *dao) GetUserDialogConfig(ctx context.Context, params *model.UserDialogConfig) (model.UserDialogConfig, bool,
	error) {
	var dialogConfigs []model.UserDialogConfig
	err := d.tdsqlDB.Select(ctx, &dialogConfigs, getUserDialogConfig, params.BotBizID, params.UserBizID, params.UserType)
	if err != nil {
		log.ErrorContextf(ctx, "Get user dialog config error: %+v", err)
		return model.UserDialogConfig{}, false, err
	}
	if len(dialogConfigs) == 0 {
		return model.UserDialogConfig{}, false, nil
	}

	return dialogConfigs[0], true, err
}
