// chat
//
// @(#)finance.go  Tuesday, May 21, 2024
// Copyright(c) 2024, leyton@Tencent. All rights reserved.

package dao

import (
	"context"
	"errors"
	"fmt"
	"math"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/common/v3/utils"
	"git.woa.com/dialogue-platform/go-comm/clues"
	"git.woa.com/dialogue-platform/go-comm/pf"
	admin "git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_admin_config_server"
	"git.woa.com/ivy/protobuf/trpc-go/qbot/finance/finance"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
)

// 计费上报类型
const (
	// FinanceSubBizTypeKnowledgeQA 知识问答(对话调用-API调用)
	FinanceSubBizTypeKnowledgeQA = "KnowledgeQA"
	// FinanceSubBizTypeKnowledgeQADialogTest 知识问答(对话调用-对话测试)
	FinanceSubBizTypeKnowledgeQADialogTest = "KnowledgeQADialogTest"
	// FinanceSubBizTypeKnowledgeQAUser 知识问答(对话调用-分享用户端对话)
	FinanceSubBizTypeKnowledgeQAUser = "KnowledgeQAUser"

	// financeSubBizTypeSearchEngine 搜索引擎
	financeSubBizTypeSearchEngine = "SearchEngine"
	// financeSubBizTypeConcurrency 并发
	financeSubBizTypeConcurrency = "Concurrency"
	// FinanceSubBizTypeRoleCommands 角色指令一键优化
	FinanceSubBizTypeRoleCommands = "RoleCommands"
	// FinanceSubBizTypeEvaluateTest 应用评测
	FinanceSubBizTypeEvaluateTest = "EvaluateTest"
)

// DescribeAccountStatus 获取账户状态
// DescribeAccountStatus(ctx context.Context, req *finance.DescribeAccountStatusReq)
// (*finance.DescribeAccountStatusRsp, error)

// GetModelStatus 获取模型状态, 账户状态 0可用, 1不可用, -1不可用
func (d *dao) GetModelStatus(ctx context.Context, corpID uint64, modelName string) int {
	if config.App().Finance.Disabled {
		log.InfoContextf(ctx, "GetModelStatus: %s Finance disabled, return 0", modelName)
		return 0
	}
	if info := d.GetModelFinanceInfo(ctx, modelName); info.GetIsFree() {
		log.InfoContextf(ctx, "model: %s model is free, status return 0", modelName)
		return 0
	}
	t0 := time.Now()
	uin, sid, err := d.GetUinByCorpID(ctx, corpID)
	clues.AddTrackE(ctx, "GetModelStatus.GetUinByCorpID", clues.M{"corpID": corpID, "uin": uin, "sid": sid}, err)
	if err != nil {
		log.ErrorContextf(ctx, "Invoke GetUinByCorpID error: %+v, req: %+v", err, corpID)
		return -1
	}
	pf.StartElapsed(ctx, "finance.DescribeAccountStatus")
	log.InfoContextf(ctx, "REQ|GetModelStatus request: %d, %s", corpID, modelName)
	req := &finance.DescribeAccountStatusReq{
		Biz: &finance.Biz{BizType: finance.BizType_BIZ_TYPE_LKE, SubBizType: FinanceSubBizTypeKnowledgeQA},
		Account: &finance.Account{
			Sid: finance.SID(sid),
			Uin: fmt.Sprintf("%d", uin),
		},
		ModelName: modelName,
	}
	// opts := []client.Option{WithTrpcSelector()}
	rsp, err := d.financeCli.DescribeAccountStatus(ctx, req)
	pf.AppendSpanElapsed(ctx, "finance.DescribeAccountStatus")
	clues.AddTrack4RPC(ctx, "finance.GetModelStatus", req, rsp, err, t0)
	log.InfoContextf(ctx, "RESP|GetModelStatus %v, ERR: %v, %s", rsp, err, time.Since(t0).String())
	if err != nil {
		log.ErrorContextf(ctx, "Invoke finance.GetModelStatus error: %+v, req: %+v", err, req)
		var err0 *errs.Error
		if ok := errors.As(err, &err0); ok { // 当前模型无计费配置
			if err0.Code == 400 {
				clues.AddT(ctx, "finance.not.configured", modelName)
				return 1
			}
		}
		return -1
	}
	return int(rsp.GetStatus())
}

// GetModelConcurrency 获取模型并发数, 小于0则说明获取异常, 同时返回是否独享并发模型
func (d *dao) GetModelConcurrency(ctx context.Context, corpID uint64, modelName string) (int, bool) {
	if config.App().Finance.Disabled {
		clues.AddT(ctx, "Finance.Disabled", true)
		return 0, false
	}
	t0 := time.Now()
	uin, sid, err := d.GetUinByCorpID(ctx, corpID)
	clues.AddTrackE(ctx, "GetModelConcurrency.GetUinByCorpID", clues.M{"corpID": corpID, "uin": uin, "sid": sid}, err)
	if err != nil {
		log.ErrorContextf(ctx, "Invoke GetUinByCorpID error: %+v, req: %+v", err, corpID)
		return -1, false
	}
	pf.StartElapsed(ctx, "finance.DescribeAccountInfo")
	log.InfoContextf(ctx, "REQ|GetModelConcurrency request: %d, %s", corpID, modelName)
	req := &finance.DescribeAccountInfoReq{
		Biz: &finance.Biz{BizType: finance.BizType_BIZ_TYPE_LKE, SubBizType: financeSubBizTypeConcurrency},
		Account: &finance.Account{
			Sid: finance.SID(sid),
			Uin: fmt.Sprintf("%d", uin),
		},
		ModelName: modelName,
	}
	// opts := []client.Option{WithTrpcSelector()}
	rsp, err := d.financeCli.DescribeAccountInfo(ctx, req)
	pf.AppendSpanElapsed(ctx, "finance.DescribeAccountInfo")
	clues.AddTrack4RPC(ctx, "GetModelConcurrency.finance.DescribeAccountInfo", req, rsp, err, t0)
	log.InfoContextf(ctx, "RESP|DescribeAccountInfo %v, ERR: %v, %s", rsp, err, time.Since(t0).String())
	if err != nil {
		log.ErrorContextf(ctx, "Invoke finance.DescribeAccountInfo error: %+v, req: %+v", err, req)
		return -1, false
	}
	return int(rsp.GetBalance()), rsp.GetIsExclusive()
}

// DescribeAccountInfo 获取企业账户信息
// DescribeAccountInfo(ctx context.Context, req *finance.DescribeAccountInfoReq)
// (*finance.DescribeAccountInfoRsp, error)

// GetModelToken 获取模型 token, 返回 用量, 剩余量, -1 表示内部错误
func (d *dao) GetModelToken(ctx context.Context, corpID uint64, modelName string) (int, int) {
	if config.App().Finance.Disabled {
		clues.AddT(ctx, "Finance.Disabled", true)
		return math.MaxInt, math.MaxInt
	}
	t0 := time.Now()
	uin, sid, err := d.GetUinByCorpID(ctx, corpID)
	clues.AddTrackE(ctx, "GetModelToken.GetUinByCorpID", clues.M{"corpID": corpID, "uin": uin, "sid": sid}, err)
	if err != nil {
		log.ErrorContextf(ctx, "Invoke GetUinByCorpID error: %+v, req: %+v", err, corpID)
		return -1, -1
	}
	pf.StartElapsed(ctx, "finance.DescribeAccountInfo")
	log.InfoContextf(ctx, "REQ|GetModelConcurrency request: %d, %s", corpID, modelName)
	req := &finance.DescribeAccountInfoReq{
		Biz: &finance.Biz{BizType: finance.BizType_BIZ_TYPE_LKE, SubBizType: FinanceSubBizTypeKnowledgeQA},
		Account: &finance.Account{
			Sid: finance.SID(sid),
			Uin: fmt.Sprintf("%d", uin),
		},
		ModelName: modelName,
	}
	// opts := []client.Option{WithTrpcSelector()}
	rsp, err := d.financeCli.DescribeAccountInfo(ctx, req)
	pf.AppendSpanElapsed(ctx, "finance.DescribeAccountInfo")
	clues.AddTrack4RPC(ctx, "finance.DescribeAccountInfo", req, rsp, err, t0)
	log.InfoContextf(ctx, "RESP|DescribeAccountInfo %v, ERR: %v, %s", rsp, err, time.Since(t0).String())
	if err != nil {
		log.ErrorContextf(ctx, "Invoke finance.DescribeAccountInfo error: %+v, req: %+v", err, req)
		return -1, -1
	}
	return int(rsp.GetBalance()), int(rsp.GetTotal())
}

// ReportTokenDosage 上报用量, 原始接口
func (d *dao) ReportTokenDosage(ctx context.Context, corpID uint64, req *finance.ReportDosageReq,
	subBizType string) error {
	if config.IsReportDosageDisabled() {
		clues.AddT(ctx, "Finance.Disabled", true)
		return nil
	}
	if subBizType == "" {
		subBizType = FinanceSubBizTypeKnowledgeQA
	}
	return d.ReportDosage(ctx, corpID, req, subBizType)
}

// ReportDosage 上报用量, 原始接口
func (d *dao) ReportDosage(ctx context.Context, corpID uint64, req *finance.ReportDosageReq,
	subBizType string) error {
	t0 := time.Now()
	log.InfoContextf(ctx, "REQ|ReportDosage request: %d, %s, %s", corpID, utils.Any2String(req), subBizType)
	uin, sid, err := d.GetUinByCorpID(ctx, corpID)
	if err != nil {
		log.ErrorContextf(ctx, "Invoke GetUinByCorpID error: %+v, req: %+v", err, corpID)
		return err
	}
	req.Biz = &finance.Biz{BizType: finance.BizType_BIZ_TYPE_LKE, SubBizType: subBizType}
	req.Account = &finance.Account{Sid: finance.SID(sid), Uin: fmt.Sprintf("%d", uin)}
	// opts := []client.Option{WithTrpcSelector()}
	rsp, err := d.financeCli.ReportDosage(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "Invoke finance.ReportDosage error: %+v, req: %+v", err, req)
		return err
	}
	log.InfoContextf(ctx, "RESP|ReportDosage %v, ERR: %v, %s", rsp, err, time.Since(t0).String())
	return nil
}

// TokenDosage token 上报详情数据
// {"AppID": "xxxx", "AppType": "xxxx"}
type TokenDosage struct {
	AppID         uint64
	AppType       string
	ModelName     string    // 模型标识
	RecordID      string    // 对应 query 的 record id
	StartTime     time.Time // 用量实际发生开始时间 业务侧定义
	EndTime       time.Time // 用量实际发生结束时间 业务侧定义
	InputDosages  []int     // 输入用量详情信息
	OutputDosages []int     // 输出用量详情信息
	SearchEngine  int       // 搜索引擎调用次数
}

// ReportTokenDosage2 上报用量, 业务封装
func (d *dao) ReportTokenDosage2(ctx context.Context, corpID uint64, dosage TokenDosage, subBizType string) error {
	if status := d.GetModelStatus(ctx, corpID, dosage.ModelName); status != 0 {
		log.InfoContextf(ctx, "model:%s, status:%d, not reportTokenDosage", dosage.ModelName, status)
		return nil
	}
	if info := d.GetModelFinanceInfo(ctx, dosage.ModelName); info.GetIsFree() {
		log.InfoContextf(ctx, "model:%s, is free, not reportTokenDosage", dosage.ModelName)
		return nil
	}
	req := &finance.ReportDosageReq{
		ModelName: dosage.ModelName,
		DosageId:  dosage.RecordID,
		Payload: fmt.Sprintf(`{"AppID":"%v","AppType":"%v","SearchEngine":%v}`, dosage.AppID,
			dosage.AppType, dosage.SearchEngine),
	}
	if !dosage.StartTime.IsZero() {
		req.StartTime = uint64(dosage.StartTime.Unix()) // 单位s
	}
	if !dosage.EndTime.IsZero() {
		req.EndTime = uint64(dosage.EndTime.Unix()) // 单位s
	}
	for i := range dosage.InputDosages {
		req.List = append(req.List, &finance.ReportDosageReq_Detail{
			Dosage:  float64(dosage.InputDosages[i]),
			Payload: `{"type":"input"}`,
		})
	}
	for i := range dosage.OutputDosages {
		req.List = append(req.List, &finance.ReportDosageReq_Detail{
			Dosage:  float64(dosage.OutputDosages[i]),
			Payload: `{"type":"output"}`,
		})
	}
	return d.ReportTokenDosage(ctx, corpID, req, subBizType)
}

// SearchEngineDosage 搜索引擎上报数据
type SearchEngineDosage struct {
	AppID     uint64
	AppType   string
	RecordID  string    // 对应 query 的 record id
	StartTime time.Time // 用量实际发生开始时间 业务侧定义
	EndTime   time.Time // 用量实际发生结束时间 业务侧定义
	Dosage    int       // 用量详情信息
}

// GetSearchEngineStatus 获取搜索引擎资源状态, 账户状态 0可用, 1不可用, -1不可用
func (d *dao) GetSearchEngineStatus(ctx context.Context, corpID uint64) int {
	if config.App().Finance.SearchDisabled {
		clues.AddT(ctx, "Finance.SearchDisabled", true)
		return 0
	}
	t0 := time.Now()
	uin, sid, err := d.GetUinByCorpID(ctx, corpID)
	clues.AddTrackE(ctx, "GetSearchEngineStatus.GetUinByCorpID", clues.M{"corpID": corpID, "uin": uin,
		"sid": sid}, err)
	if err != nil {
		log.ErrorContextf(ctx, "Invoke GetUinByCorpID error: %+v, req: %+v", err, corpID)
		return -1
	}
	pf.StartElapsed(ctx, "finance.DescribeAccountStatus")
	log.InfoContextf(ctx, "REQ|GetSearchEngineStatus request: %d", corpID)
	req := &finance.DescribeAccountStatusReq{
		Biz: &finance.Biz{BizType: finance.BizType_BIZ_TYPE_LKE, SubBizType: financeSubBizTypeSearchEngine},
		Account: &finance.Account{
			Sid: finance.SID(sid),
			Uin: fmt.Sprintf("%d", uin),
		},
	}
	rsp, err := d.financeCli.DescribeAccountStatus(ctx, req)
	pf.AppendSpanElapsed(ctx, "finance.DescribeAccountStatus")
	clues.AddTrack4RPC(ctx, "finance.GetSearchEngineStatus", req, rsp, err, t0)
	log.InfoContextf(ctx, "RESP|GetSearchEngineStatus %v, ERR: %v, %s", rsp, err, time.Since(t0).String())
	if err != nil {
		log.ErrorContextf(ctx, "Invoke finance.GetSearchEngineStatus error: %+v, req: %+v", err, req)
		var err0 *errs.Error
		if ok := errors.As(err, &err0); ok { // 当前模型无计费配置
			if err0.Code == 400 {
				clues.AddT(ctx, "finance.not.configured", corpID)
				return 1
			}
		}
		return -1
	}
	return int(rsp.GetStatus())
}

// ReportSearchEngineDosage 上报搜索引擎，业务封装
func (d *dao) ReportSearchEngineDosage(ctx context.Context, corpID uint64, dosage SearchEngineDosage) error {
	if config.App().Finance.SearchDisabled {
		log.InfoContextf(ctx, "Finance.SearchDisabled", true)
		return nil
	}
	if status := d.GetSearchEngineStatus(ctx, corpID); status != 0 {
		return nil
	}
	req := &finance.ReportDosageReq{
		DosageId: dosage.RecordID,
		Payload:  fmt.Sprintf(`{"AppID":"%v","AppType":"%v"}`, dosage.AppID, dosage.AppType),
	}
	if !dosage.StartTime.IsZero() {
		req.StartTime = uint64(dosage.StartTime.Unix()) // 单位s
	}
	if !dosage.EndTime.IsZero() {
		req.EndTime = uint64(dosage.EndTime.Unix()) // 单位s
	}
	req.List = append(req.List, &finance.ReportDosageReq_Detail{
		Dosage: float64(dosage.Dosage),
	})
	return d.ReportDosage(ctx, corpID, req, financeSubBizTypeSearchEngine)
}

// ConcurrencyDosage 并发上报数据
type ConcurrencyDosage struct {
	AppID     uint64
	AppType   string
	ModelName string    // 模型标识
	RecordID  string    // 对应 query 的 record id
	StartTime time.Time // 用量实际发生开始时间 业务侧定义
	EndTime   time.Time // 用量实际发生结束时间 业务侧定义
	Dosage    int       // 用量详情信息
}

// ReportConcurrencyDosage 上报并发 业务封装
func (d *dao) ReportConcurrencyDosage(ctx context.Context, corpID uint64, dosage ConcurrencyDosage) error {
	clues.AddT(ctx, "ReportConcurrencyDosage", dosage)
	if config.App().Finance.ReportDisabled {
		clues.AddT(ctx, "Finance.ReportDisabled", true)
		return nil
	}
	if info := d.GetModelFinanceInfo(ctx, dosage.ModelName); info.GetIsFree() {
		log.InfoContextf(ctx, "model:%s is free notReportConcurrency", dosage.ModelName)
		return nil
	}
	req := &finance.ReportDosageReq{
		ModelName: dosage.ModelName,
		DosageId:  dosage.RecordID,
		Payload:   fmt.Sprintf(`{"AppID":"%v","AppType":"%v"}`, dosage.AppID, dosage.AppType),
	}
	if !dosage.StartTime.IsZero() {
		req.StartTime = uint64(dosage.StartTime.Unix()) // 单位s
	}
	if !dosage.EndTime.IsZero() {
		req.EndTime = uint64(dosage.EndTime.Unix()) // 单位s
	}
	req.List = append(req.List, &finance.ReportDosageReq_Detail{
		Dosage: float64(dosage.Dosage),
	})
	return d.ReportDosage(ctx, corpID, req, financeSubBizTypeConcurrency)
}

// PrefixFinanceCorpModel 获取计费信息前缀
// const PrefixFinanceCorpModel = "qbot:chat:finance_corp_model"

// keyFinanceCorpModel finance corp model  key
// func keyFinanceCorpModel(corpID, modelName string) string {
//	return PrefixFinanceCorpModel + ":" + corpID + ":" + modelName
// }

// GetTotalConcurrency 获取并发数
func (d *dao) GetTotalConcurrency(ctx context.Context, appID uint64, modelName string) (int, error) {
	if config.App().Finance.Disabled {
		clues.AddT(ctx, "Finance.Disabled", true)
		return 0, nil
	}
	t0 := time.Now()
	req := &admin.GetTotalConcurrencyReq{
		AppBizId:  appID,
		ModelName: modelName,
	}
	concurrenceRsp, err := d.apiCli.GetTotalConcurrency(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "GetTotalConcurrency error: %+v, req: %+v", err, req)
		return -1, err
	}
	concurrence := concurrenceRsp.TotalConcurrency
	log.InfoContextf(ctx, "GetTotalConcurrency success, concurrence: %d, cost: %s", concurrence, time.Since(t0))
	return int(concurrence), nil
}

// OverConcurrencyDosage 超并发dosage
type OverConcurrencyDosage struct {
	AppID     uint64    // 应用ID
	ModelName string    // 模型标识
	UsageTime time.Time // 用量实际发生开始时间 业务侧定义
	Content   string    // 请求内容
}

// ReportOverConcurrencyDosage 上报超并发dosage
func (d *dao) ReportOverConcurrencyDosage(ctx context.Context, app *model.App, dosage OverConcurrencyDosage) error {
	clues.AddT(ctx, "ReportOverConcurrencyDosage", dosage)
	corpID := app.GetCorpId()
	if config.App().Finance.Disabled {
		clues.AddT(ctx, "Finance.Disabled", true)
		return nil
	}
	if len(dosage.Content) == 0 {
		log.InfoContextf(ctx, "ReportOverConcurrencyDosage content is empty,dosage:%+v", dosage)
		return nil
	}
	t0 := time.Now()
	uin, sid, err := d.GetUinByCorpID(ctx, corpID)
	clues.AddTrackE(ctx, "ReportOverConcurrencyDosage.GetUinByCorpID", clues.M{"corpID": corpID, "uin": uin,
		"sid": sid}, err)
	if err != nil {
		log.ErrorContextf(ctx, "Invoke ReportOverConcurrencyDosage error: %+v, req: %+v", err, corpID)
		return err
	}
	if app.IsExclusive { // 专属并发，上报超并发
		pf.StartElapsed(ctx, "finance.ReportOverConcurrencyDosage")
		req := &finance.ReportOverConcurrencyDosageReq{
			Biz: &finance.Biz{BizType: finance.BizType_BIZ_TYPE_LKE, SubBizType: financeSubBizTypeConcurrency},
			Account: &finance.Account{
				Sid: finance.SID(sid),
				Uin: fmt.Sprintf("%d", uin),
			},
			ModelName: dosage.ModelName,
			UsageTime: uint64(dosage.UsageTime.Unix()),
			Query:     dosage.Content,
			AppId:     fmt.Sprintf("%d", dosage.AppID),
		}
		log.InfoContextf(ctx, "REQ|ReportOverConcurrencyDosage request: %d, %v", corpID, req)
		rsp, err := d.financeCli.ReportOverConcurrencyDosage(ctx, req)
		log.InfoContextf(ctx, "RESP|ReportOverConcurrencyDosage %v, ERR: %v, %s", rsp, err, time.Since(t0).String())
		pf.AppendSpanElapsed(ctx, "finance.ReportOverConcurrencyDosage")
		if err != nil {
			log.ErrorContextf(ctx, "Invoke finance.ReportOverConcurrencyDosage error: %+v, req: %+v", err, req)
			return err
		}
	} else { // 非专属并发，上报超并发
		pf.StartElapsed(ctx, "finance.ReportOverMinuteDosage")
		req := &finance.ReportOverMinuteDosageReq{
			Biz: &finance.Biz{BizType: finance.BizType_BIZ_TYPE_LKE, SubBizType: financeSubBizTypeConcurrency},
			Account: &finance.Account{
				Sid: finance.SID(sid),
				Uin: fmt.Sprintf("%d", uin),
			},
			ModelName: dosage.ModelName,
			UsageTime: uint64(dosage.UsageTime.Unix()),
			Query:     dosage.Content,
			BizAppId:  fmt.Sprintf("%d", dosage.AppID),
		}
		log.InfoContextf(ctx, "REQ|ReportOverMinuteDosage request: %d, %v", corpID, req)
		rsp, err := d.financeCli.ReportOverMinuteDosage(ctx, req)
		pf.AppendSpanElapsed(ctx, "finance.ReportOverMinuteDosage")
		log.InfoContextf(ctx, "RESP|ReportOverMinuteDosage %v, ERR: %v, %s", rsp, err, time.Since(t0).String())
		if err != nil {
			log.ErrorContextf(ctx, "Invoke finance.ReportOverMinuteDosage error: %+v, req: %+v", err, req)
			return err
		}
	}
	return nil
}
