package service

import (
	"context"
	"fmt"
	"strings"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/common/v3/plugins/i18n"
	"git.woa.com/dialogue-platform/go-comm/clues"
	"git.woa.com/dialogue-platform/go-comm/encode"
	admin "git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_admin_config_server"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_knowledge_config_server"
	pb "git.woa.com/ivy/protobuf/trpc-go/qbot/qbot/chat"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	"git.woa.com/ivy/qbot/qbot/chat/internal/dao"
	"git.woa.com/ivy/qbot/qbot/chat/internal/eventbus/botsession"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
	"git.woa.com/ivy/qbot/qbot/chat/pkg"
	"git.woa.com/ivy/qbot/qbot/chat/pkg/rate"
)

// IsTransferIntent 判断是否意图转人工
func (s *Service) IsTransferIntent(ctx context.Context, req *pb.IsTransferIntentReq) (*pb.IsTransferIntentRsp, error) {
	log.InfoContextf(ctx, "IsTransferIntent Req:%+v", req)
	app, err := s.dao.GetAppByAppKey(ctx, dao.AppReleaseScene, req.GetBotAppKey())
	if err != nil {
		return nil, pkg.ErrInternalServerError
	}
	if app == nil {
		return nil, pkg.ErrRobotNotExist
	}

	m := app.GetModel(ctx, model.AppTypeKnowledgeQA, model.ModelTypeTransfer)
	prompt, err := s.dao.TextTruncate(ctx, m, botsession.PromptCtx{Question: req.GetContent()})
	if err != nil {
		return nil, pkg.ErrInternalServerError
	}
	requestID := model.RequestID(ctx, "IsTransferIntent.SessionID", encode.GenerateUUID()) // 非流式调用
	message := m.WrapMessages("", nil, prompt)
	reqLLM := m.NewLLMRequest(requestID, message)
	rsp, err := s.dao.SimpleChat(ctx, reqLLM) // 转人工意图判断
	if err != nil {
		return nil, pkg.ErrInternalServerError
	}

	transfer := strings.TrimSpace(rsp.GetMessage().GetContent())
	log.InfoContextf(ctx, "IsTransferIntent: %s vs %s", transfer, config.App().Session.IntentTransferKeyword)
	return &pb.IsTransferIntentRsp{Hit: transfer == config.App().Session.IntentTransferKeyword}, nil
}

// RateMsgRecord 消息点赞点踩
func (s *Service) RateMsgRecord(ctx context.Context, req *pb.RateMsgRecordReq) (*pb.RateMsgRecordRsp, error) {
	log.InfoContextf(ctx, "RateMsgRecord req:%+v", req)
	app, err := s.dao.GetAppByAppKey(ctx, dao.AppReleaseScene, req.GetBotAppKey())
	if err != nil || app == nil {
		return nil, pkg.ErrRobotNotExist
	}
	r, err := s.dao.GetMsgRecordByRecordID(ctx, req.GetRecordId(), app.GetAppBizId())
	if err != nil || r == nil {
		return nil, pkg.ErrInvalidMsgRecord
	}
	if !r.GetCanRating(ctx, app.GetAppType()) {
		return nil, pkg.ErrInvalidMsgRecord
	}
	if r.IsToAPIVisitor() {
		visitor, err := s.dao.GetVisitorByID(ctx, app.GetAppBizId(), r.ToID)
		if err != nil || visitor == nil {
			return nil, pkg.ErrVisitorNotExist
		}
	} else {
		session, err := s.dao.GetSession(ctx, model.SessionTypeAny, r.SessionID)
		if err != nil || session == nil {
			return nil, pkg.ErrSessionNotFound
		}
		if session.BotBizID != app.GetAppBizId() {
			return nil, pkg.ErrInvalidMsgRecord
		}
	}
	if req.GetScore() == uint32(model.ScoreDownvote) {
		if err = s.addUnsatisfiedReply(ctx, app.GetAppBizId(), r, req.GetReasons()); err != nil {
			log.ErrorContextf(ctx, "add unsatisfied reply error: %+v", err)
			return nil, pkg.ErrRateMsgRecordFailed
		}
	}
	if err = s.dao.Rating(ctx, r.ID, model.ScoreType(req.GetScore()), req.GetReasons(), app.GetAppBizId()); err != nil {
		return nil, pkg.ErrRateMsgRecordFailed
	}
	return &pb.RateMsgRecordRsp{}, nil
}

func (s *Service) addUnsatisfiedReply(
	ctx context.Context,
	botBizID uint64,
	r *model.MsgRecord,
	reasons []string,
) error {
	related, err := s.dao.GetMsgRecordByRecordID(ctx, r.RelatedRecordID, r.BotBizID)
	if err != nil || related == nil {
		return pkg.ErrInvalidMsgRecord
	}

	ctxs, err := s.getContexts(ctx, *r)
	if err != nil {
		return err
	}

	req := &bot_knowledge_config_server.AddUnsatisfiedReplyReq{
		BotBizId: botBizID,
		RecordId: r.RecordID,
		Question: related.Content,
		Answer:   r.GetSafeContent(ctx),
		Context:  ctxs,
		Reasons:  reasons,
	}
	if req.Question == "" && len(related.FileInfos) != 0 && related.FileInfos != "[]" {
		req.Question = related.FileInfos
	}
	if req.Question == "" {
		req.Question = i18n.Translate(ctx, config.App().MultiModal.GetCaptionPrompt)
	}

	return s.dao.AddUnsatisfiedReply(ctx, req)
}

// getContexts 获取访客端会话上下文
func (s *Service) getContexts(
	ctx context.Context, r model.MsgRecord,
) ([]*bot_knowledge_config_server.UnsatisfiedReplyContext, error) {
	// 欢迎语可以默认加上
	// 目前仅限访客端点踩，并且效果评测不下发欢迎语，因此不会影响效果评测和坐席搜索点踩
	cfg := config.App().UnsatisfiedReply.ContextLength
	above, err := s.dao.GetMsgRecord(ctx, model.GetMsgRecordParam{
		LastIDCreateTime: r.CreateTime,
		Count:            cfg.Above,
		Types:            []model.RecordType{r.Type, model.RecordTypeGreeting},
		SessionID:        r.SessionID,
		IncludeBotEvil:   true,
	})
	if err != nil {
		return nil, err
	}

	below, err := s.dao.GetMsgRecord(ctx, model.GetMsgRecordParam{
		FirstIDCreateTime: r.CreateTime,
		Count:             cfg.Below,
		Types:             []model.RecordType{r.Type, model.RecordTypeGreeting},
		SessionID:         r.SessionID,
		IncludeBotEvil:    true,
	})
	if err != nil {
		return nil, err
	}

	all := append(above, r)
	all = append(all, below...)
	return helper.Map(all, model.MsgRecord.ToUnsatisfiedReplyContext), nil
}
func (s *Service) checkShareCode(ctx context.Context, shareCode string) error {
	if shareCode == "" {
		log.WarnContextf(ctx, "[param invalid] ShareCode is empty string")
		return pkg.ErrBadRequest
	}
	if !helper.CheckShareCode(shareCode) {
		log.WarnContextf(ctx, "[param invalid] GetSharePageWsToken, ShareCode:%s is invalid", shareCode)
	}
	reached, err := rate.Increment(ctx, shareCode, 1) // 限频限流
	if err != nil || reached {
		log.WarnContextf(ctx, "ShareCode:%s SSE rate limit reached", shareCode)
		return pkg.ErrAppRateLimiter
	}
	return nil
}

// GetSharePageWsToken 校验分享链接并获取地址
func (s *Service) GetSharePageWsToken(ctx context.Context,
	req *pb.GetSharePageWsTokenReq) (*pb.GetSharePageWsTokenRsp, error) {
	ctx = clues.NewTrackContext(ctx)
	defer func(ctx *context.Context) { clues.Flush(*ctx) }(&ctx)
	clues.AddTrackData(ctx, "GetSharePageWsTokenReq", req)
	// 1. 校验并获取应用信息
	app, err := s.validateAndGetApp(ctx, req.GetShareCode())
	if err != nil {
		return nil, err
	}
	// 2. 余量查询, 这个位置只下发余量, 不拦截
	b := s.getModelBalance(ctx, app)
	// 3. 下发token给前端，
	reqToken := pb.GetWsTokenReq{Type: uint32(model.ConnTypeVisitor), BotAppKey: fmt.Sprintf("%d",
		app.GetAppBizId()), VisitorBizId: fmt.Sprintf("%d", pkg.StaffID(ctx)),
	}
	resp, err := s.GetWsToken(ctx, &reqToken)
	clues.AddTrackE(ctx, "s.GetWsToken", clues.M{"reqToken": &reqToken, "resp": resp}, err)
	if err != nil {
		return nil, err
	}
	params := make([]uint64, 0)
	params = append(params, app.Id)
	all, err := s.dao.GetExperienceRecommendQuestion(ctx, params)
	clues.AddTrackE(ctx, "dao.GetExperienceRecommendQuestion", clues.M{"params": params, "all": all}, err)
	if err != nil {
		return nil, pkg.ErrInternalServerError
	}
	recommendQuestions := make([]string, 0)
	for i := 0; i < len(all); i++ {
		recommendQuestions = append(recommendQuestions, all[i].Question)
	}
	experienceRecommendQuestions := make([]*pb.ExperienceRecommendQuestion, 0)
	for i := 0; i < len(all); i++ {
		experienceRecommendQuestion := &pb.ExperienceRecommendQuestion{
			Question: all[i].Question, Content: all[i].Content, ShowType: all[i].ShowType,
		}
		experienceRecommendQuestions = append(experienceRecommendQuestions, experienceRecommendQuestion)
	}
	reqLimit := &admin.GetAppChatInputNumReq{
		AppBizId:  app.GetAppBizId(),
		ModelName: app.GetModelName(),
	}
	inputLenLimit := s.dao.GetAppChatInputLimit(ctx, reqLimit)
	singleWorkflowID := app.GetKnowledgeQa().GetWorkflowId()
	singleWorkflowInfo := &pb.KnowledgeQaSingleWorkflow{WorkflowId: singleWorkflowID}
	if singleWorkflowID != "" {
		singleWorkflowInfo, _ = s.getWorkflowInfoByBizID(ctx, app.GetAppBizId(), singleWorkflowID)
		singleWorkflowInfo.Status = "PUBLISHED"
		singleWorkflowInfo.IsEnable = true
	}
	return &pb.GetSharePageWsTokenRsp{
		Token: resp.Token, AppType: app.AppType, Name: app.BaseConfig.Name, BotBizId: app.GetAppBizId(),
		SeatBizId: pkg.StaffID(ctx), Avatar: app.BaseConfig.Avatar, Greeting: app.GetGreeting(),
		RecommendQuestions: recommendQuestions, ExperienceRecommendQuestion: experienceRecommendQuestions,
		Balance: float64(b), InputLenLimit: inputLenLimit, Pattern: app.GetKnowledgeQa().GetPattern(),
		SingleWorkflow: singleWorkflowInfo,
	}, nil
}

// validateAndGetApp 校验分享码并获取应用信息
func (s *Service) validateAndGetApp(ctx context.Context, shareCode string) (*model.App, error) {
	// 校验分享码
	if err := s.checkShareCode(ctx, shareCode); err != nil {
		return nil, err
	}

	// 获取机器人ID
	robotID, err := s.dao.GetRobotIDByShareCode(ctx, shareCode)
	clues.AddTrackE(ctx, "dao.GetRobotIDByShareCode", robotID, err)
	if err != nil {
		return nil, err
	}
	if robotID == 0 {
		return nil, pkg.ErrRobotNotExist
	}

	// 获取应用信息
	app, err := s.dao.GetAppByBizID(ctx, dao.AppReleaseScene, robotID)
	clues.AddTrackE(ctx, "dao.GetAppByBizID", app, err)
	if err != nil {
		return nil, err
	}
	if app == nil {
		return nil, pkg.ErrRobotNotExist
	}
	if app.Status != 2 {
		return nil, pkg.ErrRobotStopOrArrearage
	}

	return app, nil
}

func (s *Service) getModelBalance(ctx context.Context, app *model.App) int {
	// acctType := "KnowledgeQA"
	// if app.GetAppType() == "summary" || app.GetAppType() == "classify" {
	//	acctType = "KnowledgeSummary"
	// }
	// accTypeList := []model.AccountType{{Type: acctType}}
	// a, err := s.dao.DescribeAccountBalance(ctx, app.GetCorpId(), accTypeList)
	//
	// var balance float64
	// if err == nil && len(a.AccountList) > 0 {
	//	balance = a.AccountList[0].Total
	// }
	// // 体验有余量, 直接使用
	// if balance > 0 {
	//	return 1
	// }
	mn := app.GetMainModelName()
	st := s.dao.GetModelStatus(ctx, app.GetCorpId(), mn)
	clues.AddT(ctx, "getModelBalance.dao.GetModelToken",
		clues.M{"corpID": app.GetCorpId(), "modelName": mn, "status": st, "appType": app.GetAppType()})
	// 明确账户不可用, 返回余量小于0
	if st == 1 {
		return -1
	}
	return 1
}

// GetTokenUsage 获取token用量
func (s *Service) GetTokenUsage(ctx context.Context, req *pb.TokenUsageReq) (*pb.TokenUsageRsp, error) {
	log.DebugContextf(ctx, "GetTokenUsage: %+v", req)
	rsp := &pb.TokenUsageRsp{}
	return rsp, nil
}

// GetAppBizIDByRecord 通过会话记录获取应用ID
func (s *Service) GetAppBizIDByRecord(ctx context.Context,
	req *pb.GetAppBizIDByRecordReq) (*pb.GetAppBizIDByRecordRsp, error) {
	rsp := &pb.GetAppBizIDByRecordRsp{}
	// startTimestamp := req.GetStartTimestamp()
	// var ids []uint64
	// records, err := s.dao.GetBotRecordWithTimestamp(ctx, time.Unix(startTimestamp, 0))
	// if err != nil {
	// 	return rsp, err
	// }
	// for _, record := range records {
	//	if record.BotBizID == 0 {
	//		continue
	//	}
	//	ids = append(ids, record.BotBizID)
	// }
	// rsp.AppBizId = helper.Unique(ids)
	return rsp, nil
}

func (s *Service) getWorkflowInfoByBizID(ctx context.Context, bizID uint64,
	singleWorkflowID string) (*pb.KnowledgeQaSingleWorkflow, error) {
	workflowInfos, err := s.dao.GetWorkflowByIDs(ctx, bizID, []string{singleWorkflowID})
	if err != nil {
		log.WarnContextf(ctx, "GetWorkflowByIDs err:%v", err)
		return &pb.KnowledgeQaSingleWorkflow{}, err
	}
	workflowInfo, found := workflowInfos[singleWorkflowID]
	if !found {
		log.WarnContextf(ctx, "workflowInfo not found, singleWorkflowID:%s", singleWorkflowID)
		return &pb.KnowledgeQaSingleWorkflow{}, nil
	}
	return &pb.KnowledgeQaSingleWorkflow{
		WorkflowId:   workflowInfo.WorkflowId,
		WorkflowName: workflowInfo.Name,
		WorkflowDesc: workflowInfo.Desc,
		Status:       workflowInfo.Status,
		IsEnable:     workflowInfo.IsEnable,
	}, nil
}
