// chat
//
// @(#)experience_sse.go  Wednesday, May 15, 2024
// Copyright(c) 2024, leyton@Tencent. All rights reserved.

package service

import (
	"context"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/baicaoyuan/apex/proto/message"
	"git.woa.com/dialogue-platform/go-comm/clues"
	"git.woa.com/dialogue-platform/go-comm/pf"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/dao"
	"git.woa.com/ivy/qbot/qbot/chat/internal/eventbus"
	"git.woa.com/ivy/qbot/qbot/chat/internal/metrics"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model/event"
	"git.woa.com/ivy/qbot/qbot/chat/pkg"
	jsoniter "github.com/json-iterator/go"
)

// ExperienceSseEventHandler SSE 事件处理方法
func (s *Service) ExperienceSseEventHandler(ctx context.Context, ev *message.Event) (rev *message.Event, err error) {
	ctx = pf.NewPipelineFlowContext(ctx)
	defer func(ctx *context.Context) { clues.Flush(*ctx) }(&ctx)
	log.InfoContextf(ctx, "ExperienceSseEventHandler req: %+v", helper.Object2String(ev))
	pf.StartElapsed(ctx, "SSE."+event.EventExperience)
	clues.AddT(ctx, "ExperienceSseEventHandler", ev)
	defer reportSseMetrics(ctx)
	pkg.WithInterfaceType(ctx, "sse")

	switch ev.GetType() {
	case message.EventTypeDisconnect:
		return s.handleDisconnect(ctx, ev)
	case message.EventTypeConnect:
		return s.handleExperienceConnect(ctx, ev)
	default:
		return &message.Event{}, nil
	}
}

// handleExperienceConnect authenticates, initializes, and pushes the dialog event.
func (s *Service) handleExperienceConnect(ctx context.Context, ev *message.Event) (*message.Event, error) {
	req, err := getRequest(ctx, ev)
	req.OriginContent = req.Content
	req.Environment = event.EventExperience
	req.StartTime = time.Now()
	if err != nil {
		return nil, err
	}

	app, err := s.dao.GetAppByAppKey(ctx, dao.AppTestScene, req.BotAppKey)
	clues.AddTrackE(ctx, "dao.GetAppByAppKey",
		clues.M{"scene": dao.AppTestScene, "appKey": req.BotAppKey, "app": app}, err)
	if err != nil {
		return nil, err
	}
	if app == nil {
		return nil, pkg.ErrRobotNotExist
	}

	// 从这里开始 send 和  experience 的逻辑是一样的
	if err := s.checkAppBlacklist(app); err != nil {
		return nil, err
	}

	if err := s.enforceRateLimit(ctx, app, req.Content); err != nil {
		metrics.ReportSseClientConnect("0")
		return nil, pkg.ErrAppRateLimiter
	}

	visitor := s.loadVisitor(ctx, app, req.VisitorBizID)

	if err := validateLabels(req.VisitorLabels); err != nil {
		return nil, err
	}

	labels, err := getSseLabels(&req)
	if err != nil {
		return nil, err
	}

	conn := newConn(ev.GetClientId(), app, visitor, labels)

	if err := s.registerClient(ctx, conn, req.Incremental); err != nil {
		return nil, pkg.ErrInternalServerError
	}

	reqByte, _ := jsoniter.Marshal(req)
	if err := eventbus.Push(ctx, conn, event.EventDialog, reqByte); err != nil {
		return nil, pkg.ErrInternalServerError
	}

	return &message.Event{}, nil
}
