package service

import (
	"context"
	"fmt"
	"time"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/common/v3/errors"
	"git.woa.com/dialogue-platform/common/v3/sync/errgroupx"
	"git.woa.com/dialogue-platform/go-comm/encode"
	pb "git.woa.com/ivy/protobuf/trpc-go/qbot/qbot/chat"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	"git.woa.com/ivy/qbot/qbot/chat/internal/dao"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
	"git.woa.com/ivy/qbot/qbot/chat/pkg"
	jsoniter "github.com/json-iterator/go"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common"
	trtc "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/trtc/v20190722"
)

// GetSharePageRTCToken 获取实时通话Token
func (s *Service) GetSharePageRTCToken(ctx context.Context, req *pb.GetSharePageRTCTokenReq) (
	*pb.GetSharePageRTCTokenRsp, error) {
	t0 := time.Now()
	log.InfoContextf(ctx, "REQ|GetSharePageRTCToken %+v", req)
	// 校验分享码
	if err := checkShareCode(ctx, req.GetShareCode()); err != nil {
		return nil, err
	}
	// 通过shareCode获取robotId
	robotID, err := s.getBotBizIDByShareCode(ctx, req.GetShareCode())
	if err != nil {
		return nil, err
	}
	// 获取token
	reqToken := pb.GetRTCTokenReq{BotBizId: robotID, Type: uint32(model.ConnTypeVisitor)}
	resp, err := s.GetRTCToken(ctx, &reqToken)
	if err != nil {
		return nil, err
	}
	log.InfoContextf(ctx, "RESP|GetSharePageRTCToken %s %s", resp.GetToken(), time.Since(t0))
	rsp := &pb.GetSharePageRTCTokenRsp{
		Token:         resp.GetToken(),
		SdkAppId:      resp.GetSdkAppId(),
		UserId:        resp.GetUserId(),
		UserSig:       resp.GetUserSig(),
		RobotUserId:   resp.GetRobotUserId(),
		RobotUserSig:  resp.GetRobotUserSig(),
		AvatarUserId:  resp.GetAvatarUserId(),
		AvatarUserSig: resp.GetAvatarUserSig(),
		RoomId:        resp.GetRoomId(),
	}
	log.InfoContextf(ctx, "RESP|GetSharePageRTCToken %s %s", rsp.GetToken(), time.Since(t0))
	return rsp, nil
}

// GetRTCToken 获取实时通话Token
func (s *Service) GetRTCToken(ctx context.Context, req *pb.GetRTCTokenReq) (*pb.GetRTCTokenRsp, error) {
	t0 := time.Now()
	log.InfoContextf(ctx, "REQ|GetRTCToken %+v", req)
	// 校验用户和bot的关系
	_, err := s.dao.CheckSession(ctx)
	if err != nil {
		if errs.Code(err) == errs.Code(pkg.ErrUserNotLogin) {
			return nil, err
		}
		return nil, pkg.ErrInternalServerError
	}
	// 校验参数botBizID
	app, err := s.getAppByBotBizID(ctx, req.GetBotBizId(), req.GetType())
	if err != nil {
		return nil, err
	}
	// 检查RTC配置
	if err = checkRTCConfig(ctx, req.GetBotBizId(), app); err != nil {
		return nil, err
	}
	// 生成token
	token, rtcToken, err := s.createRTCToken(ctx, app, req.GetType())
	if err != nil {
		return nil, err
	}
	log.InfoContextf(ctx, "RESP|GetRTCToken %s %s", token, time.Since(t0))
	tokenResp := &pb.GetRTCTokenRsp{
		Token:         token,
		SdkAppId:      uint32(rtcToken.SdkAppID),
		UserId:        rtcToken.UserID,
		UserSig:       rtcToken.UserSig,
		RobotUserId:   rtcToken.RobotUserID,
		RobotUserSig:  rtcToken.RobotUserSig,
		AvatarUserId:  rtcToken.AvatarUserID,
		AvatarUserSig: rtcToken.AvatarUserSig,
		RoomId:        rtcToken.RoomID,
	}
	return tokenResp, nil
}

// StartRTCConversation 开启实时通话
func (s *Service) StartRTCConversation(ctx context.Context, req *pb.StartRTCConversationReq) (
	*pb.StartRTCConversationRsp, error) {
	t0 := time.Now()
	log.InfoContextf(ctx, "REQ|StartRTCConversation %+v", req)
	// 校验参数
	if err := checkStartRTCConversationReq(ctx, req); err != nil {
		return nil, err
	}
	// 获取用户信息
	u, err := s.dao.CheckSession(ctx)
	if err != nil {
		if errs.Code(err) == errs.Code(pkg.ErrUserNotLogin) {
			return nil, err
		}
		return nil, pkg.ErrInternalServerError
	}
	// 鉴权token
	rtcToken, err := s.authRTCToken(ctx, req)
	if err != nil {
		return nil, err
	}
	// 获取应用配置
	app, err := s.getAppByBotBizID(ctx, req.GetBotBizId(), rtcToken.ConnType)
	if err != nil {
		return nil, err
	}
	// 检查RTC配置
	if err = checkRTCConfig(ctx, req.GetBotBizId(), app); err != nil {
		return nil, err
	}
	// 检查是否开启AI对话
	taskID := ""
	defer func() {
		if taskID == "" {
			s.releaseRTCConcurrency(ctx, rtcToken)
		}
	}()
	// 检查并发限制
	reached, err := s.dao.LimitRTCConcurrency(ctx, rtcToken)
	if err != nil || reached {
		return nil, pkg.ErrRTCConcurrencyLimiter
	}
	// 获取会话
	session, err := s.getSession(ctx, req, u, rtcToken)
	if err != nil {
		return nil, err
	}
	// 开启AI对话
	request := genStartAIConversationRequest(ctx, req, rtcToken, app, session)
	taskID, err = s.dao.StartAIConversation(trpc.CloneContext(ctx), request)
	if err != nil {
		return nil, err
	}
	// 异步保存任务信息
	go func(ctx context.Context) {
		defer errors.PanicHandler()
		rtcType := helper.When(app.GetKnowledgeQa().GetAiCall().EnableDigitalHuman, model.RTCTypeDigitalHuman,
			model.RTCTypeVoice)
		rtcToken.RTCType = string(rtcType)
		saveErr := s.dao.SaveRTCTask(ctx, taskID, rtcToken)
		if saveErr != nil {
			log.WarnContextf(ctx, "save rtc token failed, taskID:%s error:%v", taskID, saveErr)
		} else {
			log.InfoContextf(ctx, "save rtc token success, taskID:%s", taskID)
		}
	}(trpc.CloneContext(ctx))
	log.InfoContextf(ctx, "RESP|StartRTCConversation %s taskID:%v", time.Since(t0), taskID)
	rsp := &pb.StartRTCConversationRsp{
		TaskId: taskID,
	}
	return rsp, nil
}

// StopRTCConversation 关闭实时通话
func (s *Service) StopRTCConversation(ctx context.Context, req *pb.StopRTCConversationReq) (
	*pb.StopRTCConversationRsp, error) {
	t0 := time.Now()
	log.InfoContextf(ctx, "REQ|StopRTCConversation %+v", req)
	// 校验参数
	if len(req.GetTaskId()) == 0 {
		log.WarnContextf(ctx, "TaskId not exist or empty")
		return nil, pkg.ErrBadRequest
	}
	// 校验任务是否存在，防止安全扫描大量请求
	if config.App().RTC.EnableTaskValid {
		rtcToken, err := s.dao.GetRTCTask(ctx, req.GetTaskId())
		if err != nil {
			log.WarnContextf(ctx, "StopRTCConversation| get rtc task failed, taskID:%s", req.GetTaskId())
			return nil, pkg.ErrInternalServerError
		}
		if rtcToken == nil {
			log.InfoContextf(ctx, "RESP|StopRTCConversation task not found, cost:%s taskID:%v", time.Since(t0),
				req.GetTaskId())
			return nil, pkg.ErrRTCTaskNotExist
		}
	}
	// 停止AI对话
	err := s.dao.StopAIConversation(ctx, req.GetTaskId())
	if err != nil {
		return nil, pkg.ErrInternalServerError
	}
	// 异步清理任务信息
	go func(ctx context.Context) {
		defer errors.PanicHandler()
		clearErr := s.dao.ClearRTCTask(ctx, req.GetTaskId())
		if clearErr != nil {
			log.WarnContextf(ctx, "StopRTCConversation| clear rtc task failed, taskID:%s", req.GetTaskId())
			return
		}
		log.InfoContextf(ctx, "StopRTCConversation| clear rtc task success, taskID:%s", req.GetTaskId())
	}(trpc.CloneContext(ctx))
	log.InfoContextf(ctx, "RESP|StopRTCConversation %s taskID:%v", time.Since(t0), req.GetTaskId())
	return &pb.StopRTCConversationRsp{}, nil
}

func (s *Service) getAppByBotBizID(ctx context.Context, botBizID uint64, typ uint32) (*model.App, error) {
	if botBizID == 0 {
		log.WarnContextf(ctx, "[param invalid] getAppByBotBizID, BotBizId:%d is invalid", botBizID)
		return nil, pkg.ErrBadRequest
	}
	// 区分体验环境和正式环境
	connType := model.ConnType(typ)
	scene := dao.AppReleaseScene
	if connType == model.ConnTypeExperience {
		scene = dao.AppTestScene
	}
	// 获取应用配置
	app, err := s.dao.GetAppByBizID(ctx, scene, botBizID)
	if err != nil {
		return app, err
	}
	if app == nil {
		log.WarnContextf(ctx, "[param invalid] getAppByBotBizID, BotBizId:%d app not exist", botBizID)
		return nil, pkg.ErrRobotNotExist
	}
	// 正式环境判断应用是否开启
	if scene == dao.AppReleaseScene && app.Status != 2 {
		log.WarnContextf(ctx, "[param invalid] getAppByBotBizID, BotBizId:%d app is stoped", botBizID)
		return nil, pkg.ErrRobotStopOrArrearage
	}
	return app, nil
}

func (s *Service) getBotBizIDByShareCode(ctx context.Context, shareCode string) (uint64, error) {
	// 1. 校验分享码
	if err := checkShareCode(ctx, shareCode); err != nil {
		return 0, err
	}
	// 通过shareCode获取robotId
	robotID, err := s.dao.GetRobotIDByShareCode(ctx, shareCode)
	if err != nil {
		return 0, err
	}
	if robotID == 0 {
		return 0, pkg.ErrRobotNotExist
	}
	return robotID, nil
}

func (s *Service) releaseRTCConcurrency(ctx context.Context, token *model.RTCToken) {
	err := s.dao.ReleaseRTCConcurrency(ctx, token)
	if err != nil {
		log.WarnContextf(ctx, "appID:%d corpID:%d release rtc concurrency failed, error: %v", token.BotBizID,
			token.CorpID, err)
	}
}

func (s *Service) getSession(ctx context.Context, req *pb.StartRTCConversationReq, user *model.CorpStaff,
	token *model.RTCToken) (*model.Session, error) {
	// 未传递sessionId，则创建一个新的session
	if req.GetSessionId() == "" {
		typ := model.SessionTypeNormal
		if token.ConnType == uint32(model.ConnTypeExperience) {
			typ = model.SessionTypeExperience
		}
		// 获取token
		reqSession := pb.CreateSessionReq{BotBizId: token.BotBizID, Type: uint32(typ), SeatBizId: req.GetSeatBizId()}
		resp, err := s.CreateSession(ctx, &reqSession)
		if err != nil {
			return nil, err
		}
		return &model.Session{
			SessionID:    resp.GetSessionId(),
			VisitorID:    user.ID,
			VisitorBizID: user.BusinessID,
		}, nil
	}
	// 检查session是否存在
	session, err := s.dao.GetSession(ctx, model.SessionTypeAny, req.GetSessionId())
	if err != nil {
		return nil, pkg.ErrInternalServerError
	}
	if !session.IsVisitor(user.ID) {
		return nil, pkg.ErrSessionNotFound
	}
	if session.BotBizID != token.BotBizID {
		return nil, pkg.ErrSessionNotFound
	}
	return session, nil
}

func checkShareCode(ctx context.Context, shareCode string) error {
	if shareCode == "" {
		log.WarnContextf(ctx, "[param invalid] ShareCode is empty string")
		return pkg.ErrBadRequest
	}
	if !helper.CheckShareCode(shareCode) {
		log.WarnContextf(ctx, "[param invalid] GetSharePageWsToken, ShareCode:%s is invalid", shareCode)
	}
	return nil
}

func checkRTCConfig(ctx context.Context, botBizID uint64, app *model.App) error {
	// 判断是否开启语音互动
	aiCall := app.GetKnowledgeQa().GetAiCall()
	if aiCall == nil || !aiCall.EnableVoiceInteract {
		log.WarnContextf(ctx, "RTC botBizID:%d not enable rtc", botBizID)
		return pkg.ErrAudioInteractNotEnabled
	}
	// 判断是否开启语音通话
	if !aiCall.EnableVoiceCall {
		log.WarnContextf(ctx, "RTC botBizID:%d not enable rtc", botBizID)
		return pkg.ErrRTCNotEnabled
	}
	// 检查声音配置
	if aiCall.Voice.VoiceType == 0 {
		log.WarnContextf(ctx, "RTC botBizID:%d not config voice", botBizID)
		return pkg.ErrRTCVoiceNotConfig
	}
	// 检查数字人配置
	if aiCall.EnableDigitalHuman && aiCall.DigitalHuman.AssetKey == "" {
		log.WarnContextf(ctx, "RTC botBizID:%d not config digitalHuman", botBizID)
		return pkg.ErrRTCDigitalHumanNotConfig
	}
	return nil
}

func (s *Service) createRTCToken(ctx context.Context, app *model.App, typ uint32) (string, *model.RTCToken, error) {
	// 生成用户ID
	userID := encode.GenerateUUID()
	robotUserID := userID + config.GetRTCRobotUserSuffix()
	avatarUserID := userID + config.GetRTCAvatarUserSuffix()
	// / 获取sdkAppID、userKey、userSigTTL
	sdkAppID, userKey, userSigTTL := config.GetTRTCSdkAppID(), config.GetTRTCUserKey(), config.GetRTCUserSigTTL()
	// 生成userSig
	var userSig, robotUserSig, avatarUserSig string
	roomID := uint32(0)
	g, ctx := errgroupx.WithContext(ctx)
	g.Go(func() error {
		var err error
		userSig, err = s.dao.GenRTCUserSig(trpc.CloneContext(ctx), sdkAppID, userKey, userID, userSigTTL)
		return err
	})
	g.Go(func() error {
		var err error
		robotUserSig, err = s.dao.GenRTCUserSig(trpc.CloneContext(ctx), sdkAppID, userKey, robotUserID, userSigTTL)
		return err
	})
	g.Go(func() error {
		var err error
		avatarUserSig, err = s.dao.GenRTCUserSig(trpc.CloneContext(ctx), sdkAppID, userKey, avatarUserID, userSigTTL)
		return err
	})
	g.Go(func() error {
		// 通过应用+房间路由测试特性功能
		roomMap := config.App().RTC.RoomMap
		if len(roomMap) > 0 {
			if roomID = roomMap[app.GetAppBizId()]; roomID > 0 {
				log.InfoContextf(trpc.CloneContext(ctx), "Get special roomID, appID:%d roomID:%d",
					app.GetAppBizId(), roomID)
				return nil
			}
		}
		var err error
		roomID, err = s.dao.AssignRoomID(trpc.CloneContext(ctx))
		return err
	})
	err := g.Wait()
	if err != nil {
		return "", nil, pkg.ErrInternalServerError
	}
	// 生成token和房间ID
	token := encode.GenerateUUID()
	rtcConn := &model.RTCToken{
		ConnType:      typ,
		Token:         token,
		BotBizID:      app.GetAppBizId(),
		CorpID:        app.GetCorpId(),
		SdkAppID:      sdkAppID,
		UserID:        userID,
		UserSig:       userSig,
		RobotUserID:   robotUserID,
		RobotUserSig:  robotUserSig,
		AvatarUserID:  avatarUserID,
		AvatarUserSig: avatarUserSig,
		RoomID:        roomID,
		Timestamp:     uint64(time.Now().Unix()),
	}
	// 保存token
	err = s.dao.SaveTRCToken(trpc.CloneContext(ctx), token, rtcConn)
	if err != nil {
		return "", nil, pkg.ErrInternalServerError
	}
	return token, rtcConn, nil
}

func (s *Service) authRTCToken(ctx context.Context, req *pb.StartRTCConversationReq) (*model.RTCToken, error) {
	token := req.GetTempToken()
	rtcToken, err := s.dao.AuthRTCToken(trpc.CloneContext(ctx), token)
	if err != nil || rtcToken == nil {
		return nil, pkg.ErrAuthTokenFailed
	}
	// 检查机器人bizID是否匹配
	if rtcToken.BotBizID != req.GetBotBizId() {
		log.WarnContextf(ctx, "StartRTCConversation botBizID not match, token: %s, botBizID: %d, "+
			"token.BotBizID: %d", token, req.GetBotBizId(), rtcToken.BotBizID)
		return nil, pkg.ErrAuthTokenFailed
	}
	return rtcToken, nil
}

func checkStartRTCConversationReq(ctx context.Context, req *pb.StartRTCConversationReq) error {
	if len(req.GetTempToken()) == 0 {
		log.WarnContextf(ctx, "Token not exist or empty")
		return pkg.ErrBadRequest
	}
	if req.GetBotBizId() == 0 {
		log.WarnContextf(ctx, "BotBizId not exist or empty")
		return pkg.ErrBadRequest
	}
	if err := checkSTTLanguage(ctx, req.GetSttConfig().GetLanguage()); err != nil {
		return err
	}
	if _, err := parseCustomVariables(ctx, req.GetCustomVariables()); err != nil {
		return err
	}
	return nil
}

func checkSTTLanguage(ctx context.Context, language string) error {
	sttLanguages := config.GetSTTLanguages()
	if language == "" || len(sttLanguages) == 0 {
		return nil
	}
	matchLanguage := false
	for _, lang := range sttLanguages {
		if language == lang {
			matchLanguage = true
			break
		}
	}
	if !matchLanguage {
		log.WarnContextf(ctx, "Language not match, language: %s, sttLanguages: %v",
			language, sttLanguages)
		return pkg.ErrBadRequest
	}
	return nil
}

func genStartAIConversationRequest(ctx context.Context, req *pb.StartRTCConversationReq, rtcToken *model.RTCToken,
	app *model.App, session *model.Session) *trtc.StartAIConversationRequest {
	request := trtc.NewStartAIConversationRequest()
	request.SessionId = common.StringPtr(rtcToken.Token)
	request.SdkAppId = common.Uint64Ptr(rtcToken.SdkAppID)
	request.RoomId = common.StringPtr(fmt.Sprintf("%d", rtcToken.RoomID))
	// 机器人配置
	request.AgentConfig = &trtc.AgentConfig{
		UserId:       common.StringPtr(rtcToken.RobotUserID),
		UserSig:      common.StringPtr(rtcToken.RobotUserSig),
		TargetUserId: common.StringPtr(rtcToken.UserID),
	}
	// 欢迎语
	if app.GetGreeting() != "" {
		request.AgentConfig.WelcomeMessage = common.StringPtr(app.GetGreeting())
	}
	// agent配置
	agentConfig := config.App().RTC.AgentConfig
	if agentConfig.Enable {
		if agentConfig.MaxIdleTime > 0 {
			request.AgentConfig.MaxIdleTime = common.Uint64Ptr(agentConfig.MaxIdleTime)
		}
		if agentConfig.InterruptSpeechDuration > 0 {
			request.AgentConfig.InterruptSpeechDuration = common.Uint64Ptr(agentConfig.InterruptSpeechDuration)
		}
		if agentConfig.AllowOneWord {
			request.AgentConfig.FilterOneWord = common.BoolPtr(false)
		}
		if agentConfig.TurnDetectionMode > 0 {
			request.AgentConfig.TurnDetectionMode = common.Uint64Ptr(agentConfig.TurnDetectionMode)
		}
		if agentConfig.FilterBracketsContent > 0 {
			request.AgentConfig.FilterBracketsContent = common.Uint64Ptr(agentConfig.FilterBracketsContent)
		}
	}
	// 语音识别配置
	if req.GetSttConfig().GetLanguage() != "" {
		request.STTConfig = &trtc.STTConfig{
			Language: common.StringPtr(req.GetSttConfig().GetLanguage()),
		}
	}
	sttConfig := config.App().RTC.STTConfig
	if sttConfig.Enable {
		if request.STTConfig == nil {
			request.STTConfig = &trtc.STTConfig{
				Language: common.StringPtr(config.GetSTTLanguage()),
			}
		}
		if sttConfig.VadSilenceTime >= 240 && sttConfig.VadSilenceTime <= 2000 {
			request.STTConfig.VadSilenceTime = common.Uint64Ptr(sttConfig.VadSilenceTime)
		}
		if sttConfig.HotWordList != "" {
			request.STTConfig.HotWordList = common.StringPtr(sttConfig.HotWordList)
		}
	}
	// 大模型配置
	request.LLMConfig = common.StringPtr(genLLMConfig(ctx, app, session, rtcToken, req.GetCustomVariables()))
	// 语音合成配置
	request.TTSConfig = common.StringPtr(genTTSConfig(ctx, app))
	// 数字人配置
	request.AvatarConfig = common.StringPtr(genAvatarConfig(ctx, app, rtcToken))
	return request
}

func genLLMConfig(ctx context.Context, app *model.App, session *model.Session, rtcToken *model.RTCToken,
	customVariables string) string {
	apiURL := config.App().LLM.APIURL
	if rtcToken.ConnType == uint32(model.ConnTypeExperience) {
		apiURL = config.App().LLM.APIExpURL
	}
	llmConfig := &model.LLMConfig{
		LLMType:           model.LLMTypeLKE,
		APIKey:            app.AppKey,
		APIUrl:            apiURL,
		Timeout:           config.GetLLMTimout(),
		SessionID:         session.SessionID,
		VisitorBizID:      fmt.Sprintf("%d", session.VisitorBizID),
		Streaming:         true,
		IsRetThoughtEvent: config.App().LLM.IsRetThoughtEvent,
	}
	// 自定义变量
	variables, _ := parseCustomVariables(ctx, customVariables)
	if len(variables) > 0 {
		llmConfig.CustomVariables = variables
	}
	configStr := helper.Object2String(llmConfig)
	log.InfoContextf(ctx, "llm config is: %s", configStr)
	return configStr
}

func genTTSConfig(ctx context.Context, app *model.App) string {
	tts := config.App().TTS
	ttsConfig := &model.TTSConfig{
		TTSType:   model.TTSTypeTencent,
		AppID:     uint64(tts.AppID),
		SecretID:  tts.SecretID,
		SecretKey: tts.SecretKey,
		VoiceType: app.GetKnowledgeQa().GetAiCall().GetVoice().GetVoiceType(),
	}
	configStr := helper.Object2String(ttsConfig)
	log.InfoContextf(ctx, "tts config is: %s", configStr)
	return configStr
}

func genAvatarConfig(ctx context.Context, app *model.App, rtcToken *model.RTCToken) string {
	if !app.GetKnowledgeQa().GetAiCall().EnableDigitalHuman {
		log.InfoContextf(ctx, "avatar not enable")
		return ""
	}
	digitalHuman := config.App().DigitalHuman
	avatarConfig := &model.AvatarConfig{
		AvatarType:         model.AvatarTypeTencent,
		Appkey:             digitalHuman.AppKey,
		AccessToken:        digitalHuman.AccessToken,
		AssetVirtualmanKey: app.GetKnowledgeQa().GetAiCall().GetDigitalHuman().GetAssetKey(),
		DriverType:         config.GetDigitalHumanDriverType(),
		AvatarUserID:       rtcToken.AvatarUserID,
		AvatarUserSig:      rtcToken.AvatarUserSig,
		SpeechParam: model.SpeechParam{
			TimbreKey: app.GetKnowledgeQa().GetAiCall().GetVoice().GetTimbreKey(),
		},
	}
	configStr := helper.Object2String(avatarConfig)
	log.InfoContextf(ctx, "avatar config is: %s", configStr)
	return configStr
}

func parseCustomVariables(ctx context.Context, customVariables string) (map[string]string, error) {
	variablesMap := make(map[string]string)
	if customVariables == "" {
		return nil, nil
	}
	err := jsoniter.Unmarshal([]byte(customVariables), &variablesMap)
	if err != nil {
		log.WarnContextf(ctx, "parse custom variables error: %v", err)
		return nil, pkg.ErrBadRequest
	}
	return variablesMap, nil
}
