// chat
//
// @(#)send_sse.go  Wednesday, May 15, 2024
// Copyright(c) 2024, leyton@Tencent. All rights reserved.

package service

import (
	"context"
	"encoding/base64"
	"strings"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/baicaoyuan/apex/proto/message"
	"git.woa.com/dialogue-platform/go-comm/clues"
	"git.woa.com/dialogue-platform/go-comm/pf"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	"git.woa.com/ivy/qbot/qbot/chat/internal/dao"
	"git.woa.com/ivy/qbot/qbot/chat/internal/eventbus"
	"git.woa.com/ivy/qbot/qbot/chat/internal/metrics"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model/event"
	"git.woa.com/ivy/qbot/qbot/chat/pkg"
	jsoniter "github.com/json-iterator/go"
	"go.opentelemetry.io/otel/trace"
)

// SseEventHandler processes SSE connect/disconnect events.
func (s *Service) SseEventHandler(ctx context.Context, ev *message.Event) (*message.Event, error) {
	ctx = pf.NewPipelineFlowContext(ctx)
	defer func(ctx *context.Context) { clues.Flush(*ctx) }(&ctx)
	pf.StartElapsed(ctx, "SSE."+event.EventSend)
	attachMetrics(ctx, ev)
	defer reportSseMetrics(ctx)

	switch ev.GetType() {
	case message.EventTypeDisconnect:
		return s.handleDisconnect(ctx, ev)
	case message.EventTypeConnect:
		return s.handleConnect(ctx, ev)
	default:
		return &message.Event{}, nil
	}
}

// handleDisconnect removes a disconnected client.
func (s *Service) handleDisconnect(ctx context.Context, ev *message.Event) (*message.Event, error) {
	_ = s.dao.DelSseClient(ctx, ev.GetClientId())
	return &message.Event{}, nil
}

// handleConnect authenticates, initializes, and pushes the dialog event.
func (s *Service) handleConnect(ctx context.Context, ev *message.Event) (*message.Event, error) {
	req, err := getRequest(ctx, ev)
	if err != nil {
		return nil, err
	}
	// 设置自定义 TraceID
	customTraceID := s.dao.GetRequestInterruptTraceID(ctx, req.RequestID)
	if len(customTraceID) > 0 {
		ctx = SetCustomTraceID(ctx, customTraceID)
	}

	app, err := s.loadApp(ctx, req.BotAppKey)
	if err != nil {
		return nil, err
	}

	if err := s.checkAppBlacklist(app); err != nil {
		return nil, err
	}

	if err := s.enforceRateLimit(ctx, app, req.Content); err != nil {
		metrics.ReportSseClientConnect("0")
		return nil, pkg.ErrAppRateLimiter
	}

	visitor := s.loadVisitor(ctx, app, req.VisitorBizID)

	if err := validateLabels(req.VisitorLabels); err != nil {
		return nil, err
	}

	labels, err := getSseLabels(&req)
	if err != nil {
		return nil, err
	}

	conn := newConn(ev.GetClientId(), app, visitor, labels)
	if err := s.registerClient(ctx, conn, req.Incremental); err != nil {
		return nil, pkg.ErrInternalServerError
	}

	reqBytes, _ := jsoniter.Marshal(req)
	if err := eventbus.Push(ctx, conn, event.EventDialog, reqBytes); err != nil {
		return nil, pkg.ErrInternalServerError
	}

	return &message.Event{}, nil
}

// loadApp retrieves the App by key.
func (s *Service) loadApp(ctx context.Context, appKey string) (*model.App, error) {
	app, err := s.dao.GetAppByAppKey(ctx, dao.AppReleaseScene, appKey)
	clues.AddTrackE(ctx, "dao.GetAppByAppKey", clues.M{"scene": dao.AppReleaseScene, "appKey": appKey}, err)
	if err != nil {
		return nil, err
	}
	if app == nil {
		return nil, pkg.ErrRobotNotExist
	}
	return app, nil
}

// checkAppBlacklist ensures the App is not blacklisted.
func (s *Service) checkAppBlacklist(app *model.App) error {
	for _, id := range config.App().App.BlackList {
		if app.GetAppBizId() == id {
			return pkg.ErrAppIDInBlacklist
		}
	}
	return nil
}

// enforceRateLimit applies rate limiting by App.
func (s *Service) enforceRateLimit(ctx context.Context, app *model.App, content string) error {
	reached, err := s.limitByApp(ctx, app, content)
	if err != nil {
		return err
	}
	if reached {
		log.InfoContextf(ctx, "botAppKey:%s SSE rate limit reached", app.GetAppKey())
		return pkg.ErrAppRateLimiter
	}
	return nil
}

// loadVisitor retrieves or creates the visitor entity.
func (s *Service) loadVisitor(ctx context.Context, app *model.App, visitorBizID string) *model.Visitor {
	visitor, err := s.dao.MustGetVisitor(ctx, app.GetId(), app.GetAppBizId(), visitorBizID)
	clues.AddTrackE(ctx, "dao.MustGetVisitor", clues.M{"botID": app.GetId(), "botBizID": app.GetAppBizId(),
		"visitorBizID": visitorBizID}, err)
	log.WarnContextf(ctx, "Get visitor err, use zero visitor id")
	visitor = &model.Visitor{} // https://tapd.woa.com/tapd_fe/70080800/task/detail/1070080800075742299
	return visitor
}

// validateLabels checks visitor label count.
func validateLabels(labels []model.Label) error {
	if len(labels) > config.GetLabelCount() {
		return pkg.ErrInvalidVisitorLabel
	}
	return nil
}

// newConn constructs a Conn object.
func newConn(clientID string, app *model.App, visitor *model.Visitor, labels []model.Label) *model.Conn {
	return &model.Conn{
		IsSSE:          true,
		Type:           model.ConnTypeAPIVisitor,
		ClientID:       clientID,
		APIBotBizID:    app.GetAppBizId(),
		CorpStaffID:    visitor.ID,
		CorpStaffBizID: visitor.ID,
		VisitorLabels:  labels,
	}
}

// registerClient stores SSE client and initializes cache if needed.
func (s *Service) registerClient(ctx context.Context, conn *model.Conn, incremental bool) error {
	if err := s.dao.SetSseClient(ctx, conn.ClientID, true); err != nil {
		return err
	}
	if incremental {
		dao.InitOutputCache(ctx, conn.ClientID)
	}
	return nil
}

// attachMetrics initializes context metrics and statistics.
func attachMetrics(ctx context.Context, ev *message.Event) {
	statistics := &pkg.PPLStatistics{PPLStartTime: time.Now()}
	pkg.WithInterfaceType(ctx, "sse")
	pkg.WithStatistics(ctx, statistics)
	clues.AddT(ctx, "SseEventHandler", ev)
}

// reportSseMetrics reports connect success/failure.
func reportSseMetrics(ctx context.Context) {
	success := helper.When(ctx.Err() == nil, "1", "0")
	metrics.ReportSseClientConnect(success)
}

func getRequest(ctx context.Context, ev *message.Event) (event.DialogEvent, error) {
	req := event.DialogEvent{}
	payload := ev.GetPayload().GetStringValue()
	bs, err := base64.StdEncoding.DecodeString(payload)
	if err != nil {
		log.WarnContextf(ctx, "Decode sse payload error: %+v, payload: %+v", err, payload)
		return req, pkg.ErrBadRequest
	}

	err = jsoniter.Unmarshal(bs, &req)
	clues.AddTrackE(ctx, "json.Unmarshal", &req, err)
	if err != nil {
		log.WarnContextf(ctx, "Unmarshal sse payload error: %+v, payload: %+v", err, string(bs))
		return req, pkg.ErrBadRequest
	}
	if !req.IsValid(ctx) || len(req.BotAppKey) < 5 || len(req.BotAppKey) > 128 {
		return req, pkg.ErrBadRequest
	}
	req.OriginContent = req.Content
	req.Environment = event.EventSend
	req.StartTime = time.Now() // todo 提前

	return req, nil
}

func getSseLabels(req *event.DialogEvent) ([]model.Label, error) {
	var labels []model.Label
	for _, l := range req.VisitorLabels {
		label := model.Label{
			Name:   strings.TrimSpace(l.Name),
			Values: helper.Map(l.Values, strings.TrimSpace),
		}
		if !label.IsValid() {
			return nil, pkg.ErrInvalidVisitorLabel
		}
		labels = append(labels, label)
	}
	return labels, nil
}

// SetCustomTraceID 设置自定义的 TraceID
func SetCustomTraceID(ctx context.Context, customTraceID string) context.Context {
	// 解析自定义的 TraceID
	traceID, err := trace.TraceIDFromHex(customTraceID)
	if err != nil {
		// fmt.Printf("trace.TraceIDFromHex error: %+v\n", err)
		// 处理错误，可能返回原始 context 或生成新的 TraceID
		return ctx
	}

	// 获取当前的 SpanContext
	currentSpanCtx := trace.SpanContextFromContext(ctx)

	// 创建新的 SpanContext，保留其他属性但使用新的 TraceID
	newSpanCtx := trace.NewSpanContext(trace.SpanContextConfig{
		TraceID:    traceID,
		SpanID:     currentSpanCtx.SpanID(), // 保持原有 SpanID
		TraceFlags: currentSpanCtx.TraceFlags(),
		TraceState: currentSpanCtx.TraceState(),
		Remote:     currentSpanCtx.IsRemote(),
	})

	// 将新的 SpanContext 注入到 Context 中
	return trace.ContextWithSpanContext(ctx, newSpanCtx)
}
