package service

import (
	"context"
	"encoding/base64"
	"fmt"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	pb "git.woa.com/ivy/protobuf/trpc-go/qbot/qbot/chat"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
	"git.woa.com/ivy/qbot/qbot/chat/pkg"
)

// GetSharePageDigitalHumanSignature 获取数智人签名
func (s *Service) GetSharePageDigitalHumanSignature(ctx context.Context, req *pb.GetSharePageDigitalHumanSignatureReq) (
	*pb.GetSharePageDigitalHumanSignatureRsp, error) {
	t0 := time.Now()
	// 校验分享码
	if err := checkShareCode(ctx, req.GetShareCode()); err != nil {
		return nil, err
	}
	// 通过shareCode获取robotId
	robotID, err := s.getBotBizIDByShareCode(ctx, req.GetShareCode())
	if err != nil {
		return nil, err
	}
	// 获取token
	reqToken := pb.GetDigitalHumanSignatureReq{BotBizId: robotID, RawQuery: req.GetRawQuery(),
		Type: uint32(model.ConnTypeVisitor)}
	resp, err := s.GetDigitalHumanSignature(ctx, &reqToken)
	if err != nil {
		return nil, err
	}
	log.InfoContextf(ctx, "RESP|GetSharePageDigitalHumanSignature %s %s", resp.GetSignature(), time.Since(t0))
	rsp := &pb.GetSharePageDigitalHumanSignatureRsp{
		Signature: resp.GetSignature(),
		Appkey:    resp.Appkey,
		Timestamp: resp.Timestamp,
		Expired:   resp.Expired,
	}
	return rsp, nil
}

// GetDigitalHumanSignature 获取数智人签名
func (s *Service) GetDigitalHumanSignature(ctx context.Context, req *pb.GetDigitalHumanSignatureReq) (
	*pb.GetDigitalHumanSignatureRsp, error) {
	t0 := time.Now()
	log.InfoContextf(ctx, "REQ|GetDigitalHumanSignature %+v", req)
	param := map[string]string{}
	if req.GetRawQuery() != "" {
		// 解析rawQuery
		bs, err := base64.StdEncoding.DecodeString(req.GetRawQuery())
		if err != nil {
			log.WarnContextf(ctx, "[param invalid]decode rawQuery failed. err:%v", err)
			return nil, pkg.ErrBadRequest
		}
		// 解析参数
		param, err = helper.ParseRawQuery(string(bs))
		if err != nil {
			log.WarnContextf(ctx, "[param invalid]parse rawQuery failed. err:%v", err)
			return nil, pkg.ErrBadRequest
		}
	}
	// 获取用户信息
	u, err := s.dao.CheckSession(ctx)
	if err != nil {
		if errs.Code(err) == errs.Code(pkg.ErrUserNotLogin) {
			return nil, err
		}
		return nil, pkg.ErrInternalServerError
	}
	// 校验参数botBizID
	app, err := s.getAppByBotBizID(ctx, req.GetBotBizId(), req.GetType())
	if err != nil {
		return nil, err
	}
	// 检查RTC配置
	if err = checkDigitalHumanConfig(ctx, req.GetBotBizId(), app); err != nil {
		return nil, err
	}
	// 获取签名
	param["userID"] = fmt.Sprintf("%d", u.BusinessID)
	sign, err := s.dao.GenDigitalHumanSignature(ctx, param)
	if err != nil {
		return nil, err
	}
	log.InfoContextf(ctx, "RESP|GetDigitalHumanSignature %s %s", sign.Signature, time.Since(t0))
	rsp := &pb.GetDigitalHumanSignatureRsp{
		Signature: sign.Signature,
		Appkey:    sign.Appkey,
		Timestamp: sign.Timestamp,
		Expired:   sign.Expired,
	}
	return rsp, nil
}

func checkDigitalHumanConfig(ctx context.Context, botBizID uint64, app *model.App) error {
	// 判断是否开启语音互动
	aiCall := app.GetKnowledgeQa().GetAiCall()
	if aiCall == nil || !aiCall.EnableVoiceInteract {
		log.WarnContextf(ctx, "RTC botBizID:%d not enable rtc", botBizID)
		return pkg.ErrAudioInteractNotEnabled
	}
	// 检查是否开启数字人
	if !app.GetKnowledgeQa().GetAiCall().EnableDigitalHuman {
		log.WarnContextf(ctx, "RTC botBizID:%d not enable digitalHuman", botBizID)
		return pkg.ErrRTCDigitalHumanNotEnabled
	}
	// 检查数字人配置
	if aiCall.DigitalHuman.AssetKey == "" {
		log.WarnContextf(ctx, "RTC botBizID:%d not config digitalHuman", botBizID)
		return pkg.ErrRTCDigitalHumanNotConfig
	}
	// 检查声音配置
	if aiCall.Voice.VoiceType == 0 {
		log.WarnContextf(ctx, "RTC botBizID:%d not config voice", botBizID)
		return pkg.ErrRTCVoiceNotConfig
	}
	return nil
}
