package service

import (
	"context"
	"encoding/base64"
	"strconv"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/baicaoyuan/apex/proto/message"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	"git.woa.com/ivy/qbot/qbot/chat/internal/dao"
	"git.woa.com/ivy/qbot/qbot/chat/internal/eventbus"
	"git.woa.com/ivy/qbot/qbot/chat/internal/metrics"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model/event"
	"git.woa.com/ivy/qbot/qbot/chat/pkg"
	jsoniter "github.com/json-iterator/go"
)

// AgentRoleTipswordsOptSSEEventHandler agent 模式下角色扮演的提示词优化 SSE 事件处理方法
func (s *Service) AgentRoleTipswordsOptSSEEventHandler(ctx context.Context, ev *message.Event) (
	rev *message.Event, err error) {
	defer func() {
		success := helper.When(err == nil, "1", "0")
		metrics.ReportSseClientConnect(success)
		log.InfoContextf(ctx, "AgentRoleTipswordsOptSSEEventHandler ReportSseClientConnect end |Process")
	}()
	log.InfoContextf(ctx, "AgentRoleTipswordsOptSSEEventHandler start ev|%v", ev)
	typ := ev.GetType()
	if typ == message.EventTypeDisconnect {
		_ = s.dao.DelSseClient(ctx, ev.GetClientId())
		return &message.Event{}, nil
	}
	if typ != message.EventTypeConnect {
		return &message.Event{}, nil
	}

	payload := ev.GetPayload().GetStringValue()
	bs, err := base64.StdEncoding.DecodeString(payload)
	if err != nil {
		log.ErrorContextf(ctx, "Decode sse payload error: %+v, payload: %+v", err, payload)
		return nil, pkg.ErrBadRequest
	}
	req := event.AgentRoleTipwordsOptEvent{}
	if err := jsoniter.Unmarshal(bs, &req); err != nil {
		log.ErrorContextf(ctx, "Unmarshal sse payload error: %+v, payload: %+v", err, string(bs))
		return nil, pkg.ErrBadRequest
	}
	log.InfoContextf(ctx, "AgentRoleTipswordsOptSSEEventHandler req|%+v", &req)
	// 校验请求参数
	if err := req.IsValid(); err != nil {
		return nil, err
	}
	var app *model.App
	app, err = s.dao.GetAppByAppKey(ctx, dao.AppTestScene, req.BotAppKey)
	log.InfoContextf(ctx, "AgentRoleTipswordsOptSSEEventHandler GetAppByAppKey app|%+v", app)
	if err != nil {
		return nil, err
	}
	if app == nil {
		return nil, pkg.ErrRobotNotExist
	}
	for _, blacklistedID := range config.App().App.BlackList {
		if app.GetAppBizId() == blacklistedID {
			return nil, pkg.ErrAppIDInBlacklist
		}
	}
	if reached, err := s.limitByApp(ctx, app, req.Content); err != nil || reached {
		log.InfoContextf(ctx, "botAppKey:%s SSE rate limit reached", req.BotAppKey)
		return nil, pkg.ErrAppRateLimiter
	}

	visitor, err := s.dao.MustGetVisitor(ctx, app.GetId(), app.GetAppBizId(), req.VisitorBizID)
	if err != nil {
		// return nil, pkg.ErrInternalServerError
		log.WarnContextf(ctx, "Get visitor err, use zero visitor id")
		visitor = &model.Visitor{} // https://tapd.woa.com/tapd_fe/70080800/task/detail/1070080800075742299
	}

	conn := &model.Conn{
		IsSSE:          true,
		Type:           model.ConnTypeAPIVisitor,
		ClientID:       ev.GetClientId(),
		APIBotBizID:    app.GetAppBizId(),
		CorpStaffID:    visitor.ID,
		CorpStaffBizID: visitor.ID,
	}
	log.InfoContextf(ctx, "AgentRoleTipswordsOptSSEEventHandler RegisterSSEClient conn|%+v", conn)
	if err := s.dao.SetSseClient(ctx, conn.ClientID, true); err != nil {
		return nil, pkg.ErrInternalServerError
	}

	req.VisitorBizID = strconv.FormatUint(app.GetAppBizId(), 10)
	log.InfoContextf(ctx, "AgentRoleTipswordsOptSSEEventHandler Push e0|%+v", req)
	// 尽量还是使用 eventbus 框架执行，eventbus会在对话结束后处理 sse 连接
	if err := eventbus.Push(ctx, conn, event.EventTipWordOptimization, req); err != nil {
		return nil, pkg.ErrInternalServerError
	}

	return &message.Event{}, nil
}
