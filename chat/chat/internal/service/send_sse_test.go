package service

import (
	"context"
	"fmt"
	"testing"

	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
)

// TestSetCustomTraceID tests the SetCustomTraceID function.
func TestSetCustomTraceID(t *testing.T) {
	// 初始化一个基础 context
	ctx := context.Background()

	fmt.Println("=== 方法一：直接修改 Context 中的 TraceID ===")

	// 原始 TraceID（通常为空或默认值）
	fmt.Printf("原始 TraceID: %s\n", model.TraceID(ctx))

	// 设置自定义 TraceID
	// customTraceID := "abcdef1234567890abcdef1234567890"
	customTraceID := "aabccdeaf4567890abcdef1234567890"
	newCtx := SetCustomTraceID(ctx, customTraceID)

	fmt.Printf("修改后 TraceID: %s\n", model.TraceID(newCtx))
}
