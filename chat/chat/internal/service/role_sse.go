package service

import (
	"context"
	"encoding/base64"
	"strconv"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/baicaoyuan/apex/proto/message"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	"git.woa.com/ivy/qbot/qbot/chat/internal/dao"
	"git.woa.com/ivy/qbot/qbot/chat/internal/eventbus"
	"git.woa.com/ivy/qbot/qbot/chat/internal/metrics"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model/event"
	"git.woa.com/ivy/qbot/qbot/chat/pkg"
	jsoniter "github.com/json-iterator/go"
)

// RoleSSEEventHandler SSE 事件处理方法
func (s *Service) RoleSSEEventHandler(ctx context.Context, ev *message.Event) (rev *message.Event, err error) {
	defer func() {
		success := helper.When(err == nil, "1", "0")
		metrics.ReportSseClientConnect(success)
		log.InfoContextf(ctx, "RoleSSEEventHandler ReportSseClientConnect end |Process")
	}()
	log.InfoContextf(ctx, "RoleSSEEventHandler start ev|%v", ev)
	pkg.WithInterfaceType(ctx, "sse")
	typ := ev.GetType()
	if typ == message.EventTypeDisconnect {
		_ = s.dao.DelSseClient(ctx, ev.GetClientId())
		return &message.Event{}, nil
	}
	if typ != message.EventTypeConnect {
		return &message.Event{}, nil
	}

	req, err := s.parseRoleSSEEventReq(ctx, ev)
	if err != nil {
		return nil, err
	}
	var app *model.App
	if req.Type == "" {
		app, err = s.dao.GetAppByAppKey(ctx, dao.AppTestScene, req.BotAppKey)
	} else {
		var bizID uint64
		bizID, err = strconv.ParseUint(req.VisitorBizID, 10, 64)
		if err != nil {
			log.ErrorContextf(ctx, "[param invalid] ParseUint error: %+v, VisitorBizID: %+v",
				err, req.VisitorBizID)
			return nil, pkg.ErrBadRequest
		}
		app, err = s.dao.GetAppByBizID(ctx, dao.AppTestScene, bizID)
	}
	log.InfoContextf(ctx, "RoleSSEEventHandler GetAppByAppKey app|%+v", app)
	if err != nil {
		return nil, err
	}
	if app == nil {
		return nil, pkg.ErrRobotNotExist
	}
	for _, blacklistedID := range config.App().App.BlackList {
		if app.GetAppBizId() == blacklistedID {
			return nil, pkg.ErrAppIDInBlacklist
		}
	}
	if reached, err := s.limitByApp(ctx, app, req.Content); err != nil || reached {
		log.InfoContextf(ctx, "botAppKey:%s SSE rate limit reached", req.BotAppKey)
		return nil, pkg.ErrAppRateLimiter
	}

	visitor, err := s.dao.MustGetVisitor(ctx, app.GetId(), app.GetAppBizId(), req.VisitorBizID)
	if err != nil {
		// return nil, pkg.ErrInternalServerError
		log.WarnContextf(ctx, "Get visitor err, use zero visitor id")
		visitor = &model.Visitor{} // https://tapd.woa.com/tapd_fe/70080800/task/detail/1070080800075742299
	}

	conn := &model.Conn{
		IsSSE:          true,
		Type:           model.ConnTypeAPIVisitor,
		ClientID:       ev.GetClientId(),
		APIBotBizID:    app.GetAppBizId(),
		CorpStaffID:    visitor.ID,
		CorpStaffBizID: visitor.ID,
	}
	log.InfoContextf(ctx, "RoleSSEEventHandler RegisterSSEClient conn|%+v", conn)
	if err := s.dao.SetSseClient(ctx, conn.ClientID, true); err != nil {
		return nil, pkg.ErrInternalServerError
	}

	req.VisitorBizID = strconv.FormatUint(app.GetAppBizId(), 10)
	log.InfoContextf(ctx, "RoleSSEEventHandler Push e0|%+v", req)
	if err := eventbus.Push(ctx, conn, event.EventSSeRoleSend, *req); err != nil {
		return nil, pkg.ErrInternalServerError
	}

	return &message.Event{}, nil
}

func (s *Service) parseRoleSSEEventReq(ctx context.Context, ev *message.Event) (*event.SSeRoleSendEvent, error) {
	payload := ev.GetPayload().GetStringValue()
	bs, err := base64.StdEncoding.DecodeString(payload)
	if err != nil {
		log.ErrorContextf(ctx, "Decode sse payload error: %+v, payload: %+v", err, payload)
		return nil, pkg.ErrBadRequest
	}
	req := event.SSeRoleSendEvent{}
	if err = jsoniter.Unmarshal(bs, &req); err != nil {
		log.ErrorContextf(ctx, "Unmarshal sse payload error: %+v, payload: %+v", err, string(bs))
		return nil, pkg.ErrBadRequest
	}
	log.InfoContextf(ctx, "RoleSSEEventHandler req|%+v", &req)
	// 校验请求参数
	if err = req.IsValid(); err != nil {
		return nil, err
	}
	return &req, nil
}
