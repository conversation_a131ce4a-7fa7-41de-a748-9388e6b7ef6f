package rewrite

import (
	"context"
	"fmt"
	"strings"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	llmm "git.woa.com/dialogue-platform/proto/pb-stub/llm-manager-server"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	"git.woa.com/ivy/qbot/qbot/chat/internal/eventbus/botsession"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
)

// Rewrite 普通改写
func (r *QueryRewrite) Rewrite(ctx context.Context, bs *botsession.BotSession) (string, string, error) {
	m := bs.App.GetModel(ctx, model.AppTypeKnowledgeQA, model.ModelTypeQueryRewrite) // 改写模型
	if !m.GetIsEnabled() {
		log.InfoContextf(ctx, "QueryRewrite is disabled")
		return bs.OriginContent, "", nil
	}
	threshold := config.App().Bot.Rewrite.RewriteThreshold
	if threshold != 0 && len([]rune(bs.OriginContent)) > threshold {
		log.InfoContextf(ctx, "QueryRewrite is disabled, len(bs.OriginContent) > threshold")
		return bs.OriginContent, "", nil
	}
	histories := r.makeRewriteHistories(ctx, bs, int(m.GetHistoryLimit()))
	if len(histories) == 0 {
		log.InfoContextf(ctx, "QueryRewrite histories empty")
		return bs.OriginContent, "", nil
	}
	wordCount := int(m.PromptWordsLimit) - len([]rune(bs.OriginContent)) - 100 // 100 为安全边界
	histories = model.TruncateQAHistories(ctx, histories, wordCount)           // 截断太长的历史记录
	var his [][2]string
	for i := range histories {
		his = append(his, [2]string{histories[i][0].Content, histories[i][1].Content})
	}
	return r.execRewrite(ctx, bs, m, his, bs.OriginContent)
}

// MultiModalRewrite 多模态改写
func (r *QueryRewrite) MultiModalRewrite(ctx context.Context, bs *botsession.BotSession) (string, string, error) {
	log.InfoContextf(ctx, "MultiModalRewrite begin")
	m := bs.App.GetModel(ctx, model.AppTypeKnowledgeQA, model.ModelTypeQueryRewrite) // 改写模型
	if !m.GetIsEnabled() {
		log.InfoContextf(ctx, "MultiModalRewrite is disabled")
		return bs.OriginContent, "", nil
	}
	// 获取历史记录中的图片和文件
	oldImageQueues, oldFileQueues := r.memory.GetHistoryImagesAndFiles(ctx, bs.Session.SessionID)
	bs.ImageQueue = oldImageQueues
	bs.FileQueue = oldFileQueues
	// 获取当前请求中的图片和文件信息
	newImageQueues, newFileQueues := r.getNewImagesAndFiles(ctx, bs)
	bs.ImageQueue = append(bs.ImageQueue, newImageQueues...)
	bs.FileQueue = append(bs.FileQueue, newFileQueues...)
	// 更新历史记录 轮次
	r.memory.UpdateMultiModalHistory(ctx, oldImageQueues, newImageQueues, bs.Session.SessionID)
	r.memory.UpdateRealTimeFileHistory(ctx, oldFileQueues, newFileQueues, bs.Session.SessionID)
	// 组装请求改写模型的首轮内容 todo 首轮长度是否限制一下？
	histories := wrapQueryRewriteFirstRound(oldImageQueues, newImageQueues, oldFileQueues, newFileQueues)
	if len(histories) == 0 { // 多模态改写，首轮内容不能为空
		log.InfoContextf(ctx, "MultiModalRewrite histories empty")
		return bs.OriginContent, "", nil
	}
	// 历史会话记录获取
	historyList, err := r.memory.GetMultiModalHistories(ctx, bs, true)
	if err != nil {
		log.WarnContextf(ctx, "MultiModalRewrite GetMultiModalHistories error:%s", err.Error())
		return bs.OriginContent, "", err
	}
	// 截断逻辑
	if len(historyList) != 0 {
		wordCount := int(m.PromptWordsLimit) - len([]rune(histories[0][1])) - len(bs.OriginContent) - 100 // 100 为安全边界
		temp := TruncateQueryRewriteHistories(ctx, historyList, wordCount)                                // 截断太长的历史记录
		histories = append(histories, temp...)
	}
	// 当前Query处理
	var query = bs.OriginContent
	// 处理Query中的占位符
	imagePlaces := "" // 图片占位符
	for i, image := range helper.GetAllImage(bs.OriginContent) {
		query = strings.Replace(query, image, fmt.Sprintf("【图%d】", i+1+len(oldImageQueues)), 1)
		imagePlaces += fmt.Sprintf("【图%d】", i+1+len(oldImageQueues))
	}
	threshold := config.App().Bot.Rewrite.RewriteThreshold
	if threshold != 0 && len([]rune(query)) > threshold {
		log.WarnContextf(ctx, "MultiModalRewrite is disabled, len(query) > threshold，query:%s", query)
		return query, "", nil
	}
	log.DebugContextf(ctx, "histories:%s,query:%s", helper.Object2StringEscapeHTML(histories), query)
	// 执行改写
	rewrote, prompt, err := r.execRewrite(ctx, bs, m, histories, query)
	// 如果开启了图文检索，且改写后没有图片占位符，需要补上【图片占位符】
	if bs.App.GetKnowledgeQa().GetImageTextRetrieval() && !strings.Contains(rewrote, "【图") {
		rewrote = imagePlaces + rewrote
		log.WarnContextf(ctx, "MultiModalRewrite no image,imagePlaces:%s final query:%s", imagePlaces, rewrote)
	}
	return rewrote, prompt, err
}

// SimpleImageRewrite 用于简单的图片重写，不用历史记录
func (r *QueryRewrite) SimpleImageRewrite(ctx context.Context, bs *botsession.BotSession) (string, string, error) {
	m := bs.App.GetModel(ctx, model.AppTypeKnowledgeQA, model.ModelTypeQueryRewrite) // 改写模型
	if !m.GetIsEnabled() {
		log.InfoContextf(ctx, "SimpleImageRewrite is disabled")
		return bs.OriginContent, "", nil
	}
	// 获取历史记录中的图片和文件
	oldImageQueues, oldFileQueues := r.memory.GetHistoryImagesAndFiles(ctx, bs.Session.SessionID)
	// 获取当前请求中的图片和文件信息
	newImageQueues, newFileQueues := r.getNewImagesAndFiles(ctx, bs)
	// 更新历史记录 轮次
	r.memory.UpdateMultiModalHistory(ctx, oldImageQueues, newImageQueues, bs.Session.SessionID)
	r.memory.UpdateRealTimeFileHistory(ctx, oldFileQueues, newFileQueues, bs.Session.SessionID)
	// 组装请求改写模型的首轮内容 todo 首轮长度是否限制一下？
	histories := wrapQueryRewriteFirstRound(nil, newImageQueues, nil, newFileQueues)
	if len(histories) == 0 { // 多模态改写，首轮内容不能为空
		log.InfoContextf(ctx, "SimpleImageRewrite histories empty")
		return bs.OriginContent, "", nil
	}
	// 当前Query处理
	var query = bs.OriginContent
	// 处理Query中的占位符
	for i, image := range helper.GetAllImage(bs.OriginContent) {
		query = strings.Replace(bs.OriginContent, image, fmt.Sprintf("【图%d】", i+1), 1)
	}
	log.DebugContextf(ctx, "histories:%s,query:%s", helper.Object2StringEscapeHTML(histories), query)
	// 执行改写
	return r.execRewrite(ctx, bs, m, histories, query)
}

// ComplexQueryRewrite 复杂query的改写
// 改写后的子query更新到bs
func (r *QueryRewrite) ComplexQueryRewrite(ctx context.Context, bs *botsession.BotSession) error {
	if !bs.App.GetKnowledgeQa().GetComplexProblemRetrieval() { // op开启，才支持复杂query的拆解
		log.InfoContextf(ctx, "ComplexQueryRewrite is disabled, appBizId:%d", bs.App.GetAppBizId())
		return nil
	}
	m := bs.App.GetModel(ctx, model.AppTypeKnowledgeQA, model.ModelTypeComplexQueryRewrite)
	if !m.GetIsEnabled() {
		log.InfoContextf(ctx, "ComplexQueryRewrite is disabled")
		return nil
	}
	threshold := config.App().Bot.Rewrite.RewriteThreshold
	if threshold != 0 && len([]rune(bs.OriginContent)) > threshold {
		log.InfoContextf(ctx, "ComplexQueryRewrite is disabled, len(bs.OriginContent)%d > threshold")
		return nil
	}
	histories := r.makeRewriteHistories(ctx, bs, int(m.GetHistoryLimit()))
	wordCount := int(m.PromptWordsLimit) - len([]rune(bs.OriginContent)) - 100 // 100 为安全边界
	histories = model.TruncateQAHistories(ctx, histories, wordCount)           // 截断太长的历史记录
	var his [][2]string
	for i := range histories {
		his = append(his, [2]string{histories[i][0].Content, histories[i][1].Content})
	}
	rsp, err := r.execRewriteV2(ctx, bs, model.QueryRewriteReq{
		Model:       m,
		Histories:   his,
		RewriteType: model.RewriteTypeComplexQuery,
		OriQuery:    bs.OriginContent,
	})
	if err != nil {
		log.WarnContextf(ctx, "ComplexQueryRewrite error: %s", err.Error())
		return err
	}
	if rsp.Status == model.RewriteStatusSuccess {
		subQueries := strings.Split(rsp.RewriteQuery, "%%")
		if len(subQueries) > 1 {
			rsp.ReviseQueries = subQueries
		}
	}
	bs.QueryRewrite = rsp
	return nil
}

// HunYuanImageRewrite 混元多模态改写
func (r *QueryRewrite) HunYuanImageRewrite(ctx context.Context, bs *botsession.BotSession) (rewrote string, err error) {
	start := time.Now()
	defer func() {
		log.InfoContextf(ctx, "HunYuanImageRewrite cost %d, rewrote:%s -> %s, err:%v",
			time.Since(start).Milliseconds(), bs.PromptCtx.Question, rewrote, err)
	}()
	rewrote = bs.PromptCtx.Question
	imagePlaces := helper.GetImagePlaceholder(rewrote) // 图片占位符
	req := &llmm.Request{
		RequestId:   model.RequestID(ctx, bs.RequestID, bs.RecordID),
		ModelName:   config.GetDialogMLLMModelName(bs.Session.BotBizID),
		AppKey:      fmt.Sprintf("%d", bs.Session.BotBizID),
		Messages:    make([]*llmm.Message, 0),
		PromptType:  llmm.PromptType_TEXT,
		RequestType: llmm.RequestType_ONLINE,
		Biz:         "cs",
	}
	prompt := config.App().MultiModal.GetRewritePrompt
	message := &llmm.Message{
		Role:    llmm.Role_USER,
		Content: bs.PromptCtx.Question + "\n\n" + prompt, // 算法同学说 占位符放前面
		Images:  bs.Images,
	}
	if config.OnlyCosURLModel(req.ModelName) {
		message.Images = helper.ReplaceDomain(bs.Images)
	}
	req.Messages = append(req.Messages, message)
	rsp, err := r.dao.SimpleChat(ctx, req) // Query改写
	if err != nil {
		log.WarnContextf(ctx, "HunYuanImageRewrite error: %s", err.Error())
		return rewrote, err
	}
	if rsp.GetMessage().GetContent() == "" {
		log.WarnContextf(ctx, "HunYuanImageRewrite rewrite empty")
		return rewrote, nil
	}
	rewrote = strings.Replace(rsp.GetMessage().GetContent(), config.App().MultiModal.RewritePrefix, "", -1)
	if !strings.Contains(rewrote, "【图") { // 占位符被改没了，需要补回
		rewrote = strings.Join(imagePlaces, "") + rewrote
	}
	return rewrote, err
}

// execRewrite 执行改写请求
func (r *QueryRewrite) execRewrite(ctx context.Context, bs *botsession.BotSession, rewriteModel *model.AppModel,
	histories [][2]string, content string) (rewrote string, prompt string, err error) {
	pCtx := botsession.PromptCtx{Question: content, MultiRoundHistories: histories}
	prompt, err = r.dao.TextTruncate(ctx, rewriteModel, pCtx)
	if err != nil {
		return content, prompt, err
	}
	requestID := model.RequestID(ctx, bs.Session.SessionID, bs.RecordID)
	message := rewriteModel.WrapMessages("", nil, prompt)
	req := rewriteModel.NewLLMRequest(requestID, message)
	rsp, err := r.dao.SimpleChat(ctx, req) // Query改写
	if err != nil {
		log.ErrorContextf(ctx, "rewrite error: %s", err.Error())
		return content, prompt, err
	}
	if len(rsp.GetMessage().GetContent()) == 0 {
		log.WarnContextf(ctx, "LLM rewrite result is empty")
		return content, prompt, nil
	}
	rewrote = rsp.GetMessage().GetContent()
	log.InfoContextf(ctx, "Rewrite: %s => %s", content, rewrote)
	// todo 上报改写结果，维度：模型名，应用id，耗时，是否改写
	return rewrote, prompt, nil
}

// execRewriteV2 执行改写请求
// todo 其他类型改写，后续都优化逐步切到该方法
func (r *QueryRewrite) execRewriteV2(ctx context.Context, bs *botsession.BotSession,
	req model.QueryRewriteReq) (model.QueryRewriteRsp, error) {
	var rsp model.QueryRewriteRsp
	defer func() {
		log.InfoContextf(ctx, "%s rewrite result: %s", req.OriQuery, helper.Object2String(rsp))
	}()
	pCtx := botsession.PromptCtx{Question: req.OriQuery, MultiRoundHistories: req.Histories}
	prompt, err := r.dao.TextTruncate(ctx, req.Model, pCtx)
	if err != nil {
		return rsp, err
	}
	rsp.Prompt = prompt
	requestID := model.RequestID(ctx, bs.Session.SessionID, bs.RecordID)
	message := req.Model.WrapMessages("", nil, prompt)
	llmReq := req.Model.NewLLMRequest(requestID, message)
	llmRsp, err := r.dao.SimpleChat(ctx, llmReq) // Query改写
	if err != nil {
		log.ErrorContextf(ctx, "rewrite error: %s", err.Error())
		rsp.Status = model.RewriteStatusError
		return rsp, err
	}
	if llmRsp.GetMessage().GetContent() == "" {
		rsp.Status = model.RewriteStatusEmpty
		return rsp, nil
	}
	if llmRsp.GetMessage().GetContent() == req.OriQuery {
		rsp.Status = model.RewriteStatusNoChange
		return rsp, nil
	}
	rsp.Status = model.RewriteStatusSuccess
	rsp.RewriteQuery = llmRsp.GetMessage().GetContent()
	rsp.RewriteType = req.RewriteType
	rsp.OriQuery = req.OriQuery
	// todo 上报改写结果，维度：模型名，应用id，耗时，是否改写
	return rsp, nil
}

// getNewImagesAndFiles 获取当前请求中的图片和文件信息
func (r *QueryRewrite) getNewImagesAndFiles(ctx context.Context, bs *botsession.BotSession) ([]*model.ImageQueue,
	[]*model.FileQueue) {
	// 获取当前请求中的图片
	newImageQueues := r.memory.GetNewImageQueues(ctx, bs)

	// 获取当前请求中的文件内容,
	newFileQueues := r.memory.GetNewRealTimeFileQueues(bs.FileInfos)
	return newImageQueues, newFileQueues
}

// wrapQueryRewriteFirstRound 组装请求改写模型的首轮内容
func wrapQueryRewriteFirstRound(oldImageQueues []*model.ImageQueue,
	newImageQueues []*model.ImageQueue, oldFileQueues []*model.FileQueue, newFileQueues []*model.FileQueue) [][2]string {
	// 组装请求改写模型的首轮内容
	histories := make([][2]string, 0)
	var content string
	for _, fileQueue := range oldFileQueues { // 文件在前
		content += fmt.Sprintf("《%s》摘要：%s\n\n", fileQueue.FileName, fileQueue.Summary)
	}
	for _, fileQueue := range newFileQueues {
		content += fmt.Sprintf("《%s》摘要：%s\n\n", fileQueue.FileName, fileQueue.Summary)
	}
	for i, imageQueue := range oldImageQueues { // 图片在后
		content += fmt.Sprintf("【图%d】描述：%s\n\n", i+1, imageQueue.Caption)
	}
	for i, imageQueue := range newImageQueues {
		content += fmt.Sprintf("【图%d】描述：%s\n\n", i+1+len(oldImageQueues), imageQueue.Caption)
	}
	if len(content) > 0 {
		histories = append(histories, [2]string{"描述所有的文件和图片", content})
	}
	return histories
}

// TruncateQueryRewriteHistories 截断历史记录histories,倒序遍历，超过wordsCount以后就截断返回
func TruncateQueryRewriteHistories(ctx context.Context, histories [][2]string, wordsCount int) [][2]string {
	length := len(histories)
	totalLength := 0                   // 当前总长度
	for i := length - 1; i >= 0; i-- { // 总数2000
		if len([]rune(histories[i][0])) >= wordsCount-totalLength { // 问题已经超了
			log.InfoContextf(ctx, "histories after truncated: %s", helper.Object2String(histories[i+1:length]))
			return histories[i+1 : length] // i+1 把问题一起丢掉
		}
		totalLength += len([]rune(histories[i][0]))
		if len([]rune(histories[i][1])) > wordsCount-totalLength { // 答案超了, 等于不算超
			histories[i][1] = model.TruncateSentence([]rune(histories[i][1])[:wordsCount-totalLength])
		}
		totalLength += len([]rune(histories[i][1]))
		if totalLength >= wordsCount {
			log.InfoContextf(ctx, "histories after truncated: %s", helper.Object2String(histories[i:length]))
			return histories[i:length] // 保留截断的部分
		}
	}
	return histories
}

// makeRewriteHistories 构建改写历史上下文
func (r *QueryRewrite) makeRewriteHistories(ctx context.Context, bs *botsession.BotSession,
	limit int) [][2]model.HisMessage {
	num := helper.When(limit > len(bs.ChatHistoriesV2.ChatStack), len(bs.ChatHistoriesV2.ChatStack), limit)
	start := len(bs.ChatHistoriesV2.ChatStack) - num // 指定截取的起始位置
	pairs := make([][2]model.HisMessage, 0, num)
	for i := start; i < len(bs.ChatHistoriesV2.ChatStack); i++ {
		item := bs.ChatHistoriesV2.ChatStack[i]
		question := helper.When(item.RewriteQuery != "", item.RewriteQuery, item.OriginQuery) // 使用改写后的query
		q := model.HisMessage{RecordID: item.RelatedRecordID, Content: question}
		a := model.HisMessage{RecordID: item.RecordID, Content: item.GetAssistantContent() + item.OptionCardsStr,
			Intent: item.Intent, IntentCategory: item.IntentCategory}
		pairs = append(pairs, [2]model.HisMessage{q, a})
	}
	return pairs
}
