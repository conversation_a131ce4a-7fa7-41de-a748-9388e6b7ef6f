package eventbus

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/common/v3/errors"
	"git.woa.com/dialogue-platform/common/v3/utils"
	"git.woa.com/dialogue-platform/go-comm/clues"
	"git.woa.com/dialogue-platform/go-comm/pf"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	"git.woa.com/ivy/qbot/qbot/chat/internal/dao"
	"git.woa.com/ivy/qbot/qbot/chat/internal/eventbus/botsession"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model/event"
	"git.woa.com/ivy/qbot/qbot/chat/pkg"
	ispkg "git.woa.com/ivy/qbot/qbot/infosec/pkg"
	jsoniter "github.com/json-iterator/go"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common"
	tchttp "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/http"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/profile"
)

// hunYuanSearchReply 混元搜索回复
func hunYuanSearchReply(ctx context.Context, bs *botsession.BotSession) (bool, bool, string, error) {
	if ok, ver := supportNewSearchEngineVersion(ctx, bs); ok {
		if ver == "v2" { // 走搜狗+总结
			err := searchReplyV2(ctx, bs)
			return false, false, "", err
		} else if ver == "v3" { // 走元宝tob联网
			_, _, _, err := searchReplyV3(ctx, bs)
			if err != nil { // 降级兜底走主模型
				err = searchEngineDirectReply(ctx, bs)
			}
			return false, false, "", err
		}
	}
	m := bs.App.GetModel(ctx, model.AppTypeKnowledgeQA, bs.ModelType)
	clues.AddTrackData(ctx, "App.GetModel", map[string]any{
		"model": m, "appType": model.AppTypeKnowledgeQA, "type": bs.ModelType,
	})
	m.ModelName = config.GetSearchModelName(bs.App.GetAppBizId())
	_, cancel := context.WithCancel(ctx)
	defer cancel()
	showSearchEngine(ctx, bs, cancel)
	bs.NeedCheck = false // 混元不需要安全检查
	bs.TokenStat.UpdateProcedure(event.NewProcessingTSProcedure(ctx, event.ProcedureSE))
	SendTokenStat(ctx, bs, bs.TokenStat)
	if bs.DebugMessage == nil { // 非调试场景，才计量
		searchEngineStatus := bus.dao.GetSearchEngineStatus(ctx, bs.App.GetCorpId())
		if searchEngineStatus != 0 {
			d := event.ProcedureDebugging{Content: bs.PromptCtx.Question, CustomVariables: bs.CustomVariablesForDisplay}
			bs.TokenStat.UpdateProcedure(event.NewFailedTSProcedure(ctx, event.ProcedureSE,
				event.WithResourceStatusUnAvailable(), event.WithProcedureDebugging(d)))
			SendTokenStat(ctx, bs, bs.TokenStat)
			return false, false, "", pkg.ErrNoSearchEngineBalance
		}
	}
	startTi := time.Now()
	req := m.NewHunYuanSearchRequest(ctx, bs.Session, bs.RecordID, bs.PromptCtx.Question, bs.SystemRole)
	log.InfoContextf(ctx, "R|botReply|NewHunYuanSearchRequest|%s %+v", bs.EventSource, req)
	st, last, lastEvil, isModelRejected, err := streamReply(ctx, bs, req,
		event.ProcedureSE, model.ReplyMethodSearch, nil)
	if err != nil {
		return false, false, "", err
	}
	if bs.DebugMessage == nil { // 非调试场景，才上报计量
		reportSearchEngineDosage(ctx, bs.App, bs.TokenStat.RecordID, startTi)
	}
	if last == nil { // 没有输出
		return false, isModelRejected, "", nil
	}

	isEvil := lastEvil.GetResultCode() == ispkg.ResultEvil
	if !last.GetFinished() || isEvil { // 最后一个包含敏感词也需要兜底结束
		last.Finished = true
		_ = bus.dao.DoEmitWsClient(ctx, bs.To.ClientID,
			bs.NewReplyEvent(ctx, last, isEvil, model.ReplyMethodSearch, st, []string{}), cancel,
		)
	}
	reply := last.GetMessage().GetContent()
	method := helper.When(isEvil, model.ReplyMethodEvil, model.ReplyMethodSearch)
	m0 := bs.NewBotRecord(ctx, reply, req, method, lastEvil, st)
	newMsg, stat := event.GetMsgRecordAndTokenStat(ctx, m0)
	bus.dao.CreateMsgRecord(ctx, newMsg, stat) // for answer
	if err != nil {
		return false, false, "", err
	}

	return isEvil, isModelRejected, "", nil // 通过Output为空，不去找参考来源
}

func reportSearchEngineDosage(ctx context.Context, app *model.App, recordID string, startTi time.Time) {
	dosage := dao.SearchEngineDosage{
		AppID:     app.GetAppBizId(),
		AppType:   app.GetAppType(),
		RecordID:  recordID,
		StartTime: startTi,
		EndTime:   time.Now(),
		Dosage:    1, // 搜索引擎上报用量次数为1
	}
	_ = bus.dao.ReportSearchEngineDosage(ctx, app.GetCorpId(), dosage)
}

// searchEngineDirectReply 搜索引擎用尽，兜底回复
func searchEngineDirectReply(ctx context.Context, bs *botsession.BotSession) error {
	m := getAppModel(ctx, bs)
	histories, useRole, err := getHistoryAndRole(ctx, bs, m.GetHistoryLimit(), m.GetHistoryWordsLimit())
	if err != nil {
		return err
	}
	_, _, err = generalReply(ctx, bs, m, histories, useRole)
	return err
}

// searchReplyV2 先调搜索，再调总结模型
func searchReplyV2(ctx context.Context, bs *botsession.BotSession) error {
	pf.StartElapsedAsMetrics(ctx, config.App().StageTaskName.SearchProForDS)
	bs.TokenStat.UpdateProcedure(event.NewProcessingTSProcedure(ctx, event.ProcedureSE))
	SendTokenStat(ctx, bs, bs.TokenStat)
	pages, err := searchProImpl(ctx, bs.PromptCtx.Question)
	uin, cellphone := pkg.Uin(ctx), ""
	if bs.To != nil && bs.To.CorpStaffBizID > 0 {
		cellphone = bus.dao.GetUserCellphoneByCorpStaffBizID(ctx, bs.To.CorpStaffBizID)
		log.DebugContextf(ctx, "cellphone: %s", cellphone)
	}
	pf.AppendFullSpanElapsed(ctx, config.App().StageTaskName.SearchProForDS, bs.App.GetMainModelName(), "",
		uin, -1, cellphone)
	if err != nil {
		log.WarnContextf(ctx, "fail to search: %s", err.Error()) // 搜索失败降级，只是记录日志
	}
	_, cancel := context.WithCancel(ctx)
	defer cancel()
	showSearchEngine(ctx, bs, cancel)
	d := event.ProcedureDebugging{CustomVariables: bs.CustomVariablesForDisplay}
	p := event.NewSuccessTSProcedure(ctx, event.ProcedureSE, nil, d, nil)
	bs.TokenStat.UpdateSuccessProcedure(p)
	SendTokenStat(ctx, bs, bs.TokenStat)
	bs.TokenStat.UpdateProcedure(event.NewProcessingTSProcedure(ctx, event.ProcedureLLM))
	SendTokenStat(ctx, bs, bs.TokenStat)
	refDocs := getRefDocs(pages)
	m := bs.App.GetModel(ctx, model.AppTypeKnowledgeQA, model.ModelTypeMessage)
	histories, useRole, err := getHistoryAndRole(ctx, bs, m.GetHistoryLimit(), m.GetHistoryWordsLimit())
	if err != nil {
		log.ErrorContextf(ctx, "fail to get history and role: %s", err.Error())
	}
	sysRole := m.GetSysPrompt(ctx, useRole, model.IsSelfAwarenessIntent(bs.IntentCate), bs.SystemRole)
	m.PromptLimit = bus.dao.GetModelPromptLimit(ctx, m.GetModelName()) -
		bus.dao.GetTextTokenLen(ctx, getHistoryText(histories)+sysRole)
	m.PromptLimit = config.GetModelPromptLength(bs.App.GetAppBizId(), m.PromptLimit) // 白名单逻辑
	promptCtx := model.SearchProSummaryPromptCtx{
		CurQuestion:  bs.PromptCtx.Question,
		SearchResult: refDocs,
		RepQuestion:  bs.PromptCtx.Question,
		CurDate:      time.Now().Format("2006-01-02 15:04:05"),
	}
	m.Prompt = config.App().DeepSeekConf.SummaryPromptTpl
	prompt, err := bus.dao.TextTruncate(ctx, m, promptCtx) // 生成Prompt
	if err != nil {
		log.ErrorContextf(ctx, "fail to truncate prompt: %s", err.Error())
		return err
	}
	log.InfoContextf(ctx, "prompt: %s", prompt)
	bs.SearchProResults = pages[0:len(refDocs)] // 设置参考来源
	inferParams := m.CreateModelParamsObject(bs.EnableRandomSeed)
	message := m.WrapMessages(sysRole, histories, prompt)
	req := m.NewLLMRequestWithModelParams(bs.LLMRequestID, message, inferParams)
	startTime, last, lastEvil, _, err := streamReply(ctx, bs, req,
		event.ProcedureLLM, model.ReplyMethodSearch, histories)
	if err != nil || last == nil {
		return err
	}
	isEvil := lastEvil.GetResultCode() == ispkg.ResultEvil
	if !last.GetFinished() || isEvil { // 最后一个包含敏感词也需要兜底结束
		last.Finished = true
		_ = bus.dao.DoEmitWsClient(ctx, bs.To.ClientID,
			bs.NewReplyEvent(ctx, last, isEvil, model.ReplyMethodSearch, startTime, []string{}), cancel,
		)
	}
	reply := last.GetMessage().GetContent()
	method := helper.When(isEvil, model.ReplyMethodEvil, model.ReplyMethodSearch)
	m0 := bs.NewBotRecord(ctx, reply, req, method, lastEvil, startTime)
	newMsg, stat := event.GetMsgRecordAndTokenStat(ctx, m0)
	stat.AgentThought, _ = jsoniter.MarshalToString(bs.Thought)
	_, err = bus.dao.CreateMsgRecord(ctx, newMsg, stat) // for answer
	if err != nil {
		log.ErrorContextf(ctx, "fail to create msg record: %s", err.Error())
	}
	return nil
}

func searchProImpl(ctx context.Context, query string) ([]*model.SearchResultPage, error) {
	// 实例化一个认证对象，入参需要传入腾讯云账户 SecretId 和 SecretKey，此处还需注意密钥对的保密
	// 代码泄露可能会导致 SecretId 和 SecretKey 泄露，并威胁账号下所有资源的安全性。以下代码示例仅供参考，建议采用更安全的方式来使用密钥，
	// 请参见：https://cloud.tencent.com/document/product/1278/85305
	// 密钥可前往官网控制台 https://console.cloud.tencent.com/cam/capi 进行获取
	credential := common.NewCredential(config.App().DeepSeekConf.SearchSecretID, config.App().DeepSeekConf.SearchSecretKey)
	cpf := profile.NewClientProfile()
	cpf.HttpProfile.Endpoint = "tms.tencentcloudapi.com" // todo 后面考虑内网域名
	cpf.HttpProfile.ReqMethod = "POST"
	client := common.NewCommonClient(credential, "", cpf)
	request := tchttp.NewCommonRequest("tms", "2020-12-29", "SearchPro")
	searchReq := model.SearchProRequestBody{}
	searchReq.Query = query
	searchReq.Mode = 2
	params := utils.Any2String(searchReq)
	err := request.SetActionParameters(params)
	if err != nil {
		log.ErrorContextf(ctx, "fail to SetActionParameters: %s", err.Error())
		return nil, err
	}
	response := tchttp.NewCommonResponse()
	err = client.Send(request, response)
	if err != nil {
		log.ErrorContextf(ctx, "fail to invoke search pro api: %s", err.Error())
		return nil, err
	}

	log.InfoContextf(ctx, "search pro response: %s", string(response.GetBody()))
	spBody := model.SearchProResponseBody{}
	err = json.Unmarshal(response.GetBody(), &spBody)
	if err != nil {
		log.ErrorContextf(ctx, "fail to unmarshal response body: %s", err.Error())
		return nil, err
	}
	pages := make([]*model.SearchResultPage, 0)
	for _, pageStr := range spBody.Pages {
		vr := model.New(pageStr)
		var e error
		page := &model.SearchResultPage{}
		if vr.IsVerticalResult() {
			page, e = HandleVerticalResult(ctx, pageStr, vr)
		} else {
			e = json.Unmarshal([]byte(pageStr), page)
		}

		if e != nil {
			log.WarnContextf(ctx, "fail to unmarshal search result page: %s", e.Error())
			continue
		}
		pages = append(pages, page)
	}
	return pages, nil
}

// searchReplyV3 元宝tob联网搜索回复
func searchReplyV3(ctx context.Context, bs *botsession.BotSession) (bool, bool, string, error) {
	pf.StartElapsedAsMetrics(ctx, config.App().StageTaskName.SearchProForDS)
	_, cancel := context.WithCancel(ctx)
	defer cancel()
	showSearchEngine(ctx, bs, cancel)
	bs.NeedCheck = true // 混元ai搜索需要审核
	bs.TokenStat.UpdateProcedure(event.NewProcessingTSProcedure(ctx, event.ProcedureSE))
	SendTokenStat(ctx, bs, bs.TokenStat)
	// 调试和限免不计量
	if !config.App().DeepSeekConf.SearchEnableFree && bs.DebugMessage == nil { // todo 等计费上线后，就去掉该判断
		searchEngineStatus := bus.dao.GetSearchEngineStatus(ctx, bs.App.GetCorpId())
		if searchEngineStatus != 0 {
			d := event.ProcedureDebugging{Content: bs.PromptCtx.Question, CustomVariables: bs.CustomVariablesForDisplay}
			bs.TokenStat.UpdateProcedure(event.NewFailedTSProcedure(ctx, event.ProcedureSE,
				event.WithResourceStatusUnAvailable(), event.WithProcedureDebugging(d)))
			SendTokenStat(ctx, bs, bs.TokenStat)
			return false, false, "", pkg.ErrNoSearchEngineBalance
		}
	}
	m := bs.App.GetModel(ctx, model.AppTypeKnowledgeQA, model.ModelTypeMessage)
	// 获取历史消息
	requestID := model.RequestID(ctx, bs.Session.SessionID, bs.RecordID)
	histories, useRole, err := getHistoryAndRole(ctx, bs, m.GetHistoryLimit(), m.GetHistoryWordsLimit())
	if err != nil {
		log.ErrorContextf(ctx, "fail to get history and role: %s", err.Error())
	}
	sysRole := m.GetSysPrompt(ctx, useRole, model.IsSelfAwarenessIntent(bs.IntentCate), bs.SystemRole)
	message := m.WrapMessages(sysRole, histories, bs.PromptCtx.Question)
	start := time.Now()
	req := m.NewYuanBaoSearchRequest(requestID, message)
	req.ModelName = getYuanBaoSearchModelName(ctx, bs) // 修改模型名
	log.InfoContextf(ctx, "R|botReply|NewYuanBaoSearchRequest|%s %+v", bs.EventSource, req)
	st, last, lastEvil, isModelRejected, err := streamReply(ctx, bs, req,
		event.ProcedureSE, model.ReplyMethodSearch, nil)
	uin, cellphone := pkg.Uin(ctx), ""
	if bs.To != nil && bs.To.CorpStaffBizID > 0 {
		cellphone = bus.dao.GetUserCellphoneByCorpStaffBizID(ctx, bs.To.CorpStaffBizID)
		log.DebugContextf(ctx, "cellphone: %s", cellphone)
	}
	// 上报搜索引擎用量
	if !config.App().DeepSeekConf.SearchEnableFree && bs.DebugMessage == nil { // 调试和限免不计量
		reportSearchEngineDosage(ctx, bs.App, bs.TokenStat.RecordID, start)
	}
	pf.AppendFullSpanElapsed(ctx, config.App().StageTaskName.SearchProForDS, bs.App.GetMainModelName(), "",
		uin, -1, cellphone)
	if err != nil {
		log.ErrorContextf(ctx, "R|botReply|NewYuanBaoSearchRequest|%s %+v", bs.EventSource, err)
		return false, false, "", err
	}
	if last == nil { // 没有输出
		return false, isModelRejected, "", nil
	}
	isEvil := lastEvil.GetResultCode() == ispkg.ResultEvil
	if !last.GetFinished() || isEvil { // 最后一个包含敏感词也需要兜底结束
		last.Finished = true
		_ = bus.dao.DoEmitWsClient(ctx, bs.To.ClientID,
			bs.NewReplyEvent(ctx, last, isEvil, model.ReplyMethodSearch, st, []string{}), cancel,
		)
	}
	reply := last.GetMessage().GetContent()
	method := helper.When(isEvil, model.ReplyMethodEvil, model.ReplyMethodSearch)
	m0 := bs.NewBotRecord(ctx, reply, req, method, lastEvil, st)
	newMsg, stat := event.GetMsgRecordAndTokenStat(ctx, m0)
	stat.AgentThought, _ = jsoniter.MarshalToString(bs.Thought)
	bus.dao.CreateMsgRecord(ctx, newMsg, stat) // for answer
	return false, false, "", nil
}

// HandleVerticalResult 处理垂类结果
func HandleVerticalResult(ctx context.Context, pageStr string,
	vr *model.VerticalResult) (*model.SearchResultPage, error) {
	defer func() {
		if r := recover(); r != nil {
			log.ErrorContextf(ctx, "Recovered from panic:", r)
			return
		}
	}()
	if vr.IsDomesticWeather() {
		return HandleDomesticWeather(ctx, pageStr)
	} else if vr.IsInternationalWeather() {
		return HandleInternationalWeather(ctx, pageStr)
	} else if vr.IsStock() {
		return HandleStock(ctx, pageStr)
	} else if vr.IsMedicalV1() {
		return HandleMedicalV1(ctx, pageStr)
	} else if vr.IsBaikeV1() {
		return HandleBaikeV1(ctx, pageStr)
	} else if vr.IsBaikeV2() {
		return HandleBaikeV2(ctx, pageStr)
	} else if vr.IsBaikeV3() {
		return HandleBaikeV3(ctx, pageStr)
	} else if vr.IsCalendar() {
		return HandleCalendar(ctx, pageStr)
	} else if vr.IsStarV3() || vr.IsStarV4() {
		return HandleStarV3(ctx, pageStr)
	} else if vr.IsGoldV2() || vr.IsGoldV3() {
		return HandleGoldV2(ctx, pageStr)
	} else if vr.IsCarV1() {
		return HandleCarV1(ctx, pageStr)
	} else if vr.IsCarV2() {
		return HandleCarV2(ctx, pageStr)
	} else if vr.IsExchangerate() {
		return HandleExchangerate(ctx, pageStr)
	} else if vr.IsTrainV1() || vr.IsTrainV2() || vr.IsTrainV3() || vr.IsPhone() ||
		vr.IsMedicalV2() || vr.IsStarV1() || vr.IsStarV2() || vr.IsGoldV1() {
		return HandleCommonCard(ctx, pageStr)
	}
	page := &model.SearchResultPage{}
	return page, errors.New("unsupported vertical result")
}

// HandleDomesticWeather 处理国内天气结果
func HandleDomesticWeather(ctx context.Context, pageStr string) (*model.SearchResultPage, error) {
	dw := &model.DomesticWeather{}
	err := jsoniter.Unmarshal([]byte(pageStr), dw)
	if err != nil {
		return nil, err
	}
	passage := ""
	d, _ := jsoniter.MarshalToString(dw.JSONData.DisplayInfo.Day)
	h, _ := jsoniter.MarshalToString(dw.JSONData.DisplayInfo.Hour)
	passage = d + h

	page := &model.SearchResultPage{
		Passage: passage,
		Score:   10,
		URL:     dw.JSONData.DisplayInfo.URL,
		Title:   dw.JSONData.DisplayInfo.Location + " 天气",
		Date:    "",
	}
	return page, nil
}

// HandleInternationalWeather 处理国际天气结果
func HandleInternationalWeather(ctx context.Context, pageStr string) (*model.SearchResultPage, error) {
	iw := &model.InternationalWeather{}
	err := jsoniter.Unmarshal([]byte(pageStr), iw)
	if err != nil {
		return nil, err
	}
	passage := ""
	d, _ := jsoniter.MarshalToString(iw.Display.Subitem.Subdisplay.Day)
	passage = d

	page := &model.SearchResultPage{
		Passage: passage,
		Score:   10,
		URL:     iw.Display.URL,
		Title:   iw.Display.Title,
		Date:    "",
	}
	return page, nil
}

// HandleStock 处理股票结果
func HandleStock(ctx context.Context, pageStr string) (*model.SearchResultPage, error) {
	s := &model.Stock{}
	err := jsoniter.Unmarshal([]byte(pageStr), s)
	if err != nil {
		return nil, err
	}

	passage, _ := jsoniter.MarshalToString(s.JSONData.DisplayInfo.Group)

	page := &model.SearchResultPage{
		Passage: passage,
		Score:   10,
		URL:     s.JSONData.BaseInfo.URL,
		Title:   s.JSONData.BaseInfo.Title,
		Date:    "",
	}
	return page, nil
}

// HandleMedicalV1 .
func HandleMedicalV1(ctx context.Context, pageStr string) (*model.SearchResultPage, error) {
	medical := &model.MedicalRootV1{}
	err := jsoniter.Unmarshal([]byte(pageStr), medical)
	if err != nil {
		return nil, err
	}
	if medical.Display.URL == "" || medical.Display.Title == "" {
		return nil, fmt.Errorf("url or title is empty")
	}

	passage, _ := jsoniter.MarshalToString(medical.Display.SubDisplay)
	page := &model.SearchResultPage{
		Passage: passage,
		Score:   10,
		URL:     medical.Display.URL,
		Title:   medical.Display.Title,
		Date:    medical.Display.Date,
	}
	return page, nil
}

// HandleBaikeV2 .
func HandleBaikeV2(ctx context.Context, pageStr string) (*model.SearchResultPage, error) {
	baike := &model.BaikeRootV2{}
	err := jsoniter.Unmarshal([]byte(pageStr), baike)
	if err != nil {
		return nil, err
	}
	if baike.Display.URL == "" || baike.Display.Title == "" {
		log.ErrorContextf(ctx, "snake recall baike2: %v", baike.Display)
		return nil, fmt.Errorf("url or title is empty")
	}
	passage, _ := jsoniter.MarshalToString(baike.Display.KdJSONStr)
	page := &model.SearchResultPage{
		Passage: passage,
		Score:   10,
		URL:     baike.Display.URL,
		Title:   baike.Display.Title,
		Date:    baike.Display.Date,
	}
	return page, nil
}

// HandleBaikeV1 .
func HandleBaikeV1(ctx context.Context, pageStr string) (*model.SearchResultPage, error) {
	baike := &model.BaikeRootV1{}
	err := jsoniter.Unmarshal([]byte(pageStr), baike)
	if err != nil {
		return nil, err
	}
	if baike.Display.URL == "" || baike.Display.Title == "" {
		return nil, fmt.Errorf("url or title is empty")
	}
	passage, _ := jsoniter.MarshalToString(baike.Display)
	page := &model.SearchResultPage{
		Passage: passage,
		Score:   10,
		URL:     baike.Display.URL,
		Title:   baike.Display.Title,
		Date:    "",
	}
	return page, nil
}

// HandleBaikeV3 .
func HandleBaikeV3(ctx context.Context, pageStr string) (*model.SearchResultPage, error) {
	baike := &model.BaikeRootV3{}
	err := jsoniter.Unmarshal([]byte(pageStr), baike)
	if err != nil {
		return nil, err
	}
	if baike.Display.URL == "" || baike.Display.Title == "" {
		return nil, fmt.Errorf("url or title is empty")
	}
	log.InfoContextf(ctx, "snake recall baike3: %v", baike.Display)
	passage := baike.Display.Content
	page := &model.SearchResultPage{
		Passage: passage,
		Score:   10,
		URL:     baike.Display.URL,
		Title:   baike.Display.Title,
		Date:    baike.Display.Data,
	}
	return page, nil
}

// HandleCalendar .
func HandleCalendar(ctx context.Context, pageStr string) (*model.SearchResultPage, error) {
	calendar := &model.CalendarRoot{}
	err := jsoniter.Unmarshal([]byte(pageStr), calendar)
	if err != nil {
		return nil, err
	}
	if calendar.Display.URL == "" || calendar.Display.Title == "" {
		return nil, fmt.Errorf("url or title is empty")
	}
	passage, _ := jsoniter.MarshalToString(calendar.Display)
	page := &model.SearchResultPage{
		Passage: passage,
		Score:   10,
		URL:     calendar.Display.URL,
		Title:   calendar.Display.Title,
		Date:    calendar.Display.Date,
	}
	return page, nil
}

// HandleCommonCard .
func HandleCommonCard(ctx context.Context, pageStr string) (*model.SearchResultPage, error) {
	commonCard := &model.CommonRoot{}
	err := jsoniter.Unmarshal([]byte(pageStr), commonCard)
	if err != nil {
		log.ErrorContextf(ctx, "snake recall common: %s", err)
		return nil, err
	}
	if commonCard.Display.URL == "" || commonCard.Display.Title == "" {
		return nil, fmt.Errorf("url or title is empty")
	}
	passage, _ := jsoniter.MarshalToString(commonCard.Display)
	page := &model.SearchResultPage{
		Passage: passage,
		Score:   10,
		URL:     commonCard.Display.URL,
		Title:   commonCard.Display.Title,
		Date:    commonCard.Display.Date,
	}
	return page, nil
}

// HandleExchangerate .
func HandleExchangerate(ctx context.Context, pageStr string) (*model.SearchResultPage, error) {
	exchangerate := &model.ExchangerateRoot{}
	err := jsoniter.Unmarshal([]byte(pageStr), exchangerate)
	if err != nil {
		return nil, err
	}
	if exchangerate.JSONData.BaseInfo.URL == "" || exchangerate.JSONData.BaseInfo.Title == "" {
		return nil, fmt.Errorf("url or title is empty")
	}
	passage, _ := jsoniter.MarshalToString(exchangerate.JSONData.DisplayInfo)
	page := &model.SearchResultPage{
		Passage: passage,
		Score:   10,
		URL:     exchangerate.JSONData.BaseInfo.URL,
		Title:   exchangerate.JSONData.BaseInfo.Title,
		Date:    "",
	}
	return page, nil
}

// HandleStarV3 .
func HandleStarV3(ctx context.Context, pageStr string) (*model.SearchResultPage, error) {
	star := &model.StarRootV3{}
	err := jsoniter.Unmarshal([]byte(pageStr), star)
	if err != nil {
		return nil, err
	}
	if star.JSONData.BaseInfo.URL == "" || star.JSONData.BaseInfo.Title == "" {
		return nil, fmt.Errorf("url or title is empty")
	}
	passage, _ := jsoniter.MarshalToString(star.JSONData.DisplayInfo)
	page := &model.SearchResultPage{
		Passage: passage,
		Score:   10,
		URL:     star.JSONData.BaseInfo.URL,
		Title:   star.JSONData.BaseInfo.Title,
		Date:    "",
	}
	return page, nil
}

// HandleGoldV2 .
func HandleGoldV2(ctx context.Context, pageStr string) (*model.SearchResultPage, error) {
	gold := &model.GoldRootV2{}
	err := jsoniter.Unmarshal([]byte(pageStr), gold)
	if err != nil {
		return nil, err
	}
	if gold.JSONData.BaseInfo.URL == "" || gold.JSONData.BaseInfo.Title == "" {
		return nil, fmt.Errorf("url or title is empty")
	}
	passage, _ := jsoniter.MarshalToString(gold.JSONData.DisplayInfo)
	page := &model.SearchResultPage{
		Passage: passage,
		Score:   10,
		URL:     gold.JSONData.BaseInfo.URL,
		Title:   gold.JSONData.BaseInfo.Title,
		Date:    "",
	}
	return page, nil
}

// HandleCarV1 .
func HandleCarV1(ctx context.Context, pageStr string) (*model.SearchResultPage, error) {
	car := &model.CarRootV1{}
	err := jsoniter.Unmarshal([]byte(pageStr), car)
	if err != nil {
		return nil, err
	}
	if car.Display.URL == "" || car.Display.Title == "" {
		return nil, fmt.Errorf("url or title is empty")
	}
	passage, _ := jsoniter.MarshalToString(car.Display.Group)
	page := &model.SearchResultPage{
		Passage: passage,
		Score:   10,
		URL:     car.Display.URL,
		Title:   car.Display.Title,
		Date:    car.Display.Date,
	}
	return page, nil
}

// HandleCarV2 .
func HandleCarV2(ctx context.Context, pageStr string) (*model.SearchResultPage, error) {
	car := &model.CarRootV2{}
	err := jsoniter.Unmarshal([]byte(pageStr), car)
	if err != nil {
		return nil, err
	}
	if car.JSONData.BaseInfo.URL == "" || car.JSONData.BaseInfo.Title == "" {
		return nil, fmt.Errorf("url or title is empty")
	}
	passage, _ := jsoniter.MarshalToString(car.JSONData.DisplayInfo)
	page := &model.SearchResultPage{
		Passage: passage,
		Score:   10,
		URL:     car.JSONData.BaseInfo.URL,
		Title:   car.JSONData.BaseInfo.Title,
		Date:    "",
	}
	return page, nil
}

func showSearchEngine(ctx context.Context, bs *botsession.BotSession, cancel context.CancelFunc) {
	if bs.App.KnowledgeQa.GetUseSearchEngine() && bs.App.KnowledgeQa.GetShowSearchEngine() {
		go func() {
			defer errors.PanicHandler()
			_ = bus.dao.DoEmitWsClient(ctx, bs.To.ClientID, &event.ShowSearchEngineEvent{
				ShowSearchEngine: true, // 显示调用搜索引擎中
			}, cancel)
		}()
	}
}

func getRefDocs(pages []*model.SearchResultPage) []model.RefDoc {
	refDocs := make([]model.RefDoc, 0)
	for i, page := range pages {
		if i >= config.App().DeepSeekConf.SearchResultSize {
			break
		}
		refDocs = append(refDocs, model.RefDoc{
			Num:    i + 1,
			Title:  page.Title,
			Date:   page.Date,
			RefDoc: page.Passage,
		})
	}
	return refDocs
}

// supportNewSearchEngineVersion 是否支持新版本的搜索引擎
func supportNewSearchEngineVersion(ctx context.Context, bs *botsession.BotSession) (bool, string) {
	if bs.App.IsDeepSeekModel(ctx) || bs.IsCustomModel { // ds or 自定义
		ver := "v3"
		if bs.App.GetKnowledgeQa().GetSearchEngine() == model.SearchEngineTypeSogou { // 搜狗
			ver = "v2"
		}
		// 其他情况都是V3
		return true, ver
	}
	return false, ""
}

// getYuanBaoSearchModelName 获取元宝搜索模型名称
func getYuanBaoSearchModelName(ctx context.Context, bs *botsession.BotSession) string {
	if bs.App.IsDeepSeekModel(ctx) {
		return config.App().DeepSeekConf.SearchModel[bs.App.GetModelName()] // 修改模型名
	}
	if bs.IsCustomModel { // 自定义模型固定+search
		return fmt.Sprintf("%s-%s", bs.App.GetMainModelName(), "search")
	}
	return ""
}
