package botsession

import "git.woa.com/ivy/qbot/qbot/chat/helper"

// KBAgentPromptData 用于渲染模板的数据模型
type KBAgentPromptData struct {
	CurrentDate             string
	DatabaseInfo            string
	KBAgentToolsDescription string
	ProjectRequests         string
	Examples                string
	UserQuery               string
	HistoryMessages         string
}

// NewKBAgentPromptData 创建一个新的 KBAgentPromptData 实例
func NewKBAgentPromptData(userQuery string) *KBAgentPromptData {
	return &KBAgentPromptData{
		CurrentDate: helper.GetStringTime(),
		UserQuery:   userQuery,
	}
}
