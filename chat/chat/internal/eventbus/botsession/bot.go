// Package botsession TODO
package botsession

import (
	"context"
	"fmt"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/common/v3/plugins/i18n"
	"git.woa.com/dialogue-platform/common/v3/utils"
	"git.woa.com/dialogue-platform/go-comm/encode"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_DM"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF_DM"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/knowledge"
	llmm "git.woa.com/dialogue-platform/proto/pb-stub/llm-manager-server"
	"git.woa.com/ivy/protobuf/trpc-go/qbot/qbot/chat"
	"git.woa.com/ivy/protobuf/trpc-go/qbot/qbot/infosec"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	"git.woa.com/ivy/qbot/qbot/chat/internal/dao"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model/event"
	"github.com/google/uuid"
	jsoniter "github.com/json-iterator/go"
	"golang.org/x/exp/slices"
)

// ChatMessage 消息详情
type ChatMessage struct {
	Content string
	Role    chat.LLMRole
}

// ChatAtom 每一轮的对话信息【含问题和答案】
type ChatAtom struct {
	RecordID        string        // 答案记录ID
	RelatedRecordID string        // 问题记录ID
	Intent          string        // 算法返回意图
	IntentCategory  string        // 意图达成方式
	OriginQuery     string        // 原始query
	RewriteQuery    string        // 改写后的query
	Message         []ChatMessage // 当轮的对话信息，包括用户输入和机器人回复等
	HasOptionCards  bool          // 是否有选项卡
	OptionCardsStr  string        // 多意图下发的选项卡信息
}

// StreamInfo 存储流式过程中的信息
type StreamInfo struct {
	FirstThought       bool   // 第一个思维链
	FinalThought       bool   // 最后一个思维链
	PreviousRspContent string // 前一个包的回复内容
	MatchPlaceholder   rune   // 命中起始占位符
	InPlaceholderStat  int    // 占位符匹配起始位置
}

// ChatHistories 用户对话记录
type ChatHistories struct {
	ChatStack []ChatAtom // 对话历史信息
}

// Flags 机器人会话标记位
type Flags struct {
	IsBareReply         bool // 是否保守回复
	IsRejectReply       bool // 是否是拒答回复
	IsPriorityQA        bool // 是否问答直出回复
	IsGlobalKnowledge   bool // 是否是全局知识回复
	IsBusy              bool // 超限回复
	IsKnowledgeQAIntent bool // 是否是知识问答意图
	IsJudgeModelReject  bool // 是否需要模型拒答判断
	IsDocSegmentReject  bool // 是否是文档段落被拒答
	IsMLLMReject        bool // 是否是多模态阅读理解拒答
	CanUseTask          bool // 是否可以调用任务型，调用任务型需要满足一定条件
	IsSummaryReject     bool // 摘要拒答，当前不支持单文档摘要。先拒答
	IsSummary           bool // 单文档问答
	IsFileOrImage       bool // 文件或图片
	IsClarifyConfirm    bool // 澄清确认
	IsMultiModal        bool // 是否在多模态流程中
	IsRealTimeDocument  bool // 是否在实时文档流程中
	IsFaqReply          bool // 是否是FAQ回复
	IsEvil              bool // 是否命中恶意内容
	IsMultiIntentReply  bool // 是否是多意图回复
}

// IsDirectReply 判断是否由机器人直接回复
func (f Flags) IsDirectReply() bool {
	return f.IsRejectReply || f.IsPriorityQA || f.IsGlobalKnowledge || f.IsBusy || f.IsSummaryReject
}

// BotSession 机器人会话
type BotSession struct {
	// ID 信息
	SessionID                 string
	RecordID                  string
	RelatedRecordID           string
	RequestID                 string
	LLMRequestID              string // 调用大模型的requestID
	OriginContent             string // 输入信息
	FileInfos                 []*model.FileInfo
	Images                    []string
	StreamingThrottle         int
	SystemRole                string // 用户传入的系统角色, --story=117062263
	IsEvaluateTest            bool   // 是否应用评测；内部调用
	SystemInfo                map[string]string
	CustomVariables           map[string]string // 自定义参数
	CustomVariablesForDisplay []string          // 自定义参数，仅用于前端展示
	StartTime                 time.Time
	WorkflowID                string
	WorkflowSearchExtraParam  *knowledge.WorkflowSearchExtraParam
	WorkflowInput             map[string]string // 工作流输入变量
	CorpStaff                 *model.CorpStaff  // 企业员工信息
	ToolOuputs                []event.ToolOuput // 用户上报的本地工具执行结果
	AgentConfig               event.AgentConfig // 每次对话的动态 agent 配置

	// 环境信息
	EventSource string // 本次建立 botsession 的 event 来源
	Type        model.RecordType
	ModelType   model.ModelType
	SessionType model.SessionType
	Scene       dao.AppScene
	Env         KEP_DM.RunEnvType // 工作流和任务型 不好混用
	QueryType   KEP_DM.QueryType
	// 配置信息
	NeedCheck bool // 送审
	To        *model.Conn
	Session   *model.Session
	App       *model.App
	Labels    []*knowledge.VectorLabel
	// 结果信息
	PromptCtx                   PromptCtx
	ChatHistories               []ChatHistory         // todo 后续这里优化去掉
	ChatHistoriesV2             ChatHistories         // 新版历史消息，后续模型使用基于这个去做裁剪
	LLMHisMessages              [][2]model.HisMessage // 阅读理解历史消息，根据应用配置的轮次保留
	Knowledge                   []*knowledge.SearchKnowledgeRsp_SearchRsp_Doc
	Workflows                   *KEP_WF_DM.RetrieveWorkflowsReply // 检索到的topN工作流
	CustomizeIntent             event.RoleCommand
	RejectedQuestion            any
	Flags                       Flags // 状态位标记
	Intent                      string
	IntentCate                  string
	Placeholders                map[string]string
	CustomParams                []string              // 自定义参数
	TokenStat                   *event.TokenStatEvent // token 使用量统计
	TokenStat2                  *event.TokenStatEvent // token 使用量统计, 如果 dm 回第二个包时使用
	Caption                     string
	ReferIndex                  []int // 大模型返回的引用索引
	Msg                         *model.MsgRecord
	QuestionAnswers             [][2]string
	ImageQueue                  []*model.ImageQueue
	FileQueue                   []*model.FileQueue
	Reference                   string
	QuoteInfos                  string
	KnowledgeTruncatedIndex     int // 知识点截断索引,用于匹配参考来源
	EnableRandomSeed            bool
	DebugMessage                *event.DebugMessage // 调试信息，支持调试接口传入上下文
	ToolsInfo                   map[string]model.AgentTool
	AgentStatus                 *model.AgentStatus
	WorkflowDebug               bool                  // 是否是工作流调试，包含了PDL调试
	QueryRewrite                model.QueryRewriteRsp // 用来存储改写结果
	Plugins                     []*event.PluginInfo
	AgentSystemVar              model.SystemVar
	Thought                     *event.AgentThoughtEvent  // 思考过程
	SearchProResults            []*model.SearchResultPage // 搜狗的搜索结果
	ModelName                   string                    // 用户指定模型名称请求
	ChatHistoryFromAPI          []*event.ChatHistory
	StreamInfo                  StreamInfo
	Verbose                     int
	IsCustomModel               bool                 // 是否自定义模型
	IsModelFree                 bool                 // 是否是免费模型
	IsDocPriorityAfterRetrieval bool                 // 检索结果中是否文档最高优
	IntentRsp                   model.IntentRsp      // 意图识别结果
	SearchNetwork               model.SearchNetwork  // 对话段配置了是否联网
	EnableMultiIntent           bool                 // 是否支持下发多意图
	SearchResultLen             int                  // 检索结果个数
	Stream                      model.ChatStream     `json:"stream"`          // 是否开启流式输出
	WorkflowStatus              model.WorkflowStatus `json:"workflow_status"` // 是否开启工作流
	ChannelType                 int                  // 渠道类型:10000 公众号 10002 企业微信
	LKEUserID                   string               // 应用角色id
}

// String 会话字符串
func (bs BotSession) String() string {
	return helper.Object2StringEscapeHTML(bs)
}

// NewBotRecord 生成机器人回复消息
func (bs BotSession) NewBotRecord(ctx context.Context,
	content string,
	req *llmm.Request, method model.ReplyMethod,
	evil *infosec.CheckRsp, st time.Time,
) model.MsgRecord {
	prompt, _ := jsoniter.MarshalToString(req)
	// TODO(sim): add track
	knowledge, _ := jsoniter.MarshalToString(bs.Knowledge)
	rejectedQuestion, _ := jsoniter.MarshalToString(bs.RejectedQuestion)
	var ts, stat string
	if bs.TokenStat != nil {
		ts, _ = jsoniter.MarshalToString(bs.TokenStat)
		stat = bs.TokenStat.GetLLMMStatsJSON()
	}
	content, _, _ = helper.MatchSearchResults(ctx, content)
	quoteInfos := bs.QuoteInfos
	if utf8.RuneCountInString(quoteInfos) > 512 {
		quoteInfos = ""
	}
	var option []string
	if method == model.ReplyMethodMultiIntent {
		option = bs.IntentRsp.MultiIntent.OptionCards
	}
	log.DebugContextf(ctx, "in msg record intent: %s intentCate: %s", bs.Intent, bs.IntentCate)
	return model.MsgRecord{
		Type:             bs.Type,
		BotBizID:         bs.App.AppBizId,
		RecordID:         bs.RecordID,
		RelatedRecordID:  bs.RelatedRecordID,
		ToID:             bs.To.CorpStaffID,
		ToType:           bs.To.GetSourceType(),
		FromID:           bs.App.GetId(),
		FromType:         model.SourceTypeRobot,
		Content:          content,
		StatisticInfo:    stat,
		Labels:           helper.Object2String(bs.Labels),
		Intent:           bs.GetIntent(),
		IntentCategory:   bs.IntentCate,
		Reference:        bs.Reference,
		Prompt:           prompt,
		Knowledge:        knowledge,
		RejectedQuestion: rejectedQuestion,
		ReplyMethod:      method,
		CreateTime:       st,
		ResultCode:       evil.GetResultCode(),
		ResultType:       evil.GetResultType(),
		SessionID:        bs.Session.SessionID,
		CfgVersionID:     bs.App.GetConfigVersionId(),
		TraceID:          model.TraceID(ctx),
		TokenStat:        ts,
		QuoteInfos:       quoteInfos,
		OptionCards:      utils.Any2String(option),
		ChannelType:      bs.ChannelType,
	}
}

// NewTaskBotRecord 生成机器人回复消息 for DM
func (bs BotSession) NewTaskBotRecord(ctx context.Context,
	content string, req *llmm.Request, method model.ReplyMethod,
	evil *infosec.CheckRsp, st time.Time, ets *event.TokenStatEvent, optionCards []string,
	tf *event.TaskFlowDebugInfo,
) model.MsgRecord {
	prompt, _ := jsoniter.MarshalToString(req)
	var recordID, tsj string
	var statisticInfo string
	if ets != nil {
		recordID = ets.RecordID
		tsj, _ = jsoniter.MarshalToString(ets)
		statisticInfo = ets.GetLLMMStatsJSON()
	}
	var oc string
	if len(optionCards) > 0 {
		oc, _ = jsoniter.MarshalToString(optionCards)
		if utf8.RuneCountInString(oc) > 1024 {
			oc = ""
		}
	}
	var tfj string
	if tf != nil {
		tfj, _ = jsoniter.MarshalToString(tf)
		if utf8.RuneCountInString(tfj) > 65533 {
			tfj = ""
		}
	}
	return model.MsgRecord{
		BotBizID:        bs.App.GetAppBizId(),
		Type:            bs.Type,
		RecordID:        recordID,
		RelatedRecordID: bs.RelatedRecordID,
		ToID:            bs.To.CorpStaffID,
		ToType:          bs.To.GetSourceType(),
		FromID:          bs.App.GetId(),
		FromType:        model.SourceTypeRobot,
		Content:         content,
		Intent:          bs.Intent,
		IntentCategory:  bs.IntentCate,
		StatisticInfo:   statisticInfo,
		Prompt:          prompt,
		ReplyMethod:     method,
		CreateTime:      st,
		ResultCode:      evil.GetResultCode(),
		ResultType:      evil.GetResultType(),
		SessionID:       bs.Session.SessionID,
		CfgVersionID:    bs.App.GetConfigVersionId(),
		TraceID:         model.TraceID(ctx),
		TokenStat:       tsj,
		OptionCards:     oc,
		TaskFlow:        tfj,
	}
}

// NewWorkflowRecord 生成机器人回复消息 for workflow
func (bs BotSession) NewWorkflowRecord(ctx context.Context,
	content string, method model.ReplyMethod, evil *infosec.CheckRsp,
	ets *event.TokenStatEvent, optionCards []string, reference []model.Reference,
) model.MsgRecord {
	var recordID, tsj, oc string
	var statisticInfo string
	if ets != nil {
		recordID = ets.RecordID
		tsj, _ = jsoniter.MarshalToString(ets)
		statisticInfo = ets.GetLLMMStatsJSON()
	}
	labelStr, _ := jsoniter.MarshalToString(bs.Labels)
	if len(optionCards) > 0 {
		oc, _ = jsoniter.MarshalToString(optionCards)
		if utf8.RuneCountInString(oc) > 1024 {
			oc = ""
		}
	}
	return model.MsgRecord{
		BotBizID:        bs.App.GetAppBizId(),
		Type:            bs.Type,
		RecordID:        recordID,
		RelatedRecordID: bs.RelatedRecordID,
		ToID:            bs.To.CorpStaffID,
		ToType:          bs.To.GetSourceType(),
		FromID:          bs.App.GetId(),
		FromType:        model.SourceTypeRobot,
		Content:         content,
		Intent:          bs.Intent,
		IntentCategory:  bs.IntentCate,
		Labels:          labelStr,
		StatisticInfo:   statisticInfo,
		Prompt:          "",
		ReplyMethod:     method,
		CreateTime:      time.Now(),
		ResultCode:      evil.GetResultCode(),
		ResultType:      evil.GetResultType(),
		SessionID:       bs.Session.SessionID,
		CfgVersionID:    bs.App.GetConfigVersionId(),
		TraceID:         model.TraceID(ctx),
		TokenStat:       tsj,
		OptionCards:     oc,
		Reference:       helper.Object2String(reference),
	}
}

// ToDirectReply 转为直接回复文案和回复方式
func (bs BotSession) ToDirectReply(ctx context.Context) (string, model.ReplyMethod) {
	f := bs.Flags
	cfg := config.App().Bot
	if f.IsBusy {
		replies := cfg.BusyReplies
		reply, method := replies[uint8(model.RecordTypeMessage)], model.ReplyMethodBusy
		if v, ok := replies[uint8(bs.Type)]; ok {
			reply = v
		}
		return i18n.Translate(ctx, reply), method
	}
	if f.IsRejectReply {
		return i18n.Translate(ctx, cfg.RejectedReply), model.ReplyMethodRejected
	}
	if f.IsGlobalKnowledge {
		return bs.Knowledge[0].GetAnswer(), model.ReplyGlobalKnowledge
	}
	if f.IsPriorityQA {
		return bs.Knowledge[0].GetAnswer(), model.ReplyMethodPriorityQA
	}
	if f.IsBareReply {
		return bs.App.GetKnowledgeQa().GetOutput().GetBareAnswer(), model.ReplyMethodBare
	}
	if f.IsSummaryReject {
		return i18n.Translate(ctx, cfg.SummaryReject), model.ReplyMethodRejected
	}
	if f.IsFaqReply {
		return bs.Knowledge[0].GetAnswer(), model.ReplyMethodPriorityQA // 复用ReplyMethod
	}
	if f.IsMultiIntentReply {
		return cfg.MultiIntentReply, model.ReplyMethodMultiIntent
	}
	if f.IsClarifyConfirm {
		return cfg.ClarifyConfirmReply, model.ReplyMethodClarifyConfirm // 澄清确认
	}
	return "", 0
}

// NewDirectBotRecord 生成机器人直接回复消息
func (bs BotSession) NewDirectBotRecord(ctx context.Context, content string, method model.ReplyMethod,
	st time.Time) model.MsgRecord {
	return bs.NewBotRecord(ctx, content, nil, method, nil, st)
}

// NewReplyEvent 生成回复事件
func (bs BotSession) NewReplyEvent(ctx context.Context, rsp *llmm.Response, isEvil bool,
	replyMethod model.ReplyMethod, st time.Time, optionCards []string) model.WsEvent {
	reply := rsp.GetMessage().GetContent()
	reply = helper.RemoveReference(bs.App.AppBizId, reply)
	reply = strings.ReplaceAll(reply, "\n引用:", "")
	reply = strings.ReplaceAll(reply, "\n引用：", "")
	replyMethod = helper.When(isEvil, model.ReplyMethodEvil, replyMethod)
	if bs.Type == model.RecordTypeMessage ||
		bs.Type == model.RecordTypeExperience ||
		bs.Type == model.RecordTypeSearch ||
		bs.Type == model.RecordTypeOPDebug {
		var positions, indexes []int
		reply, positions, indexes = helper.MatchSearchResults(ctx, reply)
		quoteInfos := bs.GetQuoteInfos(rsp, positions, indexes)
		if isEvil {
			quoteInfos = []*model.QuoteInfo{}
		}
		r := &event.ReplyEvent{
			RequestID:       bs.RequestID,
			SessionID:       bs.Session.SessionID,
			Content:         helper.When(isEvil, i18n.Translate(ctx, config.App().Bot.EvilReply), reply),
			FromName:        bs.App.BaseConfig.GetName(),
			FromAvatar:      bs.App.BaseConfig.GetAvatar(),
			RecordID:        bs.RecordID,
			RelatedRecordID: bs.RelatedRecordID,
			Timestamp:       st.Unix(),
			IsFinal:         rsp.GetFinished(),
			IsFromSelf:      false,
			CanRating:       model.CanMsgRating(bs.Type, replyMethod, bs.App.GetAppType()),
			CanFeedback:     model.CanMsgFeedback(bs.Type, replyMethod),
			IsEvil:          isEvil,
			IsLLMGenerated:  bs.IsLLMGenerated(replyMethod),
			Knowledge:       helper.Map(bs.Knowledge, NewReplyKnowledge),
			ReplyMethod:     replyMethod,
			IntentCategory:  bs.IntentCate,
			OptionCards:     optionCards,
			CustomParams:    bs.CustomParams,
			FileInfos:       bs.FileInfos,
			QuoteInfos:      quoteInfos,
		}
		switch bs.EventSource {
		case event.EventTagExtraction, event.EventTagExtractionExperience:
			if isEvil {
				r.Content = i18n.Translate(ctx, config.App().Bot.TagEvilReply)
			} else {
				r.Content = ""
				if reply == "" {
					r.Content = i18n.Translate(ctx, config.App().App.TagExtraction.DefaultExtractAnswer)
				}
				r.Tags = event.ParseText2Tags(ctx, reply)
			}
		case event.EventKnowledgeSummary, event.EventKnowledgeSummaryExperience:
			if isEvil {
				r.Content = i18n.Translate(ctx, config.App().Bot.SummaryEvilReply)
			}
		case event.EventExperience, event.EventSend:
			if reply == "" {
				if rsp.GetMessage().GetReasoningContent() != "" { // content为空，思考不为空，不下发内容
					r.Content = ""
				} else {
					r.Content = i18n.Translate(ctx, config.App().Bot.EmptyReply)
				}
			}
		default:
			log.DebugContextf(ctx, "unknown event source: %s", bs.EventSource)
		}
		return r
	}
	return nil
}

// GetQuoteInfos 生成引用信息
func (bs BotSession) GetQuoteInfos(rsp *llmm.Response, positions []int, indexes []int) []*model.QuoteInfo {
	var quoteInfos []*model.QuoteInfo
	if len(indexes) == 0 {
		return quoteInfos
	}
	if bs.SearchResultLen > 0 {
		for i, index := range indexes {
			if index > bs.SearchResultLen {
				continue
			}
			quoteInfo := &model.QuoteInfo{
				Position: positions[i],
				Index:    indexes[i],
			}
			quoteInfos = append(quoteInfos, quoteInfo)
		}
		return quoteInfos
	}
	return quoteInfos
}

// NewTaskReplyEvent 生成回复事件
func (bs BotSession) NewTaskReplyEvent(ctx context.Context, recordID string,
	rsp *llmm.Response, isEvil bool, replyMethod model.ReplyMethod,
	st time.Time, optionCards []string, debugInfo *event.TaskFlowDebugInfo,
) model.WsEvent {
	reply := rsp.GetMessage().GetContent()
	replyMethod = helper.When(isEvil, model.ReplyMethodEvil, replyMethod)
	if bs.Type == model.RecordTypeMessage ||
		bs.Type == model.RecordTypeExperience ||
		bs.Type == model.RecordTypeSearch ||
		bs.Type == model.RecordTypeOPDebug {
		re := &event.ReplyEvent{
			RequestID:       bs.RequestID,
			SessionID:       bs.Session.SessionID,
			Content:         helper.When(isEvil, i18n.Translate(ctx, config.App().Bot.EvilReply), reply),
			FromName:        bs.App.BaseConfig.GetName(),
			FromAvatar:      bs.App.BaseConfig.GetAvatar(),
			RecordID:        recordID,
			RelatedRecordID: bs.RelatedRecordID,
			Timestamp:       st.Unix(),
			IsFinal:         rsp.GetFinished(),
			IsFromSelf:      false,
			CanRating:       model.CanMsgRating(bs.Type, replyMethod, bs.App.GetAppType()),
			CanFeedback:     true, // model.CanMsgFeedback(ts.Type, replyMethod),
			IsEvil:          isEvil,
			IsLLMGenerated:  bs.IsLLMGenerated(replyMethod),
			ReplyMethod:     replyMethod,
			OptionCards:     optionCards,
			TaskFlow:        debugInfo,
		}
		if debugInfo != nil && debugInfo.Type == event.ResponseTypeCustom {
			re.IsLLMGenerated = false
		}
		return re
	}
	return nil
}

// GetReferences 获取引用
func GetReferences(ref []*KEP_WF_DM.Reference) []model.Reference {
	res := make([]model.Reference, 0)
	for _, v := range ref {
		res = append(res, model.Reference{
			ID:    v.GetID(),
			Type:  v.GetType(),
			URL:   v.GetUrl(),
			DocID: v.GetDocID(),
			Name:  v.GetName(),
		})
	}
	return res
}

// NewWorkflowReplyEvent 生成回复事件
func (bs BotSession) NewWorkflowReplyEvent(ctx context.Context, rsp *KEP_WF_DM.RunWorkflowReply,
	isEvil bool, replyMethod model.ReplyMethod, debugInfo *event.WorkflowDebugInfo,
) model.WsEvent {
	reply := rsp.GetRespond().GetContent()
	replyMethod = helper.When(isEvil, model.ReplyMethodEvil, replyMethod)
	if bs.Type == model.RecordTypeMessage ||
		bs.Type == model.RecordTypeExperience ||
		bs.Type == model.RecordTypeSearch ||
		bs.Type == model.RecordTypeOPDebug {
		reply, _, _ = helper.MatchSearchResults(context.Background(), reply)
		b, _ := jsoniter.MarshalToString(rsp.GetRespond().GetAnswerOutput())
		re := &event.ReplyEvent{
			RequestID:       bs.RequestID,
			SessionID:       bs.Session.SessionID,
			Content:         helper.When(isEvil, i18n.Translate(ctx, config.App().Bot.EvilReply), reply),
			FromName:        bs.App.BaseConfig.GetName(),
			FromAvatar:      bs.App.BaseConfig.GetAvatar(),
			RecordID:        bs.RecordID,
			RelatedRecordID: bs.RelatedRecordID,
			Timestamp:       bs.StartTime.Unix(),
			IsFinal:         rsp.IsFinal,
			IsFromSelf:      false,
			CanRating:       model.CanMsgRating(bs.Type, replyMethod, bs.App.GetAppType()),
			CanFeedback:     bs.EventSource == event.EventExperience, // model.CanMsgFeedback(ts.Type, replyMethod),
			IsEvil:          isEvil,
			IsLLMGenerated:  bs.IsLLMGenerated(replyMethod),
			ReplyMethod:     replyMethod,
			Workflow:        debugInfo,
			OptionCards:     rsp.GetRespond().GetOptionCards(),
			CustomParams:    []string{b},
		}
		return re
	}
	return nil
}

// GetIntent 获取意图
func (bs BotSession) GetIntent() string {
	length := len([]rune(bs.Intent))
	if length <= 100 {
		return bs.Intent
	}
	return string([]rune(bs.Intent)[:100]) + "..."
}

// NewBusySession 构造机器人忙会话
func NewBusySession(to *model.Conn, session *model.Session, app *model.App,
	typ model.RecordType, requestID, relatedRecordID string) *BotSession {
	return &BotSession{
		Session:         session,
		Type:            typ,
		RecordID:        uuid.NewString(),
		RelatedRecordID: relatedRecordID,
		RequestID:       requestID,
		To:              to,
		App:             app,
		Flags:           Flags{IsBusy: true},
	}
}

// IsKnowledgeOutputStream 知识问答是否流失输出   流式：true，非流式false
func (bs BotSession) IsKnowledgeOutputStream() bool {
	if bs.Stream == model.ChatStreamEnabled { // 对话端开启流式，即认为开启流式，不需要判断配置
		return true
	}
	// Method输出方式 1：流式 2：非流式
	if bs.Stream == model.ChatStreamDefault && bs.App.GetKnowledgeQa().GetOutput() != nil &&
		bs.App.GetKnowledgeQa().GetOutput().GetMethod() == 1 {
		// 对话端未设置流式/非流式，就采用配置的值
		return true
	}
	return false
}

// IsWorkflowOutputStream 工作流是否流失输出   流式：true，非流式false
func (bs BotSession) IsWorkflowOutputStream() bool {
	if bs.Stream == model.ChatStreamEnabled { // 对话端开启流式，即认为开启流式，不需要判断配置
		return true
	}
	// Method输出方式 1：流式 2：非流式
	if bs.Stream == model.ChatStreamDefault && bs.App.GetKnowledgeQa().GetOutput() != nil &&
		bs.App.GetKnowledgeQa().GetOutput().GetMethod() == 1 {
		// 对话端未设置流式/非流式，就采用配置的值
		return true
	}
	return false
}

// CopyBotSession 复制一个新的会话信息
func (bs BotSession) CopyBotSession() *BotSession {
	newBs := bs
	var ts event.TokenStatEvent
	if bs.TokenStat != nil {
		ts = *bs.TokenStat
	}
	ts.Procedures = nil // 初始化复制出来的会话对象的调用过程
	ts.RecordID = encode.GenerateSessionID()
	newBs.RecordID = ts.RecordID
	newBs.TokenStat = &ts
	return &newBs
}

// IsClarifyConfirm 是否需要澄清确认
func (bs BotSession) IsClarifyConfirm(filterKey string, filterQuestions []string) bool {
	cfg := config.App().ClarifyConfirm
	if !slices.Contains(cfg.WhiteList, bs.App.GetAppBizId()) || cfg.TopN == 0 || !bs.Flags.IsKnowledgeQAIntent {
		return false
	}
	qas := bs.getClarifyConfirmQas()
	if len(qas) == 0 {
		return false
	}
	confidence := bs.App.GetQaConfidence(filterKey)
	for _, v := range qas {
		if v.GetConfidence() >= confidence {
			return false
		}
	}
	return len(getFilterClarifyConfirmQa(filterQuestions, qas)) > 0
}

func (bs BotSession) getClarifyConfirmQas() []*knowledge.SearchKnowledgeRsp_SearchRsp_Doc {
	// 需要判断置信度是否在{问答检索匹配度,问答对直接回复的匹配度}之间
	qas := helper.Filter(bs.Knowledge, func(k *knowledge.SearchKnowledgeRsp_SearchRsp_Doc) bool {
		return k.GetDocType() == model.DocTypeQA && k.GetConfidence() > 0
	})
	return qas
}

// Filter 函数接受一个集合和一个筛选函数，返回一个新的集合，包含满足条件的元素。
func Filter(collection []*knowledge.SearchKnowledgeRsp_SearchRsp_Doc, condition func(
	*knowledge.SearchKnowledgeRsp_SearchRsp_Doc) bool) []*knowledge.SearchKnowledgeRsp_SearchRsp_Doc {
	var result []*knowledge.SearchKnowledgeRsp_SearchRsp_Doc
	for _, item := range collection {
		if condition(item) {
			result = append(result, item)
		}
	}
	return result
}

func getFilterClarifyConfirmQa(filterQuestions []string,
	qas []*knowledge.SearchKnowledgeRsp_SearchRsp_Doc) []*knowledge.SearchKnowledgeRsp_SearchRsp_Doc {
	if len(filterQuestions) == 0 {
		return qas
	}
	filterQas := make([]*knowledge.SearchKnowledgeRsp_SearchRsp_Doc, 0)
	for _, v := range qas {
		question := v.GetQuestion()
		if v.GetSimilarQuestionExtra().GetSimilarId() > 0 {
			question = v.GetSimilarQuestionExtra().GetSimilarQuestion()
		}
		if slices.Contains(filterQuestions, question) {
			continue
		}
		filterQas = append(filterQas, v)
	}
	return filterQas
}

// ToClarifyConfirmReply 转为澄清确认回复文案和回复方式
func (bs BotSession) ToClarifyConfirmReply(filterQuestions []string) (string, model.ReplyMethod) {
	qas := getFilterClarifyConfirmQa(filterQuestions, bs.getClarifyConfirmQas())
	if len(qas) == 0 {
		return "", 0
	}
	sort.Slice(qas, func(i, j int) bool {
		return qas[i].GetConfidence() > qas[j].GetConfidence()
	})
	topN := config.App().ClarifyConfirm.TopN
	unique := make(map[string]struct{})
	questions := make([]string, 0)
	for _, v := range qas {
		question := v.GetQuestion()
		if v.GetSimilarQuestionExtra().GetSimilarId() > 0 {
			question = v.GetSimilarQuestionExtra().GetSimilarQuestion()
		}
		if _, ok := unique[question]; !ok {
			unique[question] = struct{}{}
			questions = append(questions, fmt.Sprintf("[%s]()", question))
		}
		if len(questions) >= topN {
			break
		}
	}
	reply := fmt.Sprintf("猜你想问:<br><br>%s", strings.Join(questions, "<br>"))
	return reply, model.ReplyMethodClarifyConfirm
}

// IsLLMGenerated 判断是否由大模型生成
func (bs BotSession) IsLLMGenerated(replyMethod model.ReplyMethod) bool {
	return replyMethod != model.ReplyMethodBare &&
		replyMethod != model.ReplyMethodRejected && replyMethod != model.ReplyMethodEvil &&
		replyMethod != model.ReplyMethodPriorityQA && replyMethod != model.ReplyMethodGreeting &&
		replyMethod != model.ReplyMethodBusy && replyMethod != model.ReplyGlobalKnowledge
}

// NewReplyKnowledge 构造回复事件中的知识
func NewReplyKnowledge(k *knowledge.SearchKnowledgeRsp_SearchRsp_Doc) event.ReplyKnowledge {
	rk := event.ReplyKnowledge{ID: fmt.Sprintf("%d", k.GetRelatedBizId()), Type: k.GetDocType()}
	if k.GetDocType() == 2 { // 片段
		rk.SegID = fmt.Sprintf("%d", k.GetRelatedId())
	}
	return rk
}

// NewAgentReplyEvent 生成回复事件
func (bs BotSession) NewAgentReplyEvent(ctx context.Context, rsp *llmm.Response, isEvil bool,
	replyMethod model.ReplyMethod, st time.Time, extra event.ReplyExtraInfo) model.WsEvent {
	reply := bs.GetAgentReply(ctx, rsp)
	reply = helper.ReplaceURLPlaceholders(reply, bs.Placeholders)
	reply = helper.ReplaceInvalidHTML(reply)
	reply = helper.FillEmptyLinkText(reply)
	reply = strings.ReplaceAll(reply, "\\n", "\n") // todo 统一加到配置里
	reply = strings.ReplaceAll(reply, "\\\\", "\\")
	reply = strings.ReplaceAll(reply, "\\u0026", "\u0026")
	reply = strings.ReplaceAll(reply, "\\u003c", "\u003c")
	reply = strings.ReplaceAll(reply, "\\u003e", "\u003e")
	replyMethod = helper.When(isEvil, model.ReplyMethodEvil, replyMethod)
	if bs.Type == model.RecordTypeMessage ||
		bs.Type == model.RecordTypeExperience ||
		bs.Type == model.RecordTypeSearch ||
		bs.Type == model.RecordTypeOPDebug {
		reply, positions, indexes := helper.MatchSearchResults(ctx, reply)
		quoteInfos := bs.GetQuoteInfos(rsp, positions, indexes)
		r := &event.ReplyEvent{
			RequestID:       bs.RequestID,
			SessionID:       bs.Session.SessionID,
			Content:         helper.When(isEvil, i18n.Translate(ctx, config.App().Bot.EvilReply), reply),
			FromName:        bs.App.BaseConfig.GetName(),
			FromAvatar:      bs.App.BaseConfig.GetAvatar(),
			RecordID:        bs.RecordID,
			RelatedRecordID: bs.RelatedRecordID,
			Timestamp:       st.Unix(),
			IsFinal:         rsp.GetFinished(),
			IsFromSelf:      false,
			CanRating:       model.CanMsgRating(bs.Type, replyMethod, bs.App.GetAppType()),
			CanFeedback:     model.CanMsgFeedback(bs.Type, replyMethod),
			IsEvil:          isEvil,
			IsLLMGenerated:  bs.IsLLMGenerated(replyMethod),
			Knowledge:       helper.Map(bs.Knowledge, NewReplyKnowledge),
			ReplyMethod:     replyMethod,
			IntentCategory:  bs.IntentCate,
			CustomParams:    bs.CustomParams,
			FileInfos:       bs.FileInfos,
			QuoteInfos:      quoteInfos,
			ExtraInfo:       extra,
		}
		switch bs.EventSource {
		case event.EventExperience, event.EventSend:
			if reply == "" {
				r.Content = i18n.Translate(ctx, config.App().Bot.EmptyReply)
			}
		default:
			log.DebugContextf(ctx, "unknown event source: %s", bs.EventSource)
		}
		return r
	}
	return nil
}

// GetAgentReply 生成机器人回复消息
func (bs BotSession) GetAgentReply(ctx context.Context, rsp *llmm.Response) (reply string) {
	if len(rsp.GetMessage().GetToolCalls()) == 0 {
		// 无工具调用,content 就是 reply
		return rsp.GetMessage().GetContent()
	}
	function := rsp.GetMessage().GetToolCalls()[0].GetFunction()
	switch function.Name {
	case "ask_user":
		return model.MatchAskUserReply(function.GetArguments())
	case "answer_directly":
		return model.MatchAnswerDirectlyReply(function.GetArguments())
	case "task_completed":
		return model.MatchTaskCompletedReply(function.GetArguments())
	case "response_to_user":
		return model.MatchResponseToUserReply(function.GetArguments())
	default: // 上游校验过function.Name，不会走到这里
		log.ErrorContextf(ctx, "unknown function name: %s", function.Name)
		return reply
	}
}

// GetAssistantContent 获取机器人的回复内容
func (msg *ChatAtom) GetAssistantContent() string {
	for _, v := range msg.Message {
		if v.Role == chat.LLMRole_ASSISTANT {
			return v.Content
		}
	}
	return ""
}

// GetFinanceSubBizType 获取计费子业务类型
func (bs BotSession) GetFinanceSubBizType() string {
	if bs.IsEvaluateTest {
		return dao.FinanceSubBizTypeEvaluateTest
	}
	if bs.To == nil {
		return dao.FinanceSubBizTypeKnowledgeQA
	}
	switch bs.To.Type {
	case model.ConnTypeVisitor:
		return dao.FinanceSubBizTypeKnowledgeQAUser
	case model.ConnTypeExperience:
		return dao.FinanceSubBizTypeKnowledgeQADialogTest
	case model.ConnTypeAPIVisitor:
		return dao.FinanceSubBizTypeKnowledgeQA
	}
	return dao.FinanceSubBizTypeKnowledgeQA
}

// MakeChatAPIHistory .
func (bs BotSession) MakeChatAPIHistory() [][2]model.HisMessage {
	var histories [][2]model.HisMessage
	for i := 0; i < len(bs.ChatHistoryFromAPI); i = i + 2 {
		userContent := ""
		botContent := ""
		if bs.ChatHistoryFromAPI[i].Role == chat.LLMRole_USER {
			userContent = bs.ChatHistoryFromAPI[i].Content
		}
		if i+1 < len(bs.ChatHistoryFromAPI) {
			if bs.ChatHistoryFromAPI[i+1].Role == chat.LLMRole_ASSISTANT {
				botContent = bs.ChatHistoryFromAPI[i+1].Content
			}
		}
		histories = append(histories, [2]model.HisMessage{
			{Content: userContent},
			{Content: botContent},
		})
	}
	return histories
}

// IsSearchEngineEnabled TODO
func (bs BotSession) IsSearchEngineEnabled() bool {
	if bs.App.GetKnowledgeQa().GetUseSearchEngine() && bs.SearchNetwork != model.SearchNetworkDisabled {
		return true
	}
	return false
}

// IsWorkflowStatusEnabled 是否开启工作流
func (bs BotSession) IsWorkflowStatusEnabled() bool {
	if bs.WorkflowStatus == model.WorkflowStatusEnabled || bs.WorkflowStatus == model.WorkflowStatusDefault {
		return true
	}
	return false
}

// HasKnowledge 是否有知识库
func (bs BotSession) HasKnowledge(ctx context.Context) (flag bool) {
	defer func() {
		log.InfoContextf(ctx, "HasKnowledge: %v", flag)
	}()
	if len(bs.Knowledge) == 0 {
		return flag
	}
	docs := make([]*knowledge.SearchKnowledgeRsp_SearchRsp_Doc, 0)
	if !bs.App.GetCombinedKnowledgeRetrieval() { // 没有开启组合知识检索，不含问答
		for _, doc := range bs.Knowledge {
			if doc.DocType == 2 {
				docs = append(docs, doc)
			}
		}
	} else { // 含问答
		docs = bs.Knowledge
	}
	if len(docs) > 0 {
		flag = true
	}
	return flag
}
