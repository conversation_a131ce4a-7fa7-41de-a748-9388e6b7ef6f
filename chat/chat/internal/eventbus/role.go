package eventbus

import (
	"context"
	"errors"
	"regexp"
	"strings"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/common/v3/sync/errgroupx"
	"git.woa.com/dialogue-platform/go-comm/clues"
	"git.woa.com/dialogue-platform/go-comm/encode"
	llmm "git.woa.com/dialogue-platform/proto/pb-stub/llm-manager-server"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	"git.woa.com/ivy/qbot/qbot/chat/internal/dao"
	"git.woa.com/ivy/qbot/qbot/chat/internal/eventbus/botsession"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model/event"
	"git.woa.com/ivy/qbot/qbot/chat/pkg"
	ispkg "git.woa.com/ivy/qbot/qbot/infosec/pkg"
	"github.com/google/uuid"
	jsoniter "github.com/json-iterator/go"
)

func init() {
	handlers[event.EventSSeRoleSend] = &SendRoleEventHandler{}
}

// SendRoleEventHandler 消息上行事件处理器
type SendRoleEventHandler struct{}

// NewRequest 创建事件请求
func (e *SendRoleEventHandler) NewRequest(ctx context.Context, conn *model.Conn, bs []byte) (any, error) {
	log.InfoContextf(ctx, "SendRoleEventHandler NewRequest %+v", conn)
	req := event.SSeRoleSendEvent{}
	if err := jsoniter.Unmarshal(bs, &req); err != nil {
		log.ErrorContextf(ctx, "Unmarshal event req error: %+v, data: %s", err, bs)
		return nil, err
	}
	if err := req.IsValid(); err != nil {
		return nil, err
	}
	req.OriginContent = req.Content
	// 框架未携带, 则使用业务数据
	pkg.WithSessionID(ctx, req.SessionID)
	pkg.WithRequestID(ctx, req.RequestID)
	pkg.WithTraceID(ctx, req.RequestID)
	return req, nil
}

// Process 处理事件请求
func (e *SendRoleEventHandler) Process(ctx context.Context, coon *model.Conn, req any) error {
	log.InfoContextf(ctx, "SendRoleEventHandler Process start %+v %+v", coon, req)
	defer func(ctx *context.Context) { clues.Flush(*ctx) }(&ctx)
	ctx = clues.NewTrackContext(ctx)
	clues.AddTrackData(ctx, "Conn", coon)
	clues.AddTrackData(ctx, "SSeRoleSendEvent", req)
	log.InfoContextf(ctx, "SendRoleEventHandler Process appStatus req")
	// 判断机器人使用状态
	app, err := bus.dao.GetAppByBizID(ctx, dao.AppTestScene, coon.APIBotBizID)
	if err != nil {
		return err
	}
	if app == nil {
		return pkg.ErrRobotNotExist
	}
	if app.GetStatus() == model.AppStatusStopped {
		return pkg.ErrRobotStopOrArrearage
	}
	log.InfoContextf(ctx, "SendRoleEventHandler Process appStatus rsp app｜%+v err｜%+v", app, err)
	ev := req.(event.SSeRoleSendEvent)
	clues.AddTrackData(ctx, "query", ev.Content)
	msg := e.createMsgRecord(coon, ev)
	session := &model.Session{SessionID: ev.SessionID}
	ev = appModel(ctx, coon, session, ev, msg, app)
	log.InfoContextf(ctx, "SendRoleEventHandler Process appModel ev｜%+v", ev)
	modelFinance := bus.dao.GetModelFinanceInfo(ctx, ev.ModelName)
	if !modelFinance.GetIsCustomModel() { // 非自定义模型才检查账户状态
		// 检查账户状态
		st := bus.dao.GetModelStatus(ctx, app.GetCorpId(), ev.ModelName)
		log.InfoContextf(ctx, "SendRoleEventHandler GetModelStatus status｜%+v", st)
		// 非0表示账户不可用
		if st != 0 {
			return pkg.ErrNoBalance
		}
	} else {
		log.InfoContextf(ctx, "SendRoleEventHandler, is custom model(%s), no need to get model status",
			ev.ModelName)
	}

	checkCode, checkType := checkEvil(ctx, app, msg.BotBizID, app.CorpId, msg.RecordID, ev.Content,
		app.GetInfosecBizType())
	clues.AddTrackDataWithError(ctx, "req.content.check", checkCode, err)
	msg.ResultCode = checkCode
	msg.ResultType = checkType
	msg.BotBizID = coon.APIBotBizID
	log.InfoContextf(ctx, "SendRoleEventHandler Process  EmitWsUser")
	isEvil := msg.ResultCode == ispkg.ResultEvil
	ctx, cancel := context.WithCancel(ctx)
	_ = bus.dao.DoEmitWsClient(ctx, coon.ClientID, &event.ReplyEvent{
		IsFinal:    true,
		IsFromSelf: true,
		RequestID:  ev.RequestID,
		Content:    ev.Content,
		RecordID:   msg.RecordID,
		SessionID:  ev.SessionID,
		Timestamp:  msg.CreateTime.Unix(),
		IsEvil:     isEvil,
	}, cancel)
	if isEvil {
		return nil
	}
	log.InfoContextf(ctx, "SendRoleEventHandler Process check evil:%v", isEvil)
	return e.doProcess(ctx, coon, msg, ev, app, modelFinance.GetIsCustomModel())
}

// doProcess 核心流程、需要并发控制
func (e *SendRoleEventHandler) doProcess(ctx context.Context, coon *model.Conn, msg model.MsgRecord,
	ev event.SSeRoleSendEvent, app *model.App, isCustomModel bool) (err error) {
	cb := func() error {
		log.InfoContextf(ctx, "role sse exec begin.")
		err0 := exec(ctx, coon, msg, ev, app, isCustomModel) // 核心流程在这里
		return err0
	}

	err = limit(ctx, app, msg.RecordID, true, cb)
	if errors.Is(err, pkg.ErrConcurrenceExceeded) {
		// 上报超并发
		_ = bus.dao.ReportOverConcurrencyDosage(ctx, app, fillOverConcurrencyDosage(
			app.GetAppBizId(), app.GetMainModelName(), ev.OriginContent))
	}
	return err
}

func appModel(ctx context.Context, coon *model.Conn, session *model.Session,
	ev event.SSeRoleSendEvent, msg model.MsgRecord,
	app *model.App) event.SSeRoleSendEvent {
	replyID := uuid.NewString()
	bs := botsession.BotSession{
		Session:         session,
		Type:            model.RecordTypeMessage,
		RecordID:        replyID,
		RelatedRecordID: msg.RecordID,
		RequestID:       ev.RequestID,
		App:             app,
		To:              coon,
		PromptCtx: botsession.PromptCtx{
			Question: ev.Content,
		},
		NeedCheck:         true,
		StreamingThrottle: ev.StreamingThrottle,
		ModelType:         model.ModelTypeMessage,
		EventSource:       ev.Name(),
	}
	m := bs.App.GetModel(ctx, model.AppTypeKnowledgeQA, bs.ModelType)
	ev.ModelName = m.ModelName
	return ev
}

func exec(ctx context.Context, coon *model.Conn, msg model.MsgRecord, ev event.SSeRoleSendEvent,
	app *model.App, isCustomModel bool) error {
	ch := make(chan *llmm.Response, 100)
	var err error
	request := &llmm.Request{
		RequestId:   msg.RecordID,
		ModelName:   config.App().WorkflowConfig.WorkflowRoleModelName,
		AppKey:      ev.VisitorBizID,
		RequestType: llmm.RequestType_ONLINE,
		PromptType:  llmm.PromptType_TEXT,
	}
	spanKeyMap := make(map[string]string)
	switch ev.Type {
	case event.TypeWorkflowDescPrompt:
		assemblingWorkflowDescPrompt(ev, request)
	case event.TypeWorkflowParameterExtractor:
		spanKeyMap, err = assemblingWorkflowParameterExtractorPrompt(ctx, ev, request)
		if err != nil {
			return err
		}
	case event.TypeWorkflowLLMPrompt:
		spanKeyMap, err = assemblingWorkflowLLMPrompt(ctx, ev, request)
		if err != nil {
			return err
		}
	case event.TypeWorkflowLLMTagDesc:
		assemblingWorkflowLLMTagDesc(ev, request)
	case event.TypeWorkflowIntentDesc:
		assemblingWorkflowIntentDesc(ev, request)
		request.ModelName = config.App().WorkflowConfig.WorkflowIntentModelName
	default:
		assemblingRolePrompt(ev, request)
		if len(config.App().WorkflowConfig.RoleIntentModelName) == 0 {
			request.ModelName = ev.ModelName
		} else {
			request.ModelName = config.App().WorkflowConfig.RoleIntentModelName
		}
	}
	log.InfoContextf(ctx, "SSeRoleSendEvent exec request:%v", request)
	g, gctx := errgroupx.WithContext(ctx)
	llmctx, cancel := context.WithCancel(ctx)
	startTime := time.Now()
	g.Go(func() error {
		err := bus.dao.Chat(llmctx, request, ch, time.Now(), nil) // todo 耗时统计
		if err != nil {
			log.ErrorContextf(ctx, "SendRoleEventHandler chat stream error: %+v", err)
			return err
		}
		return nil
	})
	lastRsp, isEvil := sendReply(ctx, app, coon, msg, ev, ch, cancel, gctx, spanKeyMap)
	log.InfoContextf(ctx, "SendRoleEventHandler chat end lastRsp: %+v", lastRsp)
	if err := g.Wait(); err != nil {
		return err
	}
	if lastRsp == nil || lastRsp.GetStatisticInfo() == nil {
		log.WarnContextf(ctx, "SendRoleEventHandler chat lastRsp nil")
		return nil
	}
	if !isCustomModel { // 非自定义模型才需要上报计费
		endTime := time.Now()
		dosage := dao.TokenDosage{
			AppID:        app.GetAppBizId(),
			AppType:      app.GetAppType(),
			ModelName:    ev.ModelName,
			RecordID:     msg.RecordID,
			StartTime:    startTime,
			EndTime:      endTime,
			InputDosages: []int{int(lastRsp.GetStatisticInfo().GetInputTokens())},
		}
		if !isEvil {
			dosage.OutputDosages = []int{int(lastRsp.GetStatisticInfo().GetOutputTokens())}
		}
		err = bus.dao.ReportTokenDosage2(ctx, app.GetCorpId(), dosage, dao.FinanceSubBizTypeRoleCommands)
		if err != nil {
			log.ErrorContextf(ctx, "SendRoleEventHandler ReportTokenDosage2 failed :%+v", err)
			return err
		}
	} else {
		log.InfoContextf(ctx, "SendRoleEventHandler, is custom model, no need to report token dosage")
	}
	log.InfoContextf(ctx, "SendRoleEventHandler exec end")
	return nil
}

// assemblingRolePrompt 角色提示优化
func assemblingRolePrompt(ev event.SSeRoleSendEvent, request *llmm.Request) {
	var prompt1 string
	if strings.Contains(ev.Content, "意图") {
		prompt1 = config.App().WorkflowConfig.RoleQuery
	} else {
		prompt1 = config.App().WorkflowConfig.RoleQueryNoIntention
	}
	prompt2 := ev.Content
	promptMessage1 := llmm.Message{
		Role:    llmm.Role_SYSTEM,
		Content: prompt1,
	}
	promptMessage2 := llmm.Message{
		Role:    llmm.Role_USER,
		Content: prompt2,
	}
	request.Messages = append(request.Messages, &promptMessage1, &promptMessage2)
}

func assemblingWorkflowDescPrompt(ev event.SSeRoleSendEvent, request *llmm.Request) {
	var prompt = config.App().WorkflowConfig.DescQuery
	prompt = strings.ReplaceAll(prompt, "{工作流名称}", ev.WorkflowName)
	prompt = strings.ReplaceAll(prompt, "{工作流描述}", ev.Content)

	promptMessage := llmm.Message{
		Role:    llmm.Role_USER,
		Content: prompt,
	}
	request.Messages = append(request.Messages, &promptMessage)
}

func assemblingWorkflowParameterExtractorPrompt(ctx context.Context, ev event.SSeRoleSendEvent,
	request *llmm.Request) (map[string]string, error) {
	var prompt = config.App().WorkflowConfig.ParameterExtractor
	contentText, spanKeyMap, err := ExtractParams(ctx, ev.Content)
	if err != nil {
		log.ErrorContextf(ctx, "assemblingWorkflowParameterExtractorPrompt ExtractParams failed,err:%v", err)
		return spanKeyMap, err
	}
	prompt = strings.ReplaceAll(prompt, "{用户输入的提示词}", contentText)

	promptMessage := llmm.Message{
		Role:    llmm.Role_USER,
		Content: prompt,
	}
	request.Messages = append(request.Messages, &promptMessage)
	return spanKeyMap, nil
}

func assemblingWorkflowLLMPrompt(ctx context.Context, ev event.SSeRoleSendEvent,
	request *llmm.Request) (map[string]string, error) {
	var prompt = config.App().WorkflowConfig.LLMPrompt
	contentText, spanKeyMap, err := ExtractParams(ctx, ev.Content)
	if err != nil {
		log.ErrorContextf(ctx, "assemblingWorkflowLLMPrompt ExtractParams failed,err:%v", err)
		return spanKeyMap, err
	}
	prompt = strings.ReplaceAll(prompt, "{用户prompt}", contentText)

	promptMessage := llmm.Message{
		Role:    llmm.Role_USER,
		Content: prompt,
	}
	request.Messages = append(request.Messages, &promptMessage)
	return spanKeyMap, nil
}

func assemblingWorkflowLLMTagDesc(ev event.SSeRoleSendEvent, request *llmm.Request) {
	var prompt = config.App().WorkflowConfig.LLMTagDesc
	prompt = strings.ReplaceAll(prompt, "{标签名称}", ev.LLMTagName)
	prompt = strings.ReplaceAll(prompt, "{标签描述}", ev.Content)

	promptMessage := llmm.Message{
		Role:    llmm.Role_USER,
		Content: prompt,
	}
	request.Messages = append(request.Messages, &promptMessage)
}

func assemblingWorkflowIntentDesc(ev event.SSeRoleSendEvent, request *llmm.Request) {
	var prompt = config.App().WorkflowConfig.IntentDesc
	prompt = strings.ReplaceAll(prompt, "{intent_desc}", ev.Content)

	promptMessage := llmm.Message{
		Role:    llmm.Role_USER,
		Content: prompt,
	}
	request.Messages = append(request.Messages, &promptMessage)
}

func sendReply(ctx context.Context, app *model.App, coon *model.Conn, msg model.MsgRecord, ev event.SSeRoleSendEvent,
	ch chan *llmm.Response, cancel context.CancelFunc, gctx context.Context, spanKeyMap map[string]string) (
	*llmm.Response, bool) {
	var llmRespond *llmm.Response
	var isEvil, IsFinal, firstThought, finalThought bool
	bs := botsession.BotSession{
		To:       coon,
		RecordID: msg.RecordID,
		Thought: &event.AgentThoughtEvent{
			SessionID:  ev.SessionID,
			RequestID:  ev.RequestID,
			StartTime:  time.Now(),
			Procedures: make([]event.AgentProcedure, 0),
		},
		NeedCheck:       true,
		App:             app,
		RelatedRecordID: msg.RelatedRecordID,
	}
	cfg := config.App().Bot
	throttles := cfg.Throttles
	throttleCheck := helper.NewThrottle(throttles.Check)
	throttleThought := helper.NewThrottle(throttles.Streaming)
	for llmRespond = range ch {
		if !firstThought && llmRespond.GetMessage().GetReasoningContent() != "" { // 思维链首包
			addThoughtProcedure(ctx, &bs)
			firstThought = true
		}
		if needReplyThoughtEvent(ctx, llmRespond) { // 思维链中间包
			_, isEvil = thoughtEventReply(gctx, &bs, throttleCheck, throttleThought, llmRespond.GetMessage().
				GetReasoningContent(), time.Now(), cancel)
			if isEvil {
				IsFinal = true
				llmRespond.Finished = true
				sseRoleNewReply(gctx, coon, msg, ev, IsFinal, isEvil, "", cancel)
				return llmRespond, isEvil
			} else {
				continue
			}
		}
		if !finalThought && llmRespond.GetMessage().GetReasoningContent() != "" { // 思维链最后一包
			_, isEvil = thoughtEventFinalReply(gctx, time.Now(), &bs, llmRespond.GetMessage().GetReasoningContent(),
				cancel)
			if isEvil {
				IsFinal = true
				llmRespond.Finished = true
				sseRoleNewReply(gctx, coon, msg, ev, IsFinal, isEvil, "", cancel)
				return llmRespond, isEvil
			}
			finalThought = true
		}
		respondText := llmRespond.Message.Content
		l := len([]rune(respondText))
		if throttleCheck.Hit(l, llmRespond.GetFinished()) {
			log.InfoContextf(ctx, "SendRoleEventHandler text:%+v|len:%+v|tr:%+v", respondText, l, throttleCheck)
			checkCode, checkType := checkEvil(ctx, app, msg.BotBizID, app.GetCorpId(), msg.RecordID, respondText,
				app.GetInfosecBizType())
			msg.ResultCode = checkCode
			msg.ResultType = checkType
			isEvil = msg.ResultCode == ispkg.ResultEvil
			if isEvil {
				cancel()
			}
		}
		if llmRespond.Finished || isEvil {
			IsFinal = true
		}
		if ev.Type == event.TypeWorkflowParameterExtractor || ev.Type == event.TypeWorkflowLLMPrompt {
			for k, v := range spanKeyMap {
				respondText = strings.ReplaceAll(respondText, k, v)
			}
		}
		sseRoleNewReply(gctx, coon, msg, ev, IsFinal, isEvil, respondText, cancel)
		if IsFinal {
			log.InfoContextf(ctx, "SendRoleEventHandler exec Finished respondText:%+v", respondText)
			return llmRespond, isEvil
		}
	}
	return llmRespond, isEvil
}

func sseRoleNewReply(gctx context.Context, coon *model.Conn, msg model.MsgRecord, ev event.SSeRoleSendEvent,
	isFinal, isEvil bool, respondText string, cancel context.CancelFunc) {
	_ = bus.dao.DoEmitWsClient(gctx, coon.ClientID, &event.ReplyEvent{
		IsFinal:    isFinal,
		IsFromSelf: false,
		RequestID:  ev.RequestID,
		Content:    helper.When(isEvil, config.App().Bot.EvilReply, respondText),
		RecordID:   msg.RecordID,
		SessionID:  ev.SessionID,
		Timestamp:  msg.CreateTime.Unix(),
		IsEvil:     isEvil,
	}, cancel)
	return
}

func (e *SendRoleEventHandler) createMsgRecord(cli *model.Conn, ev event.SSeRoleSendEvent) model.MsgRecord {
	msg := model.MsgRecord{
		SessionID:  ev.SessionID,
		RecordID:   encode.GenerateSessionID(),
		Type:       model.RecordTypeMessage,
		FromID:     cli.CorpStaffID,
		FromType:   cli.GetSourceType(),
		Content:    ev.Content,
		CreateTime: time.Now(),
	}
	return msg
}

// // ConvertSpan 使用goquery解析span
// func ConvertSpan(ctx context.Context, spanText string) (text string, spanMap map[string]string, err error) {
//	spanMap = make(map[string]string)
//	log.InfoContextf(ctx, "ConvertSpan spanText:%v", spanText)
//	doc, err := goquery.NewDocumentFromReader(strings.NewReader(spanText))
//	if err != nil {
//		log.ErrorContextf(ctx, "ConvertSpan NewDocumentFromReader failed,err:%v", err)
//		return "", spanMap, err
//	}
//
//	doc.Find("span").Each(func(i int, s *goquery.Selection) {
//		spanText := "{" + s.Text() + "}"
//		spanHTML, _ := htmlNodeToString(s.Get(0))
//		spanMap[spanText] = spanHTML
//		s.ReplaceWithHtml(spanText)
//	})
//
//	newHTML, err := doc.Find("body").First().Html()
//	log.InfoContextf(ctx, "ConvertSpan newHTML:%v", newHTML)
//	return newHTML, spanMap, nil
// }
//
// func htmlNodeToString(node *html.Node) (string, error) {
//	var buf strings.Builder
//	err := html.Render(&buf, node)
//	if err != nil {
//		return "", err
//	}
//	return buf.String(), nil
// }

// ExtractParams 使用正则从文本中解析变量
func ExtractParams(ctx context.Context, spanText string) (text string, spanMap map[string]string, err error) {
	spanMap = make(map[string]string)
	paramRegexp, err := regexp.Compile(config.App().WorkflowConfig.ParamRegexp)
	if err != nil {
		log.Errorf("ExtractParams paramRegexp:%s,err:%+v", config.App().WorkflowConfig.ParamRegexp, err)
		return spanText, spanMap, err
	}
	log.InfoContextf(ctx, "ExtractParams spanText|paramRegexp:%v|%s", spanText, paramRegexp)
	matches := paramRegexp.FindAllStringSubmatch(spanText, -1)
	for _, match := range matches {
		param := match[1] // 获取捕获的内容
		param1 := "{@" + param + "}"
		param2 := "{{" + param + "}}"
		spanMap[param1] = "{{" + param + "}}"
		spanText = strings.ReplaceAll(spanText, param2, param1)
	}
	log.InfoContextf(ctx, "ExtractParams newText:%v", spanText)
	return spanText, spanMap, nil
}
