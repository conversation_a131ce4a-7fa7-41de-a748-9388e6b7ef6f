package eventbus

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/go-comm/clues"
	"git.woa.com/dialogue-platform/go-comm/encode"
	"git.woa.com/dialogue-platform/go-comm/pf"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_DM"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF_DM"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	"git.woa.com/ivy/qbot/qbot/chat/internal/dao"
	"git.woa.com/ivy/qbot/qbot/chat/internal/eventbus/botsession"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model/event"
	"git.woa.com/ivy/qbot/qbot/chat/pkg"
	ispkg "git.woa.com/ivy/qbot/qbot/infosec/pkg"
	jsoniter "github.com/json-iterator/go"
)

func init() {
	handlers[event.EventNodeDebug] = &NodeDebugEventHandler{}
}

// NodeDebugEventHandler 工作流非对话节点事件处理器
type NodeDebugEventHandler struct{}

// NewRequest 创建事件请求，参数校验等。
func (n *NodeDebugEventHandler) NewRequest(ctx context.Context, cli *model.Conn, bs []byte) (any, error) {
	log.InfoContextf(ctx, "Invoke NodeDebugEventHandler NewRequest cli: %s, req: %s",
		helper.Object2String(cli), string(bs))
	req := event.NodeDebugEvent{}
	if err := jsoniter.Unmarshal(bs, &req); err != nil {
		log.WarnContextf(ctx, "Unmarshal event req error: %+v, data: %s", err, bs)
		return nil, pkg.ErrBadRequest
	}
	if err := req.IsValid(); err != nil {
		return nil, err
	}
	// 框架未携带, 则使用业务数据
	pkg.WithRequestID(ctx, req.RequestID)
	pkg.WithTraceID(ctx, req.RequestID)
	return req, nil
}

// Process 处理事件请求
func (n *NodeDebugEventHandler) Process(ctx context.Context, cli *model.Conn, req any) (err error) {
	log.InfoContextf(ctx, "Invoke NodeDebugEventHandler Process cli: %s, req: %+v",
		helper.Object2String(cli), helper.Object2String(req))
	// TODO: 临时解决 针对Apex 链接未建立成功首次进入的请求 先sleep下再执行逻辑 确保Apex链接建立后再执行 后面Apex修复后删除
	if cli.IsSSE && config.App().SSE.EnableDeferClientClose {
		log.DebugContextf(ctx, "clientID: %s SSE.DeferClientCloseTime: %+v",
			cli.ClientID, config.App().SSE.DeferClientCloseTime)
		time.Sleep(config.App().SSE.DeferClientCloseTime)
	}
	ctx = pf.NewPipelineFlowContext(ctx)
	defer func(ctx *context.Context) { pf.Persist(*ctx) }(&ctx)
	defer func(ctx *context.Context) { clues.Flush(*ctx) }(&ctx)
	clues.AddTrackData(ctx, "Conn", cli)
	clues.AddTrackData(ctx, "NodeDebugEvent", req)

	ev := req.(event.NodeDebugEvent)
	bs := n.initBotSession(ctx, cli, ev)
	return n.doProcess(ctx, bs, ev)
}

// initBotSession 初始化botSession
func (n *NodeDebugEventHandler) initBotSession(ctx context.Context, cli *model.Conn,
	ev event.NodeDebugEvent) *botsession.BotSession {
	app, _ := bus.dao.GetAppByAppKey(ctx, dao.AppTestScene, ev.BotAppKey)
	bs := &botsession.BotSession{
		RecordID:        encode.GenerateSessionID(),
		RequestID:       ev.RequestID,
		CustomVariables: ev.CustomVariables,
		WorkflowID:      ev.WorkflowID,
		CorpStaff:       &model.CorpStaff{},
		Msg:             &model.MsgRecord{},
		WorkflowInput:   ev.WorkflowInput,
		Type:            model.RecordTypeMessage,
		SessionType:     model.SessionTypeWorkflow,
		Scene:           dao.AppReleaseScene,
		Env:             KEP_DM.RunEnvType_Product,
		To:              cli,
		Intent:          "",
		IntentCate:      "",
		ReferIndex:      []int{},
		Flags:           botsession.Flags{},
		QuestionAnswers: make([][2]string, 0),
		ToolsInfo:       make(map[string]model.AgentTool),
		AgentStatus:     &model.AgentStatus{},
		WorkflowDebug:   len(ev.WorkflowID) > 0,
		App:             app,
		NeedCheck:       true,
		OriginContent:   ev.NodeInfo,
	}
	bs.Labels = helper.Map(bs.To.VisitorLabels, model.Label.ToVectorLabel)
	for k, v := range bs.CustomVariables {
		bs.CustomVariablesForDisplay = append(bs.CustomVariablesForDisplay, fmt.Sprintf("%s:%s", k, v))
	}
	log.InfoContextf(ctx, "NodeDebugEventHandler init BotSession: %s", helper.Object2String(bs))
	return bs
}

func (n *NodeDebugEventHandler) doProcess(ctx context.Context, bs *botsession.BotSession,
	ev event.NodeDebugEvent) (err error) {
	startTime := time.Now()
	bs.RecordID = encode.GenerateSessionID() // 在这里重置RecordID
	log.InfoContextf(ctx, "botsession is:%s", bs.String())
	isEvil := false // isEvil 命中敏感，需要直接返回
	isEvil = n.checkEvil(ctx, bs, bs.OriginContent)
	bus.workflow.ReplyFromSelf(ctx, bs) // 首包回复
	if isEvil {
		return err
	}

	cfg := config.App().Bot
	ticker := time.NewTicker(time.Duration(cfg.Timeout/10) * time.Second)
	defer ticker.Stop()
	go func() {
		for {
			select {
			case <-ticker.C:
				duration := time.Since(startTime)
				elapsedTmp := duration.Milliseconds()
				tokenStat := &event.TokenStatEvent{
					SessionID: bs.SessionID,
					RequestID: bs.RequestID,
					RecordID:  bs.RecordID,
					Elapsed:   uint32(elapsedTmp),
				}
				bus.workflow.SendTokenStat(ctx, bs.To.ClientID, tokenStat)
			case <-ctx.Done():
				ticker.Stop()
				return
			}
		}
	}()
	bs.IntentCate = model.IntentTypeWorkflow
	req := n.NewWorkflowRequest(bs, ev)
	reply, err := bus.dao.NodeDebugWorkflow(ctx, req)
	if err != nil {
		return err
	}
	isEvil = n.checkEvil(ctx, bs, reply.GetNodeData().GetOutput())
	n.Reply(ctx, bs)
	if isEvil {
		return err
	}
	dmCtx, cancel := context.WithCancel(ctx)
	duration := time.Since(startTime)
	elapsed := duration.Milliseconds()
	re := NewWorkflowReplyEvent(bs, reply, uint32(elapsed))
	_ = bus.dao.DoEmitWsClient(dmCtx, bs.To.ClientID, re, cancel)
	return err
}

// NewWorkflowRequest 构造请求，请求工作流
func (n *NodeDebugEventHandler) NewWorkflowRequest(
	bs *botsession.BotSession, ev event.NodeDebugEvent) *KEP_WF_DM.DebugWorkflowNodeRequest {

	cv := make(map[string]*KEP_WF_DM.Variable, len(bs.CustomVariables)*2)
	for k, v := range bs.CustomVariables {
		cv[k] = &KEP_WF_DM.Variable{Value: v}
	}

	request := &KEP_WF_DM.DebugWorkflowNodeRequest{
		AppID:    strconv.FormatUint(bs.App.GetAppBizId(), 10),
		NodeJSON: ev.NodeInfo,
		Inputs:   bs.WorkflowInput,
		ToolInput: &KEP_WF_DM.ToolInputData{
			Header: ev.ToolInfo.Header,
			Query:  ev.ToolInfo.Query,
			Body:   ev.ToolInfo.Body,
		},
	}
	return request
}

// NewWorkflowReplyEvent 生成回复事件
func NewWorkflowReplyEvent(bs *botsession.BotSession,
	reply *KEP_WF_DM.DebugWorkflowNodeReply, elapsed uint32) model.WsEvent {
	var tokenCount uint32
	statisticInfos := reply.GetNodeData().GetStatisticInfos()
	for _, v := range statisticInfos {
		tokenCount += v.GetTotalTokens()
	}
	re := &event.NodeDebugResponseEvent{
		RequestID:  bs.RequestID,
		RecordID:   bs.RecordID,
		Elapsed:    elapsed,
		TokenCount: tokenCount,
		NodeInfo:   reply.GetNodeData(),
	}
	return re
}

// checkEvil 安全审核
func (n *NodeDebugEventHandler) checkEvil(ctx context.Context, bs *botsession.BotSession, content string) bool {
	tempRecordID := strconv.FormatUint(uint64(time.Now().UnixNano()), 10)
	if len(bs.RelatedRecordID) != 0 {
		tempRecordID = bs.RelatedRecordID
	}
	checkCode, checkType := checkEvil(ctx, bs.App, bs.App.GetAppBizId(), bs.App.CorpId, tempRecordID, content,
		bs.App.GetInfosecBizType())
	bs.Msg.ResultCode = checkCode
	bs.Msg.ResultType = checkType
	if checkCode == ispkg.ResultEvil {
		if err := bus.dao.UpdateMsgRecordCheckResult(ctx, tempRecordID, checkCode, checkType,
			bs.App.GetAppBizId()); err != nil {
			log.WarnContextf(ctx, "UpdateMsgRecordCheckResult err: %v", err)
		}
		return true
	}
	return false
}

// Reply 命中敏感词回复
func (n *NodeDebugEventHandler) Reply(ctx context.Context, bs *botsession.BotSession) {
	ctx, cancel := context.WithCancel(ctx)
	// 如果IsEvil为true，是否有必要设置ReplyMethod。这里异步执行即可。
	go bus.dao.DoEmitWsClient(ctx, bs.To.ClientID, &event.ReplyEvent{
		IsFinal:    true,
		IsFromSelf: false,
		IsEvil:     bs.Msg.ResultCode == ispkg.ResultEvil,
		RequestID:  bs.RequestID,
		FromName:   bs.App.BaseConfig.GetName(),
		FromAvatar: bs.App.BaseConfig.GetAvatar(),
		Content:    bs.OriginContent,
		RecordID:   bs.RelatedRecordID,
		SessionID:  bs.SessionID,
		Timestamp:  bs.StartTime.Unix(),
		FileInfos:  bs.FileInfos,
	}, cancel)
	return
}
