package eventbus

import (
	"context"
	"encoding/json"
	"fmt"
	"math"
	"regexp"
	"sort"
	"strconv"
	"strings"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	limiter2 "git.woa.com/dialogue-platform/common/v3/limiter"
	"git.woa.com/dialogue-platform/common/v3/plugins/i18n"
	"git.woa.com/dialogue-platform/go-comm/clues"
	llmm "git.woa.com/dialogue-platform/proto/pb-stub/llm-manager-server"
	"git.woa.com/ivy/protobuf/trpc-go/qbot/qbot/infosec"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	"git.woa.com/ivy/qbot/qbot/chat/internal/dao"
	"git.woa.com/ivy/qbot/qbot/chat/internal/eventbus/botsession"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model/event"
	"git.woa.com/ivy/qbot/qbot/chat/pkg"
	"git.woa.com/ivy/qbot/qbot/chat/pkg/limiter"
	ispkg "git.woa.com/ivy/qbot/qbot/infosec/pkg"
	jsoniter "github.com/json-iterator/go"
)

func limit(ctx context.Context, app *model.App, holder string, needCheckCustomModel bool, cb limiter.Callback) error {
	uin, sin, _ := bus.dao.GetUinByCorpID(ctx, app.GetCorpId())
	rsp, err := bus.dao.Lock(ctx, limiter2.MultiLockReq{
		Uin:       strconv.FormatUint(uin, 10),
		SID:       uint32(sin),
		AppBizID:  strconv.FormatUint(app.GetAppBizId(), 10),
		ModelName: app.GetMainModelName(),
		Holder:    holder,
	})
	if err != nil {
		log.WarnContextf(ctx, "limit ErrLock: %v, mainModel:%s, appId:%d", err,
			app.GetMainModelName(), app.GetAppBizId())
		return cb()
	}
	app.IsExclusive = rsp.IsExclusive // 更新是否专属并发
	if rsp.OK {                       // 抢锁成功，就需要unlock
		defer bus.dao.Unlock(ctx, limiter2.MultiUnlockReq{
			Uin:       strconv.FormatUint(uin, 10),
			AppBizID:  strconv.FormatUint(app.GetAppBizId(), 10),
			ModelName: app.GetMainModelName(),
			Holder:    holder,
		})
	}
	isCustomModel := false
	if needCheckCustomModel {
		modelFinance := bus.dao.GetModelFinanceInfo(ctx, app.GetMainModelName())
		isCustomModel = modelFinance.GetIsCustomModel()
	}
	if !isCustomModel && !rsp.ModelStatus { // 先判断是否欠费, 自定义模型不检查
		log.WarnContextf(ctx, "No Balance: %v, mainModel:%s, appId:%s, uin:%d", pkg.ErrNoBalance,
			app.GetMainModelName(), app.GetAppBizId(), uin)
		return pkg.ErrNoBalance
	}
	if !rsp.OK { // 再判断是否超并发
		log.WarnContextf(ctx, "limit ErrConcurrenceExceeded: %v, mainModel:%s, appId:%d, uin:%d",
			pkg.ErrConcurrenceExceeded, app.GetMainModelName(), app.GetAppBizId(), uin)
		return pkg.ErrConcurrenceExceeded
	}
	return cb()
}

func limitAgent(ctx context.Context, app *model.App, holder string, cb limiter.Callback) error {
	uin, sin, _ := bus.dao.GetUinByCorpID(ctx, app.GetCorpId())
	rsp, err := bus.dao.Lock(ctx, limiter2.MultiLockReq{
		Uin:       strconv.FormatUint(uin, 10),
		SID:       uint32(sin),
		AppBizID:  strconv.FormatUint(app.GetAppBizId(), 10),
		ModelName: app.GetAgentMainModelName(),
		Holder:    holder,
	})
	if err != nil {
		log.WarnContextf(ctx, "limitAgent ErrLock: %v, mainModel:%s, appId:%d, uin:%d", err,
			app.GetAgentMainModelName(), app.GetAppBizId(), uin)
		return cb()
	}
	if rsp.OK {
		defer bus.dao.Unlock(ctx, limiter2.MultiUnlockReq{
			Uin:       strconv.FormatUint(uin, 10),
			AppBizID:  strconv.FormatUint(app.GetAppBizId(), 10),
			ModelName: app.GetAgentMainModelName(),
			Holder:    holder,
		})
	} else {
		log.WarnContextf(ctx, "limit ErrConcurrenceExceeded: %v, mainModel:%s, appId:%d, uin:%d",
			pkg.ErrConcurrenceExceeded, app.GetAgentMainModelName(), app.GetAppBizId(), uin)
		return pkg.ErrConcurrenceExceeded
	}
	return cb()
}

func botBusyReply(ctx context.Context, busySession *botsession.BotSession) error {
	// var err error
	// busySession.ClientIDs, err = bus.dao.GetWsClientIDs(ctx, []uint64{busySession.To.CorpStaffID}, busySession.To.Type)
	// if err != nil {
	//	return err
	// }
	if _, err := botDirectReply(ctx, busySession); err != nil {
		return err
	}
	return nil
}

// botDirectReply 机器人直接回复，这里安排输出配置，使用流式or非流式返回。
func botDirectReply(ctx context.Context, bs *botsession.BotSession) (reply string, err error) {
	reply, method := bs.ToDirectReply(ctx)
	log.InfoContextf(ctx, "botDirectReply:%s, method:%v", reply, method)
	reply = replacePlaceholders(reply, bs.Placeholders) // 已采纳答案 可能包含占位符的场景
	clues.AddTrackData(ctx, "botDirectReply", map[string]any{
		"placeholders": bs.Placeholders, "reply": reply, "method": method,
	})
	mockStreamOutput(ctx, bs, reply, method, false)
	t := uint32(math.Ceil(float64(len([]rune(bs.OriginContent))) / 1.5))            // token数=文字字符/1.5 @miyahma
	llmmToken := &llmm.StatisticInfo{InputTokens: t, TotalTokens: t}                // 构造 llmm token
	if bs.Flags.IsGlobalKnowledge || bs.Flags.IsPriorityQA || bs.Flags.IsFaqReply { // 全局知识库\问答直接回复 输出也上报
		llmmToken.OutputTokens = uint32(math.Ceil(float64(len([]rune(reply))) / 1.5))
		llmmToken.TotalTokens += llmmToken.OutputTokens
	}
	d := event.ProcedureDebugging{Content: bs.PromptCtx.Question, IntentCate: bs.IntentCate,
		CustomVariables: bs.CustomVariablesForDisplay} // @halelv 调试信息
	p := event.NewSuccessTSProcedure(ctx, event.ProcedureLLM, llmmToken, d, nil)
	bs.TokenStat.UpdateSuccessProcedure(p)
	SendTokenStat(ctx, bs, bs.TokenStat)
	m0 := bs.NewDirectBotRecord(ctx, reply, method, time.Now())
	newMsg, stat := event.GetMsgRecordAndTokenStat(ctx, m0)
	bus.dao.CreateMsgRecord(ctx, newMsg, stat) // for answer
	clues.AddTrackDataWithError(ctx, "botDirectReply:ao.CreateMsgRecord", m0, err)
	return reply, err
}

// mockStreamOutput 模拟流式输出
func mockStreamOutput(ctx context.Context, bs *botsession.BotSession, reply string, method model.ReplyMethod,
	isEvil bool) {
	var option []string
	_, cancel := context.WithCancel(ctx)
	defer cancel()
	if bs.App.KnowledgeQa == nil ||
		(bs.App.KnowledgeQa.GetOutput() != nil && bs.App.KnowledgeQa.GetOutput().Method == 2) { // 输出方式 1：流式 2：非流式
		last := &llmm.Response{Message: &llmm.Message{Content: reply}, Finished: true}
		if method == model.ReplyMethodMultiIntent {
			option = bs.IntentRsp.MultiIntent.OptionCards
		}
		event := bs.NewReplyEvent(ctx, last, false, method, time.Now(), option)
		_ = bus.dao.DoEmitWsClient(ctx, bs.To.ClientID, event, cancel)
		return
	}

	step := 5 // 每次输出几个字？
	if config.App().StreamOutputBatchSize > 0 {
		step = config.App().StreamOutputBatchSize
	}
	if bs.StreamingThrottle > 0 { // 用户自定义输出速度
		step = bs.StreamingThrottle
	}
	replyRune := []rune(reply)
	length := len(replyRune)
	var replyChunk []string
	start := 0
	for i := 0; i < length; i += step {
		end := i + step
		if end > length {
			end = length
		}
		replyChunk = append(replyChunk, string(replyRune[start:end]))
	}
	// 假流式输出
	chunkLength := len(replyChunk)
	for i, chunk := range replyChunk {
		finished := false
		if i == chunkLength-1 {
			finished = true
		}
		last := &llmm.Response{
			Message: &llmm.Message{
				Content: chunk,
			},
			Finished: finished,
		}
		if method == model.ReplyMethodMultiIntent && finished {
			option = bs.IntentRsp.MultiIntent.OptionCards
		}
		event := bs.NewReplyEvent(ctx, last, isEvil, method, time.Now(), option)
		_ = bus.dao.DoEmitWsClient(ctx, bs.To.ClientID, event, cancel)
	}
}

func getHistoryAndRole(ctx context.Context, bs *botsession.BotSession,
	historyLimit, historyWordsLimit uint32) (histories [][2]model.HisMessage, useRole bool, err error) {
	// 使用opdebug里的 history
	if bs.ChatHistoryFromAPI != nil {
		histories = bs.MakeChatAPIHistory()
		log.InfoContextf(ctx, "use api histories:%s", helper.Object2String(histories))
		return histories, true, nil
	}
	// TODO 需在模型配置中增加多轮历史配置项 摘要和小结按同类型获取多轮历史时获取到的消息均无关联消息，导致多轮历史逻辑异常
	if bs.ModelType != model.ModelTypeAbstract && bs.ModelType != model.ModelTypeSummary {
		histories, err = makeMultiRoundHistories(ctx, bs.App.AppBizId,
			bs.Session, uint(historyLimit), []model.RecordType{bs.Type}, false)
		if err != nil {
			return histories, false, err
		}
	}
	switch bs.EventSource {
	case event.EventTagExtraction, event.EventTagExtractionExperience:
		useRole = false
		histories = nil // 清除历史
	case event.EventKnowledgeSummary, event.EventKnowledgeSummaryExperience:
		useRole = false
		histories = nil // 清除历史
	default:
		useRole = true
	}
	histories = model.TruncateQAHistories(ctx, histories, int(historyWordsLimit)) // 截断太长的历史记录
	return histories, useRole, nil
}

func getTagReply(ctx context.Context, bs botsession.BotSession, isEvil bool, llmReply string) string {
	if isEvil {
		return i18n.Translate(ctx, config.App().Bot.TagEvilReply)
	}
	if llmReply == "" {
		return i18n.Translate(ctx, config.App().App.TagExtraction.DefaultExtractAnswer)
	}
	tags := event.ParseText2Tags(ctx, llmReply)
	tagStr, _ := json.Marshal(tags)
	reply := string(tagStr)
	log.InfoContextf(ctx, "R|tags|Marshal %s, %s", bs.EventSource, reply)
	return reply
}

// getAppModel 从app中提取对应Model
func getAppModel(ctx context.Context, bs *botsession.BotSession) (m *model.AppModel) {
	switch bs.EventSource {
	case event.EventTagExtraction, event.EventTagExtractionExperience:
		m = bs.App.GetModel(ctx, model.AppTypeClassify, bs.ModelType)
	case event.EventKnowledgeSummary, event.EventKnowledgeSummaryExperience:
		m = bs.App.GetModel(ctx, model.AppTypeSummary, bs.ModelType)
	default:
		m = bs.App.GetModel(ctx, model.AppTypeKnowledgeQA, bs.ModelType)
		switch bs.ModelType {
		case model.ModelTypeMessage, model.ModelTypeMessageNonGeneralKnowledge:
			if bs.App.GetKnowledgeQa() != nil && bs.App.GetKnowledgeQa().GetOutput() != nil {
				bs.PromptCtx.UseQuestionClarify = bs.App.GetKnowledgeQa().GetOutput().UseQuestionClarify
				bs.PromptCtx.QuestionClarifyKeywords = bs.App.GetKnowledgeQa().GetOutput().QuestionClarifyKeywords
			}
		default:
			log.DebugContextf(ctx, "In default, getAppModel for [%s]", bs.EventSource)
		}
	}
	log.DebugContextf(ctx, "getAppModel for [%s], model name is: %s", bs.EventSource, m.GetModelName())
	return m
}

// discardMiddleResult 是否仅回尾包
func discardMiddleResult(ctx context.Context, bs *botsession.BotSession, resp *llmm.Response) bool {
	var discard bool
	switch bs.EventSource {
	case event.EventTagExtractionExperience, event.EventTagExtraction:
		if !resp.GetFinished() {
			discard = true
		}
	case event.EventKnowledgeSummaryExperience, event.EventKnowledgeSummary:
		// 非流式 输出方式 1：流式 2：非流式 SummaryOutput.Method == 2
		if bs.App != nil && bs.App.GetSummary() != nil && bs.App.GetSummary().GetOutput() != nil &&
			bs.App.GetSummary().GetOutput().GetMethod() == 2 && !resp.GetFinished() {
			discard = true
		}
	default:
		if !bs.IsKnowledgeOutputStream() && !resp.GetFinished() { // 非流式 且 不是最后一个输出，丢弃
			discard = true
		}
		// 流式,非尾包为空，丢弃（大模型首包可能出现内容为空）
		if bs.IsKnowledgeOutputStream() && !resp.GetFinished() && resp.GetMessage().GetContent() == "" &&
			resp.GetMessage().GetReasoningContent() == "" {
			discard = true
		}
	}
	if !discard && bs.StreamInfo.PreviousRspContent != "" { // 粗暴点，碰到[就丢弃，等n个字符。然后正则匹配，如果匹配到，就丢弃
		var incrementContent string
		lastLen := len(bs.StreamInfo.PreviousRspContent)
		curLen := len(resp.GetMessage().GetContent())
		if lastLen < curLen {
			incrementContent = resp.GetMessage().GetContent()[lastLen:]
		} else if lastLen > curLen {
			log.WarnContextf(ctx, "[content invalid]lastLen:%d,curLen:%d, lastContent：%s, curContent:%s",
				lastLen, curLen, bs.StreamInfo.PreviousRspContent, resp.GetMessage().GetContent())
		}
		for i, char := range incrementContent { // 判断是否进入占位符匹配流程
			if _, ok := config.App().Bot.PlaceholderConf.StartPlaceholders[string(char)]; ok {
				log.DebugContextf(ctx, "match placeholder:%s", string(char))
				bs.StreamInfo.MatchPlaceholder = char
				bs.StreamInfo.InPlaceholderStat = len(bs.StreamInfo.PreviousRspContent) + i
				discard = true
			}
		}
		if !discard && bs.StreamInfo.MatchPlaceholder != 0 {
			step := len(resp.GetMessage().GetContent()) - bs.StreamInfo.InPlaceholderStat
			if step < config.App().Bot.PlaceholderConf.StartPlaceholders[string(bs.StreamInfo.MatchPlaceholder)] {
				log.DebugContextf(ctx, "in placeholder match, but not enough, content: %s", incrementContent)
				discard = true
			} else { // 大于步长，则说明匹配到了，可以往下走
				bs.StreamInfo.MatchPlaceholder = 0
			}
		}
	}
	if !discard { // 占位符输出不完整的时候，丢弃
		reply := resp.GetMessage().GetContent()
		discard = helper.IsPlaceholderEnd(reply) || helper.IsEndWithCalc(reply) || helper.IsEndWithPicture(reply) ||
			helper.EndsWithPlaceholderPrefix(reply, "[Calculator") ||
			helper.EndsWithPlaceholderPrefix(reply, "[Picture")
	}
	log.DebugContextf(ctx, "R|discardMiddleResult|%s %t", bs.EventSource, discard)
	return discard
}

func pair(ctx context.Context, answerRecords, questionRecords []model.MsgRecord,
	useRewrite bool) [][2]model.HisMessage {
	mAnswerRecord := make(map[string]model.MsgRecord, len(answerRecords))
	for _, msg := range answerRecords {
		if msg.RelatedRecordID != "" && msg.RecordID != "" {
			mAnswerRecord[msg.RelatedRecordID] = msg // 通过问题找答案
		}
	}
	pairs := make([][2]model.HisMessage, 0, len(answerRecords))
	sort.Slice(questionRecords, func(i, j int) bool {
		return questionRecords[i].CreateTime.Before(questionRecords[j].CreateTime)
	})
	for _, questionRecord := range questionRecords {
		answerRecord, ok := mAnswerRecord[questionRecord.RecordID]
		if !ok {
			continue // 过滤掉没有问题的回答，DM返回2条的情况
		}
		answer := answerRecord.GetSafeContent(ctx)
		question := helper.When(useRewrite && questionRecord.RewroteContent != "", questionRecord.RewroteContent,
			questionRecord.Content)
		q := model.HisMessage{RecordID: questionRecord.RecordID, Content: question}
		if len(q.Content) < 1 && len(questionRecord.FileInfos) > 0 {
			temp := make([]*model.FileInfo, 0)
			err := jsoniter.UnmarshalFromString(questionRecord.FileInfos, &temp)
			if err != nil || len(temp) < 1 {
				log.ErrorContextf(context.Background(), "json.Unmarshal error: %v", err)
				temp = append(temp, &model.FileInfo{FileName: "未知文件"})
			}
			q.Content = "总结文档：" + temp[0].FileName
		}
		a := model.HisMessage{RecordID: answerRecord.RecordID, Content: answer}
		pairs = append(pairs, [2]model.HisMessage{q, a})
	}
	return pairs
}

// makeMultiRoundHistories 构造多轮历史
func makeMultiRoundHistories(ctx context.Context, botBizID uint64, session *model.Session,
	limit uint, types []model.RecordType, useRewrite bool) ([][2]model.HisMessage, error) {
	if limit == 0 {
		return nil, nil
	}

	records, err := bus.dao.GetLastNBotRecord(ctx, botBizID, session, limit, types)
	log.DebugContextf(ctx, "records:%v", helper.Object2String(records))
	if err != nil {
		return nil, err
	}
	if len(records) == 0 {
		return nil, nil
	}

	ids := make([]string, 0, len(records))
	for _, v := range records {
		ids = append(ids, v.RelatedRecordID)
	}

	relateds, err := bus.dao.GetMsgRecordsByRecordID(ctx, ids, botBizID)
	if err != nil {
		return nil, err
	}
	if len(relateds) == 0 {
		return nil, pkg.ErrInvalidMsgRecord
	}
	p := pair(ctx, records, relateds, useRewrite)
	clues.AddTrackData(ctx, "eventbus.makeMultiRoundHistories()", map[string]any{
		"records": records, "relateds": relateds, "useRewrite": useRewrite, "result": p,
	})
	return p, nil
}

// isHitRejectAnswer 是否命中拒答
func isHitRejectAnswer(s string) bool {
	var isHitReject bool
	s = strings.TrimLeft(s, " ")
	s = strings.TrimLeft(s, "\n")
	for _, pattern := range model.Patterns {
		if strings.HasPrefix(s, pattern) {
			isHitReject = true
			break
		}
	}
	return isHitReject
}

func replacePlaceholders(content string, placeholders map[string]string) string {
	for k, v := range placeholders {
		content = strings.ReplaceAll(content, k, v)
	}
	return content
}

// replaceMLLMPlaceholders 替换MLLM占位符
func replaceMLLMPlaceholders(content string, images []string) string {
	res, indexes := helper.MatchAllImage(content)
	if len(res) < 1 || len(indexes) < 1 {
		return content
	}
	for i, v := range indexes {
		if v < 1 {
			continue
		}
		if v <= len(images) {
			content = strings.ReplaceAll(content, res[i], "![]("+images[v-1]+")")
		}
	}
	return content
}

// checkEvil 天御安全审核
func checkEvil(ctx context.Context, app *model.App, botBizID, corpID uint64,
	recordID, content, appInfosecBizType string) (checkCode, checkType uint32) {
	// 获取bizType
	corpRsp, _ := bus.dao.GetCorp(ctx, recordID, corpID)
	bizType := "QD_AI_TEXT"
	if corpRsp != nil {
		bizType = corpRsp.GetInfosecBizType()
	}
	if len(appInfosecBizType) != 0 {
		bizType = appInfosecBizType
	}
	if app.IsDeepSeekModel(ctx) && isAfterQDDSStartTime(app) {
		// deepseek模型，且应用创建时间在starttime之后，则bizType为QD_DS
		// https://tapd.woa.com/tapd_fe/70080800/story/detail/1070080800123732989
		bizType = "QD_DS"
		appInfosecBizType = "QD_DS"
	}
	imageURLs := helper.GetAllImageURLs(content)
	for _, imageURL := range imageURLs { // 图片安全审核,循环调用
		checkCode, checkType = checkImageEvil(ctx, botBizID, bizType, recordID, imageURL)
		if checkCode == ispkg.ResultEvil {
			return checkCode, checkType
		}
	}
	if len(content) != 0 { // 文本安全审核
		return bus.dao.CheckTextEvil(ctx, botBizID, corpID, recordID, content, appInfosecBizType)
	}
	return checkCode, checkType
}

// checkImageEvil 检查图片内容是否违规
func checkImageEvil(ctx context.Context, botBizID uint64, bizType string,
	recordID string, imageURL string) (checkCode, checkType uint32) {

	rsp, err := bus.dao.Check(ctx, &infosec.CheckReq{
		Id:       recordID,
		Source:   "chat",
		PostTime: time.Now().Unix(),
		Type:     uint32(ispkg.CheckTypePicture),
		Content:  "",
		Url:      imageURL,
		User: &infosec.CheckReq_User{
			AccountType: uint32(ispkg.AccountTypeOther),
			Uin:         strconv.FormatUint(botBizID, 10),
		},
		BizType: bizType,
	})
	if err != nil {
		log.WarnContextf(ctx, "checkEvil error: %v", err)
		return 0, 0
	}
	checkCode = rsp.GetResultCode()
	checkType = rsp.GetResultType()
	log.DebugContextf(ctx, "checkEvil result code: %d, type: %d", checkCode, checkType)
	return checkCode, checkType
}

// isAfterQDDSStartTime 判断应用创建时间，是否在QD_DS启用时间之后
func isAfterQDDSStartTime(app *model.App) bool {
	if config.App().DeepSeekConf.QDDsStartTime == "" {
		return false
	}

	startTime, err := helper.ParseDateTimeShanghai(config.App().DeepSeekConf.QDDsStartTime)
	if err != nil {
		return false
	}
	return app.GetCreateTime() > startTime.Unix()
}

// getValidApp 获取可用的机器人
func getValidApp(ctx context.Context, botBizID uint64, scene dao.AppScene) (*model.App, error) {
	app, err := bus.dao.GetAppByBizID(ctx, scene, botBizID)
	if err != nil {
		return nil, err
	}
	if app == nil {
		return nil, pkg.ErrRobotNotExist
	}
	if app.GetStatus() == model.AppStatusStopped {
		return nil, pkg.ErrRobotStopOrArrearage
	}
	uin, _, _ := bus.dao.GetUinByCorpID(ctx, app.GetCorpId())
	pkg.WithAppID(ctx, app.GetAppBizId())
	pkg.WithUin(ctx, fmt.Sprintf("%d", uin))
	return app, nil
}

func clarifyConfirmReply(ctx context.Context, bs *botsession.BotSession, filterQuestions []string) (
	reply string, err error) {
	reply, method := bs.ToClarifyConfirmReply(filterQuestions)
	reply = replacePlaceholders(reply, bs.Placeholders) // 已采纳答案 可能包含占位符的场景
	clues.AddTrackData(ctx, "clarifyConfirmReply", map[string]any{
		"placeholders": bs.Placeholders, "reply": reply, "method": method,
	})
	mockStreamOutput(ctx, bs, reply, method, false)
	t := uint32(math.Ceil(float64(len([]rune(bs.OriginContent))) / 1.5)) // token数=文字字符/1.5 @miyahma
	llmmToken := &llmm.StatisticInfo{InputTokens: t, TotalTokens: t}     // 构造 llmm token
	if bs.Flags.IsGlobalKnowledge || bs.Flags.IsPriorityQA {             // 全局知识库\问答直接回复 输出也上报
		llmmToken.OutputTokens = uint32(math.Ceil(float64(len([]rune(reply))) / 1.5))
		llmmToken.TotalTokens += llmmToken.OutputTokens
	}
	d := event.ProcedureDebugging{Content: bs.PromptCtx.Question,
		CustomVariables: bs.CustomVariablesForDisplay} // @halelv 调试信息
	p := event.NewSuccessTSProcedure(ctx, event.ProcedureLLM, llmmToken, d, nil)
	bs.TokenStat.UpdateSuccessProcedure(p)
	SendTokenStat(ctx, bs, bs.TokenStat)
	m0 := bs.NewDirectBotRecord(ctx, reply, method, time.Now())
	newMsg, stat := event.GetMsgRecordAndTokenStat(ctx, m0)
	bus.dao.CreateMsgRecord(ctx, newMsg, stat) // for answer
	return reply, nil
}

// getHistoryText 获取历史消息
func getHistoryText(histories [][2]model.HisMessage) string {
	// 大模型实际组装history时，会添加"user:"、"bot:"等前缀，此处token数只能近似计算
	var text string
	for _, v := range histories {
		text += v[0].Content + v[1].Content
	}
	return text
}

// getFilterClarifyConfirm TODO
func getFilterClarifyConfirm(ctx context.Context, bs *botsession.BotSession) ([]string, error) {
	filterQuestions := []string{bs.OriginContent}
	records, err := bus.dao.GetLastNBotRecord(ctx, bs.App.GetAppBizId(), bs.Session, 1, []model.RecordType{bs.Type})
	log.DebugContextf(ctx, "records:%v", helper.Object2String(records))
	if err != nil {
		return nil, err
	}
	if len(records) == 0 {
		return filterQuestions, nil
	}
	var content string
	for _, v := range records {
		if v.ReplyMethod != model.ReplyMethodClarifyConfirm {
			continue
		}
		content = v.Content
	}
	if len(content) == 0 {
		return filterQuestions, nil
	}
	regx := regexp.MustCompile(`\[(.*?)\]`)
	for _, v := range regx.FindAllStringSubmatch(content, -1) {
		if len(v) == 2 && len(v[1]) > 0 {
			filterQuestions = append(filterQuestions, v[1])
		}
	}
	return filterQuestions, nil
}

func fillOverConcurrencyDosage(appBizID uint64, mainModelName, content string) dao.OverConcurrencyDosage {
	return dao.OverConcurrencyDosage{
		AppID:     appBizID,
		ModelName: mainModelName,
		UsageTime: time.Now(),
		Content:   content,
	}
}

// getPromptLimit 获取prompt的限制长度
func getPromptLimit(ctx context.Context, bs *botsession.BotSession, modelName string) int {
	promptLimit := bus.dao.GetModelPromptLimit(ctx, modelName)
	historyText := getHistoryText(bs.LLMHisMessages)
	historyLen := bus.dao.GetTextTokenLen(ctx, historyText+bs.SystemRole)
	finalLimit := promptLimit - historyLen
	finalLimit = config.GetModelPromptLength(bs.App.GetAppBizId(), finalLimit) // 白名单逻辑
	return finalLimit
}
