package eventbus

import (
	"context"
	"regexp"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/go-comm/clues"
	"git.woa.com/dialogue-platform/go-comm/encode"
	llmm "git.woa.com/dialogue-platform/proto/pb-stub/llm-manager-server"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	"git.woa.com/ivy/qbot/qbot/chat/internal/dao"
	"git.woa.com/ivy/qbot/qbot/chat/internal/eventbus/botsession"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model/event"
	"git.woa.com/ivy/qbot/qbot/chat/pkg"
	jsoniter "github.com/json-iterator/go"
)

func init() {
	handlers[event.EventRecommendedTo] = &RecommendToEventHandler{}
}

// RecommendToEventHandler 推荐问事件处理器
type RecommendToEventHandler struct{}

// NewRequest 创建事件请求
func (rteh *RecommendToEventHandler) NewRequest(ctx context.Context, cli *model.Conn, bs []byte) (any, error) {
	req := event.RecommendedToEvent{}
	if err := jsoniter.Unmarshal(bs, &req); err != nil {
		log.ErrorContextf(ctx, "Unmarshal event req error: %+v, data: %s", err, bs)
		return nil, err
	}
	return req, nil
}

// Process 处理事件请求
func (rteh *RecommendToEventHandler) Process(ctx context.Context, cli *model.Conn, req any) error {
	log.InfoContextf(ctx, "Invoke RecommendToEventHandler Process cli: %s, req: %+v",
		helper.Object2String(cli), helper.Object2String(req))
	ctx = clues.NewTrackContext(ctx)
	defer func(ctx *context.Context) { clues.Flush(*ctx) }(&ctx)
	ev := req.(event.RecommendedToEvent)
	clues.AddTrackData(ctx, "Conn", cli)
	clues.AddTrackData(ctx, "RecommendedToEvent", req)
	scene := dao.AppTestScene
	if ev.Type == model.RecordTypeMessage {
		scene = dao.AppReleaseScene
	}
	app, err := bus.dao.GetAppByBizID(ctx, scene, ev.BotBizID)
	clues.AddTrackDataWithError(ctx, ev.Name()+":dao.GetAppByBizID", map[string]any{
		"scene": scene, "BotBizID": ev.BotBizID, "app": app}, err)
	if err != nil {
		log.InfoContextf(ctx, "RecommendToEventHandler.Process.GetAppByBizID err:%+v", err)
		return err
	}
	if app == nil {
		log.InfoContextf(ctx, "RecommendToEventHandler.Process.GetAppByBizID not exist")
		return pkg.ErrRobotNotExist
	}
	// 推荐模型名称follow对话模型
	m := app.GetModel(ctx, model.AppTypeKnowledgeQA, model.ModelTypeRecommended) // 推荐模型
	if !m.GetIsEnabled() {
		log.InfoContextf(ctx, "RecommendToEventHandler.Process model disenable")
		return nil
	}
	modelName := app.GetModelName()
	m.PromptLimit = bus.dao.GetModelPromptLimit(ctx, modelName)
	m.ModelName = modelName
	recommendeds, err := recommeded(ctx, ev, m, app)
	clues.AddTrackE(ctx, "RecommendedToEvent.recommeded", ev.RecordID, err)
	if err != nil {
		return err
	}
	if len(recommendeds) == 0 {
		log.InfoContextf(ctx, "RecommendToEventHandler.Process recommendeds is nil")
		return nil
	}
	ctx, cancel := context.WithCancel(ctx)
	_ = bus.dao.DoEmitWsClient(ctx, cli.ClientID, &event.RecommendedEvent{
		RecordID:     ev.RecordID,
		Recommendeds: recommendeds,
	}, cancel)
	return nil
}

func recommeded(ctx context.Context, ev event.RecommendedToEvent, m *model.AppModel, app *model.App) (
	[]model.Recommended, error) {
	// 目前推荐问没有接入计费，后续如果接入计费需要考虑自定义模型是否要计费，20250604
	log.InfoContextf(ctx, "RecommendToEventHandler.Process ev:%+v, m:%+v", ev, m)
	histories, err := makeMultiRoundHistories(ctx, ev.BotBizID, ev.Session, uint(m.GetHistoryLimit()),
		[]model.RecordType{ev.Type}, false)
	if err != nil {
		log.InfoContextf(ctx, "RecommendToEventHandler.Process makeMultiRoundHistories err:%+v", err)
		return nil, err
	}
	var his [][2]string
	for i := range histories {
		his = append(his, [2]string{histories[i][0].Content, histories[i][1].Content})
	}
	prompt, err := dealRecommendPrompt(ctx, ev, m, his)
	clues.AddTrackDataWithError(ctx, "recommeded.RenderPrompt", map[string]any{"prompt": prompt}, err)
	if err != nil {
		log.InfoContextf(ctx, "RecommendToEventHandler.Process RenderPrompt err:%+v", err)
		return nil, err
	}
	requestID := model.RequestID(ctx, ev.Session.SessionID, encode.GenerateUUID()) // 非流式调用
	message := m.WrapMessages("", nil, prompt)
	req := m.NewLLMRequest(requestID, message)
	if app.IsDeepSeekModeAndHasThink() { // 带思维链的要加上no think
		log.InfoContextf(ctx, "model_name:%s, recommended, no think", req.GetModelName())
		msg := &llmm.Message{Role: llmm.Role_ASSISTANT, Content: config.App().DeepSeekConf.NoThinkPrompt}
		req.Messages = append(req.Messages, msg)
		req.ModelParams = config.App().DeepSeekConf.NoThinkModelParams
	}
	rsp, err := bus.dao.SimpleChat(ctx, req)
	log.InfoContextf(ctx, "RecommendToEventHandler,prompt:%s,rsp:%+v,err:%+v", prompt, rsp, err)
	clues.AddTrackDataWithError(ctx, "recommeded.SimpleChat", map[string]any{"llmm.Response": rsp}, err)
	if err != nil {
		return nil, err
	}
	if len(rsp.GetMessage().GetContent()) == 0 {
		log.WarnContextf(ctx, "recommeded is empty: %s => %s", ev.Question, rsp.GetMessage().GetContent())
		return nil, nil
	}
	recommendRegex := regexp.MustCompile(config.App().Bot.RecommendRegex)
	matches := recommendRegex.FindAllStringSubmatch(rsp.GetMessage().GetContent()+"\n", -1)
	log.InfoContextf(ctx, "recommeded matches: %v", matches)
	recommendeds := make([]model.Recommended, 0)
	for _, match := range matches {
		if len(match) != 2 {
			continue
		}
		recommendeds = append(recommendeds, model.Recommended{
			Question: match[1],
		})
	}
	return recommendeds, nil
}

// dealRecommendPrompt 处理推荐问的prompt，并截断，返回截断后的prompt
func dealRecommendPrompt(ctx context.Context, ev event.RecommendedToEvent, m *model.AppModel, his [][2]string) (
	string, error) {
	var err error
	pCtx := botsession.PromptCtx{Docs: ev.Docs, MultiRoundHistories: his, Question: ev.Question}
	prompt, limit, _ := m.RenderPromptWithoutTruncate(ctx, pCtx)
	if len([]rune(prompt)) > limit {
		truncateLength := len([]rune(prompt)) - limit
		var hisLength int
		for _, pair := range his {
			hisLength += len([]rune(pair[0])) + len([]rune(pair[1]))
		}
		if hisLength > truncateLength {
			var totalLength int
			for i := len(his) - 1; i >= 0; i-- {
				totalLength += len([]rune(his[i][0])) + len([]rune(his[i][1]))
				if totalLength >= truncateLength {
					his = his[:i]
					break
				}
			}
			pCtx = botsession.PromptCtx{Docs: ev.Docs, MultiRoundHistories: his}
		} else if hisLength == truncateLength {
			pCtx = botsession.PromptCtx{Docs: ev.Docs, MultiRoundHistories: nil}
		} else {
			truncateLength -= hisLength
			var docsLength int
			total := len(ev.Docs)
			docs := make([]model.RecommendedDoc, 0, truncateLength)
			for i := total - 1; i >= 0; i-- {
				docsLength += recommendDocsLength(ev.Docs[i])
				if docsLength >= truncateLength {
					docs = ev.Docs[:i]
					break
				}
			}
			pCtx = botsession.PromptCtx{Docs: docs, MultiRoundHistories: nil}
		}
	}
	prompt, err = bus.dao.TextTruncate(ctx, m, pCtx)
	addPromptNode(ctx, "recommeded.RenderPrompt", pCtx, prompt, err)
	return prompt, err
}

// recommendDocsLength 计算推荐问关联文档长度
func recommendDocsLength(doc model.RecommendedDoc) int {
	totalLength := 0
	// 计算每个字符串字段的长度，并累加到总长度
	totalLength += len([]rune(doc.Question))
	totalLength += len([]rune(doc.Answer))
	totalLength += len([]rune(doc.OrgData))
	return totalLength
}
