package eventbus

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/common/v3/plugins/i18n"
	"git.woa.com/dialogue-platform/go-comm/clues"
	"git.woa.com/dialogue-platform/go-comm/pf"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/knowledge"
	llmm "git.woa.com/dialogue-platform/proto/pb-stub/llm-manager-server"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	"git.woa.com/ivy/qbot/qbot/chat/internal/eventbus/botsession"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model/event"
	"git.woa.com/ivy/qbot/qbot/chat/internal/utils"
	"git.woa.com/ivy/qbot/qbot/chat/pkg"
)

// History 历史记录, 用于多轮对话,因为有了图片，原先的二元组 变为三元组
type History struct {
	Question  string
	Answer    string
	ImageURLs []string
}

// MultiModalProcess  多模态处理
func MultiModalProcess(ctx context.Context, bs *botsession.BotSession) (err error) {
	if !helper.IsQueryHasOtherCharacters(bs.PromptCtx.Question) { // 改写后，还是只有图片的情况，返回图片Caption
		return QueryWithImage(ctx, bs)
	}
	if bs.App.GetKnowledgeQa().GetImageTextRetrieval() { // 开启图文检索，走多模态阅读理解
		log.InfoContextf(ctx, "multi modal comprehension begin.")
		if err = MultiModalReadingComprehension(ctx, bs); err != nil {
			log.WarnContextf(ctx, "MultiModalReadingComprehension err:%v", err)
		}
		if err == nil && !bs.Flags.IsMLLMReject { // 没报错且没拒答，则认为已经走多模态阅读理解回复
			log.InfoContextf(ctx, "doc segment reply after multi modal comprehension")
			return nil
		}
	}
	log.InfoContextf(ctx, "multi modal dialog begin.")
	return MultiModalMultiTurnDialog(ctx, bs) // 没有开启图文检索，走多模态问答

}

// MultiModalMultiTurnDialog 多模态多轮对话,不拒答。-hunyuan-vision
func MultiModalMultiTurnDialog(ctx context.Context, bs *botsession.BotSession) (err error) {
	currImages := GetImagesFromQuery(ctx, bs, bs.PromptCtx.Question) // 当前请求中的图片
	bs.Images = currImages
	// 获取历史对话，不用Query改写的结果，直接请求MLLM
	bs.TokenStat.UpdateProcedure(event.NewProcessingTSProcedure(ctx, event.ProcedureImage))
	SendTokenStat(ctx, bs, bs.TokenStat)
	prompt := ""
	m := getAppModel(ctx, bs)
	if needDsR1Summary(ctx, bs, false) { // deepseek r1 模型，图文问答时，修改Prompt
		m1 := bs.App.GetModel(ctx, model.AppTypeKnowledgeQA, model.ModelTypeMLLMDialog)
		promptCtx := model.ImageQuizPromptCtx{
			Question: bs.PromptCtx.Question,
		}
		prompt, err = bus.dao.TextTruncate(ctx, m1, promptCtx) // 生成Prompt
		if err != nil {
			log.ErrorContextf(ctx, "fail to truncate prompt: %s", err.Error())
			return err
		}
	}
	sysRole := m.GetSysPrompt(ctx, true, model.IsSelfAwarenessIntent(bs.IntentCate), bs.SystemRole)
	message := m.WrapMessages(sysRole, nil, prompt)
	inferParams := m.CreateModelParamsObject(bs.EnableRandomSeed)
	req := m.NewLLMRequestWithModelParams(bs.LLMRequestID, message, inferParams)
	if err = MultiModalDialogRequest(ctx, *bs, req); err != nil {
		return err
	}
	if len(sysRole) > 0 { // 将系统提示词放在消息的最前面
		req.Messages = append([]*llmm.Message{{Role: llmm.Role_SYSTEM, Content: sysRole}}, req.Messages...)
	}
	bs.Flags.IsJudgeModelReject = false // 多模态对话，不判断拒答
	bs.Intent = "图文问答"
	bs.IntentCate = model.IntentTypeImageQA
	if needDsR1Summary(ctx, bs, false) {
		var llmRsp *llmm.Response
		var isStopGen bool
		llmRsp, isStopGen, err = streamToNonStream(ctx, bs, req, event.ProcedureImage)
		if err != nil {
			return err
		}
		if isStopGen {
			log.InfoContext(ctx, "receive stop generation")
			return nil
		}
		return processDsR1Summary(ctx, bs, llmRsp, model.ReplyMethodImage)
	}
	startTime, last, lastEvil, isModelRejected, err := streamReply(ctx, bs, req,
		event.ProcedureImage, model.ReplyMethodImage, nil)
	if err != nil {
		return err
	}
	if !isModelRejected && last != nil && last.Message != nil {
		// output := last.Message.Content
		_, _ = checkLastAndCreateRecord(ctx, bs, model.ReplyMethodImage, last, lastEvil, req, startTime)
	}
	return nil
}

// MultiModalReadingComprehension 多模态阅读理解
func MultiModalReadingComprehension(ctx context.Context, bs *botsession.BotSession) error {
	// 检索
	currImages := GetImagesFromQuery(ctx, bs, bs.PromptCtx.Question) // 当前请求中的图片
	bs.Images = currImages
	oldQuestion := bs.PromptCtx.Question // 保存多模态改写结果
	// 混元多模态改写
	bs.PromptCtx.Question, _ = bus.rewrite.HunYuanImageRewrite(ctx, bs) // 2.9.0开始混元多模态改写结果仅用于知识检索
	// 知识检索阶段耗时统计
	pf.StartElapsedAsMetrics(ctx, config.App().StageTaskName.SearchKnowledge)
	_ = bus.retrieval.KnowledgeSearch(ctx, bs)
	pf.AppendSpanElapsed(ctx, config.App().StageTaskName.SearchKnowledge)
	if len(bs.Knowledge) == 0 {
		return errors.New("no knowledge")
	}
	bs.Placeholders = utils.GetPlaceHolders(bs.Knowledge)
	bs.PromptCtx.Docs = bs.Knowledge
	// 包含图搜图，走阅读理解
	bs.TokenStat.UpdateProcedure(event.NewProcessingTSProcedure(ctx, event.ProcedureImage))
	SendTokenStat(ctx, bs, bs.TokenStat)
	log.DebugContextf(ctx, "MultiModalReadingComprehension, mllmRewriteQuestion: %s, hunyuanRewriteQuestion: %s",
		oldQuestion, bs.PromptCtx.Question)
	bs.PromptCtx.Question = oldQuestion // 恢复多模态改写结果，用于多模态阅读理解
	m := bs.App.GetModel(ctx, model.AppTypeKnowledgeQA, model.ModelTypeMLLMComprehension)
	m.PromptLimit = bus.dao.GetModelPromptLimit(ctx,
		config.GetMultiModalComprehensionModelName(bs.App.AppBizId)) - len([]rune(bs.SystemRole)) + 2000
	docPromptLimit := m.PromptLimit - len([]rune(bs.PromptCtx.Question)) -
		len(currImages)*config.App().MultiModal.OneImageTokenLength // 剩余的最大长度
	supportMaxImage := calImagePlaceholderCount(ctx, bs.Knowledge, docPromptLimit, len(currImages))
	m.PromptLimit = m.PromptLimit - supportMaxImage*config.App().MultiModal.OneImageTokenLength
	m.ModelName = config.GetMultiModalComprehensionModelName(bs.App.AppBizId)
	log.DebugContextf(ctx, "Prompt is : %s, model name is: %s", m.GetPrompt(), m.GetModelName())
	prompt, err := bus.dao.TextTruncate(ctx, m, bs.PromptCtx) // 生成Prompt
	log.DebugContextf(ctx, "R|MultiModalReadingComprehension|prompt %s, %s, ERR: %v",
		helper.Object2StringEscapeHTML(bs.PromptCtx), prompt, err)
	if err != nil {
		addPromptNode(ctx, "MultiModalReadingComprehension.RenderPrompt", bs.PromptCtx, prompt, err)
		return err
	}
	bs.Flags.IsJudgeModelReject = true // MLLM需要判断拒答
	bs.TokenStat.UpdateProcedure(event.NewProcessingTSProcedure(ctx, event.ProcedureKnowledge))
	SendTokenStat(ctx, bs, bs.TokenStat)

	sysRole := m.GetSysPrompt(ctx, true, false, bs.SystemRole)
	message := m.WrapMessages(sysRole, nil, prompt)
	inferParams := m.CreateModelParamsObject(bs.EnableRandomSeed)
	req := m.NewLLMRequestWithModelParams(bs.LLMRequestID, message, inferParams)
	temp := replacePlaceholders(prompt, bs.Placeholders)                                // 把Prompt中的占位符替换为真实链接
	b, images := helper.ExtractLinkWithPlaceholder(temp, 0, supportMaxImage)            // 把图片url替换成图片占位符
	queryWithPh := ReplaceQueryWithPlaceholder(ctx, bs.PromptCtx.Question, len(images)) // query占位符的处理
	b = strings.Replace(b, bs.PromptCtx.Question, queryWithPh, 1)                       // b 是最终的用户prompt
	req.ModelName = config.GetMultiModalComprehensionModelName(bs.App.AppBizId)
	req.Messages[len(req.Messages)-1].Content = b
	bs.Images = append(images, currImages...) // 存的是替换成占位符的图片
	bs.Intent = "多模态阅读理解"
	bs.IntentCate = model.IntentTypeMLLM
	req.Messages[len(req.Messages)-1].Images = bs.Images

	log.InfoContextf(ctx, "R|MultiModalReadingComprehension|NewLLMRequest|%s,req: %s",
		bs.EventSource, helper.Object2StringEscapeHTML(req))
	addPromptNode(ctx, "MultiModalReadingComprehension.RenderPrompt", bs.PromptCtx, b, nil)
	startTime, last, lastEvil, isModelRejected, err := streamReply(ctx, bs, req,
		event.ProcedureKnowledge, model.ReplyMethodDecorator, nil)
	if err != nil {
		return err
	}
	if !isModelRejected && last != nil && last.Message != nil {
		_, _ = checkLastAndCreateRecord(ctx, bs, model.ReplyMethodDecorator, last, lastEvil, req, startTime)
		if last.GetMessage().GetContent() != "" { // 多模态出参考来源
			matchRefer(ctx, bs, last.GetMessage().GetContent(), bs.Placeholders)
		}
	}
	bs.Flags.IsMLLMReject = isModelRejected
	return nil
}

// docsContainsImageSearchImage 判断文档是否包含图搜图
// 0： 向量/混合检索的结果； 1：text2sql的结果；2：图搜图； 3：文搜图
func docsContainsImageSearchImage(docs []*knowledge.SearchKnowledgeRsp_SearchRsp_Doc) bool {
	for _, doc := range docs {
		if len(doc.GetImageUrls()) > 0 || doc.GetResultType() == 2 {
			return true
		}
	}
	return false
}

// MultiModalDialogRequest 多模态对话请求
func MultiModalDialogRequest(ctx context.Context, bs botsession.BotSession, req *llmm.Request) (err error) {
	// 多模态对话 使用的model_name
	req.ModelName = config.GetDialogMLLMModelName(bs.App.AppBizId)
	multiModalHistories, err := GetDialogHistories(ctx, bs.App.AppBizId,
		bs.Session, []model.RecordType{bs.Type}, true, bs.App.GetHistoryLimit())
	if err != nil {
		return err
	}
	var totalIndex int
	req.Messages, totalIndex = wrapMessages(ctx, multiModalHistories, len(bs.Images)) // todo
	if len(bs.Images) != 0 {                                                          // 当前请求中有图片
		placeholder, query := "", bs.OriginContent
		temp := helper.GetAllImage(bs.OriginContent)
		for i, v := range temp {
			placeholder = fmt.Sprintf(config.App().MultiModal.Placeholder, i+totalIndex)
			query = strings.Replace(query, v, placeholder, 1)
		}
		message := &llmm.Message{Role: llmm.Role_USER,
			Content: query,
			Images:  bs.Images,
		}
		req.Messages = append(req.Messages, message)
	} else {
		req.Messages = append(req.Messages, &llmm.Message{Role: llmm.Role_USER, Content: bs.OriginContent})
	}
	if config.OnlyCosURLModel(req.ModelName) {
		for idx, val := range req.Messages {
			req.Messages[idx].Content = helper.RemoveContentPlaceholder(val.Content)
			req.Messages[idx].Images = helper.ReplaceDomain(req.Messages[idx].Images)
		}
	}
	log.InfoContextf(ctx, "mllm request: %s", helper.Object2StringEscapeHTML(req))
	return nil
}

// GetDialogHistories 构造多模态历史记录 for 多轮对话
func GetDialogHistories(ctx context.Context, botBizID uint64, session *model.Session,
	types []model.RecordType, useRewrite bool, limit uint32,
) ([]*History, error) {
	records, err := bus.dao.GetLastNBotRecord(ctx, botBizID, session, uint(limit), types)
	log.DebugContextf(ctx, "records:%v", helper.Object2String(records))
	if err != nil {
		return nil, err
	}
	if len(records) == 0 {
		return nil, nil
	}
	ids := make([]string, 0, len(records))
	for _, v := range records {
		ids = append(ids, v.RelatedRecordID)
	}
	relates, err := bus.dao.GetMsgRecordsByRecordID(ctx, ids, botBizID)
	if err != nil {
		return nil, err
	}
	if len(relates) == 0 {
		return nil, pkg.ErrInvalidMsgRecord
	}
	histories := wrapDialogHistories(ctx, records, relates, useRewrite)
	clues.AddTrackData(ctx, "MakeMultiModalHistories()", map[string]any{
		"records": records, "relates": relates, "useRewrite": useRewrite, "histories": histories,
	})
	log.DebugContextf(ctx, "MultiModal Histories:%s", helper.Object2String(histories))
	return histories, nil
}

// wrapDialogHistories 生成问题答案对，records 是答案 relates 是问题
func wrapDialogHistories(ctx context.Context, records, relates []model.MsgRecord, useRewrite bool) []*History {
	mp := make(map[string]model.MsgRecord, len(relates))
	md := helper.New()
	for _, v := range relates {
		mp[v.RecordID] = v
	}
	historyList := make([]*History, 0)
	var totalIndex = 0 // 处理图片的总index
	for _, v := range records {
		related := mp[v.RelatedRecordID]
		// 问题中仅仅传了文件（摘要的场景） 过滤掉
		if len(related.Content) == 0 {
			continue
		}
		if helper.IsQueryContainsImage(related.Content) { // 问题中包含图片
			_, p := md.ExtractLinkWithPlaceholder([]byte(related.Content))
			var placeholder string
			for i := 0; i < len(p); i++ {
				placeholder = placeholder + fmt.Sprintf(config.App().MultiModal.Placeholder, i+totalIndex)
			}
			totalIndex = totalIndex + len(p)
			var question string
			if helper.IsQueryOnlyImage(related.Content) { // 问题中仅包含图片
				question = placeholder + i18n.Translate(ctx, config.App().MultiModal.GetCaptionPrompt)
			} else {
				question = placeholder + helper.When(useRewrite && related.RewroteContent != "",
					related.RewroteContent, related.Content)
			}
			historyList = append(historyList, &History{
				Question:  question,
				Answer:    v.GetSafeContent(ctx), // caption在答案里面
				ImageURLs: helper.GetImagesFromPlaceholders(p),
			})
		}
	}
	log.InfoContextf(ctx, "historyList:%v", helper.Object2String(historyList))
	return historyList
}

func wrapMessages(ctx context.Context, histories []*History, currImageCount int) ([]*llmm.Message, int) {
	var messages []*llmm.Message
	var totalIndex = 0
	for _, v := range histories {
		if len(v.ImageURLs) != 0 {
			canAdd := config.GetMaxImageCount() - currImageCount - totalIndex
			if len(v.ImageURLs) > canAdd {
				// 把imageURLs不超的部分传回去
				if canAdd > 0 {
					messages = append(messages, &llmm.Message{Role: llmm.Role_USER,
						Content: v.Question,
						Images:  v.ImageURLs[:canAdd],
					})
					messages = append(messages, &llmm.Message{Role: llmm.Role_ASSISTANT, Content: v.Answer})
				}
				totalIndex = config.GetMaxImageCount() - currImageCount
				break
			}
			messages = append(messages, &llmm.Message{Role: llmm.Role_USER,
				Content: v.Question,
				Images:  v.ImageURLs,
			})
			totalIndex += len(v.ImageURLs)
		} else {
			messages = append(messages, &llmm.Message{Role: llmm.Role_USER, Content: v.Question})
		}
		messages = append(messages, &llmm.Message{Role: llmm.Role_ASSISTANT, Content: v.Answer})
	}
	log.DebugContextf(ctx, "histories: %s, messages: %s", helper.Object2String(histories), helper.Object2String(messages))
	return messages, totalIndex
}

// GetImagesFromQuery 从改写后的Query中获取图片
func GetImagesFromQuery(ctx context.Context, bs *botsession.BotSession, query string) []string {
	var images []string
	imageIndexs := helper.GetQueryRewriteImageIndex(query)
	log.DebugContextf(ctx, "imageIndexs: %s, ImageQueue:%s",
		helper.Object2StringEscapeHTML(imageIndexs), helper.Object2StringEscapeHTML(bs.ImageQueue))
	for _, index := range imageIndexs {
		if index <= len(bs.ImageQueue) && index > 0 {
			images = append(images, bs.ImageQueue[index-1].ImageURL)
		}
	}
	log.DebugContextf(ctx, "images: %s", helper.Object2StringEscapeHTML(images))

	return images
}

// ReplaceQueryWithPlaceholder 使用多模态占位符替换改写后的Query中占位符
// 2.8之前【图1】 -> <ut_im##age_here_index>1</ut_im##age_here_index>[Picture 1]
// 2.8【图1】 -> Picture 1: <image>[Picture 1]
func ReplaceQueryWithPlaceholder(ctx context.Context, query string, startIndex int) string {
	res := helper.GetQueryRewriteImagePlaceholder(query)
	for i, item := range res {
		query = strings.Replace(query, item,
			fmt.Sprintf(config.App().MultiModal.PlaceholderV2, i+startIndex+1, i+startIndex+1), 1)
	}
	log.DebugContextf(ctx, "rewrite final query is: %s", query)
	return query
}

// truncMultiModalDocs 截断带图的文档
func truncMultiModalDocs(ctx context.Context, docs []*knowledge.SearchKnowledgeRsp_SearchRsp_Doc, promptLimit,
	imageCountInQuery int) (images []string, knowledge []*knowledge.SearchKnowledgeRsp_SearchRsp_Doc) {
	log.DebugContextf(ctx, "deal docs contains image")
	if len(docs) == 0 {
		return images, docs
	}
	totalLength, preLength, maxImageCount := 0, 0, config.GetMaxImageCount()-imageCountInQuery
	if maxImageCount < 1 || promptLimit < 1 {
		return images, docs
	}
	// 再处理普通的图
	for _, doc := range docs {
		knowledge = append(knowledge, doc)
		var currImages []string
		currContent := ""
		if doc.GetDocType() == 1 || doc.GetDocType() == 4 {
			currContent = doc.GetAnswer()
		} else {
			currContent = doc.GetOrgData()
		}
		currImages = helper.GetAllImageURLs(currContent)
		totalLength += len(currImages)*config.App().MultiModal.OneImageTokenLength + len([]rune(currContent))

		if totalLength > promptLimit {
			images = getSubImages(images, currImages, currContent, maxImageCount, preLength, promptLimit)
			break
		}
		preLength = totalLength
		images = append(images, currImages...)
		if len(images) > maxImageCount {
			images = images[:maxImageCount]
			break
		}
	}
	return images, knowledge
}

// calImagePlaceholderCount 计算有多少张图片可以替换成占位符
func calImagePlaceholderCount(ctx context.Context, docs []*knowledge.SearchKnowledgeRsp_SearchRsp_Doc, promptLimit,
	imageCountInQuery int) int {
	supportMaxImageNum := 0
	maxImageCount := config.GetMaxImageCount() - imageCountInQuery
	// 先计算下切片的总长度
	docLen := 0
	for _, doc := range docs {
		currContent := ""
		if doc.GetDocType() == 1 || doc.GetDocType() == 4 {
			currContent = doc.GetAnswer()
		} else {
			currContent = doc.GetOrgData()
		}
		docLen += len([]rune(currContent))
	}
	// 判断切片长度是否超过模型窗口
	if docLen > promptLimit { // 图片不能替换成占位符
		supportMaxImageNum = 0
	} else { // 计算有多少张图片可以替换占位符
		supportMaxImageNum = (promptLimit - docLen) / config.App().MultiModal.OneImageTokenLength
	}
	supportMaxImageNum = helper.When(supportMaxImageNum > maxImageCount, maxImageCount, supportMaxImageNum)
	log.InfoContextf(ctx, "supportImageNum:%d ", supportMaxImageNum)
	return supportMaxImageNum
}
