// chat
//
// @(#)tag_extraction.go  Tuesday, February 27, 2024
// Copyright(c) 2024, leyton@Tencent. All rights reserved.

package eventbus

import (
	"context"
	"encoding/json"
	"errors"
	"strconv"
	"sync"
	"time"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	errors2 "git.woa.com/dialogue-platform/common/v3/errors"
	admin "git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_admin_config_server"
	llmm "git.woa.com/dialogue-platform/proto/pb-stub/llm-manager-server"
	"git.woa.com/ivy/protobuf/trpc-go/qbot/qbot/infosec"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	"git.woa.com/ivy/qbot/qbot/chat/internal/dao"
	"git.woa.com/ivy/qbot/qbot/chat/internal/eventbus/botsession"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model/event"
	"git.woa.com/ivy/qbot/qbot/chat/pkg"
	ispkg "git.woa.com/ivy/qbot/qbot/infosec/pkg"
	"github.com/google/uuid"
	jsoniter "github.com/json-iterator/go"
)

func init() {
	handlers[event.EventTagExtraction] = &TagExtractionEventHandler{}
}

// TagExtractionEventHandler 标签提取体验事件处理器
type TagExtractionEventHandler struct{}

// NewRequest TODO
func (e *TagExtractionEventHandler) NewRequest(ctx context.Context, conn *model.Conn, data []byte) (any, error) {
	log.InfoContextf(ctx, "I|TagExtractionEventHandler|NewRequest %+v %s", conn, string(data))
	req := event.TagExtractionEvent{}
	if err := jsoniter.Unmarshal(data, &req); err != nil {
		log.ErrorContextf(ctx, "Unmarshal event req error: %+v, data: %s", err, data)
		return nil, err
	}
	if !req.IsValid() {
		return nil, pkg.ErrBadRequest
	}
	return req, nil
}

// Process 处理事件请求
func (e *TagExtractionEventHandler) Process(ctx context.Context, cli *model.Conn, req any) error {
	// TODO: 临时解决 针对Apex 链接未建立成功首次进入的请求 先sleep下再执行逻辑 确保Apex链接建立后再执行 后面Apex修复后删除
	if cli.IsSSE && config.App().SSE.EnableDeferClientClose {
		log.DebugContextf(ctx, "clientID: %s SSE.DeferClientCloseTime: %+v",
			cli.ClientID, config.App().SSE.DeferClientCloseTime)
		time.Sleep(config.App().SSE.DeferClientCloseTime)
	}
	log.InfoContextf(ctx, "I|TagExtractionEventHandler|Process %+v %+v", cli, req)
	ev := req.(event.TagExtractionEvent)
	// 框架未携带, 则使用业务数据
	pkg.WithSessionID(ctx, ev.SessionID)
	pkg.WithRequestID(ctx, ev.RequestID)
	pkg.WithTraceID(ctx, ev.RequestID)
	pkg.WithLoginUserType(ctx, cli.LoginUserType)
	var err error
	session := &model.Session{
		SessionID: ev.SessionID,
		BotBizID:  cli.APIBotBizID, VisitorID: cli.CorpStaffID, VisitorBizID: cli.CorpStaffBizID,
	}
	if cli.Type != model.ConnTypeAPIVisitor {
		if session, err = bus.dao.GetSession(ctx, model.SessionTypeNormal, ev.SessionID); err != nil {
			return err
		}
		if session == nil {
			return pkg.ErrSessionNotFound
		}
	}
	log.InfoContextf(ctx, "R|TagExtractionEventHandler|botsession %+v", session)
	app, err := getValidApp(ctx, session.BotBizID, dao.AppReleaseScene)
	if err != nil {
		return err
	}
	if !app.IsAppTypeClassify() {
		return pkg.ErrAppTypeNotSupported
	}

	// 如果用户有传入相关参数, 则按用户的做一次覆盖
	ev = e.useUserInput(ctx, ev, app)
	if err = e.checkChatWordsLimit(ctx, app, ev); err != nil {
		return err
	}
	from := &model.CorpStaff{}
	if cli.Type != model.ConnTypeAPIVisitor {
		if from, err = bus.dao.GetCorpStaffByBizID(ctx, cli.CorpStaffBizID); err != nil {
			return err
		}
		if from == nil {
			return pkg.ErrVisitorNotExist
		}
	}

	msg, err := e.CreateMsgRecord(ctx, cli, app, ev)
	if err != nil {
		return err
	}
	ctx, cancel := context.WithCancel(ctx)
	_ = bus.dao.DoEmitWsClient(ctx, cli.ClientID, &event.ReplyEvent{
		IsFinal:    true,
		IsFromSelf: true,
		SessionID:  ev.SessionID,
		RequestID:  ev.RequestID,
		FromName:   from.NickName,
		FromAvatar: from.Avatar,
		Content:    ev.Content,
		RecordID:   msg.RecordID,
		Timestamp:  msg.CreateTime.Unix(),
		IsEvil:     false,
	}, cancel)

	if err := limit(ctx, app, msg.RecordID, false, func() error {
		return e.exec(ctx, cli, session, app, ev, &msg)
	}); err != pkg.ErrConcurrenceExceeded {
		return err
	}
	return botBusyReply(ctx,
		botsession.NewBusySession(cli, session, app, model.RecordTypeMessage, ev.RequestID, msg.RecordID),
	)
}

// CreateMsgRecord 创建消息记录
func (e *TagExtractionEventHandler) CreateMsgRecord(ctx context.Context,
	cli *model.Conn, app *model.App, ev event.TagExtractionEvent) (model.MsgRecord, error) {
	msg := model.MsgRecord{
		BotBizID:     app.GetAppBizId(),
		SessionID:    ev.SessionID,
		RecordID:     uuid.NewString(),
		Type:         model.RecordTypeMessage,
		ToID:         app.GetId(),
		ToType:       model.SourceTypeRobot,
		FromID:       cli.CorpStaffID,
		FromType:     cli.GetSourceType(),
		Content:      ev.Content,
		CreateTime:   time.Now(),
		CfgVersionID: app.GetConfigVersionId(),
		TraceID:      model.TraceID(ctx),
	}
	msgID, err := bus.dao.CreateMsgRecord(ctx, msg, nil) // for query
	if err != nil {
		msg.ID = uint64(msgID)
	}
	return msg, err
}

func (e *TagExtractionEventHandler) useUserInput(ctx context.Context,
	ev event.TagExtractionEvent, app *model.App) event.TagExtractionEvent {
	if app.GetClassify() == nil {
		return ev
	}
	if len(ev.ModelName) > 0 {
		if v, ok := app.GetClassify().GetModuleList()[ev.ModelName]; ok {
			if app.GetClassify().GetModel() == nil {
				app.Classify.Model = make(map[string]*admin.AppModelInfo)
			}
			app.Classify.Model[string(model.ModelTypeClassifyExtract)] = v
		}
	}
	if len(ev.Tags) > 0 {
		var labels []*admin.ClassifyLabel
		for _, t := range ev.Tags {
			labels = append(labels, &admin.ClassifyLabel{
				Name:        t.Name,
				Description: t.Description,
				Values:      t.Values,
			})
		}
		app.Classify.Labels = labels
	}
	log.InfoContextf(ctx, "REPLACED|classify %+v", app.GetClassify())
	return ev
}

// exec 执行消息
func (e *TagExtractionEventHandler) exec(
	ctx context.Context,
	cli *model.Conn, session *model.Session, app *model.App, ev event.TagExtractionEvent, msg *model.MsgRecord,
) (err error) {
	replyID := uuid.NewString()
	botSession := botsession.BotSession{
		Session:         session,
		Type:            model.RecordTypeMessage,
		RecordID:        replyID,
		RelatedRecordID: msg.RecordID,
		RequestID:       ev.RequestID,
		App:             app,
		To:              cli,
		PromptCtx: botsession.PromptCtx{
			Question: ev.Content,
			Tags: func() []*admin.ClassifyLabel {
				if app != nil && app.GetClassify() != nil {
					return app.GetClassify().GetLabels()
				}
				return nil
			}(),
		},
		NeedCheck:         true,
		StreamingThrottle: ev.StreamingThrottle,
		ModelType:         app.GetClassifyModelType(),
		EventSource:       ev.Name(),
		TokenStat: &event.TokenStatEvent{
			SessionID:   ev.SessionID,
			RequestID:   ev.RequestID,
			RecordID:    replyID,
			StartTime:   time.Now(),
			EventSource: ev.Name(),
		},
	}
	initTokenBalance(ctx, botSession.TokenStat, bus.dao, app.GetCorpId(), app.GetMainModelName())
	_, _, _, err = tagReply(ctx, &botSession)
	if err != nil {
		return err
	}
	return nil
}

// checkChatWordsLimit 用户输入长度限制
func (e *TagExtractionEventHandler) checkChatWordsLimit(ctx context.Context, app *model.App,
	ev event.TagExtractionEvent) error {
	if ev.Content == "" {
		log.ErrorContextf(ctx, "R|TagExtractionEventHandler|checkChatWordsLimit empty content")
		return pkg.ErrBadRequest
	}
	defaultAllowMaxTags := config.App().App.TagExtraction.AllowMaxTags
	if len(ev.Tags) > defaultAllowMaxTags { // api 调用时可能传入
		log.ErrorContextf(ctx, "R|TagExtractionEventHandler|checkChatWordsLimit "+
			"over classify max tags limit:%d len(ev.tags):%d", defaultAllowMaxTags, len(ev.Tags))
		return pkg.ErrBadRequest
	}
	// 长度限制: 一个汉字按一个字符计算
	contentLen := len([]rune(ev.Content))
	m := app.GetModel(ctx, model.AppTypeClassify, app.GetClassifyModelType())
	chatWordsLimit := m.GetChatWordsLimit()
	if chatWordsLimit > 0 {
		if uint32(contentLen) > chatWordsLimit {
			log.ErrorContextf(ctx, "R|TagExtractionEventHandler|checkChatWordsLimit "+
				"over model chat words limit:%d contentLen:%d", chatWordsLimit, contentLen)
			return pkg.ErrBadRequest
		}
		return nil
	}
	defaultQueryMaxLen := config.App().App.TagExtraction.DefaultQueryMaxLen
	if defaultQueryMaxLen > 0 && len([]rune(ev.Content)) > defaultQueryMaxLen {
		log.ErrorContextf(ctx, "R|TagExtractionEventHandler|checkChatWordsLimit "+
			"over classify default chat words limit:%d contentLen:%d", defaultQueryMaxLen, contentLen)
		return pkg.ErrBadRequest
	}
	return nil
}

// renderTagPrompt 生成标签提取 prompt, 一个汉字按一个字符计算, 如果过长, 则拆分多个 prompt, 多次调用大模型
//
// 详见需求评论
// 【知识引擎2.0】页面结构调整-知识摘要应用
// https://tapd.woa.com/project_qrobot/prong/stories/view/1070080800116171367
func renderTagPrompt(ctx context.Context, promptTmpl string, promptCtx botsession.PromptCtx, limit int) ([]string,
	error) {
	if config.App().App.TagExtraction.DefaultQueryMaxLen == 0 {
		p0, err := bus.dao.TextTruncateCommon(ctx, promptTmpl, promptCtx, limit)
		return []string{p0}, err
	}
	// 计算 tags 的总长度
	var tagsLen int
	for i := range promptCtx.Tags {
		tagsLen += len([]rune(promptCtx.Tags[i].Name)) + len([]rune(promptCtx.Tags[i].Description))
		for j := range promptCtx.Tags[i].Values {
			tagsLen += len([]rune(promptCtx.Tags[i].Values[j]))
		}
	}

	// 总 prompt 的文本长度(计算 tags 的总长度 + 加上用户输入的文本)
	total := tagsLen + len([]rune(promptCtx.Question))
	if total < limit {
		p0, err := bus.dao.TextTruncateCommon(ctx, promptTmpl, promptCtx, limit)
		return []string{p0}, err
	}

	// 标签, 文本, 分批次处理
	// 是否需要分两批文本抽取
	m := limit / 2
	if len([]rune(promptCtx.Question)) < m {
		promptCtxList := splitPromptCtx(ctx, promptCtx.Question, promptCtx.Tags, limit)
		return promptCtxList2string(ctx, promptTmpl, promptCtxList, limit)
	}

	// 标签, 文本, 各取一半的量, 做为输入
	q1 := string([]rune(promptCtx.Question)[:m])
	q2 := string([]rune(promptCtx.Question)[m:])

	q1List := splitPromptCtx(ctx, q1, promptCtx.Tags, limit)
	q2List := make([]botsession.PromptCtx, len(q1List))
	for i := range q1List {
		e := q1List[i]
		e.Question = q2
		q2List[i] = e
	}

	promptCtxList := append(q1List, q2List...)
	return promptCtxList2string(ctx, promptTmpl, promptCtxList, limit)
}

func promptCtxList2string(ctx context.Context,
	promptTmpl string, promptCtxList []botsession.PromptCtx, limit int) ([]string, error) {
	var promptList []string
	for i := range promptCtxList {
		log.InfoContextf(ctx, "R|splitPromptCtx %d, %+v", len(promptList), promptList)
		p0, err := bus.dao.TextTruncateCommon(ctx, promptTmpl, promptCtxList[i], limit)
		if err != nil {
			log.ErrorContextf(ctx, "R|RenderPrompt [%d] Err: %v", i, err)
			continue
		}
		promptList = append(promptList, p0)
	}
	if len(promptList) == 0 {
		msg := "标签分隔处理错误"
		log.ErrorContextf(ctx, "R|RenderPrompt Err: %s", msg)
		return nil, errors.New(msg)
	}
	log.InfoContextf(ctx, "R|promptList %+v", promptList)
	return promptList, nil
}

func splitPromptCtx(ctx context.Context, query string, tags []*admin.ClassifyLabel, limit int) []botsession.PromptCtx {
	ql := len([]rune(query)) // query length
	al := limit - ql         // available length
	var promptCtxList []botsession.PromptCtx
	p := botsession.PromptCtx{
		Question: query,
	}
	var tl int // tag length
	for i := 0; i < len(tags); i++ {
		tl0 := len([]rune(tags[i].Name)) + len([]rune(tags[i].Description))
		for j := range tags[i].Values {
			tl0 += len([]rune(tags[i].Values[j]))
		}
		if tl+tl0 <= al {
			tl += tl0
			p.Tags = append(p.Tags, tags[i])
		} else {
			if len(p.Tags) > 0 {
				promptCtxList = append(promptCtxList, p)
				p = botsession.PromptCtx{
					Question: query,
					Tags:     []*admin.ClassifyLabel{tags[i]},
				}
			} else {
				p.Tags = append(p.Tags, tags[i])
			}
			tl = tl0
		}
	}
	promptCtxList = append(promptCtxList, p)
	return promptCtxList
}

func tagReply(ctx context.Context, bs *botsession.BotSession) (bool, bool, string, error) {
	m := getAppModel(ctx, bs)
	log.DebugContextf(ctx, "R|botReply|GetModel type: %v, model: %s", bs.ModelType, helper.Object2String(m))
	prompt, err := bus.dao.TextTruncate(ctx, m, bs.PromptCtx)
	log.DebugContextf(ctx, "R|botReply|prompt %s, %s, ERR: %v", helper.Object2String(bs.PromptCtx), prompt, err)
	if err != nil {
		return false, false, "", err
	}
	promptList, err := renderTagPrompt(ctx, m.GetPrompt(), bs.PromptCtx, int(m.GetPromptWordsLimit()))
	if err != nil {
		log.InfoContextf(ctx,
			"R|renderTagPrompt %v, %v, %d, ERR: %v", m.GetPrompt(), bs.PromptCtx, m.GetPromptWordsLimit(), err)
		return false, false, "", err
	}
	bs.TokenStat.UpdateProcedure(event.NewProcessingTSProcedure(ctx, event.ProcedureLLM))
	SendTokenStat(ctx, bs, bs.TokenStat)
	st := time.Now()
	var respList []*llmm.Response
	// 每批 5并发请求大模型
	const llmConcurrency = 5
	for i := 0; i < len(promptList)/llmConcurrency+1; i++ {
		wg := &sync.WaitGroup{}
		resultCh := make(chan *llmm.Response, llmConcurrency)
		min := len(promptList)
		if (i+1)*llmConcurrency < min {
			min = (i + 1) * llmConcurrency
		}
		for j := i * llmConcurrency; j < min; j++ {
			wg.Add(1)
			go func(j int, ctx context.Context) {
				defer errors2.PanicHandler()
				defer wg.Done()
				resp, err := singleExtraction(ctx, bs, promptList[j])
				if err == nil {
					resultCh <- resp
				}
			}(j, trpc.CloneContext(ctx))
		}
		wg.Wait()
		close(resultCh)
		for r := range resultCh {
			respList = append(respList, r)
		}
	}
	log.InfoContextf(ctx, "R|tagReply|respList len: %d, %v", len(respList), respList)
	if len(respList) == 0 {
		return false, false, "", err
	}
	last := mergeLLMResp(ctx, respList)
	reply := last.GetMessage().GetContent()

	var lastEvil *infosec.CheckRsp
	var isEvil bool
	if reply != "" {
		lastEvil, err = bus.dao.Check(ctx, tagReplyCheckReq(ctx, bs, st, reply))
		if err == nil {
			isEvil = lastEvil.GetResultCode() == ispkg.ResultEvil
		}
	}
	ctx, cancel := context.WithCancel(ctx)
	_ = bus.dao.DoEmitWsClient(ctx, bs.To.ClientID,
		bs.NewReplyEvent(ctx, last, isEvil, model.ReplyMethodModel, st, []string{}), cancel,
	)
	sendFinishTokenStat(ctx, bs, nil, nil, last, event.ProcedureLLM)
	method := helper.When(isEvil, model.ReplyMethodEvil, model.ReplyMethodModel)

	requestID := model.RequestID(ctx, bs.Session.SessionID, bs.RecordID) // 非流式调用
	message := m.WrapMessages("", nil, prompt)
	req0 := m.NewLLMRequest(requestID, message)
	reply = getTagReply(ctx, *bs, isEvil, reply)
	m0 := bs.NewBotRecord(ctx, reply, req0, method, lastEvil, st)
	newMsg, stat := event.GetMsgRecordAndTokenStat(ctx, m0)
	if _, err := bus.dao.CreateMsgRecord(ctx, newMsg, stat); err != nil { // for answer
		return false, false, "", err
	}
	return isEvil, false, reply, nil
}

func tagReplyCheckReq(ctx context.Context, bs *botsession.BotSession, st time.Time, reply string) *infosec.CheckReq {
	appBizID := strconv.FormatUint(bs.App.AppBizId, 10)
	// 获取bizType
	corpRsp, _ := bus.dao.GetCorp(ctx, bs.RelatedRecordID, bs.App.CorpId)
	bizType := "QD_AI_TEXT"
	if corpRsp != nil {
		bizType = corpRsp.GetInfosecBizType()
	}
	if len(bs.App.GetInfosecBizType()) != 0 {
		bizType = bs.App.GetInfosecBizType()
	}
	return &infosec.CheckReq{
		User:     &infosec.CheckReq_User{Uin: appBizID, AccountType: uint32(ispkg.AccountTypeOther)},
		Id:       bs.RecordID,
		Source:   "chat",
		PostTime: st.Unix(),
		Type:     uint32(ispkg.CheckTypeText),
		Content:  reply,
		BizType:  bizType,
	}
}

func singleExtraction(ctx context.Context, bs *botsession.BotSession, prompt string) (*llmm.Response, error) {
	t0 := time.Now()
	m := getAppModel(ctx, bs)

	requestID := model.RequestID(ctx, bs.Session.SessionID, bs.RecordID) // 非流式调用
	message := m.WrapMessages("", nil, prompt)
	req := m.NewLLMRequest(requestID, message)
	resp, err := bus.dao.SimpleChat(ctx, req)
	log.InfoContextf(ctx, "R|SimpleChat2 %+v, %+v, ERR: %v, %s", req, resp, err, time.Since(t0))
	return resp, err
}

func mergeLLMResp(ctx context.Context, respList []*llmm.Response) *llmm.Response {
	tags := make(map[string][]any)
	last := &llmm.Response{Finished: true, StatisticInfo: &llmm.StatisticInfo{}, Message: &llmm.Message{}}
	// 是否提取出有效的标签
	var isValid bool
	// 把 resp 数组, 写入同一个 resp 中
	for i := range respList {
		// tag 合并
		if respList[i].GetMessage() != nil && len(respList[i].GetMessage().GetContent()) > 0 {
			var m map[string][]any
			err := json.Unmarshal([]byte(respList[i].GetMessage().GetContent()), &m)
			if err != nil {
				log.ErrorContextf(ctx, "mergeLLMResp|Unmarshal %v", err)
				continue
			}
			for k, v := range m {
				if len(v) > 0 {
					isValid = true // 只要提取出一个有效的标签就认为有效
				}
				tv, ok := tags[k]
				if ok {
					tags[k] = append(tv, tags[k]...)
				} else {
					tags[k] = v
				}
			}
		}
		// 统计信息合并
		if respList[i].GetStatisticInfo() != nil {
			last.StatisticInfo.FirstTokenCost += respList[i].GetStatisticInfo().GetFirstTokenCost()
			last.StatisticInfo.TotalCost += respList[i].GetStatisticInfo().GetTotalCost()
			last.StatisticInfo.InputTokens += respList[i].GetStatisticInfo().GetInputTokens()
			last.StatisticInfo.OutputTokens += respList[i].GetStatisticInfo().GetOutputTokens()
			last.StatisticInfo.TotalTokens += respList[i].GetStatisticInfo().GetTotalTokens()
		}
	}
	content := ""
	if isValid {
		t, _ := json.Marshal(tags)
		content = string(t)
	}
	last.Message.Content = content
	return last
}
