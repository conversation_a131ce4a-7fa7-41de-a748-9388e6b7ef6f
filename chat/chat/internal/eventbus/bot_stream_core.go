package eventbus

import (
	"context"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/go-comm/pf"
	llmm "git.woa.com/dialogue-platform/proto/pb-stub/llm-manager-server"
	"git.woa.com/ivy/protobuf/trpc-go/qbot/qbot/infosec"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	"git.woa.com/ivy/qbot/qbot/chat/internal/eventbus/botsession"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model/event"
	"git.woa.com/ivy/qbot/qbot/chat/pkg"
)

// streamNew 向前端流式输出
func streamNew(ctx context.Context, cancel context.CancelFunc,
	bs *botsession.BotSession, ch chan *llmm.Response, req *llmm.Request, eventPrName string,
	replyMethod model.ReplyMethod, histories [][2]model.HisMessage, startIndex int, frontReply string,
) (last *llmm.Response, t time.Time, lastEvil *infosec.CheckRsp, isTimeout bool, isModelRejected bool) {

	// 初始化流处理组件
	streamCtx := initStreamContext(ctx, bs, req, startIndex)
	defer streamCtx.cleanup()

	// 主循环处理
	for {
		select {
		case <-streamCtx.timeout.C:
			return streamCtx.handleTimeout(cancel, bs, eventPrName)

		case <-streamCtx.ticker.C:
			if shouldStop := handleMiddleStop(ctx, cancel, bs, last, eventPrName); shouldStop {
				return last, t, lastEvil, false, false
			}

		case rsp, ok := <-ch:
			result := streamCtx.processResponse(ctx, cancel, bs, rsp, ok, req, eventPrName,
				histories, frontReply, replyMethod, last, &t, &lastEvil)
			if result.shouldReturn {
				return result.last, result.t, result.lastEvil, result.isTimeout, result.isModelRejected
			}
		}
	}
}

// StreamContext 封装流处理上下文
type StreamContext struct {
	throttleCheck     *helper.Throttle
	throttleStreaming *helper.Throttle
	throttleThought   *helper.Throttle
	ticker            *time.Ticker
	timeout           *time.Timer
	index             int
	flag              bool
	preUseToken       uint32
	statistics        *pkg.PPLStatistics
	mainModel         string
	intentionCategory string
	uin               string
	appid             string
}

// ProcessResult 处理结果
type ProcessResult struct {
	last            *llmm.Response
	t               time.Time
	lastEvil        *infosec.CheckRsp
	isTimeout       bool
	isModelRejected bool
	shouldReturn    bool
}

func initStreamContext(ctx context.Context, bs *botsession.BotSession, req *llmm.Request,
	startIndex int) *StreamContext {
	throttleCheck, throttleStreaming, throttleThought, ticker, timeout, index := initStreamVars(ctx, bs, startIndex)

	statistics := pkg.GetStatistics(ctx)
	uin := pkg.Uin(ctx)
	appid := pkg.AppID(ctx)
	mainModel := getMainModel(req)
	intentionCategory := bs.IntentCate

	if statistics != nil {
		statistics.MainModel = mainModel
		statistics.IntentionCategory = intentionCategory
	}

	bs.StreamInfo.FirstThought, bs.StreamInfo.FinalThought = false, false
	bs.StreamInfo.PreviousRspContent = ""

	return &StreamContext{
		throttleCheck:     &throttleCheck,
		throttleStreaming: &throttleStreaming,
		throttleThought:   &throttleThought,
		ticker:            ticker,
		timeout:           timeout,
		index:             index,
		statistics:        statistics,
		mainModel:         mainModel,
		intentionCategory: intentionCategory,
		uin:               uin,
		appid:             appid,
	}
}

func (sc *StreamContext) cleanup() {
	sc.ticker.Stop()
	sc.timeout.Stop()
	if sc.statistics != nil {
		reportMetrics(sc.statistics, sc.uin, sc.appid, sc.mainModel, sc.intentionCategory)
		pkg.WithStatistics(context.Background(), sc.statistics)
	}
}

func (sc *StreamContext) handleTimeout(cancel context.CancelFunc, bs *botsession.BotSession,
	eventPrName string) (*llmm.Response, time.Time, *infosec.CheckRsp, bool, bool) {
	cancel()
	log.ErrorContext(context.Background(), "ctx cancel stream llm timeout")
	bs.TokenStat.UpdateProcedure(event.NewFailedTSProcedure(context.Background(), eventPrName))
	SendTokenStat(context.Background(), bs, bs.TokenStat)
	return nil, time.Time{}, nil, true, false
}

func (sc *StreamContext) processResponse(ctx context.Context, cancel context.CancelFunc,
	bs *botsession.BotSession, rsp *llmm.Response, ok bool, req *llmm.Request,
	eventPrName string, histories [][2]model.HisMessage, frontReply string,
	replyMethod model.ReplyMethod, last *llmm.Response, t *time.Time, lastEvil **infosec.CheckRsp) ProcessResult {

	// 检查通道状态
	if !ok {
		return ProcessResult{last: last, t: *t, lastEvil: *lastEvil, shouldReturn: true}
	}

	// 处理token报告
	if needReportToken(ctx, bs, sc.preUseToken, rsp) {
		sc.preUseToken = rsp.GetStatisticInfo().GetTotalTokens()
	}

	sc.timeout.Stop()

	// 处理搜索结果（仅首次）
	if !sc.flag {
		processSearchResults(ctx, cancel, bs, rsp)
		sc.flag = true
	}

	// 更新响应时间
	if last == nil {
		*t = time.Now()
	}
	last = rsp

	reply := frontReply + rsp.GetMessage().GetContent()

	// 数学计算特殊处理
	if result := sc.handleMathCalculation(ctx, bs, req, reply, eventPrName); result.shouldReturn {
		return result
	}

	// 特殊响应处理
	if result := sc.handleSpecialResponse(ctx, rsp, last, bs, reply, eventPrName, histories, cancel, req,
		*t); result.shouldReturn {
		return result
	}

	// 拒绝检查
	if result := sc.handleRejectCheck(ctx, cancel, bs, rsp, reply, req, histories, eventPrName,
		last); result.shouldReturn {
		return result
	}

	// 内容安全检查
	if result := sc.handleEvilCheck(ctx, cancel, bs, *t, reply, rsp, req, histories, eventPrName, last,
		lastEvil); result.shouldReturn {
		return result
	}

	// 流式输出处理
	sc.handleStreamOutput(ctx, bs, reply, rsp, *t, replyMethod, cancel)

	return ProcessResult{shouldReturn: false}
}

func (sc *StreamContext) handleMathCalculation(ctx context.Context, bs *botsession.BotSession,
	req *llmm.Request, reply, eventPrName string) ProcessResult {

	if eventPrName != event.ProcedurePOTMath {
		return ProcessResult{shouldReturn: false}
	}

	if ct, ok := ExistsCalc(reply, sc.index); ok {
		_, calcLast, calcLastEvil, isModelRejected, _ := enterIntoCalcPOT(ctx, bs, req, ct)
		return ProcessResult{
			last:            calcLast,
			lastEvil:        calcLastEvil,
			isModelRejected: isModelRejected,
			shouldReturn:    true,
		}
	}

	return ProcessResult{shouldReturn: false}
}

func (sc *StreamContext) handleSpecialResponse(ctx context.Context, rsp, last *llmm.Response,
	bs *botsession.BotSession, reply, eventPrName string, histories [][2]model.HisMessage,
	cancel context.CancelFunc, req *llmm.Request, t time.Time) ProcessResult {

	lastEvil, isEvil, needContinue := handleSpecialResponse(ctx, rsp, last, bs, reply, eventPrName,
		histories, cancel, req, *sc.throttleCheck, *sc.throttleThought, t, sc.statistics,
		sc.mainModel, sc.intentionCategory, sc.uin)

	if isEvil {
		last.Finished = true
		last.Message.Content = config.App().Bot.EvilReply
		return ProcessResult{
			last:         last,
			t:            t,
			lastEvil:     lastEvil,
			shouldReturn: true,
		}
	}

	if needContinue {
		bs.StreamInfo.PreviousRspContent = rsp.GetMessage().GetContent()
		return ProcessResult{shouldReturn: false}
	}

	return ProcessResult{shouldReturn: false}
}

func (sc *StreamContext) handleRejectCheck(ctx context.Context, cancel context.CancelFunc,
	bs *botsession.BotSession, rsp *llmm.Response, reply string, req *llmm.Request,
	histories [][2]model.HisMessage, eventPrName string, last *llmm.Response) ProcessResult {

	if !needJudgeReject(ctx, bs, rsp) {
		return ProcessResult{shouldReturn: false}
	}

	if isHitRejectAnswer(reply) {
		pf.AddNode(ctx, pf.PipelineNode{Key: "stream.HitReject"})
		log.InfoContextf(ctx, "ctx cancel,reply: %s isHitRejectAnswer", rsp.GetMessage().GetContent())
		cancel()
		sendFinishTokenStat(ctx, bs, req, histories, &llmm.Response{StatisticInfo: &llmm.StatisticInfo{}}, eventPrName)
		last.Finished = true
		return ProcessResult{
			last:            last,
			isModelRejected: true,
			shouldReturn:    true,
		}
	}

	if !rsp.GetFinished() {
		bs.StreamInfo.PreviousRspContent = rsp.GetMessage().GetContent()
		return ProcessResult{shouldReturn: false}
	}

	return ProcessResult{shouldReturn: false}
}

func (sc *StreamContext) handleEvilCheck(ctx context.Context, cancel context.CancelFunc,
	bs *botsession.BotSession, t time.Time, reply string, rsp *llmm.Response,
	req *llmm.Request, histories [][2]model.HisMessage, eventPrName string,
	last *llmm.Response, lastEvil **infosec.CheckRsp) ProcessResult {

	if !bs.NeedCheck || !(*sc.throttleCheck).Hit(len([]rune(reply)), rsp.GetFinished()) {
		return ProcessResult{shouldReturn: false}
	}

	_, _, checkLastEvil, isEvil := handleEvilCheck(ctx, cancel, bs, t, reply, rsp, req,
		histories, eventPrName, last)
	*lastEvil = checkLastEvil

	if isEvil {
		return ProcessResult{
			last:         last,
			t:            t,
			lastEvil:     *lastEvil,
			shouldReturn: true,
		}
	}

	return ProcessResult{shouldReturn: false}
}

func (sc *StreamContext) handleStreamOutput(ctx context.Context, bs *botsession.BotSession,
	reply string, rsp *llmm.Response, t time.Time, replyMethod model.ReplyMethod, cancel context.CancelFunc) {

	throttleFinish(ctx, bs, len([]rune(reply)), *sc.throttleStreaming, rsp, t, replyMethod, sc.index, cancel)
	bs.StreamInfo.PreviousRspContent = rsp.GetMessage().GetContent()
	sc.index++
}

// getMainModel 辅助函数
func getMainModel(req *llmm.Request) string {
	if req != nil {
		return req.ModelName
	}
	return ""
}

func handleMiddleStop(ctx context.Context, cancel context.CancelFunc, bs *botsession.BotSession,
	last *llmm.Response, eventPrName string) bool {
	return middleStop(ctx, cancel, bs, last, eventPrName)
}
