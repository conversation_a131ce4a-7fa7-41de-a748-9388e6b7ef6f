package eventbus

import (
	"context"
	"strings"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/common/v3/errors"
	"git.woa.com/dialogue-platform/go-comm/encode"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_knowledge_config_server"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/knowledge"
	llmm "git.woa.com/dialogue-platform/proto/pb-stub/llm-manager-server"
	pb "git.woa.com/ivy/protobuf/trpc-go/qbot/qbot/chat"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/dao"
	"git.woa.com/ivy/qbot/qbot/chat/internal/eventbus/botsession"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
	"git.woa.com/ivy/qbot/qbot/chat/internal/utils"
	"git.woa.com/ivy/qbot/qbot/chat/pkg"
	"github.com/google/uuid"
)

// GetAnswerFromDocsImpl 获取文档答案
func GetAnswerFromDocsImpl(ctx context.Context, request *pb.GetAnswerFromDocsRequest) (
	chan *pb.GetAnswerFromDocsReply, error) {
	chReply := make(chan *pb.GetAnswerFromDocsReply)

	// step 1: 查询APP
	scene := dao.AppTestScene
	if request.FilterKey == pb.Filter_FILTER_PRODUCTION {
		scene = dao.AppReleaseScene
	}
	app, err := bus.dao.GetAppByBizID(ctx, scene, request.RobotID)
	if err != nil || app == nil {
		log.ErrorContextf(ctx, "GetAppByBizID failed, error: %v", err)
		return nil, pkg.ErrRobotNotExist
	}

	// step 2: 检索
	docs, placeholders, err := SearchDocs(ctx, request, app)
	if err != nil {
		log.ErrorContextf(ctx, "SearchDocs failed, error: %v", err)
		return nil, err
	}
	// step 3: 生成Prompt
	promptCtx := &botsession.PromptCtx{}
	promptCtx.Docs = docs
	promptCtx.Question = request.Question
	m := app.GetModel(ctx, model.AppTypeKnowledgeQA, model.ModelTypeMessage)
	m.PromptLimit = bus.dao.GetModelPromptLimit(ctx, m.GetModelName()) - len([]rune(app.RoleDescription()))
	prompt, err := bus.dao.TextTruncate(ctx, m, promptCtx) // 生成Prompt
	log.InfoContextf(ctx, "RenderPrompt, prompt: %s", prompt)
	if err != nil {
		return nil, err
	}
	//  step 3: 构造请求
	requestID := model.RequestID(ctx, request.SessionID, encode.GenerateUUID()) // 非流式调用
	message := m.WrapMessages("", nil, prompt)
	reqLLM := m.NewLLMRequest(requestID, message)
	// step 4: 请求LLM
	go bus.dao.UpdateWorkflowPrompt(ctx, request.RobotID,
		request.SessionID, helper.Object2StringEscapeHTML(reqLLM)) // 更新工作流prompt
	GetAnswerFromLLM(ctx, reqLLM, chReply, request, docs, placeholders)

	return chReply, nil
}

// SearchDocs 检索知识库
func SearchDocs(ctx context.Context, req *pb.GetAnswerFromDocsRequest,
	app *model.App) ([]*bot_knowledge_config_server.MatchReferReq_Doc, map[string]string, error) {
	docs := make([]*bot_knowledge_config_server.MatchReferReq_Doc, 0)
	log.InfoContextf(ctx, "SearchDocs, req: %s", helper.Object2String(req))
	bs := &botsession.BotSession{}
	bs.Knowledge = make([]*knowledge.SearchKnowledgeRsp_SearchRsp_Doc, 0) // 存储结果
	reqNew := &knowledge.SearchKnowledgeReq_SearchReq{
		BotBizId:       app.GetAppBizId(),
		Question:       req.GetQuestion(),
		Labels:         ToVectorLabel(req.Labels),
		UsePlaceholder: app.CanUsePlaceholder(),
		ImageUrls:      bs.Images,
		ModelName:      app.GetModelName(),
	}
	scene := knowledge.SceneType_PROD
	if req.FilterKey == pb.Filter_FILTER_SANDBOX {
		scene = knowledge.SceneType_TEST
	}

	rsp, _ := bus.dao.SearchKnowledge(ctx, reqNew, scene)
	bs.Knowledge = rsp
	for _, doc := range rsp {
		docs = append(docs, &bot_knowledge_config_server.MatchReferReq_Doc{
			DocId:     doc.DocId,
			DocType:   doc.DocType,
			RelatedId: doc.RelatedId,
			Question:  doc.Question,
			Answer:    doc.Answer,
			OrgData:   doc.OrgData,
			IsBigData: doc.IsBigData,
			SheetInfo: doc.SheetInfo,
		})
	}
	placeholders := utils.GetPlaceHolders(bs.Knowledge)
	log.InfoContextf(ctx, "SearchDocs, rsp: %s, docs: %s", req, docs)
	return docs, placeholders, nil
}

// ToVectorLabel 转换标签
func ToVectorLabel(labels []*pb.Label) []*knowledge.VectorLabel {
	var res []*knowledge.VectorLabel
	for _, l := range labels {
		res = append(res, &knowledge.VectorLabel{Name: l.Name, Values: l.Values})
	}
	return res
}

// ToMatchReferPreviewDoc 转换文档
func ToMatchReferPreviewDoc(
	docs []*bot_knowledge_config_server.SearchPreviewRsp_Doc) []*bot_knowledge_config_server.MatchReferReq_Doc {
	var res []*bot_knowledge_config_server.MatchReferReq_Doc
	for _, doc := range docs {
		res = append(res, &bot_knowledge_config_server.MatchReferReq_Doc{
			DocId:     doc.DocId,
			DocType:   doc.DocType,
			RelatedId: doc.RelatedId,
			Question:  doc.Question,
			Answer:    doc.Answer,
			OrgData:   doc.OrgData,
			IsBigData: doc.IsBigData,
			SheetInfo: doc.SheetInfo,
		})
	}
	return res
}

// ToMatchReferReleaseDoc 转换文档
func ToMatchReferReleaseDoc(
	docs []*bot_knowledge_config_server.SearchRsp_Doc) []*bot_knowledge_config_server.MatchReferReq_Doc {
	var res []*bot_knowledge_config_server.MatchReferReq_Doc
	for _, doc := range docs {
		res = append(res, &bot_knowledge_config_server.MatchReferReq_Doc{
			DocId:     doc.DocId,
			DocType:   doc.DocType,
			RelatedId: doc.RelatedId,
			Question:  doc.Question,
			Answer:    doc.Answer,
			OrgData:   doc.OrgData,
			IsBigData: doc.IsBigData,
			SheetInfo: doc.SheetInfo,
		})
	}
	return res
}

// GetAnswerFromLLM 请求LLM
func GetAnswerFromLLM(ctx context.Context, reqLLM *llmm.Request,
	chReply chan *pb.GetAnswerFromDocsReply, request *pb.GetAnswerFromDocsRequest,
	docs []*bot_knowledge_config_server.MatchReferReq_Doc, placeholders map[string]string) {
	ch := make(chan *llmm.Response, 100)
	llmCtx, cancel := context.WithCancel(ctx)
	go func() {
		defer errors.PanicHandler()
		err := bus.dao.Chat(llmCtx, reqLLM, ch, time.Now(), nil) // todo: 耗时统计
		if err != nil {
			log.ErrorContextf(llmCtx, "Chat error: %v", err)
		}
		log.InfoContextf(llmCtx, "Chat finish")
	}()
	var reply *pb.GetAnswerFromDocsReply
	go func() {
		defer errors.PanicHandler()
		defer func() {
			chReply <- reply
			close(chReply)
		}()
		for {
			reply = &pb.GetAnswerFromDocsReply{}
			select {
			case <-ctx.Done():
				log.InfoContextf(ctx, "GetAnswerFromDocsImpl, ctx done")
				reply.Code = -1
				reply.ErrMsg = "canceled"
				reply.Finished = true
				return
			case llmRsp, ok := <-ch:
				if !ok || llmRsp == nil {
					log.DebugContextf(ctx, "GetAnswerFromDocsImpl, llmRsp is nil")
					reply.Code = -1
					reply.ErrMsg = "llmRsp error"
					reply.Finished = true
					cancel()
					return
				}
				reply.Code = llmRsp.Code
				reply.ErrMsg = llmRsp.ErrMsg
				reply.RequestID = llmRsp.GetRequestId()
				llmRsp.Message.Content = replacePlaceholders(llmRsp.Message.Content, placeholders)

				content := helper.RemoveReference(request.RobotID, llmRsp.Message.Content)
				content = strings.ReplaceAll(content, "\n引用:", "")
				content = strings.ReplaceAll(content, "\n引用：", "")
				content, _, _ = helper.MatchSearchResults(ctx, content)
				reply.Message = &pb.LLMMessage{
					Role:    pb.LLMRole(llmRsp.Message.Role),
					Content: content,
				}
				reply.Finished = llmRsp.Finished
				if llmRsp.StatisticInfo != nil {
					reply.StatisticInfo = &pb.StatisticInfo{
						FirstTokenCost: llmRsp.StatisticInfo.FirstTokenCost,
						TotalCost:      llmRsp.StatisticInfo.TotalCost,
						InputTokens:    llmRsp.StatisticInfo.InputTokens,
						OutputTokens:   llmRsp.StatisticInfo.OutputTokens,
						TotalTokens:    llmRsp.StatisticInfo.TotalTokens,
					}
				} else {
					log.WarnContextf(ctx, "llmRsp.StatisticInfo is empty, llmRsp: %v", llmRsp)
				}
				if reply.Finished {
					log.DebugContextf(ctx, "handleLLMOutput, finished")
					reply.References = processDMReference(ctx, reply.Message.GetContent(), docs, request)
					log.InfoContextf(ctx, "MatchRefer finish, reply: %s", helper.Object2String(reply))
					return
				}
				chReply <- reply // 避免2次final
			}
		}
	}()
}

// ToReplyReference TODO
func ToReplyReference(refers []*bot_knowledge_config_server.MatchReferRsp_Refer) []*pb.Reference {
	res := make([]*pb.Reference, 0)
	for _, refer := range refers {
		res = append(res, &pb.Reference{
			DocID:    refer.DocId,
			ID:       refer.ReferId,
			Name:     refer.Name,
			Type:     refer.DocType,
			Url:      refer.Url,
			DocBizID: refer.DocBizId,
			DocName:  refer.DocName,
			QABizID:  refer.QaBizId,
		})
	}
	return res
}

func processDMReference(ctx context.Context, reply string,
	docs []*bot_knowledge_config_server.MatchReferReq_Doc,
	request *pb.GetAnswerFromDocsRequest) (refer []*pb.Reference) {
	if len(docs) < 1 {
		log.InfoContextf(ctx, "SearchDocs finish, docs is empty")
		return refer
	}

	referIndex := helper.GetAllSubMatch(request.RobotID, reply)
	mDocs := make([]*bot_knowledge_config_server.MatchReferReq_Doc, 0)
	disableMatch := false
	if len(referIndex) > 0 {
		disableMatch = true
		for _, index := range referIndex {
			if index > len(docs) {
				log.WarnContextf(ctx, "index out of range, index: %d, knowledge: %v", index, docs)
				continue
			}
			doc := docs[index-1] // index从1开始
			mDocs = append(mDocs, &bot_knowledge_config_server.MatchReferReq_Doc{
				DocId:     doc.GetDocId(),
				DocType:   doc.GetDocType(),
				RelatedId: doc.GetRelatedId(),
				Question:  doc.GetQuestion(),
				Answer:    doc.GetAnswer(),
				OrgData:   doc.GetOrgData(),
				IsBigData: doc.GetIsBigData(),
				SheetInfo: doc.GetSheetInfo(),
			})
		}
	} else {
		mDocs = docs
	}
	// MatchRefer 匹配参考来源
	refers, err := bus.dao.MatchRefer(ctx, &bot_knowledge_config_server.MatchReferReq{
		BotBizId:              request.RobotID,
		Docs:                  mDocs,
		Answer:                reply,
		IsRelease:             request.FilterKey == pb.Filter_FILTER_PRODUCTION,
		MsgId:                 uuid.NewString(),
		Question:              request.Question,
		IgnoreConfidenceScore: disableMatch,
	})
	if err != nil {
		log.ErrorContextf(ctx, "MatchRefer failed, error: %v", err)
	} else {
		refer = ToReplyReference(refers)
	}
	return refer
}
