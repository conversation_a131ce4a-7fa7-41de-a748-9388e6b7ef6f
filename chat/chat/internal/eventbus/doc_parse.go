package eventbus

import (
	"context"
	"errors"
	"strconv"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/common/v3/sync/errgroupx"
	"git.woa.com/dialogue-platform/go-comm/clues"
	"git.woa.com/dialogue-platform/go-comm/encode"
	"git.woa.com/dialogue-platform/go-comm/pf"
	parse "git.woa.com/dialogue-platform/lke_proto/pb-protocol/knowledge"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	"git.woa.com/ivy/qbot/qbot/chat/internal/dao"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model/event"
	"git.woa.com/ivy/qbot/qbot/chat/pkg"
	jsoniter "github.com/json-iterator/go"
)

func init() {
	handlers[event.EventSseDocParse] = &DocParseEventHandler{}
}

// DocParseEventHandler 文档解析事件处理器
type DocParseEventHandler struct {
}

// DocParseSummaryDosage 文档解析摘要dosgae
type DocParseSummaryDosage struct {
	CorpID uint64
	Dosage dao.TokenDosage
}

// NewRequest 创建事件请求
func (d *DocParseEventHandler) NewRequest(ctx context.Context, conn *model.Conn, bs []byte) (any, error) {
	log.InfoContextf(ctx, "Invoke DocParseEventHandler NewRequest cli: %s, req: %s",
		helper.Object2String(conn), string(bs))
	req := event.SseDocParseEvent{}
	if err := jsoniter.Unmarshal(bs, &req); err != nil {
		log.ErrorContextf(ctx, "Unmarshal event req error: %+v, data: %s", err, string(bs))
		return nil, pkg.ErrBadRequest
	}
	if !req.IsValid() {
		return nil, pkg.ErrBadRequest
	}
	// 框架未携带, 则使用业务数据
	pkg.WithSessionID(ctx, req.SessionID)
	pkg.WithRequestID(ctx, req.RequestID)
	pkg.WithTraceID(ctx, req.RequestID)
	return req, nil
}

// Process 处理事件请求
func (d *DocParseEventHandler) Process(ctx context.Context, cli *model.Conn, req any) (err error) {
	if cli.IsSSE && config.App().SSE.EnableDeferClientClose {
		log.DebugContextf(ctx, "clientID: %s SSE.DeferClientCloseTime: %+v",
			cli.ClientID, config.App().SSE.DeferClientCloseTime)
		time.Sleep(config.App().SSE.DeferClientCloseTime)
	}
	ctx = pf.NewPipelineFlowContext(ctx)
	defer func(ctx *context.Context) { pf.Persist(*ctx) }(&ctx)
	defer func(ctx *context.Context) { clues.Flush(*ctx) }(&ctx)
	clues.AddTrackData(ctx, "Conn", cli)
	clues.AddTrackData(ctx, "SseDocParseEvent", req)
	log.InfoContextf(ctx, "Invoke DocParseEventHandler Process clie: %s, req: %s",
		helper.Object2String(cli), helper.Object2String(req))
	ev := req.(event.SseDocParseEvent)
	app, err := bus.dao.GetAppByBizID(ctx, helper.When(len(ev.ShareCode) > 0, dao.AppReleaseScene, dao.AppTestScene),
		cli.APIBotBizID)
	if err != nil {
		return err
	}
	// 写DB
	docMsg := model.MsgDocRecord{
		BotBizID:  cli.APIBotBizID,
		SessionID: ev.SessionID,
		RecordID:  encode.GenerateSessionID(),
		FileURL:   ev.CosURL,
		CosID:     bus.dao.GenerateSeqID(),
		DocName:   ev.FileName,
		ModelName: app.GetModelName(),
		Summary:   "",
		IsDeleted: false,
	}
	_, err = bus.dao.CreateMsgDocRecord(ctx, docMsg)
	if err != nil {
		return err
	}
	return d.doProcess(ctx, cli, docMsg, ev, app)
}

// doProcess 核心流程、需要并发控制
func (d *DocParseEventHandler) doProcess(ctx context.Context, coon *model.Conn, docMsg model.MsgDocRecord,
	ev event.SseDocParseEvent, app *model.App) (err error) {
	cb := func() error {
		log.InfoContextf(ctx, "docParse sse exec begin.")
		err0 := d.docParseCore(ctx, coon, docMsg, ev, app) // 核心流程在这里
		return err0
	}

	err = limit(ctx, app, docMsg.RecordID, false, cb)
	if errors.Is(err, pkg.ErrConcurrenceExceeded) {
		// 上报超并发
		_ = bus.dao.ReportOverConcurrencyDosage(ctx, app, fillOverConcurrencyDosage(
			app.GetAppBizId(), app.GetMainModelName(), ev.CosURL))
	}
	return err
}

// docParseCore 核心流程
func (d *DocParseEventHandler) docParseCore(ctx context.Context, conn *model.Conn, docMsg model.MsgDocRecord,
	ev event.SseDocParseEvent, app *model.App) (err error) {
	parseReq := &parse.StreamSaveDocReq{
		ReqType: parse.StreamSaveDocReq_TASK_PARSE,
		TaskReq: &parse.TaskReq{
			FileName:  ev.FileName,
			CosBucket: ev.CosBucket,
			CosUrl:    ev.CosURL,
			CosUrlId:  docMsg.CosID,
			FileType:  ev.FileType,
			BotBizId:  app.GetAppBizId(),
			ETag:      ev.ETag,
			CosHash:   ev.CosHash,
			Size:      helper.GetUint64FromString(ev.Size),
			SessionId: ev.SessionID,
			ModelName: app.GetModelName(),
		},
	}
	summaryDosage := fillDocParseSummaryDosage(app, docMsg.RecordID)
	cfg := config.App().Bot
	ch := make(chan *parse.StreamSaveDocRsp, cfg.ResponseChannelSize)
	g, gCtx := errgroupx.WithContext(ctx)
	parseCtc, cancel := context.WithCancel(ctx)
	signal := make(chan int, 10)
	g.Go(func() error {
		if err := bus.dao.StreamSaveDoc(parseCtc, parseReq, ch, signal); !errors.Is(err, context.Canceled) {
			return err
		}
		return nil
	})
	g.Go(func() error {
		isTimeout := d.streamParseReply(gCtx, cancel, conn, ch, signal, summaryDosage)
		return helper.When(isTimeout, pkg.ErrLLMTimeout, nil)
	})
	if err = g.Wait(); err != nil {
		return err
	}

	return nil
}

// streamParseReply 流式处理文档解析回复
func (d *DocParseEventHandler) streamParseReply(ctx context.Context, cancel context.CancelFunc,
	cli *model.Conn, ch chan *parse.StreamSaveDocRsp, signal chan int, summaryDosage DocParseSummaryDosage) (
	isTimeout bool) {
	cfg := config.App().Bot
	timeout := time.NewTimer(time.Duration(cfg.Timeout) * time.Second)
	ticker := time.NewTicker(time.Duration(cfg.StopGeneration.CheckInterval) * time.Millisecond)
	clues.AddT(ctx, "stream().vars", map[string]any{"StopGeneration": cfg.StopGeneration, "Timeout": cfg.Timeout})
	defer timeout.Stop()
	for {
		select {
		case <-timeout.C:
			signal <- 1
			_ = bus.dao.DoEmitWsClient(ctx, cli.ClientID, initParsingFailedEvent(cli.SessionID), cancel)
			log.ErrorContext(ctx, "doc parsing timeout")
			return true
		case <-ticker.C:
			if yes := bus.dao.IsSseClientValid(ctx, cli.ClientID); !yes {
				log.WarnContextf(ctx, "clientID: %s is invalid", cli.ClientID)
				signal <- 1
				return false
			}
		case rsp, ok := <-ch:
			timeout.Stop()
			if !ok {
				return false
			}
			if rsp.GetIsFinal() && rsp.GetTaskRsp().GetStatus() == parse.TaskRsp_SUCCESS {
				// 解析完 且成功 要落库
				_ = bus.dao.UpdateDocInfo(ctx, rsp.GetTaskRsp().GetDocId(),
					cli.APIBotBizID, rsp.GetTaskRsp().GetCosUrlId(), rsp.GetTaskRsp().GetSummary())
				//  上报计费
				_ = d.reportDocParseSummaryToken(ctx, summaryDosage, rsp.GetTaskRsp().GetStatisticInfo())
			}
			if rsp.GetIsFinal() {
				clues.AddTrackData(ctx, "knowledge.docParse", rsp)
			}
			if yes := bus.dao.IsSseClientValid(ctx, cli.ClientID); !yes {
				log.WarnContextf(ctx, "clientID: %s is invalid", cli.ClientID)
				signal <- 1
				return false
			}
			_ = bus.dao.DoEmitWsClient(ctx, cli.ClientID, &event.ParsingEvent{
				Status:       rsp.GetTaskRsp().GetStatus().String(),
				Process:      rsp.GetTaskRsp().GetProgress().GetProgress(),
				DocID:        strconv.FormatUint(rsp.GetTaskRsp().GetDocId(), 10),
				IsFinal:      rsp.GetIsFinal(),
				Timestamp:    time.Now().Unix(),
				SessionID:    cli.SessionID,
				ErrorMessage: rsp.GetTaskRsp().GetErrMsg(),
			}, cancel)
		}
	}
}

func initParsingFailedEvent(sessionID string) *event.ParsingEvent {
	return &event.ParsingEvent{
		Status:       "FAILED",
		Process:      0,
		DocID:        "0",
		IsFinal:      true,
		Timestamp:    time.Now().Unix(),
		SessionID:    sessionID,
		ErrorMessage: "解析超时",
	}
}

// fillDocParseSummaryDosage TODO
func fillDocParseSummaryDosage(app *model.App, recordID string) DocParseSummaryDosage {
	return DocParseSummaryDosage{
		CorpID: app.GetCorpId(),
		Dosage: dao.TokenDosage{
			AppID:     app.GetAppBizId(),
			AppType:   app.GetAppType(),
			ModelName: app.GetModelName(),
			RecordID:  recordID,
			StartTime: time.Now(),
		},
	}
}

func (d *DocParseEventHandler) reportDocParseSummaryToken(ctx context.Context,
	summaryDosage DocParseSummaryDosage, statisticInfo *parse.StatisticInfo) error {
	summaryDosage.Dosage.EndTime = time.Now()
	if statisticInfo.GetInputTokens() > 0 {
		summaryDosage.Dosage.InputDosages = []int{int(statisticInfo.GetInputTokens())}
	}
	if statisticInfo.GetOutputTokens() > 0 {
		summaryDosage.Dosage.OutputDosages = []int{int(statisticInfo.GetOutputTokens())}
	}
	if len(summaryDosage.Dosage.InputDosages) > 0 || len(summaryDosage.Dosage.OutputDosages) > 0 {
		return bus.dao.ReportTokenDosage2(ctx, summaryDosage.CorpID, summaryDosage.Dosage, "")
	}
	return nil
}
