package eventbus

import (
	"context"
	"errors"
	"sync"

	"git.code.oa.com/trpc-go/trpc-go/log"
	errors2 "git.woa.com/dialogue-platform/common/v3/errors"
	"git.woa.com/dialogue-platform/go-comm/encode"
	"git.woa.com/dialogue-platform/go-comm/pf"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	"git.woa.com/ivy/qbot/qbot/chat/internal/eventbus/botsession"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
	"git.woa.com/ivy/qbot/qbot/chat/pkg"
)

// doSingleWorkflowProcess 处理单工作流
func (d *DialogEventHandler) doSingleWorkflowProcess(ctx context.Context, bs *botsession.BotSession) (err error) {
	var isEvil bool
	var wg sync.WaitGroup
	wg.Add(1)
	go func() { // 安全检测
		defer errors2.PanicHandler()
		defer pf.AppendSpanElapsed(ctx, config.App().StageTaskName.CheckEvil)
		defer wg.Done()
		// 安全审核阶段耗时统计
		pf.StartElapsedAsMetrics(ctx, config.App().StageTaskName.CheckEvil)
		isEvil = d.checkEvil(ctx, bs)
		d.replyFromSelf(ctx, bs) // 首包回复
	}()
	wg.Add(1)
	go func() { // 改写 + 同义词替换
		defer errors2.PanicHandler()
		defer pf.AppendSpanElapsed(ctx, config.App().StageTaskName.QueryRewrite)
		defer wg.Done()
		// 改写 + 同义词替换阶段耗时统计
		pf.StartElapsedAsMetrics(ctx, config.App().StageTaskName.QueryRewrite)
		if config.IsSkipQueryRewrite(bs.App.GetAppBizId()) {
			log.InfoContextf(ctx, "queryRewriteAndNER skip appID: %v", bs.App.GetAppBizId())
			bs.PromptCtx.Question = bs.OriginContent
		} else {
			bs.PromptCtx.Question = queryRewriteAndNER(ctx, bs)
		}
	}()
	wg.Wait()
	if isEvil {
		return err
	}

	bs.RecordID = encode.GenerateSessionID() // 在这里重置RecordID
	ppl := func() error {
		log.InfoContextf(ctx, "botsession is:%s", bs.String())
		if err = initTokenStat(ctx, bs); err != nil { // 初始化token统计
			return err
		}
		candidateIntent := model.CandidateIntent{WorkflowID: bs.WorkflowID}
		err0 := bus.workflow.WorkflowReply(ctx, bs, candidateIntent, len(bs.WorkflowID) != 0)
		// 端到端尾包耗时统计
		statistics := pkg.GetStatistics(ctx)
		if statistics != nil {
			pf.AppendFullSpanElapsed(ctx, config.App().StageTaskName.DialogPPL, statistics.MainModel,
				statistics.IntentionCategory, pkg.Uin(ctx), -1)
		}

		if bs.DebugMessage != nil {
			log.InfoContextf(ctx, "%s botsession is debug request, dosn't need to report", bs.SessionID)
			return err0
		}
		concurrencyDosageReport(ctx, bs.TokenStat, bs.App, bus.dao)
		dosageMergeReport(ctx, bs.TokenStat, bs.App, bus.dao)

		return err0
	}

	err = limit(ctx, bs.App, bs.RelatedRecordID, false, ppl)
	if errors.Is(err, pkg.ErrConcurrenceExceeded) {
		// 上报超并发
		_ = bus.dao.ReportOverConcurrencyDosage(ctx, bs.App, fillOverConcurrencyDosage(
			bs.App.GetAppBizId(), bs.App.GetMainModelName(), bs.OriginContent))

	}

	return err
}
