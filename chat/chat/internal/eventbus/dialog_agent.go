package eventbus

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/go-comm/encode"
	"git.woa.com/ivy/qbot/qbot/chat/internal/agent"
	"git.woa.com/ivy/qbot/qbot/chat/internal/eventbus/botsession"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
	"git.woa.com/ivy/qbot/qbot/chat/pkg"
)

// doAgentProcess 处理Agent流程
func (d *DialogEventHandler) doAgentProcess(ctx context.Context, bs *botsession.BotSession) (err error) {
	// 如果Query中包含图片
	// if helper.IsQueryContainsImage(bs.OriginContent) {
	//	return pkg.ErrAgentImageUnsupported
	// }
	// 如果输入包含文件
	// if len(bs.FileInfos) > 0 {
	//	return pkg.ErrAgentFileUnsupported
	// }
	// 安全审核
	isEvil := d.checkEvil(ctx, bs)
	d.replyFromSelf(ctx, bs) // 首包回复
	if isEvil {
		return nil
	}
	bs.RecordID = encode.GenerateSessionID() // 在这里重置RecordID
	ppl := func() error {
		log.InfoContextf(ctx, "botsession is:%s", bs.String())
		if err = initTokenStat(ctx, bs); err != nil { // 初始化token统计
			return err
		}
		a := agent.New()
		bs.OriginContent = jointQueryFileInfos(bs.OriginContent, bs.FileInfos)
		err0 := a.AgentReply(ctx, bs) // 核心流程在这里
		return err0
	}

	err = limitAgent(ctx, bs.App, bs.RelatedRecordID, ppl)

	if errors.Is(err, pkg.ErrConcurrenceExceeded) {
		// 上报超并发
		_ = bus.dao.ReportOverConcurrencyDosage(ctx, bs.App, fillOverConcurrencyDosage(
			bs.App.GetAppBizId(), bs.App.GetMainModelName(), bs.OriginContent))

	}

	return err
}

// jointQueryFileInfos 拼接 query 与 fileInfos 中的文件 URL
// 格式： query 文档URL1:URL, 文档URL2:URL, ...
func jointQueryFileInfos(query string, fileInfos []*model.FileInfo) string {
	if len(fileInfos) == 0 {
		return query
	}

	var builder strings.Builder
	builder.WriteString(query)

	for i, fileInfo := range fileInfos {
		if i == 0 {
			builder.WriteString(" ") // 第一个文件前添加空格
		} else {
			builder.WriteString(", ") // 后续文件以逗号分隔
		}
		builder.WriteString(fmt.Sprintf("文档URL%d:%s", i+1, fileInfo.FileURL))
	}

	return builder.String()
}
