package eventbus

import (
	"context"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/common/v3/errors"
	"git.woa.com/dialogue-platform/go-comm/encode"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_knowledge_config_server"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/knowledge"
	llmm "git.woa.com/dialogue-platform/proto/pb-stub/llm-manager-server"
	pb "git.woa.com/ivy/protobuf/trpc-go/qbot/qbot/chat"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/dao"
	"git.woa.com/ivy/qbot/qbot/chat/internal/eventbus/botsession"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
	"git.woa.com/ivy/qbot/qbot/chat/internal/utils"
	"git.woa.com/ivy/qbot/qbot/chat/pkg"
	"github.com/google/uuid"
)

// GetAnswerFromBatchKnowledgeImpl 获取知识库答案(检索多个知识库)
func GetAnswerFromBatchKnowledgeImpl(ctx context.Context, request *pb.GetAnswerFromBatchKnowledgeRequest,
	stopSignal chan struct{}) (chan *pb.GetAnswerFromBatchKnowledgeReply, error) {
	log.DebugContextf(ctx, "GetAnswerFromBatchKnowledgeImpl, req: %s", helper.Object2String(request))
	chReply := make(chan *pb.GetAnswerFromBatchKnowledgeReply, 10)

	// step 1: 查询APP
	scene := dao.AppTestScene
	if request.FilterKey == pb.Filter_FILTER_PRODUCTION {
		scene = dao.AppReleaseScene
	}
	app, err := bus.dao.GetAppByBizID(ctx, scene, request.GetAppBizId())
	if err != nil || app == nil {
		log.ErrorContextf(ctx, "GetAppByBizID failed, error: %v", err)
		return nil, pkg.ErrRobotNotExist
	}

	// step 2: 检索
	docs, placeholders, err := SearchBatchKnowledge(ctx, request, app)
	if err != nil {
		log.ErrorContextf(ctx, "SearchDocs failed, error: %v", err)
		return nil, err
	}
	if len(docs) == 0 {
		reply := &pb.GetAnswerFromBatchKnowledgeReply{
			Message: &pb.LLMMessage{
				Role:    pb.LLMRole_NONE,
				Content: "知识库中未检索到相关结果",
			},
			Finished: true,
		}
		chReply <- reply
		defer close(chReply)
		return chReply, nil
	}
	// step 3: 生成Prompt
	promptCtx := &botsession.PromptCtx{}
	promptCtx.Docs = docs
	promptCtx.Question = request.GetQuestion()
	m := app.GetModel(ctx, model.AppTypeKnowledgeQA, model.ModelTypeMessage)
	m.ModelName = request.GetModelName()
	m.PromptLimit = bus.dao.GetModelPromptLimit(ctx, m.GetModelName()) - len([]rune(app.RoleDescription()))
	prompt, err := bus.dao.TextTruncate(ctx, m, promptCtx) // 生成Prompt
	log.InfoContextf(ctx, "RenderPrompt, prompt: %s", prompt)
	if err != nil {
		return nil, err
	}
	//  step 3: 构造请求
	requestID := model.RequestID(ctx, request.GetSessionId(), encode.GenerateUUID()) // 非流式调用
	message := m.WrapMessages("", nil, prompt)
	reqLLM := m.NewLLMRequest(requestID, message)
	// step 4: 请求LLM
	go bus.dao.UpdateWorkflowPrompt(ctx, request.GetAppBizId(),
		request.GetSessionId(), helper.Object2StringEscapeHTML(reqLLM)) // 更新工作流prompt
	log.InfoContextf(ctx, "GetAnswerFromBatchKnowledgeImpl llmreq: %v", helper.Object2StringEscapeHTML(reqLLM))
	GetAnswerFromBatchKnowledgeLLM(ctx, reqLLM, chReply, request, docs, placeholders, stopSignal)
	return chReply, nil
}

// SearchBatchKnowledge 检索多个知识库
func SearchBatchKnowledge(ctx context.Context, req *pb.GetAnswerFromBatchKnowledgeRequest, app *model.App) (
	[]*bot_knowledge_config_server.MatchReferReq_Doc, map[string]string, error) {
	docs := make([]*bot_knowledge_config_server.MatchReferReq_Doc, 0)
	log.DebugContextf(ctx, "SearchBatchKnowledge, req: %s", helper.Object2String(req))
	bs := &botsession.BotSession{}
	searchReq := getSearchBatchKnowledgeReq(app, bs, req)
	rsp, _ := bus.dao.SearchBatchKnowledge(ctx, searchReq)
	bs.Knowledge = rsp

	for _, doc := range rsp {
		similarQuestionParams := &bot_knowledge_config_server.SimilarQuestionExtra{
			SimilarId:       doc.GetSimilarQuestionExtra().GetSimilarId(),
			SimilarQuestion: doc.GetSimilarQuestionExtra().GetSimilarQuestion(),
		}
		docs = append(docs, &bot_knowledge_config_server.MatchReferReq_Doc{
			DocId:                doc.GetDocId(),
			DocType:              doc.GetDocType(),
			RelatedId:            doc.GetRelatedId(),
			Question:             doc.GetQuestion(),
			Answer:               doc.GetAnswer(),
			OrgData:              doc.GetOrgData(),
			IsBigData:            doc.GetIsBigData(),
			SimilarQuestionExtra: similarQuestionParams,
			SheetInfo:            doc.GetSheetInfo(),
		})
	}

	placeholders := utils.GetPlaceHolders(bs.Knowledge)
	log.DebugContextf(ctx, "SearchBatchKnowledge, req: %s, docs: %s", req, docs)
	return docs, placeholders, nil
}

// GetAnswerFromBatchKnowledgeLLM 请求LLM
func GetAnswerFromBatchKnowledgeLLM(ctx context.Context, reqLLM *llmm.Request,
	chReply chan *pb.GetAnswerFromBatchKnowledgeReply, request *pb.GetAnswerFromBatchKnowledgeRequest,
	docs []*bot_knowledge_config_server.MatchReferReq_Doc, placeholders map[string]string, stopSignal chan struct{}) {
	ch := make(chan *llmm.Response, 100)
	llmCtx, cancel := context.WithCancel(ctx)
	go func() {
		defer errors.PanicHandler()
		err := bus.dao.Chat(llmCtx, reqLLM, ch, time.Now(), nil) // todo: 耗时统计
		if err != nil {
			log.ErrorContextf(llmCtx, "Chat error: %v", err)
		}
		log.DebugContextf(llmCtx, "Chat finish")
	}()
	var reply *pb.GetAnswerFromBatchKnowledgeReply
	go func() {
		defer errors.PanicHandler()
		defer func() {
			chReply <- reply
			close(chReply)
		}()
		for {
			reply = &pb.GetAnswerFromBatchKnowledgeReply{}
			select {
			case <-ctx.Done():
				log.InfoContextf(ctx, "GetAnswerFromBatchKnowledgeLLM, ctx done")
				reply.Finished = true
				return
			case <-stopSignal:
				log.InfoContextf(ctx, "GetAnswerFromBatchKnowledgeLLM|stopSignal|return")
				reply.Code = -1
				reply.ErrMsg = "canceled"
				reply.Finished = true
				cancel()
				return
			case llmRsp, ok := <-ch:
				if !ok || llmRsp == nil {
					log.DebugContextf(ctx, "GetAnswerFromKnowledgeLLM, llmRsp is nil")
					reply.Code = -1
					reply.ErrMsg = "llmRsp error"
					reply.Finished = true
					return
				}
				reply.Code = llmRsp.GetCode()
				reply.ErrMsg = llmRsp.GetErrMsg()
				reply.RequestId = llmRsp.GetRequestId()
				llmRsp.Message.Content = replacePlaceholders(llmRsp.GetMessage().GetContent(), placeholders)

				content := helper.RemoveReference(request.GetAppBizId(), llmRsp.GetMessage().GetContent())
				content, _, _ = helper.MatchSearchResults(ctx, content) // 去掉知识库问答引用
				reply.Message = &pb.LLMMessage{
					Role:    pb.LLMRole(llmRsp.GetMessage().GetRole()),
					Content: content,
					Thought: llmRsp.GetMessage().GetReasoningContent(),
				}
				reply.Finished = llmRsp.GetFinished()
				if llmRsp.GetStatisticInfo() != nil {
					reply.StatisticInfo = &pb.StatisticInfo{
						FirstTokenCost: llmRsp.GetStatisticInfo().GetFirstTokenCost(),
						TotalCost:      llmRsp.GetStatisticInfo().GetTotalCost(),
						InputTokens:    llmRsp.GetStatisticInfo().GetInputTokens(),
						OutputTokens:   llmRsp.GetStatisticInfo().GetOutputTokens(),
						TotalTokens:    llmRsp.GetStatisticInfo().GetTotalTokens(),
					}
				} else {
					log.WarnContextf(ctx, "llmRsp.StatisticInfo is empty, llmRsp: %v", llmRsp)
				}
				if reply.GetFinished() {
					reply.References = processReferenceToWorkFlow(ctx, reply.GetMessage().GetContent(), docs, request)
					log.InfoContextf(ctx, "MatchRefer finish. Final reply: %s", helper.Object2String(reply))
					return
				}
				chReply <- reply // 避免2次final
			}
		}
	}()
}

func processReferenceToWorkFlow(ctx context.Context, reply string,
	docs []*bot_knowledge_config_server.MatchReferReq_Doc,
	request *pb.GetAnswerFromBatchKnowledgeRequest) (refer []*pb.Reference) {
	if len(docs) < 1 {
		log.InfoContextf(ctx, "SearchDocs finish, docs is empty")
		return refer
	}

	referIndex := helper.GetAllSubMatch(request.GetAppBizId(), reply)
	mDocs := make([]*bot_knowledge_config_server.MatchReferReq_Doc, 0)
	disableMatch := false
	if len(referIndex) > 0 {
		disableMatch = true
		for _, index := range referIndex {
			if index > len(docs) {
				log.WarnContextf(ctx, "index out of range, index: %d, knowledge: %v", index, docs)
				continue
			}
			doc := docs[index-1] // index从1开始
			mDocs = append(mDocs, &bot_knowledge_config_server.MatchReferReq_Doc{
				DocId:     doc.GetDocId(),
				DocType:   doc.GetDocType(),
				RelatedId: doc.GetRelatedId(),
				Question:  doc.GetQuestion(),
				Answer:    doc.GetAnswer(),
				OrgData:   doc.GetOrgData(),
				IsBigData: doc.GetIsBigData(),
				SheetInfo: doc.GetSheetInfo(),
			})
		}
	} else {
		mDocs = docs
	}
	// MatchRefer 匹配参考来源
	refers, err := bus.dao.MatchRefer(ctx, &bot_knowledge_config_server.MatchReferReq{
		BotBizId:              request.GetAppBizId(),
		Docs:                  mDocs,
		Answer:                reply,
		IsRelease:             request.FilterKey == pb.Filter_FILTER_PRODUCTION,
		MsgId:                 uuid.NewString(),
		Question:              request.GetQuestion(),
		IgnoreConfidenceScore: disableMatch,
	})
	if err != nil {
		log.ErrorContextf(ctx, "MatchRefer failed, error: %v", err)
	} else {
		refer = ToReplyReference(refers)
	}
	return refer
}

func getSearchBatchKnowledgeReq(app *model.App, bs *botsession.BotSession,
	req *pb.GetAnswerFromBatchKnowledgeRequest) *knowledge.SearchKnowledgeBatchReq {
	scene := knowledge.SceneType_PROD
	if req.FilterKey == pb.Filter_FILTER_SANDBOX {
		scene = knowledge.SceneType_TEST
	}
	// 处理 WorkflowSearchParam 参数
	var filters []*knowledge.Filter
	for _, filter := range req.GetWorkflowSearchParam().GetFilters() {
		filters = append(filters, &knowledge.Filter{
			DocType:    filter.GetDocType(),
			Confidence: filter.GetConfidence(),
			TopN:       filter.GetTopN(),
		})
	}
	workflowSearchStrategy := req.GetWorkflowSearchParam().GetSearchStrategy()
	var searchStrategy *knowledge.SearchStrategy = nil
	if workflowSearchStrategy != nil {
		searchStrategy = &knowledge.SearchStrategy{
			StrategyType:     knowledge.SearchStrategyTypeEnum(workflowSearchStrategy.GetStrategyType()),
			TableEnhancement: workflowSearchStrategy.GetTableEnhancement(),
		}
	}
	workflowSearchParam := &knowledge.WorkflowSearchParam{
		Filters:        filters,
		TopN:           req.GetWorkflowSearchParam().GetTopN(),
		SearchStrategy: searchStrategy,
	}
	// 处理 SearchConfig 参数
	var searchConfigs []*knowledge.SearchKnowledgeConfig
	for _, searchConfig := range req.GetSearchConfig() {
		param := searchConfig.GetWorkflowKnowledgeParam()
		var labels []*knowledge.AttrLabel
		for _, label := range param.GetLabels() {
			labels = append(labels, &knowledge.AttrLabel{
				AttrBizId:  label.GetAttrBizId(),
				AttrValues: label.GetAttrValues(),
			})
		}

		var scopes []*knowledge.KnowledgeScope
		for _, scope := range param.GetKnowledgeScope() {
			scopes = append(scopes, &knowledge.KnowledgeScope{
				ScopeType: knowledge.KnowledgeScopeTypeEnum(scope.GetScopeType()),
				Values:    scope.GetValues(),
			})
		}

		var closeKnowledge []knowledge.DocType
		for _, closeK := range param.GetCloseKnowledge() {
			closeKnowledge = append(closeKnowledge, knowledge.DocType(closeK))
		}

		workflowKnowledgeParam := &knowledge.WorkflowKnowledgeParam{
			Labels:         labels,
			KnowledgeScope: scopes,
			LabelLogicOpr:  knowledge.LogicOpr(param.GetLabelLogicOpr()),
			CloseKnowledge: closeKnowledge,
		}
		searchConfigs = append(searchConfigs, &knowledge.SearchKnowledgeConfig{
			KnowledgeBizId:         searchConfig.GetKnowledgeBizId(),
			WorkflowKnowledgeParam: workflowKnowledgeParam,
		})
	}

	searchReq := &knowledge.SearchKnowledgeBatchReq{
		KnowledgeType:       knowledge.KnowledgeType_WORKFLOW,
		SceneType:           scene,
		AppBizId:            app.GetAppBizId(),
		Question:            req.GetQuestion(),
		ImageUrls:           bs.Images,
		UsePlaceholder:      app.CanUsePlaceholder(),
		ModelName:           req.GetModelName(),
		WorkflowSearchParam: workflowSearchParam,
		SearchConfig:        searchConfigs,
		CustomVariables:     req.GetCustomVariables(),
	}
	return searchReq
}
