package eventbus

import (
	"context"
	"fmt"
	"slices"
	"sync"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/common/v3/errors"
	"git.woa.com/dialogue-platform/common/v3/plugins/i18n"
	"git.woa.com/dialogue-platform/go-comm/clues"
	"git.woa.com/dialogue-platform/go-comm/pf"
	llmm "git.woa.com/dialogue-platform/proto/pb-stub/llm-manager-server"
	"git.woa.com/ivy/protobuf/trpc-go/qbot/qbot/infosec"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	"git.woa.com/ivy/qbot/qbot/chat/internal/eventbus/botsession"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model/event"
	"git.woa.com/ivy/qbot/qbot/chat/internal/utils"
	"git.woa.com/ivy/qbot/qbot/chat/pkg"
	ispkg "git.woa.com/ivy/qbot/qbot/infosec/pkg"
	json "github.com/json-iterator/go"
	jsoniter "github.com/json-iterator/go"
)

// QueryWithImage 带图片的请求
func QueryWithImage(ctx context.Context, bs *botsession.BotSession) (err error) {
	req := &llmm.Request{
		RequestId:   model.RequestID(ctx, bs.RequestID, bs.RecordID),
		ModelName:   config.GetDialogMLLMModelName(bs.Session.BotBizID),
		AppKey:      fmt.Sprintf("%d", bs.Session.BotBizID),
		Messages:    make([]*llmm.Message, 0),
		PromptType:  llmm.PromptType_TEXT,
		RequestType: llmm.RequestType_ONLINE,
		Biz:         "cs",
	}

	bs.TokenStat.UpdateProcedure(event.NewProcessingTSProcedure(ctx, event.ProcedureImage))
	SendTokenStat(ctx, bs, bs.TokenStat)

	var placeholder string
	for i := 0; i < len(bs.Images); i++ {
		placeholder = placeholder + fmt.Sprintf(config.App().MultiModal.Placeholder, i)
	}
	prompt := i18n.Translate(ctx, config.App().MultiModal.GetCaptionPrompt)
	if len(bs.Images) > 1 {
		prompt = i18n.Translate(ctx, config.App().MultiModal.GetMultiPrompt)
	}
	message := &llmm.Message{
		Role:    llmm.Role_USER,
		Content: placeholder + prompt, // 算法同学说 占位符放前面
		Images:  bs.Images,
	}
	if config.OnlyCosURLModel(req.ModelName) {
		message.Images = helper.ReplaceDomain(bs.Images)
		message.Content = prompt
	}
	req.Messages = append(req.Messages, message)
	bs.Flags.IsJudgeModelReject = false // 这种场景不判断拒答
	bs.Intent = "纯图片输入"
	bs.IntentCate = model.IntentTypeCaption
	if needDsR1Summary(ctx, bs, false) {
		var llmRsp *llmm.Response
		var isStopGen bool
		llmRsp, isStopGen, err = streamToNonStream(ctx, bs, req, event.ProcedureImage)
		if err != nil {
			return err
		}
		if isStopGen {
			log.InfoContext(ctx, "receive stop generation")
			return nil
		}
		return processDsR1Summary(ctx, bs, llmRsp, model.ReplyMethodImage)
	}
	startTime, last, lastEvil, isModelRejected, err := streamReply(ctx, bs, req,
		event.ProcedureImage, model.ReplyMethodImage, nil)
	if err != nil {
		return err
	}
	if isModelRejected {
		pf.AddNode(ctx, pf.PipelineNode{Key: "processMultiModalReply.HitReject"})
	}
	isEvil := false
	if !isModelRejected && last != nil && last.Message != nil {
		bs.Caption = last.Message.Content
		isEvil, _ = checkLastAndCreateRecord(ctx, bs, model.ReplyMethodImage, last, lastEvil, req, startTime)
	}
	if !isEvil { // 安全审核通过，处理图片历史
		go processImageHistory(ctx, bs)
	}
	return nil
}

// processImageHistory 处理图片历史
func processImageHistory(ctx context.Context, bs *botsession.BotSession) {
	newImageQueues := processCaption(bs.Images, bs.Caption)

	// 读取历史图片记录
	history, err := bus.dao.GetMultiModalHistory(ctx, bs.Session.SessionID)
	if err != nil || len(history) == 0 {
		b, _ := json.Marshal(newImageQueues)
		_ = bus.dao.SetMultiModalHistory(ctx, bs.Session.SessionID, string(b))
		return

	}
	oldImageQueues := make([]*model.ImageQueue, 0) // 读取历史记录
	_ = json.Unmarshal([]byte(history), &oldImageQueues)
	// 更新历史记录
	bus.memory.UpdateMultiModalHistory(ctx, oldImageQueues, newImageQueues, bs.Session.SessionID)

}

func processCaption(images []string, caption string) []*model.ImageQueue {
	newImageQueues := make([]*model.ImageQueue, 0)
	if len(images) == 1 { // 一张图片 就用当前的caption结果
		newImageQueues = append(newImageQueues, &model.ImageQueue{
			ImageURL: images[0],
			Caption:  caption,
			Round:    1,
		})
	} else {
		// 多张图片，获取caption的逻辑，看了一下，都是按照【图1】 【图2】这样开头
		captions := helper.ExtractCaption(len(images), caption)
		for i, image := range images {
			temp := "image" // 兜底
			if i < len(captions) {
				temp = captions[i]
			}
			imageQueue := &model.ImageQueue{
				ImageURL: image,
				Caption:  temp,
				Round:    1,
			}
			newImageQueues = append(newImageQueues, imageQueue)
		}
	}
	return newImageQueues

}

// QueryWithImageAndFile 图片和文件同时发送的请求
func QueryWithImageAndFile(ctx context.Context, bs *botsession.BotSession) (err error) {
	// 起2个协程，分别处理图片和文件，然后合并结果
	wg := sync.WaitGroup{}
	wg.Add(2)
	imageCaption := make(chan string)
	fileSummary := make(chan string)
	go func() {
		defer errors.PanicHandler()
		defer wg.Done()
		caption, _ := processImage(ctx, bs)
		imageCaption <- caption
		log.DebugContextf(ctx, "QueryWithImageAndFile: rsp:%s", caption)
	}()

	go func() {
		defer errors.PanicHandler()
		defer wg.Done()
		summary, _ := processDocSummary(ctx, bs, false)
		log.DebugContextf(ctx, "QueryWithImageAndFile: summary:%s", summary)
		fileSummary <- summary
	}()

	go func() {
		defer errors.PanicHandler()
		wg.Wait()
		close(imageCaption)
		close(fileSummary)
	}()
	log.DebugContextf(ctx, "QueryWithImageAndFile: waiting for imageCaption and fileSummary")
	// 3. 合并结果imageCaption 和 fileSummary
	imageRes := <-imageCaption
	bs.Caption = imageRes
	fileRes := <-fileSummary
	bs.Intent = "图片和文档输入"
	bs.IntentCate = "图像描述与文档摘要"
	res := fmt.Sprintf("图片中显示了:\n %s\n\n%s", imageRes, fileRes) // 拼接显示结果
	log.DebugContextf(ctx, "QueryWithImageAndFile  result:%s", res)
	// 4. 天御安全审核
	checkCode, checkType := bus.dao.CheckTextEvil(ctx, bs.App.GetAppBizId(), bs.App.GetCorpId(), bs.RecordID, res,
		bs.App.GetInfosecBizType())
	// 5. 处理消息记录
	m0 := bs.NewBotRecord(ctx, res, &llmm.Request{}, model.ReplyMethodImage,
		&infosec.CheckRsp{ResultCode: checkCode, ResultType: checkType}, time.Now())
	newMsg, stat := event.GetMsgRecordAndTokenStat(ctx, m0)
	_, err = bus.dao.CreateMsgRecord(ctx, newMsg, stat) // for answer
	clues.AddTrackDataWithError(ctx, "QueryWithImageAndFile:dao.CreateMsgRecord", *bs.Msg, err)
	if err != nil {
		return err
	}
	// 6. 返回结果
	isEvil := bs.Msg.ResultCode == ispkg.ResultEvil
	if !isEvil {
		go processImageHistory(ctx, bs)
	}
	if needDsR1Summary(ctx, bs, isEvil) {

		var llmRsp = &llmm.Response{Message: &llmm.Message{Content: res}, Finished: true}
		return processDsR1Summary(ctx, bs, llmRsp, model.ReplyMethodImage)
	}
	mockStreamOutput(ctx, bs, res, model.ReplyMethodImage, isEvil) // 模拟流式输出
	return nil
}

// processImage 处理图片
func processImage(ctx context.Context, bs *botsession.BotSession) (string, error) {
	m := getAppModel(ctx, bs)
	req := &llmm.Request{
		RequestId:   model.RequestID(ctx, bs.RequestID, bs.RecordID),
		ModelName:   config.GetDialogMLLMModelName(bs.Session.BotBizID),
		AppKey:      fmt.Sprintf("%d", bs.Session.BotBizID),
		Messages:    make([]*llmm.Message, 0),
		PromptType:  llmm.PromptType_TEXT,
		RequestType: llmm.RequestType_ONLINE,
		Biz:         "cs",
	}
	inferParams := m.CreateModelParamsObject(bs.EnableRandomSeed)
	req.ModelParams = helper.Object2String(inferParams)
	bs.TokenStat.UpdateProcedure(event.NewProcessingTSProcedure(ctx, event.ProcedureImage))
	SendTokenStat(ctx, bs, bs.TokenStat)
	rsp, err := GetCaption(ctx, bs.Images, req, "")

	if err != nil || rsp == nil || rsp.Message == nil {
		bs.TokenStat.UpdateProcedure(event.NewFailedTSProcedure(ctx, event.ProcedureImage))
		SendTokenStat(ctx, bs, bs.TokenStat)
		return "模型未读取到内容", pkg.ErrImageRecognitionFailed
	}
	d := event.ProcedureDebugging{
		Content:         bs.OriginContent,
		CustomVariables: bs.CustomVariablesForDisplay,
	} // @halelv 调试信息
	p := event.NewSuccessTSProcedure(ctx, event.ProcedureImage, rsp.GetStatisticInfo(), d, nil)
	bs.TokenStat.UpdateSuccessProcedure(p)
	SendTokenStat(ctx, bs, bs.TokenStat)
	return rsp.Message.Content, nil
}

// QueryWithTextImageFileByMLLMComprehension 处理图文、文件并存的情况：开启图文检索，走多模态阅读理解
func QueryWithTextImageFileByMLLMComprehension(ctx context.Context, bs *botsession.BotSession) (err error) {
	bs.TokenStat.UpdateProcedure(event.NewProcessingTSProcedure(ctx, event.ProcedureImage))
	SendTokenStat(ctx, bs, bs.TokenStat)
	reWrote, reWrotePrompt, _ := bus.rewrite.SimpleImageRewrite(ctx, bs)
	log.InfoContextf(ctx, "reWrote: %s, reWrote_prompt: %s", reWrote, reWrotePrompt)
	d := event.ProcedureDebugging{
		Content:         bs.OriginContent,
		CustomVariables: bs.CustomVariablesForDisplay,
	} // @halelv 调试信息
	p := event.NewSuccessTSProcedure(ctx, event.ProcedureImage, &llmm.StatisticInfo{}, d, nil)
	bs.TokenStat.UpdateSuccessProcedure(p)
	SendTokenStat(ctx, bs, bs.TokenStat)
	bs.TokenStat.UpdateProcedure(event.NewProcessingTSProcedure(ctx, event.ProcedureFile))
	SendTokenStat(ctx, bs, bs.TokenStat)
	req := &llmm.Request{
		RequestId:   model.RequestID(ctx, bs.RequestID, bs.RecordID),
		ModelName:   config.GetMultiModalComprehensionModelName(bs.Session.BotBizID),
		AppKey:      fmt.Sprintf("%d", bs.Session.BotBizID),
		Messages:    make([]*llmm.Message, 0),
		PromptType:  llmm.PromptType_TEXT,
		RequestType: llmm.RequestType_ONLINE,
		Biz:         "cs",
	}
	am := bs.App.GetModel(ctx, model.AppTypeKnowledgeQA, model.ModelTypeMLLMComprehension)
	bs.PromptCtx = botsession.PromptCtx{Question: reWrote}
	// 统计检索耗时
	pf.StartElapsedAsMetrics(ctx, config.App().StageTaskName.SearchKnowledge)
	docs, _ := bus.retrieval.SearchRealtime(ctx, bs, bs.FileInfos, int64(am.PromptLimit)) // 获取检索实时文档
	pf.AppendSpanElapsed(ctx, config.App().StageTaskName.SearchKnowledge)
	am.PromptLimit = bus.dao.GetModelPromptLimit(ctx,
		config.GetMultiModalComprehensionModelName(bs.App.GetAppBizId())) - 200
	bs.Knowledge = docs
	bs.Placeholders = utils.GetPlaceHolders(bs.Knowledge)

	docPromptLimit := am.PromptLimit - len([]rune(bs.PromptCtx.Question)) -
		len(bs.Images)*config.App().MultiModal.OneImageTokenLength // 截断doc
	imagesInDocs, finalDocs := truncDocsWithImages(bs.Knowledge, docPromptLimit, len(bs.Images))

	if helper.IsQueryRewriteImagePlaceholder(bs.PromptCtx.Question) {
		// query占位符的处理
		bs.PromptCtx.Question = ReplaceQueryWithPlaceholder(ctx, reWrote, len(imagesInDocs))
	} else {
		// query占位符的处理
		num := len(imagesInDocs) + 1
		bs.PromptCtx.Question = fmt.Sprintf(config.App().MultiModal.PlaceholderV2, num, num) + bs.PromptCtx.Question
	}
	log.InfoContextf(ctx, "final query is: %s", bs.PromptCtx.Question)
	am.PromptLimit = docPromptLimit - len(imagesInDocs)*config.App().MultiModal.OneImageTokenLength
	// promptCtx := botsession.PromptCtx{Docs: finalDocs, Question: reWrote}
	bs.PromptCtx.Docs = finalDocs

	prompt, err := bus.dao.TextTruncate(ctx, am, bs.PromptCtx)
	clues.AddTrackE(ctx, "am.RenderPrompt", clues.M{"promptCtx": bs.PromptCtx, "prompt": prompt}, err)
	imagesInDocs = getRealImageFromPlaceholders(ctx, imagesInDocs, bs.Placeholders) // 把占位符替换为真实链接
	temp := replacePlaceholders(prompt, bs.Placeholders)                            // 把Prompt中的占位符替换为真实链接
	log.InfoContextf(ctx, "prompt: %s", temp)
	newPrompt, _ := helper.ExtractLinkWithPlaceholder(temp, 0, len(imagesInDocs))
	log.InfoContextf(ctx, "final prompt: %s, images: %s", newPrompt, helper.Object2String(imagesInDocs))
	images := append(imagesInDocs, bs.Images...)
	req.Messages = append(req.Messages, &llmm.Message{Role: llmm.Role_USER,
		Content: newPrompt,
		Images:  images,
	})
	bs.Images = images
	bs.Intent = "query图片文档输入"
	bs.IntentCate = model.IntentTypeMLLM
	inferParams := am.CreateModelParamsObject(bs.EnableRandomSeed)
	req.ModelParams = helper.Object2String(inferParams)
	return processRsp(ctx, bs, req)
}

func processRsp(ctx context.Context, bs *botsession.BotSession, req *llmm.Request) error {
	resp, err := bus.dao.SimpleChat(ctx, req) // 请求MLLM 获取结果

	defer func() {
		if err != nil {
			bs.TokenStat.UpdateProcedure(event.NewFailedTSProcedure(ctx, event.ProcedureFile))
		} else {
			d := event.ProcedureDebugging{CustomVariables: bs.CustomVariablesForDisplay} // @halelv 调试信息 2.4.0不涉及
			p := event.NewSuccessTSProcedure(ctx, event.ProcedureFile, resp.GetStatisticInfo(), d, nil)
			bs.TokenStat.UpdateSuccessProcedure(p)
		}
		SendTokenStat(ctx, bs, bs.TokenStat)
	}()
	if err != nil || resp == nil || resp.Message == nil {
		log.ErrorContextf(ctx, "E|MultiModalEvent|QueryWithTextImageFile rsp:%s, Error %v", helper.Object2String(resp), err)
		return pkg.ErrImageRecognitionFailed
	}
	resp.Message.Content = replaceMLLMPlaceholders(resp.Message.Content, bs.Images)
	// 安全审核、写对话记录、返回结果
	// 4. 天御安全审核
	checkCode, checkType := bus.dao.CheckTextEvil(ctx, bs.App.GetAppBizId(),
		bs.App.GetCorpId(), bs.RecordID, resp.Message.Content, bs.App.GetInfosecBizType())
	// 5. 处理消息记录
	m0 := bs.NewBotRecord(ctx, resp.Message.Content, req, model.ReplyMethodImage,
		&infosec.CheckRsp{ResultCode: checkCode, ResultType: checkType}, time.Now())
	newMsg, stat := event.GetMsgRecordAndTokenStat(ctx, m0)
	_, err = bus.dao.CreateMsgRecord(ctx, newMsg, stat) // for answer
	clues.AddTrackDataWithError(ctx, "QueryWithImageAndFile:dao.CreateMsgRecord", *bs.Msg, err)
	if err != nil {
		return err
	}
	// 6. 返回结果
	isEvil := bs.Msg.ResultCode == ispkg.ResultEvil
	mockStreamOutput(ctx, bs, resp.Message.Content, model.ReplyMethodImage, isEvil) // 模拟流式输出
	return nil
}

// GetCaption 获取图片描述
func GetCaption(ctx context.Context, imageURLs []string, req *llmm.Request, query string) (*llmm.Response, error) {
	var placeholder string
	for i := 0; i < len(imageURLs); i++ {
		placeholder = placeholder + fmt.Sprintf(config.App().MultiModal.Placeholder, i)
	}
	prompt := i18n.Translate(ctx, config.App().MultiModal.GetCaptionPrompt)
	if len(imageURLs) > 1 {
		prompt = i18n.Translate(ctx, config.App().MultiModal.GetMultiPrompt)
	}
	if query != "" {
		prompt = query
	}
	message := &llmm.Message{
		Role:    llmm.Role_USER,
		Content: placeholder + prompt, // 算法同学说 占位符放前面
		Images:  imageURLs,
	}
	if config.OnlyCosURLModel(req.ModelName) {
		message.Images = helper.ReplaceDomain(message.Images)
		message.Content = prompt
	}
	req.Messages = append(req.Messages, message)
	resp, err := bus.dao.SimpleChat(ctx, req) // 获取Caption
	clues.AddTrackDataWithError(ctx, "GetCaption.SimpleChat", map[string]any{"req": req, "resp": resp}, err)
	if err != nil || resp == nil || resp.Message == nil { // 下游不正经报错，返回空，需要注意。
		log.ErrorContextf(ctx, "E|MultiModalEvent|GetCaption rsp:%s, Error %v", helper.Object2String(resp), err)
		return nil, pkg.ErrImageRecognitionFailed
	}
	return resp, err
}

// processDsR1Summary 走deepseek r1做总结，因为需要出思维链
func processDsR1Summary(ctx context.Context, bs *botsession.BotSession, llmRsp *llmm.Response,
	replyMethod model.ReplyMethod) error {
	_, cancel := context.WithCancel(ctx)
	defer cancel()

	bs.TokenStat.UpdateProcedure(event.NewProcessingTSProcedure(ctx, event.ProcedureLLM))
	SendTokenStat(ctx, bs, bs.TokenStat)
	m := bs.App.GetModel(ctx, model.AppTypeKnowledgeQA, model.ModelTypeMessage)
	req := &llmm.Request{
		RequestId:   model.RequestID(ctx, bs.RequestID, bs.RecordID),
		ModelName:   m.GetModelName(),
		AppKey:      fmt.Sprintf("%d", bs.Session.BotBizID),
		Messages:    make([]*llmm.Message, 0),
		PromptType:  llmm.PromptType_TEXT,
		RequestType: llmm.RequestType_ONLINE,
		Biz:         "cs",
	}
	m1 := bs.App.GetModel(ctx, model.AppTypeKnowledgeQA, model.ModelTypeDsR1SummaryPrompt)
	promptCtx := model.DeepR1SummaryPromptCtx{
		Content:  llmRsp.GetMessage().GetContent(),
		Question: bs.PromptCtx.Question,
	}
	prompt, err := bus.dao.TextTruncate(ctx, m1, promptCtx) // 生成Prompt
	if err != nil {
		log.ErrorContextf(ctx, "fail to truncate prompt: %s", err.Error())
		return err
	}
	message := &llmm.Message{
		Role:    llmm.Role_USER,
		Content: prompt,
	}
	req.Messages = append(req.Messages, message)
	log.InfoContextf(ctx, "processDsR1Summary, req: %s", helper.Object2String(req))
	startTime, last, lastEvil, _, err := streamReply(ctx, bs, req,
		event.ProcedureLLM, replyMethod, nil)
	if err != nil || last == nil {
		return err
	}
	isEvil := lastEvil.GetResultCode() == ispkg.ResultEvil
	if !last.GetFinished() || isEvil { // 最后一个包含敏感词也需要兜底结束
		last.Finished = true
		_ = bus.dao.DoEmitWsClient(ctx, bs.To.ClientID,
			bs.NewReplyEvent(ctx, last, isEvil, replyMethod, startTime, []string{}), cancel,
		)
	}
	reply := last.GetMessage().GetContent()
	method := helper.When(isEvil, model.ReplyMethodEvil, replyMethod)
	m0 := bs.NewBotRecord(ctx, reply, req, method, lastEvil, startTime)
	newMsg, stat := event.GetMsgRecordAndTokenStat(ctx, m0)
	stat.AgentThought, _ = jsoniter.MarshalToString(bs.Thought)
	_, err = bus.dao.CreateMsgRecord(ctx, newMsg, stat) // for answer
	if err != nil {
		log.ErrorContextf(ctx, "fail to create msg record: %s", err.Error())
	}
	return nil
}

func needDsR1Summary(ctx context.Context, bs *botsession.BotSession, isEvil bool) bool {
	if len(config.App().DeepSeekConf.ImageQuizWhiteList) == 0 {
		return false
	}
	if !slices.Contains(config.App().DeepSeekConf.ImageQuizWhiteList, fmt.Sprintf("%d", bs.App.GetAppBizId())) {
		return false
	}
	if !bs.App.IsDeepSeekModeAndHasThink() || isEvil {
		return false
	}
	log.InfoContextf(ctx, "needDsR1Summary is true")
	return true
}
