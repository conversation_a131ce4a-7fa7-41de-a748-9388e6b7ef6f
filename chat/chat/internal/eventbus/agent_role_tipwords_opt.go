package eventbus

import (
	"context"
	"strings"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/common/v3/sync/errgroupx"
	"git.woa.com/dialogue-platform/go-comm/clues"
	"git.woa.com/dialogue-platform/go-comm/encode"
	llmm "git.woa.com/dialogue-platform/proto/pb-stub/llm-manager-server"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	"git.woa.com/ivy/qbot/qbot/chat/internal/dao"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model/event"
	"git.woa.com/ivy/qbot/qbot/chat/pkg"
	ispkg "git.woa.com/ivy/qbot/qbot/infosec/pkg"
	jsoniter "github.com/json-iterator/go"
)

func init() {
	handlers[event.EventTipWordOptimization] = &AgentRoleTipwordsOptHandler{}
}

// AgentRoleTipwordsOptHandler 提示词优化事件处理器
type AgentRoleTipwordsOptHandler struct{}

// NewRequest 创建事件请求
func (e AgentRoleTipwordsOptHandler) NewRequest(ctx context.Context, conn *model.Conn, bs []byte) (any, error) {
	log.InfoContextf(ctx, "AgentRoleTipwordsOptHandler NewRequest %+v", conn)
	req := event.AgentRoleTipwordsOptEvent{}
	if err := jsoniter.Unmarshal(bs, &req); err != nil {
		log.ErrorContextf(ctx, "Unmarshal TipWordOptimizationEvent req error: %+v, data: %s", err, bs)
		return nil, err
	}
	if err := req.IsValid(); err != nil {
		return nil, err
	}
	// 框架未携带, 则使用业务数据
	pkg.WithSessionID(ctx, req.SessionID)
	pkg.WithRequestID(ctx, req.RequestID)
	pkg.WithTraceID(ctx, req.RequestID)
	return req, nil
}

// Process 处理事件请求
func (e AgentRoleTipwordsOptHandler) Process(ctx context.Context, coon *model.Conn, req any) error {
	log.InfoContextf(ctx, "AgentRoleTipwordsOptHandler Process start %+v %+v", coon, req)
	defer func(ctx *context.Context) { clues.Flush(*ctx) }(&ctx)
	ctx = clues.NewTrackContext(ctx)
	clues.AddTrackData(ctx, "Conn", coon)
	clues.AddTrackData(ctx, "TipWordOptimizationEvent", req)
	log.InfoContextf(ctx, "before AgentRoleTipwordsOptHandler process")
	// 判断机器人使用状态
	app, err := bus.dao.GetAppByBizID(ctx, dao.AppTestScene, coon.APIBotBizID)
	if err != nil {
		return err
	}
	if app == nil {
		return pkg.ErrRobotNotExist
	}
	if app.GetStatus() == model.AppStatusStopped {
		return pkg.ErrRobotStopOrArrearage
	}
	log.InfoContextf(ctx, "AgentRoleTipwordsOptHandler Process appStatus rsp app｜%+v err｜%+v", app, err)
	ev := req.(event.AgentRoleTipwordsOptEvent)
	clues.AddTrackData(ctx, "query", ev.Content)
	msg := e.createMsgRecord(coon, ev)
	// 检查账户状态
	st := bus.dao.GetModelStatus(ctx, app.GetCorpId(), config.AgentConfig.TipwordsOpt.HunyuanVersion)
	log.InfoContextf(ctx, "AgentRoleTipwordsOptHandler GetModelStatus status｜%+v", st)
	// 非0表示账户不可用
	if st != 0 {
		return pkg.ErrNoBalance
	}

	checkCode, checkType := checkEvil(ctx, app, msg.BotBizID, app.CorpId, msg.RecordID, ev.Content,
		app.GetInfosecBizType())
	clues.AddTrackDataWithError(ctx, "req.content.check", checkCode, err)
	msg.ResultCode = checkCode
	msg.ResultType = checkType
	msg.BotBizID = coon.APIBotBizID
	msgID, err := bus.dao.CreateMsgRecord(ctx, msg, nil) // for query
	if err != nil {
		return err
	}
	log.InfoContextf(ctx, "AgentRoleTipwordsOptHandler Process EmitWsUser")
	msg.ID = uint64(msgID)
	isEvil := msg.ResultCode == ispkg.ResultEvil
	ctx, cancel := context.WithCancel(ctx)
	_ = bus.dao.DoEmitWsClient(ctx, coon.ClientID, &event.ReplyEvent{
		IsFinal:    true,
		IsFromSelf: true,
		RequestID:  ev.RequestID,
		Content:    ev.Content,
		RecordID:   msg.RecordID,
		SessionID:  ev.SessionID,
		Timestamp:  msg.CreateTime.Unix(),
		IsEvil:     isEvil,
	}, cancel)
	if isEvil {
		return nil
	}
	log.InfoContextf(ctx, "AgentRoleTipwordsOptHandler Process check evil")
	return e.exec(ctx, coon, msg, ev, app)
}

func (e AgentRoleTipwordsOptHandler) createMsgRecord(cli *model.Conn,
	ev event.AgentRoleTipwordsOptEvent) model.MsgRecord {
	msg := model.MsgRecord{
		SessionID:  ev.SessionID,
		RecordID:   encode.GenerateSessionID(),
		Type:       model.RecordTypeMessage,
		FromID:     cli.CorpStaffID,
		FromType:   cli.GetSourceType(),
		Content:    ev.Content,
		CreateTime: time.Now(),
	}
	return msg
}

func (e AgentRoleTipwordsOptHandler) buildPrompt(userInput string, request *llmm.Request) {
	prompt := strings.ReplaceAll(config.AgentConfig.TipwordsOpt.Prompt, "{{ user_prompt | trim }}", userInput)
	request.Messages = append(request.Messages, &llmm.Message{
		Role:    llmm.Role_USER,
		Content: prompt,
	})
}

func (e AgentRoleTipwordsOptHandler) exec(ctx context.Context, coon *model.Conn,
	msg model.MsgRecord, ev event.AgentRoleTipwordsOptEvent, app *model.App) (err error) {
	g, gctx := errgroupx.WithContext(ctx)
	request := &llmm.Request{
		RequestId:   msg.RecordID,
		ModelName:   config.AgentConfig.TipwordsOpt.HunyuanVersion,
		AppKey:      ev.VisitorBizID,
		RequestType: llmm.RequestType_ONLINE,
		PromptType:  llmm.PromptType_TEXT,
	}
	e.buildPrompt(ev.Content, request)
	llmctx, cancel := context.WithCancel(ctx)
	startTime := time.Now()
	// 开启一个协程向 ch 中写入消息流，sendReply 从 ch 中取出消息流处理 sse 的返回
	ch := make(chan *llmm.Response, 100)
	g.Go(func() error {
		err := bus.dao.Chat(llmctx, request, ch, time.Now(), nil) // todo 耗时统计
		if err != nil {
			log.ErrorContextf(ctx, "AgentRoleTipwordsOptHandler chat stream error: %+v", err)
			return err
		}
		return nil
	})

	lastRsp, isEvil := e.sendReply(ctx, app, coon, msg, ev, ch, cancel, gctx)
	log.InfoContextf(ctx, "AgentRoleTipwordsOptHandler chat end lastRsp: %+v", lastRsp)
	if err := g.Wait(); err != nil {
		return err
	}
	if lastRsp == nil || lastRsp.GetStatisticInfo() == nil {
		log.WarnContextf(ctx, "AgentRoleTipwordsOptHandler chat lastRsp nil")
		return nil
	}
	endTime := time.Now()
	dosage := dao.TokenDosage{
		AppID:        app.GetAppBizId(),
		AppType:      app.GetAppType(),
		ModelName:    config.AgentConfig.TipwordsOpt.HunyuanVersion,
		RecordID:     msg.RecordID,
		StartTime:    startTime,
		EndTime:      endTime,
		InputDosages: []int{int(lastRsp.GetStatisticInfo().GetInputTokens())},
	}
	if !isEvil {
		dosage.OutputDosages = []int{int(lastRsp.GetStatisticInfo().GetOutputTokens())}
	}
	err = bus.dao.ReportTokenDosage2(ctx, app.GetCorpId(), dosage, dao.FinanceSubBizTypeRoleCommands)
	if err != nil {
		log.ErrorContextf(ctx, "AgentRoleTipwordsOptHandler ReportTokenDosage2 failed :%+v", err)
		return err
	}
	log.InfoContextf(ctx, "AgentRoleTipwordsOptHandler exec end")
	return nil

}

func (e AgentRoleTipwordsOptHandler) sendReply(ctx context.Context, app *model.App, coon *model.Conn,
	msg model.MsgRecord, ev event.AgentRoleTipwordsOptEvent,
	ch chan *llmm.Response, cancel context.CancelFunc, gctx context.Context) (
	*llmm.Response, bool) {
	var llmRespond *llmm.Response
	var isEvil, IsFinal bool
	cfg := config.App().Bot
	throttles := cfg.Throttles
	throttleCheck := helper.NewThrottle(throttles.Check)
	for llmRespond = range ch {
		respondText := llmRespond.Message.Content
		l := len([]rune(respondText))
		if throttleCheck.Hit(l, llmRespond.GetFinished()) {
			log.InfoContextf(ctx, "AgentRoleTipwordsOptHandler text:%+v|len:%+v|tr:%+v", respondText, l, throttleCheck)
			checkCode, _ := checkEvil(ctx, app, msg.BotBizID, app.GetCorpId(), msg.RecordID, respondText,
				app.GetInfosecBizType())
			msg.ResultCode = checkCode
			isEvil = msg.ResultCode == ispkg.ResultEvil
			if isEvil {
				cancel()
			}
		}
		if llmRespond.Finished || isEvil {
			IsFinal = true
		}
		_ = bus.dao.DoEmitWsClient(gctx, coon.ClientID, &event.ReplyEvent{
			IsFinal:    IsFinal,
			IsFromSelf: false,
			RequestID:  ev.RequestID,
			Content:    respondText,
			RecordID:   msg.RecordID,
			SessionID:  ev.SessionID,
			Timestamp:  msg.CreateTime.Unix(),
			IsEvil:     isEvil,
		}, cancel)
		if IsFinal {
			log.InfoContextf(ctx, "AgentRoleTipwordsOptHandler exec Finished respondText:%+v", respondText)
			return llmRespond, isEvil
		}
	}
	return llmRespond, isEvil
}
