package eventbus

import (
	"context"
	"errors"
	"fmt"
	"sort"
	"strings"
	"sync"
	"time"
	"unicode/utf8"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	errors2 "git.woa.com/dialogue-platform/common/v3/errors"
	"git.woa.com/dialogue-platform/go-comm/clues"
	"git.woa.com/dialogue-platform/go-comm/encode"
	"git.woa.com/dialogue-platform/go-comm/pf"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_DM"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF_DM"
	kpb "git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_knowledge_config_server"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/knowledge"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	"git.woa.com/ivy/qbot/qbot/chat/internal/dao"
	"git.woa.com/ivy/qbot/qbot/chat/internal/eventbus/botsession"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model/event"
	"git.woa.com/ivy/qbot/qbot/chat/internal/utils"
	"git.woa.com/ivy/qbot/qbot/chat/pkg"
	ispkg "git.woa.com/ivy/qbot/qbot/infosec/pkg"
	jsoniter "github.com/json-iterator/go"
)

func init() {
	handlers[event.EventDialog] = &DialogEventHandler{}
}

// DialogEventHandler 对话事件处理器
type DialogEventHandler struct{}

// NewRequest 创建事件请求，参数校验等。
func (d *DialogEventHandler) NewRequest(ctx context.Context, cli *model.Conn, bs []byte) (any, error) {
	log.InfoContextf(ctx, "Invoke DialogEventHandler NewRequest cli: %s, req: %s",
		helper.Object2String(cli), string(bs))
	req := event.DialogEvent{}
	if err := jsoniter.Unmarshal(bs, &req); err != nil {
		log.WarnContextf(ctx, "Unmarshal event req error: %+v, data: %s", err, bs)
		return nil, pkg.ErrBadRequest
	}
	if !req.IsValid(ctx) {
		log.WarnContextf(ctx, "Invalid event req: %s", helper.Object2String(req))
		return nil, pkg.ErrBadRequest
	}
	// 框架未携带, 则使用业务数据
	pkg.WithSessionID(ctx, req.SessionID)
	pkg.WithRequestID(ctx, req.RequestID)
	pkg.WithTraceID(ctx, req.RequestID)
	return req, nil
}

// Process 处理事件请求
func (d *DialogEventHandler) Process(ctx context.Context, cli *model.Conn, req any) (err error) {
	log.InfoContextf(ctx, "Invoke DialogEventHandler Process cli: %s, req: %+v",
		helper.Object2String(cli), helper.Object2String(req))
	// TODO: 临时解决 针对Apex 链接未建立成功首次进入的请求 先sleep下再执行逻辑 确保Apex链接建立后再执行 后面Apex修复后删除
	if cli.IsSSE && config.App().SSE.EnableDeferClientClose {
		log.DebugContextf(ctx, "clientID: %s SSE.DeferClientCloseTime: %+v",
			cli.ClientID, config.App().SSE.DeferClientCloseTime)
		time.Sleep(config.App().SSE.DeferClientCloseTime)
	}
	ctx = pf.NewPipelineFlowContext(ctx)
	defer func(ctx *context.Context) { pf.Persist(*ctx) }(&ctx)
	defer func(ctx *context.Context) { clues.Flush(*ctx) }(&ctx)
	clues.AddTrackData(ctx, "Conn", cli)
	clues.AddTrackData(ctx, "DialogEvent", req)
	ev := req.(event.DialogEvent)
	clues.AddTrackData(ctx, "query", ev.Content)
	bs := d.initBotSession(ctx, cli, ev)
	// 开启首包耗时统计
	pf.StartElapsed(ctx, bs.EventSource+".bot.reply")
	pf.StartElapsed(ctx, "dm_get_semantic_ms")
	pf.StartElapsed(ctx, "workflow_ms")
	pf.StartElapsedAsMetrics(ctx, config.App().StageTaskName.DialogPPL) // 对话 ppl 事件计时开始

	if err := d.prepare(ctx, bs); err != nil {
		if strings.Contains(err.Error(), "msg:您选择的工作流") {
			log.WarnContextf(ctx, "prepare error: %+v", err)
		} else {
			log.ErrorContextf(ctx, "prepare error: %+v", err)
		}
		return err
	}
	if bs.App.GetKnowledgeQa().GetPattern() == model.AppModeAgent {
		return d.doAgentProcess(ctx, bs) // 智能体模式 在此分流
	}
	if bs.App.GetKnowledgeQa().GetPattern() == model.AppModeSingleWorkflow || len(bs.WorkflowID) != 0 {
		return d.doSingleWorkflowProcess(ctx, bs) // 单工作流模式 在此分流
	}
	return d.doProcess(ctx, bs)
}

// initBotSession 初始化botSession
func (d *DialogEventHandler) initBotSession(ctx context.Context, cli *model.Conn,
	ev event.DialogEvent) *botsession.BotSession {

	bs := d.createBaseBotSession(ev, cli)
	d.configureSessionByEnvironment(bs, ev, cli)
	d.setupCustomVariables(bs, ev)
	d.finalizeSession(ctx, bs, ev)

	return bs
}

// createBaseBotSession 创建基础BotSession结构
func (d *DialogEventHandler) createBaseBotSession(ev event.DialogEvent, cli *model.Conn) *botsession.BotSession {
	bs := &botsession.BotSession{}

	d.setSessionIdentifiers(bs, ev)
	d.setInputFields(bs, ev)
	d.setEnvironmentDefaults(bs, ev, cli)
	d.setStateFields(bs, ev)
	d.setModelFields(bs, ev)
	d.initializeCollections(bs)

	return bs
}

// setSessionIdentifiers 设置会话标识信息
func (d *DialogEventHandler) setSessionIdentifiers(bs *botsession.BotSession, ev event.DialogEvent) {
	bs.SessionID = ev.SessionID
	bs.RecordID = encode.GenerateSessionID()
	bs.RelatedRecordID = ""
	bs.RequestID = ev.RequestID
}

// setInputFields 设置输入相关字段
func (d *DialogEventHandler) setInputFields(bs *botsession.BotSession, ev event.DialogEvent) {
	bs.OriginContent = ev.OriginContent
	bs.FileInfos = ev.FileInfos
	bs.Images = helper.GetAllImageURLs(ev.OriginContent)
	bs.StreamingThrottle = ev.StreamingThrottle
	bs.SystemRole = ev.SystemRole
	bs.IsEvaluateTest = ev.IsEvaluateTest
	bs.SystemInfo = ev.SystemInfo
	bs.StartTime = ev.StartTime
	bs.WorkflowID = ev.WorkflowID
	bs.WorkflowInput = ev.WorkflowInput
	bs.ToolOuputs = ev.ToolOuputs
	bs.AgentConfig = ev.AgentConfig
}

// setEnvironmentDefaults 设置环境默认值
func (d *DialogEventHandler) setEnvironmentDefaults(bs *botsession.BotSession, ev event.DialogEvent, cli *model.Conn) {
	bs.EventSource = ev.Environment
	bs.Type = model.RecordTypeMessage
	bs.SessionType = model.SessionTypeNormal
	bs.Scene = dao.AppReleaseScene
	bs.Env = KEP_DM.RunEnvType_Product
	bs.To = cli
	bs.ChannelType = ev.GetChannelType()
}

// setStateFields 设置状态相关字段
func (d *DialogEventHandler) setStateFields(bs *botsession.BotSession, ev event.DialogEvent) {
	bs.NeedCheck = true
	bs.Intent = ""
	bs.IntentCate = ""
	bs.PromptCtx = botsession.PromptCtx{Question: ev.Content}
	bs.WorkflowDebug = len(ev.WorkflowID) > 0
	bs.EnableRandomSeed = ev.GenerateAgain
	bs.DebugMessage = ev.DebugMessage
	bs.Plugins = ev.Plugins
	bs.LKEUserID = ev.LKEUserID

	// Agent思考过程
	bs.Thought = &event.AgentThoughtEvent{
		SessionID:  ev.SessionID,
		RequestID:  ev.RequestID,
		StartTime:  time.Now(),
		Procedures: make([]event.AgentProcedure, 0),
	}
}

// setModelFields 设置模型和通信相关字段
func (d *DialogEventHandler) setModelFields(bs *botsession.BotSession, ev event.DialogEvent) {
	bs.ModelName = ev.ModelName
	bs.ChatHistoryFromAPI = ev.ChatHistoryFromAPI
	bs.Verbose = ev.Verbose
	bs.SearchNetwork = ev.SearchNetwork
	bs.EnableMultiIntent = ev.EnableMultiIntent
	bs.Stream = ev.Stream
	bs.WorkflowStatus = ev.WorkflowStatus
}

// initializeCollections 初始化集合类型字段
func (d *DialogEventHandler) initializeCollections(bs *botsession.BotSession) {
	bs.Placeholders = make(map[string]string)
	bs.ReferIndex = make([]int, 0)
	bs.Flags = botsession.Flags{}
	bs.QuestionAnswers = make([][2]string, 0)
	bs.ToolsInfo = make(map[string]model.AgentTool)
	bs.AgentStatus = &model.AgentStatus{}
	bs.CorpStaff = &model.CorpStaff{}
	bs.Msg = &model.MsgRecord{}
	bs.CustomVariables = make(map[string]string)
}

// configureSessionByEnvironment 根据环境配置会话参数
func (d *DialogEventHandler) configureSessionByEnvironment(bs *botsession.BotSession, ev event.DialogEvent, cli *model.Conn) {
	// 设置通道类型
	if ev.ChannelType == 0 {
		bs.ChannelType = int(cli.Type)
	}

	// 体验环境配置
	if ev.Environment == event.EventExperience {
		bs.Type = model.RecordTypeExperience
		bs.Env = KEP_DM.RunEnvType_Sandbox
		bs.Scene = dao.AppTestScene
		bs.SessionType = model.SessionTypeExperience
	}

	// 调试模式配置
	if bs.DebugMessage != nil {
		bs.Type = model.RecordTypeOPDebug
	}

	// 工作流配置
	if len(ev.WorkflowID) > 0 {
		bs.SessionType = model.SessionTypeWorkflow
	}
}

// setupCustomVariables 设置自定义变量
func (d *DialogEventHandler) setupCustomVariables(bs *botsession.BotSession, ev event.DialogEvent) {
	// 初始化自定义变量
	if len(bs.CustomVariables) == 0 {
		bs.CustomVariables = make(map[string]string)
	}

	// 复制事件中的自定义变量
	for k, v := range ev.CustomVariables {
		bs.CustomVariables[k] = v
	}

	// 设置LKE用户ID
	if bs.LKEUserID != "" {
		bs.CustomVariables["lke_userid"] = bs.LKEUserID
	}
}

// finalizeSession 完成会话初始化的最后步骤
func (d *DialogEventHandler) finalizeSession(ctx context.Context, bs *botsession.BotSession, ev event.DialogEvent) {
	// 设置标签
	bs.Labels = helper.Map(bs.To.VisitorLabels, model.Label.ToVectorLabel)

	// 构建显示用的自定义变量列表
	for k, v := range bs.CustomVariables {
		bs.CustomVariablesForDisplay = append(bs.CustomVariablesForDisplay, fmt.Sprintf("%s:%s", k, v))
	}

	// 记录日志
	log.InfoContextf(ctx, "init BotSession, reqChannelType:%d, modifiedChannelType:%d, %s",
		ev.ChannelType, bs.ChannelType, helper.Object2String(bs))
}

// prepare 预处理
func (d *DialogEventHandler) prepare(ctx context.Context, bs *botsession.BotSession) (err error) {
	// 获取session
	if err := d.getSession(ctx, bs); err != nil {
		return err
	}
	// 获取app
	app, err := getValidApp(ctx, bs.Session.BotBizID, bs.Scene)
	if err != nil {
		return err
	}
	bs.App = app
	if config.App().SupportCustomAuditSwitch {
		bs.NeedCheck = app.GetKnowledgeQa().GetEnableAudit()
	}
	if bs.ModelName != "" { // 用户传了主模型名，需要替换应用配置的主模型
		d.processModelName(ctx, bs)
	}
	if bs.App.IsDeepSeekModel(ctx) && isAfterQDDSStartTime(bs.App) {
		// deepseek模型，且应用创建时间在starttime之后，则bizType为QD_DS
		// https://tapd.woa.com/tapd_fe/70080800/story/detail/1070080800123732989
		bs.App.InfosecBizType = "QD_DS"
	}
	m := app.GetModel(ctx, model.AppTypeKnowledgeQA, model.ModelTypeMessage)
	inputLimit := int(m.GetInputLimit())
	if inputLimit == 0 {
		inputLimit = config.App().Bot.ContentLimit
	}
	if utf8.RuneCountInString(bs.PromptCtx.Question) > inputLimit {
		log.WarnContextf(ctx, "Content too long: %d > inputLimit:%d",
			utf8.RuneCountInString(bs.PromptCtx.Question), inputLimit)
		return pkg.ErrContentTooLong
	} else {
		log.DebugContextf(ctx, "Content length: %d <= inputLimit:%d",
			utf8.RuneCountInString(bs.PromptCtx.Question), inputLimit)
	}
	bs.ModelType = app.GetMessageModelType()
	modelFinance := bus.dao.GetModelFinanceInfo(ctx, app.GetMainModelName())
	bs.IsCustomModel = modelFinance.GetIsCustomModel()
	bs.IsModelFree = modelFinance.GetIsFree()
	if bs.WorkflowID == "" {
		bs.WorkflowID = app.GetKnowledgeQa().GetWorkflowId() // 单工作流模式才有值
	}
	if err = d.checkForSingleWorkflow(ctx, bs, app); err != nil {
		return err
	}
	clues.AddTrackDataWithError(ctx, bs.EventSource+":dao.GetAppByBizID", map[string]any{
		"scene": bs.Scene, "BotBizID": app.GetAppBizId(), "app": app}, err)
	pkg.WithLoginUserType(ctx, bs.To.LoginUserType)
	// 获取from
	from := &model.CorpStaff{}
	if bs.To.Type != model.ConnTypeAPIVisitor {
		if from, err = bus.dao.GetCorpStaffByBizID(ctx, bs.To.CorpStaffBizID); err != nil {
			return err
		}
		if from == nil {
			return pkg.ErrVisitorNotExist
		}
		bs.CorpStaff = from
	}
	// 这里先统一获取下用户历史轮, todo 后续改写，意图，工作流，阅读理解只需要做处理，不用再获取
	bs.ChatHistoriesV2, _ = bus.memory.GetChatHistories(ctx, bs)
	bs.LLMHisMessages = makeLLMHistories(ctx, bs) // 构建阅读理解历史记录
	// 问题写消息记录  // for query;
	if err := CreateQueryMsgRecord(ctx, bs); err != nil {
		log.WarnContextf(ctx, "createQueryMsgRecord error: %v", err)
		return nil
	}
	log.InfoContextf(ctx, "time cost before limit: %+v", time.Since(bs.StartTime).Milliseconds())
	return nil
}

func (d *DialogEventHandler) checkForSingleWorkflow(ctx context.Context, bs *botsession.BotSession,
	app *model.App) error {
	if app.GetKnowledgeQa().GetPattern() == model.AppModeSingleWorkflow {
		if bs.WorkflowID == "" {
			return pkg.ErrWorkflowIsEmpty
		}
		rsp, err := bus.dao.GetWorkflowStatus(ctx, app.GetAppBizId(), bs.WorkflowID, bs.Scene)
		if err != nil {
			return err
		}
		if bs.Scene == dao.AppTestScene && !rsp.IsExist { // 评测端工作流不存在
			return pkg.ErrWorkflowDelete
		}
		if bs.Scene == dao.AppReleaseScene && !rsp.IsExist { // 用户端工作流不存在
			return pkg.ErrWorkflowRelease
		}
		if rsp.IsExist && rsp.IsDebug { // 工作流待调试
			return pkg.ErrWorkflowDebug
		}
		if rsp.IsExist && !rsp.IsDebug && !rsp.IsEnable { // 工作流未开启
			return pkg.ErrWorkflowClose
		}
	}
	return nil
}

// doProcess TODO
// pipeline 对话ppl
func (d *DialogEventHandler) doProcess(ctx context.Context, bs *botsession.BotSession) (err error) {
	bs.RecordID = encode.GenerateSessionID() // 在这里重置RecordID
	bs.LLMRequestID = model.RequestID(ctx, bs.Session.SessionID, bs.RecordID)
	ppl := func() error {
		log.InfoContextf(ctx, "botsession is:%s", bs.String())
		err0 := d.dialogPipeline(ctx, bs) // 核心流程在这里
		if bs.DebugMessage != nil {
			log.InfoContextf(ctx, "%s botsession is debug request, dosn't need to report", bs.SessionID)
			return err0
		}
		concurrencyDosageReport(ctx, bs.TokenStat, bs.App, bus.dao)
		dosageMergeReport(ctx, bs.TokenStat, bs.App, bus.dao)
		dosageMergeReport(ctx, bs.TokenStat2, bs.App, bus.dao) // 老任务流，后续废弃
		return err0
	}

	err = limit(ctx, bs.App, bs.RelatedRecordID, false, ppl)
	if err == nil {
		go d.processImageAndFileCount(ctx, bs)
		return
	}
	if !errors.Is(err, pkg.ErrConcurrenceExceeded) {
		return err
	}
	// 上报超并发
	_ = bus.dao.ReportOverConcurrencyDosage(ctx, bs.App, fillOverConcurrencyDosage(
		bs.App.GetAppBizId(), bs.App.GetMainModelName(), bs.OriginContent))
	return err
}

// dialogPipeline 对话ppl
// todo 1、历史任务型待梳理清楚后，再做修改，也需要放到并行调用里面
func (d *DialogEventHandler) dialogPipeline(ctx context.Context, bs *botsession.BotSession) (err error) {
	log.InfoContextf(ctx, "time cost before dialogPipeline:%v", time.Since(bs.StartTime).Milliseconds())
	if err = initTokenStat(ctx, bs); err != nil { // 初始化token统计
		return err
	}
	// 特殊处理之外的其他三种情况：Query+文档  Query+图片  单独Query
	needSwitch := true // 是否走任务流,下面query改写会用到
	defer func() {
		if needSwitch {
			processFinish(trpc.CloneContext(ctx), bs) // 处理静默和拉回逻辑
		}
	}()
	if needSpecialProcess(ctx, bs) { // 特殊处理
		d.replyFromSelf(ctx, bs)              // 首包回复
		return d.specialQueryProcess(ctx, bs) // 进入特殊处理流程
	}
	start := time.Now()
	isOutput, isEvil := false, false // isOutput检索到内容，需要输出返回 ;isEvil 命中敏感，需要直接返回
	var wg sync.WaitGroup
	wg.Add(1)
	go func() { // 安全检测
		defer errors2.PanicHandler()
		defer pf.AppendSpanElapsed(ctx, config.App().StageTaskName.CheckEvil)
		defer wg.Done()
		// 安全审核阶段耗时统计
		pf.StartElapsedAsMetrics(ctx, config.App().StageTaskName.CheckEvil)
		isEvil = d.checkEvil(ctx, bs)
		d.replyFromSelf(ctx, bs) // 首包回复
	}()
	wg.Add(1)
	go func() { // 基础检索
		defer errors2.PanicHandler()
		defer pf.AppendSpanElapsed(ctx, config.App().StageTaskName.BasicSearch)
		defer wg.Done()
		// 基础检索阶段耗时统计
		pf.StartElapsedAsMetrics(ctx, config.App().StageTaskName.BasicSearch)
		isOutput = bus.retrieval.BasicSearch(ctx, bs)
	}()
	wg.Add(1)
	go func() { // 改写 + 同义词替换
		defer errors2.PanicHandler()
		defer pf.AppendSpanElapsed(ctx, config.App().StageTaskName.QueryRewrite)
		defer wg.Done()
		// 改写 + 同义词替换阶段耗时统计
		pf.StartElapsedAsMetrics(ctx, config.App().StageTaskName.QueryRewrite)
		bs.PromptCtx.Question = queryRewriteAndNER(ctx, bs)
	}()
	wg.Wait()
	log.InfoContextf(ctx, "time cost after rewrite %v", time.Since(start).Milliseconds())
	if isEvil {
		return err
	}
	if isOutput && bs.Flags.IsDirectReply() { // 检索到内容，且需要输出返回
		return d.directReply(ctx, bs)
	}
	canUseTaskFlow(ctx, bs) // 历史任务型处理
	log.DebugContextf(ctx, "canUseTaskFlow: %v", bs.Flags.CanUseTask)
	if bs.Flags.CanUseTask { // 任务型的处理
		// 任务型知识库检索阶段耗时统计
		pf.StartElapsedAsMetrics(ctx, config.App().StageTaskName.SearchKnowledge)
		docs := bus.retrieval.SearchQa(ctx, bs) // QA 意图
		pf.AppendSpanElapsed(ctx, config.App().StageTaskName.SearchKnowledge)
		bs.Placeholders = utils.GetPlaceHolders(docs)
		similarQuestions := d.GetQuestionAnswersForTaskflow(ctx, bs, docs)
		isTask, err := taskReply(ctx, bs, similarQuestions) // 串行调用任务型
		if err != nil {
			return err
		}
		if isTask { // 不是任务型，不再往下走
			needSwitch = false // 不需要拉回
			return nil
		}
	}
	if bs.Flags.IsMultiModal && helper.IsQueryRewriteImagePlaceholder(bs.PromptCtx.Question) {
		return MultiModalProcess(ctx, bs)
	}
	return d.coreProcess(ctx, bs)
}

// coreProcess 核心处理流程
func (d *DialogEventHandler) coreProcess(ctx context.Context, bs *botsession.BotSession) (err error) {
	log.InfoContextf(ctx, "core process begin.")
	if bs.Flags.IsRealTimeDocument { // 实时文档处理流程中
		log.InfoContextf(ctx, "realtime doc process begin.")
		isReject, err := RealtimeDocProcess(ctx, bs) // 单文档处理ppl
		if err == nil && !isReject {                 // 没有报错且没有拒答，直接返回
			log.InfoContextf(ctx, "realtime doc reply after RealtimeDocProcess")
			matchRecommended(ctx, bs, "", bs.Placeholders) // 出推荐问流程
			return nil
		}
	} else {
		// 知识库检索
		d.GetCandidateIntents(ctx, bs)
	}
	if bs.IsDocPriorityAfterRetrieval && bs.HasKnowledge(ctx) { // 文档最高优，先走阅读理解，看是否直接输出或者拒答
		bs.IntentCate = model.IntentTypeDoc
		if err = d.KnowledgeReplyV2(ctx, bs); err != nil {
			log.WarnContextf(ctx, "KnowledgeReplyV2 err:%v", err)
		}
		if err == nil && !bs.Flags.IsDocSegmentReject { // 没有拒答，则认为已经走文档润色回复
			log.InfoContextf(ctx, "doc segment reply after retrieval")
			return nil
		}
	}
	// 意图识别
	bs.IntentRsp = bus.intent.Recognize(ctx, bs)
	bs.Intent = bs.IntentRsp.IntentName
	bs.IntentCate = bs.IntentRsp.IntentCate
	if bs.Flags.IsMultiIntentReply { // 多意图，直接返回选项卡
		return multiIntentReply(ctx, bs)
	}
	// 最终达成方式优先级低于文档且前面没有调用过文档，则先走文档阅读理解
	if bs.IntentRsp.IsDocPriorityByRecognize && !bs.Flags.IsDocSegmentReject && bs.HasKnowledge(ctx) {
		bs.IntentCate = model.IntentTypeDoc
		if err = d.KnowledgeReplyV2(ctx, bs); err != nil {
			log.WarnContextf(ctx, "KnowledgeReplyV2 err:%v", err)
		}
		if err == nil && !bs.Flags.IsDocSegmentReject { // 没有拒答，则认为已经走文档润色回复
			log.InfoContextf(ctx, "doc segment reply after intent recognize")
			return nil
		}
	}
	bs.IntentCate = bs.IntentRsp.IntentCate
	// 走对应达成方式
	pf.StartElapsed(ctx, bs.EventSource+".bot.reply")
	err = d.processIntent(ctx, bs, bs.IntentRsp)
	pf.AppendSpanElapsed(ctx, bs.EventSource+".bot.reply")
	matchRecommended(ctx, bs, "", bs.Placeholders) // 出推荐问流程
	// 端到端尾包耗时统计
	statistics := pkg.GetStatistics(ctx)
	if statistics != nil {
		pf.AppendFullSpanElapsed(ctx, config.App().StageTaskName.DialogPPL, statistics.MainModel,
			statistics.IntentionCategory, pkg.Uin(ctx), -1)
	}
	return err
}

// GetCandidateIntents retrieves the candidate intents。并行检索文档与QA、工作流、自定义意图等。
func (d *DialogEventHandler) GetCandidateIntents(ctx context.Context, bs *botsession.BotSession) {
	// 知识库检索阶段耗时统计
	pf.StartElapsedAsMetrics(ctx, config.App().StageTaskName.SearchKnowledge)
	defer pf.AppendSpanElapsed(ctx, config.App().StageTaskName.SearchKnowledge)
	if bs.WorkflowID != "" { // 工作流调试，或者单工作流模式
		log.InfoContextf(ctx, "bs.WorkflowID：%v, is not empty", bs.WorkflowID)
		return
	}
	// 检索文档和QA
	wg := sync.WaitGroup{}
	wg.Add(3)
	// 检索文档 and QA
	go func() {
		defer errors2.PanicHandler()
		defer wg.Done()
		// 区分实时文档和知识库检索
		_ = bus.retrieval.KnowledgeSearch(ctx, bs)
		bs.PromptCtx.Docs = utils.GetPromptDocsFromSearchPreviewDocs(bs.Knowledge)
		bs.Placeholders = utils.GetPlaceHolders(bs.Knowledge)
		bs.CustomParams = utils.GetCustomParams(bs.Knowledge)
	}()

	// 检索topN 工作流
	go func() {
		defer errors2.PanicHandler()
		defer wg.Done()
		if !bs.App.IsNewWorkflow() || !bs.IsWorkflowStatusEnabled() {
			// 旧的任务流，不检索； 新工作流，且状态不为启用，不检索
			return
		}
		env := KEP_WF_DM.RunEnvType_PRODUCT
		if bs.EventSource == event.EventExperience {
			env = KEP_WF_DM.RunEnvType_SANDBOX
		}
		workflows, _ := bus.workflow.RetrieveWorkflows(ctx, env, bs.App.GetAppBizId(),
			bs.PromptCtx.Question, bs.RelatedRecordID)
		bs.Workflows = workflows
	}()

	// 提取自定义意图
	go func() {
		defer errors2.PanicHandler()
		defer wg.Done()
		log.InfoContextf(ctx, "Role description:%s", helper.Object2StringEscapeHTML(bs.App.RoleDescription()))
		bs.CustomizeIntent = utils.ParseRoleCommand(ctx, bs.App.RoleDescription())
		if len(bs.SystemRole) == 0 {
			bs.SystemRole = utils.RemoveSkills(bs.App.RoleDescription())
		}
		log.InfoContextf(ctx, "Extract custom intents:%s", helper.Object2StringEscapeHTML(bs.CustomizeIntent))
	}()
	wg.Wait()
	// 结合检索召回的内容和用户设置的优先级，判断是否文档最高优
	bs.IsDocPriorityAfterRetrieval = d.isDocPriorityAfterRetrieval(ctx, bs)
}

// processIntent 处理意图
// todo 达成方式的上报
func (d *DialogEventHandler) processIntent(ctx context.Context, bs *botsession.BotSession,
	intent model.IntentRsp) (err error) {
	mm := bs.App.GetMainModelName()
	uin := pkg.Uin(ctx)
	if bs.App.IsMedicalModel() { // 医疗大模型的特殊逻辑
		_, _, _, err = medicalReply(ctx, bs)
		pf.AppendFullSpanElapsed(ctx, bs.EventSource+".bot.reply", mm, "medical", uin, -1)
		return err
	}
	// v2.8之后，走到这里只会处理非文档的场景
	switch bs.IntentCate { // 意图路由
	case model.IntentTypeFAQ: // 需要直接回复
		return d.NewFaqReply(ctx, bs, intent.CandidateIntent)
	case model.IntentTypeWorkflow:
		return bus.workflow.WorkflowReply(ctx, bs, intent.CandidateIntent, len(bs.WorkflowID) != 0)
	case model.IntentTypeCustom:
		bs.SystemRole = bs.SystemRole + "\n" + d.AddHitSkills(intent.CandidateIntent)
		log.InfoContextf(ctx, "CustomIntent")
	case model.KnowledgeQAIntent:
		err = d.KnowledgeReply(ctx, bs, intent.CandidateIntent)
		pf.AppendFullSpanElapsed(ctx, bs.EventSource+".bot.reply", mm, "", uin, -1)
		return err
	case model.DocSummaryIntent:
		if !bs.Flags.IsRealTimeDocument {
			bs.Flags.IsSummaryReject = true  // 命中摘要型，且不在文件流程中直接返回，走未知问题兜底回复
			_, err = botDirectReply(ctx, bs) // 这里要直接返回
			return err
		}
		_, err = processDocSummary(ctx, bs, true)
		pf.AppendFullSpanElapsed(ctx, bs.EventSource+".bot.reply", mm, "doc-summary", uin, -1)
		return err
	case model.SearchEngineIntent:
		if bs.IsSearchEngineEnabled() || d.isSelectSearchEnginePlugin(ctx, bs) {
			_, _, _, err = hunYuanSearchReply(ctx, bs)
			clues.AddTrackDataWithError(ctx, "exec.hunYuanSearchReply().return", map[string]any{}, err)
			pf.AppendFullSpanElapsed(ctx, bs.EventSource+".bot.reply", mm, "hunyuan", uin, -1)
			if errors.Is(err, pkg.ErrNoSearchEngineBalance) {
				err = searchEngineDirectReply(ctx, bs)
			}
			return err
		}
	case model.MathPOTIntentCategory: // v2.8 数学计算都直接走主模型
		return llmDirectReply(ctx, bs)
	case model.SelfAwarenessIntent, model.ModelChatIntent: // 自我认知和闲聊类
		return llmDirectReply(ctx, bs)
	default:
		log.InfoContextf(ctx, "default case in processIntent")
	}
	bs.Knowledge = make([]*knowledge.SearchKnowledgeRsp_SearchRsp_Doc, 0)
	bs.PromptCtx.Docs = make([]*knowledge.SearchKnowledgeRsp_SearchRsp_Doc, 0) // 不使用文档
	err = noHitIntentReply(ctx, bs)
	pf.AppendFullSpanElapsed(ctx, bs.EventSource+".bot.reply", mm, "", uin, -1)
	return err
}

func (d *DialogEventHandler) processModelName(ctx context.Context, bs *botsession.BotSession) {
	if bs.ModelName == "" {
		return
	}
	m0 := bs.App.GetModel(ctx, model.AppTypeKnowledgeQA, model.ModelTypeMessage)
	if m0 != nil {
		m0.ModelName = bs.ModelName
	}
	m1 := bs.App.GetModel(ctx, model.AppTypeKnowledgeQA, model.ModelTypeMessageNonGeneralKnowledge)
	if m1 != nil {
		m1.ModelName = bs.ModelName
	}
	log.InfoContextf(ctx, "use modelName: %s", bs.ModelName)
}

// queryRewriteAndNER query多轮改写
func queryRewriteAndNER(ctx context.Context, bs *botsession.BotSession) string {
	var err error
	var rewrote string
	// query改写 耗时打点
	tik := time.Now()
	defer func() {
		log.InfoContextf(ctx, "queryRewriteAndNER cost time: %v", time.Since(tik).Milliseconds())
	}()
	var prompt string
	if bs.Flags.IsRealTimeDocument || bs.Flags.IsMultiModal { // 多模态改写
		rewrote, prompt, err = bus.rewrite.MultiModalRewrite(ctx, bs)
	} else {
		pf.StartElapsed(ctx, bs.EventSource+".rewrite")
		var wg sync.WaitGroup
		wg.Add(1)
		go func() {
			defer errors2.PanicHandler()
			defer wg.Done()
			rewrote, prompt, err = bus.rewrite.Rewrite(ctx, bs)
		}()
		wg.Add(1)
		go func() {
			defer errors2.PanicHandler()
			defer wg.Done()
			err2 := bus.rewrite.ComplexQueryRewrite(ctx, bs)
			if err2 != nil { // 非核心路径，异常记录就行
				log.WarnContextf(ctx, "ComplexQueryRewrite err: %v", err2)
			}
		}()
		wg.Wait()
		pf.AppendSpanElapsed(ctx, bs.EventSource+".rewrite")
	}
	if err != nil {
		rewrote = bs.PromptCtx.Question
		log.WarnContextf(ctx, "queryRewrite err: %v", err)
	}
	if err = bus.dao.UpdateRewroteContent(ctx, bs.Msg.ID, prompt, rewrote, bs.App.GetAppBizId()); err != nil {
		log.WarnContextf(ctx, "queryRewrite err: %v", err)
	}

	// ner 同义词替换，替换之后的query都是标准词了
	nerRsp, err := bus.dao.GetSynonymsNER(ctx, bs.Scene, bs.App.GetAppBizId(), rewrote)
	if err != nil {
		log.WarnContextf(ctx, "GetSynonymsNER err: %v", err)
		return rewrote
	}
	if rewrote == nerRsp.ReplacedQuery {
		return rewrote
	}
	queryForSearch, queryForLLM, standardWordsExplain := rewriteQueryAfterNER(ctx, rewrote, nerRsp)
	bs.PromptCtx.StandardWordsExplain = standardWordsExplain
	bs.QueryRewrite.QuestionForSearch = queryForSearch
	return queryForLLM
}

// rewriteQueryAfterNER 2.9.0版本 https://tapd.woa.com/tapd_fe/70080800/story/detail/1070080800122524191
func rewriteQueryAfterNER(ctx context.Context, rewrote string, nerRsp *kpb.SynonymsNERRsp) (string, string, []string) {
	// 如果rewote结果是：扬子江里面的扬子江鲟鱼介绍
	// 同义词1： 扬子江  标准词1：长江；
	// 同义词2: 扬子江鲟  标准词2： 长江鲟
	// queryForSearch 给检索用，格式是：同义词（标准词）+ query中其余的文字，例如：扬子江（长江）里面的扬子江鲟（长江鲟）介绍
	var queryForSearch string
	var queryForLLM string                              // 给阅读理解大模型用，就是ner替换后的结果
	standardMapSynonyms := make(map[string][]string, 0) // 一个标准词可以有多个同义词
	synonymMapStandard := make(map[string]string, 0)    // 一个同义词只会有一个标准词
	originSlice := make([]string, 0)

	for _, info := range nerRsp.GetNerInfo() {
		ref := info.GetRefValue()        // ref一定是标准词
		origin := info.GetOriginalText() // original text可能是标准词，也可能是同义词
		if ref == origin {               // 两者相等，说明original text是标准词，不需要替换
			continue
		}
		// 两者不相等，说明original text是同义词，需要替换
		standardMapSynonyms[ref] = append(standardMapSynonyms[ref], origin)
		synonymMapStandard[origin] = ref
	}

	if len(synonymMapStandard) == 0 {
		return rewrote, rewrote, nil
	}
	// 标准词的同义词解释，给阅读理解模型用，例如：长江就是changjiang，也是扬子江
	standardWordsExplain := make([]string, 0, len(standardMapSynonyms))
	for standard, synonymList := range standardMapSynonyms {
		if len(synonymList) == 0 {
			continue
		}
		synonymList = helper.RemoveDuplicates(synonymList)
		var builder strings.Builder
		builder.WriteString(standard)
		for i, synonym := range synonymList {
			if i == 0 {
				str := fmt.Sprintf("就是%s", synonym)
				builder.WriteString(str)
			} else {
				str := fmt.Sprintf("，也是%s", synonym)
				builder.WriteString(str)
			}
		}
		standardWordsExplain = append(standardWordsExplain, builder.String())
	}
	for k := range synonymMapStandard {
		originSlice = append(originSlice, k)
	}

	// 为啥要按照字符串长度排序之后再替换？因为可能出现长江、长江鲟，先替换长的，再替换短的，就不会重复替换
	sort.Slice(originSlice, func(i, j int) bool {
		return len(originSlice[i]) > len(originSlice[j])
	})
	placeholder := "<##*-%d-*##>"
	phMap := make(map[string]string)
	queryForSearch = rewrote
	for i, origin := range originSlice {
		ph := fmt.Sprintf(placeholder, i)
		queryForSearch = strings.ReplaceAll(queryForSearch, origin, ph)
		phMap[ph] = fmt.Sprintf("%s（%s）", origin, synonymMapStandard[origin])
	}
	for i := 0; i < len(originSlice); i++ {
		ph := fmt.Sprintf(placeholder, i)
		queryForSearch = strings.ReplaceAll(queryForSearch, ph, phMap[ph])
	}
	queryForLLM = nerRsp.ReplacedQuery
	log.InfoContextf(ctx, "rewriteQueryAfterNER: queryForSearch:%s, queryForLLM:%s, standardWordsExplain:%v",
		queryForSearch, queryForLLM, standardWordsExplain)
	return queryForSearch, queryForLLM, standardWordsExplain
}

// checkEvil 安全审核
func (d *DialogEventHandler) checkEvil(ctx context.Context, bs *botsession.BotSession) bool {
	if !bs.NeedCheck { // 跳过审核
		log.DebugContextf(ctx, "DialogEventHandler checkEvil skip %v", bs.NeedCheck)
		return false
	}
	checkCode, checkType := checkEvil(ctx, bs.App, bs.App.GetAppBizId(), bs.App.CorpId, bs.RelatedRecordID,
		bs.OriginContent, bs.App.GetInfosecBizType())
	bs.Msg.ResultCode = checkCode
	bs.Msg.ResultType = checkType
	if checkCode == ispkg.ResultEvil {
		if err := bus.dao.UpdateMsgRecordCheckResult(ctx, bs.RelatedRecordID, checkCode, checkType,
			bs.App.GetAppBizId()); err != nil {
			log.WarnContextf(ctx, "UpdateMsgRecordCheckResult err: %v", err)
		}
		return true
	}
	return false
}

// directReply 直接输出回复
func (d *DialogEventHandler) directReply(ctx context.Context, bs *botsession.BotSession) error {
	bs.PromptCtx.Docs = bs.Knowledge      // todo 是否有用？
	reply, err := botDirectReply(ctx, bs) // 这里要直接返回
	if err != nil {
		log.ErrorContextf(ctx, "directReply err: %v", err)
		return err
	}
	matchRecommended(ctx, bs, reply, bs.Placeholders)
	if bs.Flags.IsPriorityQA && !bs.App.IsDomainModel() { // 行业大模型不展示参考来源
		matchRefer(ctx, bs, reply, bs.Placeholders)
	}
	return nil
}

// isSelectSearchEnginePlugin 是否勾选搜索引擎
func (d *DialogEventHandler) isSelectSearchEnginePlugin(ctx context.Context, bs *botsession.BotSession) bool {
	for _, plugin := range bs.App.GetKnowledgeQa().GetPlugins() {
		if plugin.GetPluginId() == config.App().PluginCfg[model.SearchEngineIntent] {
			log.InfoContextf(ctx, "isSelectSearchEnginePlugin: %v", true)
			return true
		}
	}
	return false
}

// isSelectMathPlugin 是否勾选搜索引擎
func (d *DialogEventHandler) isSelectMathPlugin(ctx context.Context, bs *botsession.BotSession) bool {
	for _, plugin := range bs.App.GetKnowledgeQa().GetPlugins() {
		if plugin.GetPluginId() == config.App().PluginCfg[model.MathPOTIntent] {
			log.InfoContextf(ctx, "isSelectMathPlugin: %v", true)
			return true
		}
	}
	return false
}

// isDocPriorityAfterRetrieval 是否是文档最高优
func (d *DialogEventHandler) isDocPriorityAfterRetrieval(ctx context.Context, bs *botsession.BotSession) (docPri bool) {
	defer func() {
		log.InfoContextf(ctx, "isDocPriorityAfterRetrieval: %v", docPri)
	}()
	intents := bs.App.GetAppInfoRsp.GetKnowledgeQa().GetIntentAchievements()
	if len(intents) == 0 { // 没有配置意图优先级，返回文档非优先
		log.WarnContextf(ctx, "GetIntentAchievements empty")
		return docPri
	}
	docPriority := -1 // 文档优先级，越低越高优
	hasDoc := false   // 是否有召回文档
	confPriorityMap := make(map[string]int)
	for i, intent := range intents {
		confPriorityMap[intent] = i
		if intent == "doc" {
			docPriority = i
		}
	}
	if len(bs.Workflows.GetWorkflows()) > 0 { // 有工作流
		if confPriorityMap["workflow"] < docPriority { // 工作流更高优
			return false
		}
	}
	for _, val := range bs.Knowledge {
		if val.GetDocType() == model.DocTypeQA { // 有召回QA
			if confPriorityMap["qa"] < docPriority { // QA更高优
				return false
			}
		}
		if val.GetDocType() == model.DocTypeSegment { // 有召回文档
			hasDoc = true
		}
	}
	if !hasDoc { // 没有召回文档，返回文档非优先
		return docPri
	}
	docPri = true
	return docPri
}

// makeLLMHistories 构建阅读理解历史记录
func makeLLMHistories(ctx context.Context, bs *botsession.BotSession) [][2]model.HisMessage {
	m := getAppModel(ctx, bs)
	hisLimit := int(m.GetHistoryLimit())
	num := helper.When(hisLimit > len(bs.ChatHistoriesV2.ChatStack), len(bs.ChatHistoriesV2.ChatStack), hisLimit)
	start := len(bs.ChatHistoriesV2.ChatStack) - num // 指定截取的起始位置
	pairs := make([][2]model.HisMessage, 0, num)
	for i := start; i < len(bs.ChatHistoriesV2.ChatStack); i++ {
		item := bs.ChatHistoriesV2.ChatStack[i]
		question := helper.When(item.RewriteQuery != "", item.RewriteQuery, item.OriginQuery) // 使用改写后的query
		q := model.HisMessage{RecordID: item.RelatedRecordID, Content: question}
		a := model.HisMessage{RecordID: item.RecordID, Content: item.GetAssistantContent(),
			Intent: item.Intent, IntentCategory: item.IntentCategory}
		pairs = append(pairs, [2]model.HisMessage{q, a})
	}
	// 截断过长的历史记录
	histories := model.TruncateQAHistories(ctx, pairs, int(m.GetHistoryWordsLimit())) // 截断太长的历史记录
	return histories
}
