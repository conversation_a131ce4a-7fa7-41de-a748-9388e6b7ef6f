package eventbus

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/common/v3/sync/errgroupx"
	"git.woa.com/dialogue-platform/go-comm/clues"
	"git.woa.com/dialogue-platform/go-comm/encode"
	"git.woa.com/dialogue-platform/go-comm/pf"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_DM"
	llmm "git.woa.com/dialogue-platform/proto/pb-stub/llm-manager-server"
	"git.woa.com/ivy/protobuf/trpc-go/qbot/qbot/infosec"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	"git.woa.com/ivy/qbot/qbot/chat/internal/eventbus/botsession"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model/event"
	"git.woa.com/ivy/qbot/qbot/chat/pkg"
	ispkg "git.woa.com/ivy/qbot/qbot/infosec/pkg"
)

// OutputStage 输出阶段，任务型输出 存在同时返回2条的情况
type OutputStage uint8

const (
	// OutputNormal 返回一条结果，正常输出
	OutputNormal OutputStage = 1
	// OutputFirstDone 有2条结果，标识第一条输出完成
	OutputFirstDone OutputStage = 2
	// OutputSecondPrepare 开始输出第二条结果的准备工作，变更RequestID和RecordID
	OutputSecondPrepare OutputStage = 3
	// OutputSecond 输出第二天
	OutputSecond OutputStage = 4
)

// taskReply 任务式会话回复
func taskReply(ctx context.Context, ts *botsession.BotSession, similarQuestions []string) (isTask bool, err error) {
	if ts == nil || ts.App == nil || !ts.App.GetKnowledgeQa().GetCanUseTaskFlow() {
		return false, nil
	}
	m := ts.App.GetModel(ctx, model.AppTypeKnowledgeQA, ts.ModelType)
	clues.AddTrackData(ctx, "taskReply.ts.App.GetModel", map[string]any{
		"appType": model.AppTypeKnowledgeQA, "ModelType": ts.ModelType, "m": m,
	})
	histories, err := fillHistory(ctx, ts)
	if err != nil {
		return false, err
	}
	// if len(ts.OriginContent) > 0 &&
	//	ts.QueryType != KEP_DM.QueryType_SilenceTimeout &&
	//	ts.QueryType != KEP_DM.QueryType_SwitchToLastIntent {
	//	// go processSilence(trpc.CloneContext(ctx), ts) // 用户有输出，重置静默时间
	// }
	ts.TokenStat.UpdateProcedure(event.NewProcessingTSProcedure(ctx, event.ProcedureTaskFlow))
	SendTokenStat(ctx, ts, ts.TokenStat)
	req := NewTaskRequest(ctx, ts, m, histories, false, ts.SystemRole) // 构造DM请求
	req.SimilarQuestions = similarQuestions
	clues.AddTrackData(ctx, "taskReply.GetSemantic.NewTaskRequest", req)
	cfg := config.App().Bot
	ch := make(chan *KEP_DM.GetSemanticReply, cfg.ResponseChannelSize)
	g, gctx := errgroupx.WithContext(ctx)
	dmCtx, cancel := context.WithCancel(ctx)
	// 任务型阶段耗时统计
	pf.StartElapsedAsMetrics(ctx, config.App().StageTaskName.Taskflow)
	g.Go(func() error {
		return bus.dao.GetSemantic(dmCtx, req, ch) // 调用DM
	})
	var (
		st        time.Time
		last      *KEP_DM.GetSemanticReply
		lastEvil  *infosec.CheckRsp
		isTimeout bool
	)
	ts.TokenStat2 = &event.TokenStatEvent{
		SessionID:         ts.TokenStat.SessionID,
		RequestID:         ts.TokenStat.RequestID,
		RecordID:          encode.GenerateSessionID(), // answer second record id, dm 有可能回两个包, 这里提前生成备用
		StartTime:         ts.TokenStat.StartTime,
		EventSource:       ts.TokenStat.EventSource,
		MainModelName:     m.GetModelName(),
		FinanceSubBizType: ts.TokenStat.FinanceSubBizType,
	}
	clues.AddT(ctx, "streamDisplay.RecordID", clues.M{"1st": ts.TokenStat.RecordID, "2nd": ts.TokenStat2.RecordID})
	g.Go(func() error {
		last, st, lastEvil, isTimeout = streamDisplay(gctx, cancel, ts, req, ch)
		return helper.When(isTimeout, pkg.ErrLLMTimeout, nil)
	})
	if err = g.Wait(); err != nil {
		return false, err
	}
	if last == nil {
		return false, nil
	}
	return postProcess(trpc.CloneContext(ctx), ts, m, last, lastEvil, st)
}

func fillHistory(ctx context.Context, ts *botsession.BotSession) ([][2]model.HisMessage, error) {
	var histories [][2]model.HisMessage
	var err error
	// 如果有DebugMessage，则使用 DebugMessage里的history
	if ts.ChatHistoryFromAPI != nil {
		histories = ts.MakeChatAPIHistory()
		log.InfoContextf(ctx, "use api histories:%s", helper.Object2String(histories))
		return histories, nil
	}
	if ts.ModelType != model.ModelTypeAbstract &&
		ts.ModelType != model.ModelTypeSummary &&
		ts.QueryType != KEP_DM.QueryType_SilenceTimeout &&
		ts.QueryType != KEP_DM.QueryType_SwitchToLastIntent {
		histories, err = makeMultiRoundHistories(
			ctx, ts.App.AppBizId, ts.Session, uint(config.App().Bot.HistoryLimit), []model.RecordType{ts.Type}, false,
		)
		log.DebugContextf(ctx, "histories :%v", histories)
	}
	clues.AddTrackDataWithError(ctx, "taskReply.makeMultiRoundHistories", map[string]any{
		"botsession": ts.Session, "limit": config.App().Bot.HistoryLimit, "histories": histories}, err)
	return histories, err
}

// NewTaskRequest 构造请求，请求任务式会话
func NewTaskRequest(ctx context.Context, ts *botsession.BotSession, m *model.AppModel,
	histories [][2]model.HisMessage, useRole bool, sysRole string) *KEP_DM.GetSemanticRequest {

	// 组装历史消息
	var messages []*KEP_DM.Message
	if len(sysRole) > 0 {
		messages = append(messages, &KEP_DM.Message{Role: KEP_DM.Role_System, Content: sysRole})
	} else {
		if role := strings.TrimSpace(m.RoleDesc); useRole && role != "" {
			messages = append(messages, &KEP_DM.Message{Role: KEP_DM.Role_System, Content: role})
		}
	}
	for _, pair := range model.TruncateHistories(histories, int(m.GetPromptWordsLimit())) {
		messages = append(messages,
			&KEP_DM.Message{Role: KEP_DM.Role_User, Content: pair[0].Content, RecordID: pair[0].RecordID},
			&KEP_DM.Message{Role: KEP_DM.Role_Assistant, Content: pair[1].Content, RecordID: pair[1].RecordID},
		)
	}
	// 生成请求体
	log.DebugContextf(ctx, "DM request history: %+v", messages)
	systemVariables := make(map[string]*KEP_DM.Variable, 0)
	for k, v := range ts.SystemInfo {
		systemVariables[k] = &KEP_DM.Variable{Value: v}
	}
	cv := make(map[string]*KEP_DM.Variable, len(ts.CustomVariables)*2)
	for k, v := range ts.CustomVariables {
		cv[k] = &KEP_DM.Variable{Value: v}
	}
	return &KEP_DM.GetSemanticRequest{
		SessionID:       ts.Session.SessionID,
		RunEnv:          ts.Env,
		RobotID:         strconv.Itoa(int(ts.App.GetAppBizId())),
		QueryType:       ts.QueryType,
		Query:           ts.OriginContent,
		QueryHistory:    messages,
		RewriteQuery:    ts.PromptCtx.Question,
		SystemVariables: systemVariables,
		CustomVariables: cv,
		RecordID:        pkg.RecordID(ctx),
		RespondRecordID: ts.TokenStat.RecordID,
	}
}

func postProcess(ctx context.Context, ts *botsession.BotSession, m *model.AppModel,
	last *KEP_DM.GetSemanticReply, lastEvil *infosec.CheckRsp, st time.Time) (bool, error) {
	clues.AddT(ctx, "postProcess.TokenStatEvent", clues.M{"tse1": ts.TokenStat, "tse2": ts.TokenStat2})
	ctx = trpc.CloneContext(ctx) // 重置ctx，上面超时，下面还能继续
	// 敏感审核
	isEvil := lastEvil.GetResultCode() == ispkg.ResultEvil
	if last != nil && (!last.IsFinal || isEvil) { // 最后一个包含敏感词也需要兜底结束
		llmRsp := &llmm.Response{Message: &llmm.Message{Content: ""}, Finished: true}
		llmRsp.GetMessage().Content = getLastReply(last, ts.QuestionAnswers)
		var debugInfo = &event.TaskFlowDebugInfo{}
		if len(last.Responds) == 2 {
			debugInfo = getTaskFlowDebugInfo(ts.QueryType, last, 1)
		} else if len(last.Responds) == 1 {
			debugInfo = getTaskFlowDebugInfo(ts.QueryType, last, 0)
		}
		debugInfo.QueryRewrite = ts.PromptCtx.Question
		recID := ts.TokenStat.RecordID
		re := ts.NewTaskReplyEvent(ctx, recID, llmRsp, isEvil, model.ReplyMethodModel, st, getOptionCards(last, 0), debugInfo)
		ctx, cancel := context.WithCancel(ctx)
		_ = bus.dao.DoEmitWsClient(ctx, ts.To.ClientID, re, cancel)
	}
	if len(last.Responds) == 0 && len(last.HitSimilarQuestions) == 0 {
		return false, nil
	}
	newReq := &llmm.Request{
		RequestId:   ts.RequestID,
		ModelName:   m.GetModelName(),
		AppKey:      fmt.Sprintf("%d", m.BotBizID),
		RequestType: llmm.RequestType_ONLINE,
		Messages:    make([]*llmm.Message, 0),
		PromptType:  llmm.PromptType_TEXT,
	}
	var reply string
	if len(last.Responds) != 0 {
		reply = last.Responds[0].Content
		ts.Intent = last.Responds[0].GetHitIntent().GetIntentName()
		ts.IntentCate = model.IntentTypeWorkflow
	} else if len(last.HitSimilarQuestions) != 0 {
		for _, v := range ts.QuestionAnswers {
			if v[0] == last.HitSimilarQuestions[0] {
				reply = v[1]
			}
		}
		if reply == "" && len(ts.QuestionAnswers) > 0 { // 大模型可能返回的QA  不再传入的里面，这里取top1 作为输出
			reply = ts.QuestionAnswers[0][1] // 取top1的answer
		}
		ts.Intent = last.HitSimilarQuestions[0]
		ts.IntentCate = model.IntentTypeFAQ
		reply = replacePlaceholders(reply, ts.Placeholders)
	}
	method := helper.When(isEvil, model.ReplyMethodEvil, getReplyMethod(last))
	tf0 := getTaskFlowDebugInfo(ts.QueryType, last, 0)
	m0 := ts.NewTaskBotRecord(ctx, reply, newReq, method, lastEvil, st, ts.TokenStat, getOptionCards(last, 0), tf0)
	newMsg, stat := event.GetMsgRecordAndTokenStat(ctx, m0)
	_, err := bus.dao.CreateMsgRecord(ctx, newMsg, stat) // for answer
	clues.AddTrackDataWithError(ctx, "postProcess:dao.CreateMsgRecord", m0, err)
	if err != nil {
		return true, err
	}
	clues.AddT(ctx, "postProcess:last", last)
	updateReference(ctx, ts, last)
	if len(last.Responds) == 2 { // DM可能返回两条，要分开处理
		reply = last.Responds[1].Content
		method = helper.When(isEvil, model.ReplyMethodEvil, getReplyMethod(last))
		ts.RelatedRecordID = ""
		tf1 := getTaskFlowDebugInfo(ts.QueryType, last, 1)
		m1 := ts.NewTaskBotRecord(ctx, reply, newReq, method, lastEvil, st, ts.TokenStat2, getOptionCards(last, 1), tf1)
		newMsg, stat = event.GetMsgRecordAndTokenStat(ctx, m1)
		_, err := bus.dao.CreateMsgRecord(ctx, newMsg, stat) // for answer
		clues.AddTrackDataWithError(ctx, "postProcess:dao.CreateMsgRecord.2", m1, err)
		if err != nil {
			return true, err
		}
	}
	if last.HitType == KEP_DM.HitType_FAQ {
		processFinish(ctx, ts)
	}
	return true, nil
}

func getLastReply(last *KEP_DM.GetSemanticReply, questionAnswers [][2]string) (reply string) {
	if len(last.Responds) == 2 {
		reply = last.Responds[1].Content
	} else if len(last.Responds) == 1 {
		reply = last.Responds[0].Content
	} else if len(last.HitSimilarQuestions) != 0 {
		for _, v := range questionAnswers {
			if v[0] == last.HitSimilarQuestions[0] {
				reply = v[1]
			}
		}
		if reply == "" && len(questionAnswers) > 0 { // 大模型可能返回的QA  不再传入的里面，这里取top1 作为输出
			reply = questionAnswers[0][1] // 取top1的answer
		}
	}
	return reply
}

// streamDisplay 向前端流式输出
func streamDisplay(ctx context.Context, cancel context.CancelFunc, ts *botsession.BotSession,
	req *KEP_DM.GetSemanticRequest, ch chan *KEP_DM.GetSemanticReply,
) (last *KEP_DM.GetSemanticReply, t time.Time, lastEvil *infosec.CheckRsp, isTimeout bool) {
	cfg := config.App().Bot
	throttles := cfg.Throttles
	throttleCheck := helper.NewThrottle(throttles.Check)
	throttleStreaming := helper.NewThrottle(helper.When(ts.StreamingThrottle > 0, ts.StreamingThrottle,
		throttles.Streaming))
	ticker := time.NewTicker(time.Duration(cfg.StopGeneration.CheckInterval) * time.Millisecond)
	timeout := time.NewTimer(time.Duration(cfg.Timeout) * time.Second)
	clues.AddTrackData(ctx, "streamDisplay().vars", map[string]any{
		"throttles": throttles, "ts.StreamingThrottle": ts.StreamingThrottle,
		"StopGeneration": cfg.StopGeneration, "Timeout": cfg.Timeout,
	})
	defer ticker.Stop()
	defer timeout.Stop()
	out := new(OutputStage) // 首句是否完成：首句输出中；首句输出结束；第二句输出中；第二句输出结束；
	*out = OutputNormal
	clues.AddT(ctx, "processDisplay.*out.INIT", "OutputNormal")
	flag := 0
	for {
		select {
		case <-timeout.C:
			cancel()
			ts.TokenStat.UpdateProcedure(event.NewFailedTSProcedure(ctx, event.ProcedureTaskFlow))
			SendTokenStat(ctx, ts, ts.TokenStat)
			return last, t, lastEvil, true
		case <-ticker.C:
			if ok, _ := bus.dao.IsGenerationStopped(ctx, ts.To.CorpStaffID, ts.TokenStat.RecordID); ok {
				cancel()
				sendFinishToken(ctx, ts, ts.TokenStat, req, last, 0)
				return last, t, lastEvil, false
			}
		case rsp, ok := <-ch:
			timeout.Stop()
			if !ok {
				return last, t, lastEvil, false
			}
			if last == nil {
				t = time.Now()
			}
			last = rsp
			if rsp.IsFinal {
				clues.AddTrackData(ctx, "streamDisplay.GetSemantic.GetSemanticReply.final", rsp)
				if len(rsp.Responds) == 0 {
					sendFinishToken(ctx, ts, ts.TokenStat, req, nil, 0)
				}
			}
			if rsp.HitType == KEP_DM.HitType_NotHit || rsp.HitType == KEP_DM.HitType_DOC {
				return last, t, lastEvil, false
			}
			if (rsp.HitType == KEP_DM.HitType_Intent || rsp.HitType == KEP_DM.HitType_IntentClarification) &&
				flag == 0 {
				flag = 1 // 仅仅处理一次标记
				_ = bus.dao.SetTaskFlowStatus(ctx, ts.Session.SessionID, true)
			}
			lastEvil = processDisplay(ctx, ts, req, rsp, throttleCheck, throttleStreaming, out, t)
			if lastEvil.GetResultCode() == ispkg.ResultEvil {
				cancel()
				return last, t, lastEvil, false
			}
		}
	}
}

func sendFinishToken(ctx context.Context, bs *botsession.BotSession, ets *event.TokenStatEvent,
	req *KEP_DM.GetSemanticRequest, reply *KEP_DM.GetSemanticReply, rspIndex int) {
	if reply == nil {
		p := event.NewSuccessTSProcedure(ctx, event.ProcedureTaskFlow, nil,
			event.ProcedureDebugging{Content: req.Query, IntentCate: bs.IntentCate,
				CustomVariables: bs.CustomVariablesForDisplay}, nil)
		ets.UpdateSuccessProcedure(p)
		SendTokenStat(ctx, bs, ets)
		return
	}
	// @boyucao @halelv 调试信息
	d := event.ProcedureDebugging{Content: req.Query, CustomVariables: bs.CustomVariablesForDisplay}
	llmmToken := &llmm.StatisticInfo{}
	// LLM统计信息。
	// 因为没命中意图的时候也消耗了LLM token，但Responds为空，所以，Responds为空的时候，统计信息取这里的；
	// Responds非空就取Responds里面的。
	if len(reply.GetResponds()) == 0 {
		if len(reply.GetLLMStatisticInfos()) > 0 {
			for i := 0; i < len(reply.GetLLMStatisticInfos()); i++ {
				llmmToken.InputTokens += reply.GetLLMStatisticInfos()[i].InputTokens
				llmmToken.OutputTokens += reply.GetLLMStatisticInfos()[i].OutputTokens
				llmmToken.TotalTokens += reply.GetLLMStatisticInfos()[i].TotalTokens
			}
		}
	} else {
		var rsp *KEP_DM.Respond
		if len(reply.GetResponds()) > rspIndex {
			rsp = reply.GetResponds()[rspIndex]
		}
		if rsp != nil && rsp.GetLLMStatisticInfos() != nil && len(rsp.GetLLMStatisticInfos()) > 0 {
			for i := 0; i < len(rsp.GetLLMStatisticInfos()); i++ {
				llmmToken.InputTokens += rsp.GetLLMStatisticInfos()[i].InputTokens
				llmmToken.OutputTokens += rsp.GetLLMStatisticInfos()[i].OutputTokens
				llmmToken.TotalTokens += rsp.GetLLMStatisticInfos()[i].TotalTokens
			}
		}
		if rsp.GetHitIntent() != nil {
			d.TaskFlow = event.TaskFlowSummary{
				IntentName:        rsp.GetHitIntent().GetIntentName(),
				UpdatedSlotValues: rsp.GetHitIntent().GetUpdatedSlotValues(),
				RunNodes:          rsp.GetHitIntent().GetRunNodes(),
				Purposes:          rsp.GetHitIntent().GetPurposes(),
			}
		}
	}
	p := event.NewSuccessTSProcedure(ctx, event.ProcedureTaskFlow, llmmToken, d, nil)
	ets.UpdateSuccessProcedure(p)
	SendTokenStat(ctx, bs, ets)
}

// processDisplay 处理展示
func processDisplay(ctx context.Context, ts *botsession.BotSession,
	req *KEP_DM.GetSemanticRequest, rsp *KEP_DM.GetSemanticReply,
	throttleCheck, throttleStreaming helper.Throttle,
	out *OutputStage,
	t time.Time,
) (lastEvil *infosec.CheckRsp) {
	if len(rsp.Responds) < 1 && len(rsp.HitSimilarQuestions) < 1 { // 两句话 分阶段展示:前提 DM  不会同时返回2句话
		return lastEvil
	}
	var reply = processReply(ctx, ts, rsp, out)
	if len(reply) == 0 {
		return lastEvil
	}
	log.DebugContextf(ctx, "process dm rsp: %s", helper.Object2String(rsp))
	l := len([]rune(reply))
	isFirstReply := throttleStreaming.IsFirstReply()
	isEvil, lastEvil := checkTaskEvil(ctx, ts, throttleCheck, l, rsp, reply)
	if isEvil {
		return lastEvil
	}
	replyMethod := getReplyMethod(rsp)
	cluseReplyMethod(ctx, rsp, replyMethod)
	if replyMethod == model.ReplyMethodTaskAnswer || (len(rsp.Responds) != 0 &&
		rsp.Responds[0].HitIntent != nil && rsp.Responds[0].HitIntent.Status == KEP_DM.HitIntentStatus_Failed) {
		// bus.dao.StopSilenceDetect(ctx, ts.Session.SessionID)        // 答案或者运行失败，要停止静默检测
		_ = bus.dao.SetTaskFlowStatus(ctx, ts.Session.SessionID, false) // 答案或者运行失败，标记任务流结束
	}
	llmRsp := &llmm.Response{Message: &llmm.Message{Content: reply}, Finished: rsp.IsFinal}
	if *out == OutputFirstDone { // 当 dm 有两个回包时, 第一个包结束, 进入此分支
		clues.AddT(ctx, "processDisplay.*out", "OutputFirstDone")
		llmRsp.Finished = true // 让第一条正常结束输出
		sendFinishToken(ctx, ts, ts.TokenStat, req, rsp, 0)
		*out = OutputSecondPrepare
		clues.AddT(ctx, "processDisplay.*out.UPDATE", "OutputSecondPrepare")
	}
	if *out == OutputSecondPrepare || throttleStreaming.Hit(l, rsp.IsFinal) {
		optionCards := getOptionCards(rsp, 0)
		taskFlowDebugInfo := getTaskFlowDebugInfo(ts.QueryType, rsp, 0)
		if rsp.IsFinal && len(rsp.Responds) == 2 && *out != OutputSecondPrepare { // 两个包同时返回且final=true
			taskFlowDebugInfo = getTaskFlowDebugInfo(ts.QueryType, rsp, 1)
			optionCards = getOptionCards(rsp, 1)
		}
		taskFlowDebugInfo.QueryRewrite = ts.PromptCtx.Question
		recID := ts.TokenStat.RecordID
		if *out == OutputSecond {
			recID = ts.TokenStat2.RecordID
		}
		ctx, cancel := context.WithCancel(ctx)
		re := ts.NewTaskReplyEvent(ctx, recID, llmRsp, false, replyMethod, t, optionCards, taskFlowDebugInfo)
		// 任务型首包、尾包耗时统计记录两次分别表示首包、尾包耗时
		if isFirstReply {
			pf.AppendSpanElapsed(ctx, config.App().StageTaskName.Taskflow)
			// 端到端首包耗时
			pf.AppendSpanElapsed(ctx, config.App().StageTaskName.DialogPPL)
		} else if rsp.GetIsFinal() {
			pf.AppendSpanElapsed(ctx, config.App().StageTaskName.Taskflow)
		}
		_ = bus.dao.DoEmitWsClient(ctx, ts.To.ClientID, re, cancel)
		sendDMFinishToken(ctx, ts, req, rsp, out, l, optionCards) // token 使用量统计
		processReference(ctx, out, ts, rsp)
	}
	if *out == OutputSecondPrepare {
		*out = OutputSecond // 替换RequestId完成，前端可以正常显示2条
		clues.AddT(ctx, "processDisplay.*out.UPDATE", "OutputSecond")
		// 当 dm 第二个包, 只有单包, 无流式下一个包时, 做一次补偿下发。这里有可能一次性 两个消息一起返回且final
		if rsp.GetIsFinal() {
			clues.AddT(ctx, "dm.GetSemanticReply.final", "Responds[1].single")
			lastEvil = processDisplay(ctx, ts, req, rsp, throttleCheck, throttleStreaming, out, t)
			return lastEvil
		}
	}
	return lastEvil
}

func processReference(ctx context.Context, out *OutputStage, bs *botsession.BotSession, rsp *KEP_DM.GetSemanticReply) {
	if (*out == OutputSecondPrepare && len(rsp.Responds) > 0 && len(rsp.Responds[0].References) > 0) ||
		(rsp.IsFinal && len(rsp.Responds) == 1 && len(rsp.Responds[0].References) > 0) {
		refs := getReferences(rsp.Responds[0].References)
		re2 := &event.ReferenceEvent{
			RecordID:   bs.TokenStat.RecordID,
			References: refs,
		}
		ctx, cancel := context.WithCancel(ctx)
		_ = bus.dao.DoEmitWsClient(ctx, bs.To.ClientID, re2, cancel)
		clues.AddTrackData(ctx, "processDisplay.EmitWsUser", map[string]any{
			"To.CorpStaffID": bs.To.CorpStaffID, "To.Type": bs.To.Type, "ReferenceEvent": re2,
		})
	}
}

func updateReference(ctx context.Context, bs *botsession.BotSession, rsp *KEP_DM.GetSemanticReply) {
	if rsp.IsFinal && len(rsp.Responds) == 1 && len(rsp.Responds[0].References) > 0 {
		refs := getReferences(rsp.Responds[0].References)
		err := bus.dao.UpdateMsgRecordRef(ctx, bs.RecordID, refs, bs.App.GetAppBizId())
		clues.AddTrackE(ctx, "dao.UpdateMsgRecordRef", bs.RecordID, err)
	}
}

func processReply(ctx context.Context, ts *botsession.BotSession,
	rsp *KEP_DM.GetSemanticReply, out *OutputStage) (reply string) {
	if rsp.HitType == KEP_DM.HitType_FAQ && len(rsp.HitSimilarQuestions) != 0 {
		for _, v := range ts.QuestionAnswers {
			if v[0] == rsp.HitSimilarQuestions[0] {
				reply = v[1]
			}
		}
		if reply == "" && len(ts.QuestionAnswers) > 0 { // 大模型可能返回的QA  不再传入的里面，这里取top1 作为输出
			reply = ts.QuestionAnswers[0][1] // 取top1的answer
		}
	} else if len(rsp.Responds) != 0 {
		reply = rsp.Responds[0].Content
	}
	if len(rsp.Responds) == 2 && *out == OutputNormal {
		*out = OutputFirstDone
		clues.AddT(ctx, "processReply.*.out.UPDATE", "OutputFirstDone")
	} else if len(rsp.Responds) == 2 && *out == OutputSecond {
		// bus.dao.StopSilenceDetect(ctx, ts.Session.SessionID) // 拉回，停止静默
		reply = rsp.Responds[1].Content
	}
	reply = replacePlaceholders(reply, ts.Placeholders) // 可能包含占位符的场景
	return reply
}

func sendDMFinishToken(ctx context.Context, ts *botsession.BotSession,
	req *KEP_DM.GetSemanticRequest, rsp *KEP_DM.GetSemanticReply, out *OutputStage, l int, optionCards []string) {
	if !rsp.IsFinal {
		return
	}
	clues.AddT(ctx, "final.throttleStreaming.Hit", clues.M{"l": l, "rsp.Responds.len": len(rsp.Responds)})
	clues.AddT(ctx, "processDisplay.final.optionCards", optionCards)
	if *out == OutputNormal { // dm 回包, 只有一个 Respond 时, 进入此分支
		clues.AddT(ctx, "sendDMFinishToken.final.*out", "OutputNormal")
		sendFinishToken(ctx, ts, ts.TokenStat, req, rsp, 0)
	} else if *out == OutputSecond {
		clues.AddT(ctx, "sendDMFinishToken.final.*out", "OutputSecond")
		sendFinishToken(ctx, ts, ts.TokenStat2, req, rsp, 1)
	}
}

func cluseReplyMethod(ctx context.Context, rsp *KEP_DM.GetSemanticReply, replyMethod model.ReplyMethod) {
	if rsp.GetIsFinal() {
		clues.AddTrackData(ctx, "processDisplay.final.getReplyMethod", replyMethod)
	}
}

func checkTaskEvil(ctx context.Context, bs *botsession.BotSession,
	throttleCheck helper.Throttle,
	l int, rsp *KEP_DM.GetSemanticReply, reply string) (isEvil bool, lastEvil *infosec.CheckRsp) {
	appBizID := strconv.FormatUint(bs.App.AppBizId, 10)
	if bs.NeedCheck && throttleCheck.Hit(l, rsp.IsFinal) {
		// 获取bizType
		corpRsp, _ := bus.dao.GetCorp(ctx, bs.RelatedRecordID, bs.App.CorpId)
		bizType := "QD_AI_TEXT"
		if corpRsp != nil {
			bizType = corpRsp.GetInfosecBizType()
		}
		if len(bs.App.GetInfosecBizType()) != 0 {
			bizType = bs.App.GetInfosecBizType()
		}
		cr := &infosec.CheckReq{
			User:     &infosec.CheckReq_User{Uin: appBizID, AccountType: uint32(ispkg.AccountTypeOther)},
			Id:       bs.TokenStat.RecordID,
			Source:   "chat",
			PostTime: time.Now().Unix(),
			Type:     uint32(ispkg.CheckTypeText),
			Content:  reply,
			BizType:  bizType,
		}
		evil, err := bus.dao.BatchRequestCheck(ctx, cr, model.InfoSecCheckMaxContentLength)
		clues.AddTrackE(ctx, "checkTaskEvil", clues.M{"cr": cr, "evil": evil}, err)
		if err == nil {
			for _, v := range evil {
				lastEvil = v
				if lastEvil.GetResultCode() == ispkg.ResultEvil {
					return true, lastEvil
				}
			}
		}
	}
	return false, lastEvil
}

func getReplyMethod(rsp *KEP_DM.GetSemanticReply) model.ReplyMethod {
	replyMethod := model.ReplyMethodModel
	if (rsp.HitType == KEP_DM.HitType_Intent || rsp.HitType == KEP_DM.HitType_IntentClarification) &&
		len(rsp.Responds) != 0 && rsp.Responds[0].HitIntent != nil &&
		rsp.Responds[0].HitIntent.CurNodeType == KEP_DM.FlowNodeType_ANSWER {
		replyMethod = model.ReplyMethodTaskAnswer
	} else if rsp.HitType == KEP_DM.HitType_Intent || rsp.HitType == KEP_DM.HitType_IntentClarification {
		replyMethod = model.ReplyMethodTaskFlow
	} else if rsp.HitType == KEP_DM.HitType_FAQ {
		replyMethod = model.ReplyMethodPriorityQA
	}

	return replyMethod
}

func getTaskFlowDebugInfo(queryType KEP_DM.QueryType, dmReply *KEP_DM.GetSemanticReply,
	index uint8) *event.TaskFlowDebugInfo {
	if len(dmReply.Responds) == 0 || dmReply.Responds[index].HitIntent == nil {
		return &event.TaskFlowDebugInfo{}
	}
	rsp := &event.TaskFlowDebugInfo{
		TaskFlowName: dmReply.Responds[index].HitIntent.IntentName,
		TaskFlowID:   dmReply.Responds[index].HitIntent.TaskFlowID,
		QueryRewrite: "",
		HitIntent:    dmReply.Responds[index].HitIntent.IntentName,
		SlotInfo:     dmReply.Responds[index].HitIntent.SlotValues,
		APIResponse:  dmReply.Responds[index].HitIntent.APIResponseValues,
		Type:         event.ResponseTypeLLM,
	}

	if len(dmReply.Responds) != 0 && dmReply.Responds[0].Type == KEP_DM.RespondType_RT_CUSTOM {
		rsp.Type = event.ResponseTypeCustom
	} // 高优先级

	if queryType == KEP_DM.QueryType_SwitchToLastIntent || index == 1 { // 返回两个结果，一定是拉回。
		rsp.Type = event.ResponseTypeSwitch
	}
	if queryType == KEP_DM.QueryType_SilenceTimeout {
		rsp.Type = event.ResponseTypeSilence
	}
	return rsp
}

func getOptionCards(rsp *KEP_DM.GetSemanticReply, index int) []string {
	optionCards := make([]string, 0)
	if !rsp.IsFinal || len(rsp.Responds) == 0 {
		return optionCards
	}
	if len(rsp.Responds) <= index {
		return optionCards
	}
	r := rsp.Responds[index]
	if len(r.RespondActions) == 0 {
		return optionCards
	}
	for _, action := range r.RespondActions {
		if action.Type == KEP_DM.RespondActionType_OptionCards {
			_ = json.Unmarshal([]byte(action.Data), &optionCards)
			break
		}
	}
	return optionCards
}

func getReferences(ref []*KEP_DM.Reference) []model.Reference {
	res := make([]model.Reference, 0)
	for _, v := range ref {
		res = append(res, model.Reference{
			ID:    v.GetID(),
			Type:  v.GetType(),
			URL:   v.GetUrl(),
			DocID: v.GetDocID(),
			Name:  v.GetName(),
		})
	}
	return res
}

// processFinish 处理结束流程
func processFinish(ctx context.Context, bs *botsession.BotSession) {
	yes := bus.dao.IsInTaskFlow(ctx, bs.Session.SessionID)
	clues.AddT(ctx, "processFinish.dao.IsInTaskFlow", map[string]any{"sessionID": bs.Session.SessionID, "yes": yes})
	if yes { // 拉回
		// 请求DM，任务型拉回话术
		log.DebugContextf(ctx, "QueryType_SwitchToLastIntent")
		newBs := bs.CopyBotSession()
		clues.AddT(ctx, "processFinish.yes.ts.RecordID", newBs.TokenStat.RecordID)
		// go func() {
		//	//  上面processSilence也是一个协程，有可能会执行的比这里慢，先临时加个sleep
		//	time.Sleep(1 * time.Second)
		//	_ = bus.dao.StopSilenceDetect(trpc.CloneContext(ctx), newBs.Session.SessionID)
		// }() // 触发拉回 停止静默
		newBs.QueryType = KEP_DM.QueryType_SwitchToLastIntent
		_, _ = taskReply(ctx, newBs, []string{})
		if bs.DebugMessage == nil {
			dosageMergeReport(ctx, newBs.TokenStat, bs.App, bus.dao)
		}
	}
}
