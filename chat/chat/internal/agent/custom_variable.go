package agent

import (
	"context"
	"fmt"
	"strconv"

	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/agent_config_server"
	plugin "git.woa.com/dialogue-platform/lke_proto/pb-protocol/plugin_config_server"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/eventbus/botsession"
	"git.woa.com/ivy/qbot/qbot/chat/pkg"
)

// getLabelValue 获取标签值
func (a *ChatAgentImpl) getLabelValue(
	ctx context.Context, bs *botsession.BotSession,
	input *agent_config_server.Input) []string {
	switch input.GetInputType() {
	case agent_config_server.InputSourceEnum_USER_INPUT:
		return input.GetUserInputValue().GetValues()
	case agent_config_server.InputSourceEnum_CUSTOM_VARIABLE:
		{
			rsp := a.dao.GetVarList(ctx, &KEP.GetVarListReq{
				AppBizId: strconv.FormatUint(bs.App.GetAppBizId(), 10),
				VarIds:   []string{input.GetCustomVarId()},
				Offset:   0,
				Limit:    1,
			})
			if len(rsp) == 0 {
				log.ErrorContext(ctx, "API参数 varid(%s) 未找到", input.GetCustomVarId())
				return nil
			}
			if v, ok := bs.CustomVariables[rsp[0].VarName]; ok {
				return []string{v}
			} else {
				log.ErrorContextf(ctx, "用户未配置自定义API参数 %s", rsp[0].VarName)
				return nil
			}
		}
	default:
		log.WarnContextf(ctx, "unsupport input.GetInputType(): %s", input.GetInputType().String())
	}
	return nil
}

// getCompatibleHeaderValue 兼容的方式获取 header 的值
func (a *ChatAgentImpl) getCompatibleHeaderValue(
	ctx context.Context, bs *botsession.BotSession,
	header *agent_config_server.AgentPluginHeader) (
	defaultValue string, err error) {
	has := false
	if header.GetInput() != nil {
		switch header.GetInput().InputType {
		case agent_config_server.InputSourceEnum_USER_INPUT:
			if len(header.GetInput().GetUserInputValue().Values) > 0 {
				defaultValue = header.GetInput().GetUserInputValue().Values[0]
				has = true
			}
		case agent_config_server.InputSourceEnum_CUSTOM_VARIABLE:
			rsp := a.dao.GetVarList(ctx, &KEP.GetVarListReq{
				AppBizId: strconv.FormatUint(bs.App.GetAppBizId(), 10),
				VarIds:   []string{header.GetInput().GetCustomVarId()},
				Offset:   0,
				Limit:    1,
			})
			if len(rsp) == 0 {
				err := fmt.Errorf("API参数 varid(%s) 未找到", header.GetInput().GetCustomVarId())
				log.ErrorContext(ctx, err)
				return defaultValue, errs.New(pkg.ErrCustomVariableErrorID, err.Error())
			}
			if v, ok := bs.CustomVariables[rsp[0].VarName]; ok {
				defaultValue = v
				has = true
			} else {
				err := fmt.Errorf("用户未配置自定义API参数 %s", rsp[0].VarName)
				log.ErrorContext(ctx, err)
				return defaultValue, errs.New(pkg.ErrCustomVariableErrorID, err.Error())
			}
		}
	}
	if !has {
		// 兼容旧的 DefaultValue 数据
		defaultValue = header.GetParamValue()
	}
	return defaultValue, nil
}

// getCompatibleDefaultValue 兼容的方式获取 input 参数默认值
func (a *ChatAgentImpl) getCompatibleDefaultValue(
	ctx context.Context, bs *botsession.BotSession,
	input *agent_config_server.AgentToolReqParam) (
	defaultValue string, err error) {
	has := false
	if input.GetInput() != nil {
		switch input.GetInput().InputType {
		case agent_config_server.InputSourceEnum_USER_INPUT:
			{
				if input.GetInput().GetUserInputValue() != nil && len(input.GetInput().GetUserInputValue().Values) > 0 {
					defaultValue = input.GetInput().GetUserInputValue().Values[0]
					has = true
				}
			}
		case agent_config_server.InputSourceEnum_CUSTOM_VARIABLE:
			{
				rsp := a.dao.GetVarList(ctx, &KEP.GetVarListReq{
					AppBizId: strconv.FormatUint(bs.App.GetAppBizId(), 10),
					VarIds:   []string{input.GetInput().GetCustomVarId()},
					Offset:   0,
					Limit:    1,
				})
				if len(rsp) == 0 {
					err := fmt.Errorf("API参数 varid %s 未找到", input.GetInput().GetCustomVarId())
					log.ErrorContext(ctx, err)
					return defaultValue, errs.New(pkg.ErrCustomVariableErrorID, err.Error())
				}
				if v, ok := bs.CustomVariables[rsp[0].VarName]; ok {
					has = true
					defaultValue = v
				} else {
					err := fmt.Errorf("用户未配置自定义API参数 %s", rsp[0].VarName)
					log.ErrorContext(ctx, err)
					return defaultValue, errs.New(pkg.ErrCustomVariableErrorID, err.Error())
				}
			}
		}
	}
	if !has {
		// 兼容旧的 DefaultValue 数据
		defaultValue = input.GetDefaultValue()
	}
	return defaultValue, nil
}

// doVariableReplace 叶子结点的替换
func (a *ChatAgentImpl) doVariableReplace(ctx context.Context, bs *botsession.BotSession,
	input *agent_config_server.AgentToolReqParam, callParams map[string]interface{}) error {
	// 参数对模型不可见，可能有默认值，用默认值或者引用替换
	defaultValue, err := a.getCompatibleDefaultValue(ctx, bs, input)
	if err != nil {
		log.ErrorContextf(ctx, "getCompatibleDefaultValue error: %v", err)
		return err
	}
	if defaultValue != "" {
		callParams[input.GetName()] = HandleDeDefaultValue(input.GetType(), defaultValue)
	}
	return nil
}

func (a *ChatAgentImpl) dfsReplaceObjectArray(ctx context.Context, bs *botsession.BotSession,
	input *agent_config_server.AgentToolReqParam, callParams map[string]interface{},
	parentHidden bool) error {
	// 需要对每个 OBJECT 对象递归处理
	if v, has := callParams[input.GetName()]; has {
		// 如果模型提取出来参数
		if subMaps, ok := v.([]interface{}); ok {
			// 正常应该可以转换
			for _, subMap := range subMaps {
				if subMap != nil {
					if sub, ok := subMap.(map[string]interface{}); ok {
						if err := a.dfsReplaceDefaultValue(ctx, bs, input.GetSubParams(), sub,
							parentHidden || input.GetAgentHidden()); err != nil {
							return err
						}
					} else {
						// 这里不报错，只打印 log
						log.ErrorContextf(ctx, "function call argument not match input schema, argument: %s, schema: %s",
							helper.Object2String(subMap), helper.Object2String(input))
					}
				}
			}
		} else {
			// 这里不报错，只打印 log
			log.ErrorContextf(ctx, "function call argument not match input schema, argument: %s, schema: %s",
				helper.Object2String(v), helper.Object2String(input))
		}
	}
	// 如果模型没有提取出来值，那么对于 object 数组来说，也无法手动构建默认值，如果要构建数组长度应该为多少？
	return nil
}

func (a *ChatAgentImpl) dfsReplaceObject(ctx context.Context, bs *botsession.BotSession,
	input *agent_config_server.AgentToolReqParam, callParams map[string]interface{},
	parentHidden bool) error {
	// 需要递归处理
	if v, has := callParams[input.GetName()]; has {
		// 如果模型提取出来参数
		if subMap, ok := v.(map[string]interface{}); ok {
			// 正常应该可以转换
			if err := a.dfsReplaceDefaultValue(ctx, bs, input.GetSubParams(),
				subMap, parentHidden || input.GetAgentHidden()); err != nil {
				return err
			}
		} else {
			// 这里不报错，只打印 log
			log.ErrorContextf(ctx, "function call argument not match input schema, argument: %s, schema: %s",
				helper.Object2String(v), helper.Object2String(input))
		}
	} else {
		// 如果模型没有提取出来参数，尝试构建新的
		newMap := map[string]interface{}{}
		if err := a.dfsReplaceDefaultValue(ctx, bs, input.GetSubParams(), newMap,
			parentHidden || input.GetAgentHidden()); err != nil {
			return err
		}
		if len(newMap) > 0 {
			callParams[input.GetName()] = newMap
		}
	}
	return nil
}

// dfsReplaceDefaultValue 递归的替换工具默认值，注意子节点模型可见性要考虑父节点的可见性
func (a *ChatAgentImpl) dfsReplaceDefaultValue(ctx context.Context, bs *botsession.BotSession,
	inputs []*agent_config_server.AgentToolReqParam, callParams map[string]interface{},
	parentHidden bool) error {

	for _, input := range inputs {
		if input == nil {
			continue
		}
		switch input.Type {
		case plugin.TypeEnum_OBJECT:
			{
				if err := a.dfsReplaceObject(ctx, bs, input, callParams, parentHidden); err != nil {
					return err
				}

			}
		case plugin.TypeEnum_ARRAY_OBJECT:
			{
				if err := a.dfsReplaceObjectArray(ctx, bs, input, callParams, parentHidden); err != nil {
					return err
				}
			}
		default:
			{
				if !parentHidden && !input.GetAgentHidden() {
					// 如果父节点模型可见，并且当前节点模型也可见，不处理默认值
					continue
				}
				if err := a.doVariableReplace(ctx, bs, input, callParams); err != nil {
					return err
				}
			}
		}
	}
	return nil
}

// generalReplaceDefaultValue 替换默认值，只处理第一层参数
func (a *ChatAgentImpl) generalReplaceDefaultValue(ctx context.Context, bs *botsession.BotSession,
	inputs []*agent_config_server.AgentToolReqParam, callParams map[string]interface{}) error {

	for _, input := range inputs {
		if input == nil {
			continue
		}
		if !input.GetAgentHidden() {
			// 如果当前节点模型可见，不处理默认值
			continue
		}
		if err := a.doVariableReplace(ctx, bs, input, callParams); err != nil {
			return err
		}
	}
	return nil
}
