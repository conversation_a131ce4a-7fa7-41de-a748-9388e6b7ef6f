package agent

import (
	"context"
	"fmt"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/agent_config_server"
	"git.woa.com/dialogue-platform/proto/pb-stub/openapi"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	"git.woa.com/ivy/qbot/qbot/chat/internal/eventbus/botsession"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
	jsoniter "github.com/json-iterator/go"
)

// wrapKBAgentCtx 封装KBAgent上下文
func (a *ChatAgentImpl) wrapKBAgentCtx(
	ctx context.Context, bs *botsession.BotSession, agentName string,
) *botsession.KBAgentPromptData {
	kbAgentCtx := botsession.NewKBAgentPromptData(bs.OriginContent)
	instructionMap := a.getKBAgentInstructions(ctx, agentName)
	tools := a.getKBAgentTools(ctx, bs)
	kbAgentCtx.KBAgentToolsDescription = helper.RenderKBAgentTools(tools)
	kbAgentCtx.ProjectRequests = a.wrapProjectRequests(ctx, instructionMap)
	kbAgentCtx.Examples = a.wrapExamples(ctx, instructionMap)
	kbAgentCtx.HistoryMessages = a.wrapHistoryMessages(ctx)
	kbAgentCtx.DatabaseInfo = a.wrapKBAgentSchema(ctx, instructionMap)

	log.InfoContextf(ctx, "wrapKBAgentCtx done. res is: %s", helper.Object2StringEscapeHTML(kbAgentCtx))
	return kbAgentCtx
}

// getKBAgentPrompt 获取KBAgent Prompt
func (a *ChatAgentImpl) getKBAgentPrompt(ctx context.Context, bs *botsession.BotSession) string {
	m := bs.App.GetModel(ctx, model.AppTypeKnowledgeQA, model.ModelTypeKBAgent)
	log.InfoContextf(ctx, "KBAgent Prompt:%s", m.GetPrompt())
	return m.GetPrompt()
}

// getKBAgentInstructions 获取KBAgent指令
func (a *ChatAgentImpl) getKBAgentInstructions(
	ctx context.Context, agentName string,
) *agent_config_server.AgentInstructionsForKbAgent {
	kbAgentInstructions := &agent_config_server.AgentInstructionsForKbAgent{}
	ins, err := a.agentContainer.GetAgentInstructions(agentName)
	if err != nil {
		log.ErrorContextf(ctx, "getKBAgentInstructions error: %s", err.Error())
	}
	jsoniter.UnmarshalFromString(ins, kbAgentInstructions)
	log.InfoContextf(ctx, "getKBAgentInstructions done. res is: %s",
		helper.Object2StringEscapeHTML(kbAgentInstructions))
	return kbAgentInstructions
}

// wrapProjectRequests 封装项目请求
func (a *ChatAgentImpl) wrapProjectRequests(
	ctx context.Context, ins *agent_config_server.AgentInstructionsForKbAgent,
) (projectRequests string) {
	userRequest := ins.RetrieveRule

	sysRequest := ""
	for i, r := range config.GetAgentConfig().KBAgentConfig.SysRequest {
		sysRequest += fmt.Sprintf("%d. %s\n", i+1, r)
	}
	projectRequests = fmt.Sprintf("%s\n###任务要求\n%s\n", sysRequest, userRequest)
	log.InfoContextf(ctx, "projectRequests:%s", projectRequests)
	return projectRequests
}

// wrapExamples 封装示例
func (a *ChatAgentImpl) wrapExamples(
	ctx context.Context, ins *agent_config_server.AgentInstructionsForKbAgent,
) (examples string) {
	example := ins.GetExampleQa()
	if example == "" {
		return config.GetAgentConfig().KBAgentConfig.DefaultExample
	}
	examples = helper.ExtractJSON(example)
	log.InfoContextf(ctx, "examples:%s", examples)
	if len(examples) == 0 {
		log.InfoContextf(ctx, "no examples found,user default example")
		return config.GetAgentConfig().KBAgentConfig.DefaultExample
	}
	return examples
}

// wrapKBAgentSchema 封装KBAgent Schema
func (a *ChatAgentImpl) wrapKBAgentSchema(
	ctx context.Context, ins *agent_config_server.AgentInstructionsForKbAgent,
) (schema string) {
	schemas := ins.GetKnowledgeSchemas()

	for i, item := range schemas {
		schema += fmt.Sprintf("%d. DocID：%s，文件名：%s，文件摘要：%s\n", i+1,
			item.GetBusinessId(), item.GetName(), item.GetSummary())
	}

	log.InfoContextf(ctx, "schema:%s", schema)
	return schema
}

// getKBAgentTools 获取KBAgent中的工具 for LLM。
func (a *ChatAgentImpl) getKBAgentTools(ctx context.Context, bs *botsession.BotSession) []*openapi.Tool {
	a.handleKBAgentTools(bs)
	openAPITools := model.GetKBAgentDefaultTools()
	tools, _ := a.agentContainer.GetKBAgentFunctionCalls(ctx, bs.AgentStatus.CurrentAgent, bs)
	openAPITools = append(openAPITools, tools...)
	log.InfoContextf(ctx, "getKBAgentTools: %s", helper.Object2String(openAPITools))
	return openAPITools
}

// handleKBAgentTools 获取所有工具 for LLM
func (a *ChatAgentImpl) handleKBAgentTools(bs *botsession.BotSession) {
	// 非pdl模式，当前插件列表直接从 agent 配置中拿取
	if len(bs.WorkflowID) == 0 {
		currAgent, ok := a.agentContainer.GetSingleAgentByName(bs.AgentStatus.CurrentAgent)

		if ok {
			for _, p := range currAgent.Tools {
				plugin := &agent_config_server.DescribeAppAgentListRsp_AgentPluginInfo{}
				for _, plu := range currAgent.GetPlugins() {
					if plu.GetPluginId() == p.GetPluginId() {
						plugin = plu
					}
				}
				bs.ToolsInfo[p.ToolName] = model.AgentTool{
					AgentToolInfo: p,
					Plugin:        plugin,
				}
			}
		}
	}
}

// AddKBAgentMemory 添加KBAgent记忆
func (a *ChatAgentImpl) AddKBAgentMemory(memory *model.KBAgentMemory) {
	a.KBAgentMemory = append(a.KBAgentMemory, memory)
}

// ActionOutputData 定义 ActionOutput 的数据结构
type ActionOutputData struct {
	Code int    `json:"Code"`
	Msg  string `json:"Msg"`
	Data struct {
		Answer  string `json:"Answer"`
		Thought string `json:"Thought"`
	} `json:"Data"`
}

// wrapHistoryMessages 封装历史消息
func (a *ChatAgentImpl) wrapHistoryMessages(
	ctx context.Context,
) (historyMessages string) {
	historyMessages = ""
	for i, item := range a.KBAgentMemory {
		historyMessages += fmt.Sprintf("检索步骤%d: Thought: %s", i+1, item.Thought)
		historyMessages += fmt.Sprintf("\nAction: %s", item.Action)
		historyMessages += fmt.Sprintf("\nActionInput: %s", item.ActionInput)

		// 解析 ActionOutput 并提取 Answer
		answer := a.extractAnswerFromActionOutput(ctx, item.ActionOutput)
		historyMessages += fmt.Sprintf("\nActionOutput: %s\n", answer)
	}

	if len(historyMessages) == 0 {
		historyMessages = "无"
	}

	log.InfoContextf(ctx, "historyMessages:%s", historyMessages)
	return historyMessages
}

// extractAnswerFromActionOutput 从 ActionOutput 中提取 Answer 字段
func (a *ChatAgentImpl) extractAnswerFromActionOutput(ctx context.Context, actionOutput string) string {
	var outputData ActionOutputData

	// 尝试解析 JSON
	if err := jsoniter.Unmarshal([]byte(actionOutput), &outputData); err != nil {
		log.WarnContextf(ctx, "解析 ActionOutput JSON 失败: %v, 原始数据: %s", err, actionOutput)
		return actionOutput // 解析失败时返回原始数据
	}

	// 检查返回码是否正常
	if outputData.Code != 0 {
		log.WarnContextf(ctx, "ActionOutput 返回异常状态码: %d, 消息: %s", outputData.Code, outputData.Msg)
	}
	if outputData.Data.Answer == "" {
		log.WarnContextf(ctx, "ActionOutput 中的 Answer 字段为空")
		return actionOutput // 如果 Answer 为空，返回原始数据
	}

	return outputData.Data.Answer
}
