package agent

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/agent_config_server"
	codeRun "git.woa.com/dialogue-platform/lke_proto/pb-protocol/code_interpreter_dispatcher"
	plugin "git.woa.com/dialogue-platform/lke_proto/pb-protocol/plugin_config_server"
	pluginRun "git.woa.com/dialogue-platform/lke_proto/pb-protocol/plugin_exec_server"
	llmm "git.woa.com/dialogue-platform/proto/pb-stub/llm-manager-server"
	openapipb "git.woa.com/dialogue-platform/proto/pb-stub/openapi"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	"git.woa.com/ivy/qbot/qbot/chat/internal/eventbus/botsession"
	"git.woa.com/ivy/qbot/qbot/chat/internal/metrics"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model/event"
	"git.woa.com/ivy/qbot/qbot/chat/pkg"
	"git.woa.com/ivy/qbot/qbot/chat/pkg/agentlogger"
	jsoniter "github.com/json-iterator/go"
)

// needUseTool 判断是否需要调用工具
func (a *ChatAgentImpl) needUseTool(rsp *llmm.Response) bool {
	for _, toolCall := range rsp.GetMessage().ToolCalls {
		if !model.IsDefaultTools(toolCall.GetFunction().GetName()) { // 只要不是默认工具就需要调用
			// log.Debugf("tool name: %s, tool arguments: %s",
			//	toolCall.GetFunction().GetName(), toolCall.GetFunction().GetArguments())
			return true
		}
	}
	return false
}

// processAgentTools 处理智能体的工具
func (a *ChatAgentImpl) processAgentTools(
	ctx context.Context,
	bs *botsession.BotSession,
	rsp *llmm.Response,
) (isEvil, isFinish bool, err error) {
	tik := time.Now()
	call := rsp.GetMessage().GetToolCalls()[0]
	toolName := call.GetFunction().GetName()

	// 1. Workflow 分支
	if bs.AgentStatus.AgentType == model.AgentTypeWorkflow {
		return a.processWorkflowTools(ctx, bs, rsp)
	}

	// 2. 无效工具拦截
	localMap := a.agentContainer.GetLocalTools(bs.AgentStatus.CurrentAgent)
	if a.processInvalidTools(ctx, bs, toolName, localMap, rsp) {
		return false, false, nil
	}

	// 3. 初始化执行流程
	proc, _ := a.initProcedure(ctx, bs, rsp, toolName, call)

	// 4. 本地工具拦截
	if hit := a.hitLocalTool(ctx, bs, rsp); hit {
		proc.Debugging.Content = "中断运行，等待本地工具执行……"
		a.addProcedure(proc)
		a.ThoughtEventReply(ctx, bs)
		return false, true, a.interruptRepley(ctx, bs, rsp)
	}

	// 5. 工具执行
	toolResp, status := a.executeTool(ctx, bs, call, tik)
	if proc.Debugging.DisplayType == event.DisplayTypeSandbox {
		proc.Debugging.SandboxURL = a.SandboxURL
		proc.Debugging.DisplayURL = a.DisplayURL
	}
	proc.Status = event.ProcedureStatusSuccess
	if !status {
		proc.Status = event.ProcedureStatusFailed
	}

	// 6. 特殊展示处理
	proc = a.specialDisplayTools(ctx, proc, call, toolResp)
	a.updateThoughtProcedure(proc)
	a.ThoughtEventReply(ctx, bs)

	// 7. 输出过滤与记忆更新
	err = a.finalizeResponse(ctx, bs, toolName, call.GetId(), toolResp)
	return false, false, err
}

// initProcedure 构造并上报初始 Procedure
func (a *ChatAgentImpl) initProcedure(
	ctx context.Context,
	bs *botsession.BotSession,
	rsp *llmm.Response,
	toolName string,
	call *openapipb.ToolCall,
) (event.AgentProcedure, model.AgentTool) {
	// 查找工具信息
	tool, ok := findTool(ctx, bs.ToolsInfo, toolName)
	if !ok {
		tool = model.AgentTool{AgentToolInfo: &agent_config_server.AgentToolInfo{}}
	}
	icon := config.AgentConfig.WorkflowIcon
	if ok && tool.IconUrl != "" {
		icon = tool.IconUrl
	}

	// 构造状态
	status := a.GenerateDisplayStatus(tool.PluginName, toolName, call.GetFunction().GetArguments())
	proc := event.AgentProcedure{
		Index:  0,
		Name:   toolName,
		Title:  tool.PluginName + "/" + toolName,
		Status: event.ProcedureStatusProcessing,
		Icon:   icon,
		Debugging: event.AgentProcedureDebugging{
			DisplayType:   a.GenerateDisplayType(tool.PluginName, toolName),
			DisplayStatus: status,
			DisplayURL:    a.DisplayURL,
		},
		PluginType: int32(tool.PluginType),
	}
	if isSearchTool(toolName) {
		proc.Debugging.DisplayType = event.DisplayTypeSearch
	}
	if toolName == ToolNameKnowledge {
		proc.Debugging.DisplayType = event.DisplayTypeKnowledge
	}

	a.addProcedure(proc)
	a.ThoughtEventReply(ctx, bs)
	a.processAgentMemory(bs, rsp)
	return proc, tool
}

// executeTool 根据工具类型分流并执行，带成本上报
func (a *ChatAgentImpl) executeTool(
	ctx context.Context,
	bs *botsession.BotSession,
	call *openapipb.ToolCall,
	tik time.Time,
) (string, bool) {

	toolName := call.GetFunction().GetName()
	tool, ok := findTool(ctx, bs.ToolsInfo, toolName)
	if ok && tool.GetCallingMethod() == agent_config_server.CallingMethodTypeEnum_STREAMING.String() {
		return a.processStreamTools(ctx, bs, call)
	}

	switch toolName {
	case ToolNameKnowledge, ToolNameHunYuanSearch, ToolNameBoundedKnowledgeQA,
		ToolNameDeepSeekR1, ToolNameDeepSeekV3,
		ToolNameImageQuiz, ToolNameImageUnderstand:
		return a.processStreamTools(ctx, bs, call)
	default:
		resp, status := a.invokeTool(ctx, bs, call)
		metrics.ReportToolCost(ctx, toolName, time.Since(tik).Milliseconds())
		return resp, status
	}
}

// finalizeResponse 过滤输出、替换 URL 占位符，并更新记忆
func (a *ChatAgentImpl) finalizeResponse(
	ctx context.Context,
	bs *botsession.BotSession,
	toolName, callID, resp string,
) error {
	// 隐藏敏感或多余字段
	for _, t := range bs.ToolsInfo {
		if t.GetToolName() == toolName && len(t.Outputs) > 0 {
			m := helper.Arguments2Map(resp)
			if len(m) > 0 {
				m = helper.HiddenSubParams(t.GetOutputs(), m)
				data, _ := json.Marshal(m)
				resp = string(data)
			}
			break
		}
	}
	log.InfoContextf(ctx, "processAgentTools -- toolResponse:%v", resp)

	// 更新记忆
	if bs.AgentStatus.AgentType != model.AgentTypeKBAgent { // KBAgent 的记忆 不存储在这里
		a.updateAgentMemory(ctx, callID, resp)
	}

	return nil
}

// processInvalidTools 处理无效的工具
func (a *ChatAgentImpl) processInvalidTools(ctx context.Context,
	bs *botsession.BotSession, toolName string, localToolsMap event.AgentTool, rsp *llmm.Response) (isInvalid bool) {

	if len(bs.ToolsInfo) == 0 && len(localToolsMap.AgentName) == 0 {
		// 当前 agent 没有插件列表，但是模型却给出了函数调用，直接返回不处理插件
		log.WarnContextf(ctx, "processAgentTools ToolsInfo is empty")
		toolResponse := fmt.Sprintf("工具{%s}不存在, 请尝试使用其他工具", toolName)
		procedure := event.AgentProcedure{
			Index:     0,
			Name:      toolName,
			Title:     toolName,
			Status:    event.ProcedureStatusFailed,
			Icon:      config.AgentConfig.WorkflowIcon,
			Debugging: event.AgentProcedureDebugging{Content: toolResponse},
		}
		a.addProcedure(procedure)
		a.ThoughtEventReply(ctx, bs)
		a.processAgentMemory(bs, rsp) // 处理记忆
		a.updateAgentMemory(ctx, rsp.GetMessage().GetToolCalls()[0].GetId(), toolResponse)

		return true
	}

	return false
}

// processWorkflowTools 处理工作流的工具
func (a *ChatAgentImpl) processWorkflowTools(ctx context.Context, bs *botsession.BotSession,
	rsp *llmm.Response) (isEvil, isFinish bool, err error) {

	tik := time.Now()
	toolName := rsp.GetMessage().GetToolCalls()[0].GetFunction().GetName()

	apiInfos := model.ConvertAPIInfo(bs.ToolsInfo[bs.AgentStatus.WorkflowName].APIInfo)
	// 处理思考过程
	procedure := event.AgentProcedure{
		Index:        0,
		Name:         toolName,
		Title:        toolName,
		Status:       event.ProcedureStatusProcessing,
		Icon:         config.AgentConfig.WorkflowIcon,
		Debugging:    event.AgentProcedureDebugging{Content: ""},
		PluginType:   event.PluginTypeWorkflow,
		WorkflowName: bs.AgentStatus.WorkflowName,
	}
	a.addProcedure(procedure)
	a.ThoughtEventReply(ctx, bs)
	a.processAgentMemory(bs, rsp) // 处理记忆
	// 调用工具
	result, status := a.invokeWorkflowNode(ctx, bs, rsp.GetMessage().GetToolCalls()[0], apiInfos)
	metrics.ReportToolCost(ctx, toolName, time.Since(tik).Milliseconds())
	// if err != nil {
	//	procedure.Status = event.ProcedureStatusFailed
	//	procedure.Debugging.Content = err.Error()
	//	a.updateThoughtProcedure(procedure)
	//	a.ThoughtEventReply(ctx, bs)
	//	return false, true, err // 调用工具失败
	// }
	procedure.Status = event.ProcedureStatusSuccess
	if !status {
		procedure.Status = event.ProcedureStatusFailed
	}

	// 把result使用Json格式化一下
	procedure = a.specialDisplayTools(ctx, procedure, rsp.GetMessage().GetToolCalls()[0], result)
	a.updateThoughtProcedure(procedure)
	a.ThoughtEventReply(ctx, bs)
	// 处理memory
	log.DebugContextf(ctx, "tool call id: %s", rsp.GetMessage().GetToolCalls()[0].GetId())
	a.updateAgentMemory(ctx, rsp.GetMessage().GetToolCalls()[0].GetId(), result)
	return false, false, nil
}

// jsonIndent 格式化json
func jsonIndent(ctx context.Context, result string) string {
	var data interface{}
	if err := jsoniter.Unmarshal([]byte(result), &data); err == nil {
		if indent, err := jsoniter.MarshalIndent(data, "", "  "); err == nil {
			result = "```json\n" + string(indent) + "\n```"
		} else {
			log.ErrorContextf(ctx, "processAgentThought: %s", err)
		}
	} else {
		log.ErrorContextf(ctx, "processAgentThought: %s", err)
	}
	return result
}

// invokeTool 调用工具（补齐版）
func (a *ChatAgentImpl) invokeTool(
	ctx context.Context,
	bs *botsession.BotSession,
	tc *openapipb.ToolCall,
) (toolResponse string, status bool) {
	// --- 1. 初始化日志埋点 ---
	step := agentlogger.New(ctx, agentlogger.StepKeyPlugin, "")
	defer func() {
		step.Output = toolResponse
		step.SetResult(nil)
		step.UpsertStep(true)
	}()

	toolName := tc.GetFunction().GetName()
	step.ExtraData = toolName

	// --- 2. 处理 TokenStat & 指标 ---
	proc := event.NewToolProcessingTSProcedure(
		event.ProcedureAgentTool,
		toolName,
		helper.Object2StringEscapeHTML(tc.GetFunction()),
	)
	bs.TokenStat.UpdateAgentProcedure(proc)
	a.SendTokenStat(ctx, bs.To.ClientID, bs.TokenStat)

	// --- 3. 参数归一化 ---
	normalizeArguments(ctx, tc)

	// --- 4. KBAgent Memory 延迟处理 --- 注意闭包，传指针
	defer a.deferKBAgentMemory(ctx, bs, toolName, tc.GetFunction().GetArguments(), &toolResponse)

	// --- 5. 检索工具元信息 ---
	tool, ok := findTool(ctx, bs.ToolsInfo, toolName)
	if !ok {
		toolResponse, status = a.toolUnavailable(step, toolName, proc, bs)
		return toolResponse, status
	}

	// --- 6. MCP 工具分支 ---
	if tool.CreateType == plugin.CreateTypeEnum_MCP {
		log.DebugContextf(ctx, "tool call type MCP: %s", tool.ToolName)
		toolResponse, status = a.invokeMCPTool(ctx, bs, tc, proc, step)
		return toolResponse, status
	}

	// --- 7. 构造普通工具请求 ---
	req := a.buildRunToolReq(
		bs, tc, tool, buildLabels(bs.CustomVariables), a.getHistoryInfo(ctx, bs),
	)
	step.Input = req

	config := MetricsConfig{
		Uin:        pkg.Uin(ctx), // todo
		PluginType: int(tool.PluginType),
		CreateType: int(tool.GetCreateType()),
		ToolName:   toolName,        // 使用传入的toolName
		PluginName: tool.PluginName, // 需要根据实际情况获取
	}

	rsp, err := a.runToolWithMetrics(ctx, req, config)
	if err != nil {
		toolResponse, status = a.toolFailed(step, tool.ToolName, err, proc, bs)
		return toolResponse, status
	}

	// --- 8. 成功分支 ---
	toolResponse = rsp.GetRawResult()
	return a.toolSucceeded(step, rsp.GetRawResult(), toolName, proc, bs)
}

// normalizeArguments 参数归一化：支持 code_interpreter / codeinterpreter 特殊处理
func normalizeArguments(ctx context.Context, tc *openapipb.ToolCall) {
	data := helper.Arguments2Map(tc.Function.Arguments)
	if data == nil {
		return
	}
	if _, ok := data["code"]; ok {
		name := strings.ToLower(tc.GetFunction().GetName())
		if name == "code_interpreter" || name == "codeinterpreter" {
			data["Code"] = data["code"]
			log.DebugContextf(ctx, "invokeTool toolName:%s, data:%s",
				tc.GetFunction().GetName(),
				helper.Object2StringEscapeHTML(data),
			)
			delete(data, "code")
		}
	}
	buf, _ := json.Marshal(data)
	tc.Function.Arguments = string(buf)
}

// buildLabels 构造插件标签
func buildLabels(vars map[string]string) []*pluginRun.Label {
	if len(vars) == 0 {
		return nil
	}
	labels := make([]*pluginRun.Label, 0, len(vars))
	for k, v := range vars {
		labels = append(labels, &pluginRun.Label{
			Name:   k,
			Values: []string{v},
		})
	}
	return labels
}

// toolUnavailable 工具不存在处理
func (a *ChatAgentImpl) toolUnavailable(
	step *agentlogger.Step,
	name string,
	p event.Procedure,
	bs *botsession.BotSession,
) (string, bool) {
	msg := fmt.Sprintf("工具{%s}不可用, 请尝试使用其他工具", name)
	step.Output = msg
	bs.TokenStat.UpdateAgentProcedure(
		event.NewToolFailedTSProcedure(name, msg, p),
	)
	return msg, false
}

// toolFailed 调用失败统一处理
func (a *ChatAgentImpl) toolFailed(
	step *agentlogger.Step,
	name string,
	err error,
	p event.Procedure,
	bs *botsession.BotSession,
) (string, bool) {
	msg := fmt.Sprintf("工具{%s}调用失败: %s", name, err.Error())
	if name == ToolNameCalculator {
		msg = "科学计算执行失败"
	}
	step.Output = msg
	bs.TokenStat.UpdateAgentProcedure(
		event.NewToolFailedTSProcedure(name, msg, p),
	)
	return msg, false
}

// toolSucceeded 调用成功统一处理
func (a *ChatAgentImpl) toolSucceeded(
	step *agentlogger.Step,
	result string,
	name string,
	p event.Procedure,
	bs *botsession.BotSession,
) (string, bool) {
	step.Output = result
	bs.TokenStat.UpdateAgentProcedure(
		event.NewToolSuccessTSProcedure(name, result, p),
	)
	return result, true
}

// buildRunToolReq 构造运行工具的请求数据
func (a *ChatAgentImpl) buildRunToolReq(bs *botsession.BotSession, toolCall *openapipb.ToolCall,
	tool model.AgentTool, labels []*pluginRun.Label, histories []*pluginRun.History) *pluginRun.RunToolReq {
	return &pluginRun.RunToolReq{
		AppId:       strconv.FormatUint(bs.App.GetAppBizId(), 10),
		AppScene:    uint32(bs.Scene),
		PluginId:    tool.PluginId,
		ToolId:      tool.ToolId,
		HeaderValue: "",
		QueryValue:  "",
		BodyValue:   "",
		InputValue:  toolCall.GetFunction().Arguments,
		ExtraInfo: &pluginRun.ToolExtraInfo{
			SessionId: bs.SessionID,
			Labels:    labels,
			History:   histories,
		},
	}
}

// invokeWorkflowNode 调用工作流工具
func (a *ChatAgentImpl) invokeWorkflowNode(ctx context.Context, bs *botsession.BotSession,
	toolCall *openapipb.ToolCall, apiInfos *KEP_WF.PDLToolsInfo) (toolResponse string, status bool) {
	step, status := agentlogger.New(ctx, agentlogger.StepKeyLLM, toolCall), true
	defer func() {
		if len(toolResponse) == 0 {
			toolResponse = fmt.Sprintf("工具{%s}不可用, 请尝试使用其他工具", toolCall.GetFunction().GetName())
		}
		step.Output = toolResponse
		step.SetResult(nil)
		step.UpsertStep(true)
	}()
	for _, tool := range apiInfos.Tools {
		if tool.ToolName == toolCall.GetFunction().GetName() {
			if len(tool.ToolNodes) == 0 {
				return toolResponse, false
			}
			switch tool.ToolNodes[0].NodeType {
			case KEP_WF.NodeType_LLM:
				toolResponse, status = a.invokeWorkflowLLM(ctx, bs, toolCall, tool, step)
			case KEP_WF.NodeType_TOOL:
				toolResponse, status = a.invokeWorkflowTool(ctx, bs, toolCall, tool, step)
			case KEP_WF.NodeType_CODE_EXECUTOR:
				toolResponse, status = a.invokeWorkflowCodeExecutor(ctx, bs, toolCall, tool, step)
			case KEP_WF.NodeType_PLUGIN:
				toolResponse, status = a.invokeWorkflowPlugin(ctx, bs, toolCall, tool, step)
			default:
				log.ErrorContextf(ctx, "invokeWorkflowTool: unknown node type %v", tool.ToolNodes[0].NodeType)
				return toolResponse, false
			}
		}
	}
	return toolResponse, status
}

// invokeWorkflowLLM 调用工作流LLM节点
func (a *ChatAgentImpl) invokeWorkflowLLM(ctx context.Context, bs *botsession.BotSession,
	toolCall *openapipb.ToolCall, tool *KEP_WF.PDLTool, step *agentlogger.Step) (toolResponse string, status bool) {
	step.Key = agentlogger.StepKeyWorkflowLLM
	varMap := a.getWorkflowVariables(ctx, bs, toolCall, tool)

	requestID := model.RequestID(ctx, bs.Session.SessionID, bs.RecordID)
	llmParam := tool.GetToolNodes()[0].GetLLMNodeData()
	modelParams := &model.InferParams{
		TopP:        llmParam.TopP,
		Temperature: &llmParam.Temperature,
	}
	modelParamsStr := helper.Object2String(modelParams)
	allNames, prompt := helper.ReplacePlaceholders(llmParam.Prompt, varMap)
	log.DebugContextf(ctx, "invokeWorkflowLLM: allNames: %v, prompt: %s", allNames)
	messages := make([]*llmm.Message, 0)
	messages = append(messages, &llmm.Message{
		Role:    llmm.Role_USER,
		Content: prompt,
	})

	req := &llmm.Request{
		RequestId:   requestID,
		ModelName:   llmParam.ModelName,
		AppKey:      fmt.Sprintf("%d", bs.App.GetAppBizId()),
		Messages:    messages,
		ModelParams: modelParamsStr,
		PromptType:  llmm.PromptType_TEXT,
		RequestType: llmm.RequestType_ONLINE,
		Biz:         "cs",
	}
	step.Input = req
	step.ExtraData = toolCall.GetFunction().GetName()
	log.InfoContextf(ctx, "invokeWorkflowLLM: %s", helper.Object2StringEscapeHTML(req))
	rsp, err := a.dao.SimpleChat(ctx, req)
	if err != nil {
		toolResponse = fmt.Sprintf("工具{%s}调用失败: %s", toolCall.GetFunction().GetName(), err.Error())
		return toolResponse, false
	}
	step.Output = rsp

	return rsp.GetMessage().GetContent(), true
}

// invokeWorkflowTool 调用工作流工具节点
func (a *ChatAgentImpl) invokeWorkflowTool(ctx context.Context, bs *botsession.BotSession,
	toolCall *openapipb.ToolCall, tool *KEP_WF.PDLTool, step *agentlogger.Step) (toolResponse string, status bool) {
	step.Key = agentlogger.StepKeyWorkflowTool
	defer func() {
		step.Output = toolResponse
		step.SetResult(nil)
		step.UpsertStep(true)
	}()
	header, query, body := a.getToolVariables(ctx, bs, toolCall, tool)
	var err error
	toolParam := tool.GetToolNodes()[0].GetToolNodeData()
	step.ExtraData = toolParam
	if toolParam.API.Method == "GET" {
		toolResponse, err = a.sendGetRequest(ctx, toolParam.API.URL, header, query, body)
	} else if toolParam.API.Method == "POST" {
		toolResponse, err = a.sendPostRequest(ctx, toolParam.API.URL, header, query, body)
	}
	if err != nil {
		return fmt.Sprintf("工具{%s}调用失败: %s", toolCall.GetFunction().GetName(), err.Error()), false
	}
	if toolResponse == "" {
		return fmt.Sprintf("工具{%s}不可用, 请尝试使用其他工具", toolCall.GetFunction().GetName()), false
	}

	return toolResponse, true
}

// invokeWorkflowCodeExecutor 调用工作流代码执行节点
func (a *ChatAgentImpl) invokeWorkflowCodeExecutor(ctx context.Context, bs *botsession.BotSession,
	toolCall *openapipb.ToolCall, tool *KEP_WF.PDLTool, step *agentlogger.Step) (toolResponse string, status bool) {
	step.Key = agentlogger.StepKeyWorkflowCode
	defer func() {
		step.Output = toolResponse
		step.SetResult(nil)
		step.UpsertStep(true)
	}()

	varMap := a.getWorkflowVariables(ctx, bs, toolCall, tool)
	code := tool.GetToolNodes()[0].GetCodeExecutorNodeData().Code
	codeExecutorCtx := botsession.CodeExecutorCtx{
		CodeContent:      code,
		InputParamObject: helper.Object2String(varMap),
	}
	step.Input = codeExecutorCtx
	step.ExtraData = toolCall.GetFunction().GetName()
	prompt, _ := model.Render(ctx, config.AgentConfig.RunCode.CodeTemplate, codeExecutorCtx)
	log.DebugContextf(ctx, "invokeWorkflowCodeExecutor: prompt: %s", prompt)
	toolResponse, err := a.RunCode(ctx, bs.SessionID, prompt)
	if err != nil {
		toolResponse = fmt.Sprintf("工具{%s}调用失败: %s", toolCall.GetFunction().GetName(), err.Error())
		return toolResponse, false
	}
	return toolResponse, true
}

// invokeWorkflowPlugin 调用工作流插件节点
func (a *ChatAgentImpl) invokeWorkflowPlugin(ctx context.Context, bs *botsession.BotSession,
	toolCall *openapipb.ToolCall, tool *KEP_WF.PDLTool, step *agentlogger.Step) (toolResponse string, status bool) {
	step.Key = agentlogger.StepKeyWorkflowPlugin
	defer func() {
		step.Output = toolResponse
		step.SetResult(nil)
		step.UpsertStep(true)
	}()

	toolName := toolCall.GetFunction().GetName()
	p := event.NewToolProcessingTSProcedure(event.ProcedureAgentTool, toolName,
		helper.Object2StringEscapeHTML(toolCall.GetFunction()))

	// 如果是mcp插件，todo
	if tool.GetToolNodes()[0].PluginNodeData.PluginCreateType == KEP_WF.PluginNodeData_MCP {
		pluginID := tool.GetToolNodes()[0].PluginNodeData.PluginID
		toolID := tool.GetToolNodes()[0].PluginNodeData.ToolID
		r, err := a.dao.DescribeTool(ctx, pluginID, toolID) // todo
		if err != nil {
			toolResponse = fmt.Sprintf("工具{%s}调用失败: %s", toolCall.GetFunction().GetName(), err.Error())
			bs.TokenStat.UpdateAgentProcedure(event.NewToolSuccessTSProcedure(toolName, toolResponse, p))
			return toolResponse, false
		}
		realTool := model.AgentTool{
			AgentToolInfo: &agent_config_server.AgentToolInfo{
				ToolName:  toolName,
				McpServer: a.wrapPdlMcpServerInfo(ctx, r),
			},
		}
		realTool.McpServer.Headers = append(realTool.McpServer.Headers, wrapUserHeaders(r.GetHeader())...)
		bs.ToolsInfo[toolName] = realTool
		return a.invokeMCPTool(ctx, bs, toolCall, p, step)
	}

	header, query, body := a.getPluginVariables(ctx, bs, toolCall, tool)
	// SystemRole 映射到多 agent 的 instructions
	systemRole, _ := a.agentContainer.GetAgentInstructions(bs.AgentStatus.CurrentAgent)
	req := &pluginRun.RunToolReq{
		AppId:       strconv.FormatUint(bs.App.GetAppBizId(), 10),
		AppScene:    uint32(bs.Scene),
		PluginId:    tool.GetToolNodes()[0].PluginNodeData.PluginID,
		ToolId:      tool.GetToolNodes()[0].PluginNodeData.ToolID,
		HeaderValue: helper.Object2String(header),
		QueryValue:  helper.Object2String(query),
		BodyValue:   helper.Object2String(body),
		ExtraInfo: &pluginRun.ToolExtraInfo{
			SystemRole: systemRole,
			SessionId:  bs.SessionID,
		},
	}
	step.Input = req
	step.ExtraData = toolCall.GetFunction().GetName()
	rsp, err := a.dao.RunTool(ctx, req)
	if err != nil {
		toolResponse = fmt.Sprintf("工具{%s}调用失败: %s", toolCall.GetFunction().GetName(), err.Error())
		bs.TokenStat.UpdateAgentProcedure(event.NewToolSuccessTSProcedure(toolName, toolResponse, p))
		return toolResponse, false
	}
	bs.TokenStat.UpdateAgentProcedure(event.NewToolSuccessTSProcedure(toolName, toolResponse, p))
	toolResponse = rsp.RawResult
	return toolResponse, true
}

// getToolVariables 获取工具节点变量
func (a *ChatAgentImpl) getToolVariables(ctx context.Context, bs *botsession.BotSession,
	toolCall *openapipb.ToolCall, tool *KEP_WF.PDLTool) (headerMap, queryMap, bodyMap map[string]any) {
	log.InfoContextf(ctx, "getWorkflowVariables toolCall is: %s", helper.Object2String(toolCall))
	log.InfoContextf(ctx, "getWorkflowVariables PDLtool is: %s", helper.Object2String(tool))
	headerMap = make(map[string]any)
	for _, header := range tool.GetToolNodes()[0].ToolNodeData.GetHeader() {
		a.getRequestParam(ctx, toolCall.GetFunction().GetArguments(), bs, header, headerMap)
	}
	log.InfoContextf(ctx, "get Header Variables: %s", helper.Object2String(headerMap))
	queryMap = make(map[string]any)
	for _, query := range tool.GetToolNodes()[0].ToolNodeData.GetQuery() {
		a.getRequestParam(ctx, toolCall.GetFunction().GetArguments(), bs, query, queryMap)
	}
	log.InfoContextf(ctx, "get Query Variables: %s", helper.Object2String(queryMap))
	bodyMap = make(map[string]any)
	for _, body := range tool.GetToolNodes()[0].ToolNodeData.GetBody() {
		a.getRequestParam(ctx, toolCall.GetFunction().GetArguments(), bs, body, bodyMap)
	}
	log.InfoContextf(ctx, "get Body Variables: %s", helper.Object2String(bodyMap))
	return headerMap, queryMap, bodyMap
}

// getPluginVariables 获取插件节点变量
func (a *ChatAgentImpl) getPluginVariables(ctx context.Context, bs *botsession.BotSession,
	toolCall *openapipb.ToolCall, tool *KEP_WF.PDLTool) (headerMap, queryMap, bodyMap map[string]any) {
	log.InfoContextf(ctx, "getPluginVariables toolCall is: %s", helper.Object2String(toolCall))
	log.InfoContextf(ctx, "getPluginVariables PDLtool is: %s", helper.Object2String(tool))
	headerMap = make(map[string]any)
	for _, header := range tool.GetToolNodes()[0].GetPluginNodeData().GetToolInputs().GetHeader() {
		a.getRequestParam(ctx, toolCall.GetFunction().GetArguments(), bs, header, headerMap)
	}
	log.InfoContextf(ctx, "get Header Variables: %s", helper.Object2String(headerMap))
	queryMap = make(map[string]any)
	for _, query := range tool.GetToolNodes()[0].GetPluginNodeData().GetToolInputs().GetQuery() {
		a.getRequestParam(ctx, toolCall.GetFunction().GetArguments(), bs, query, queryMap)
	}
	log.InfoContextf(ctx, "get Query Variables: %s", helper.Object2String(queryMap))
	bodyMap = make(map[string]any)
	for _, body := range tool.GetToolNodes()[0].GetPluginNodeData().GetToolInputs().GetBody() {
		a.getRequestParam(ctx, toolCall.GetFunction().GetArguments(), bs, body, bodyMap)
	}
	log.InfoContextf(ctx, "get Body Variables: %s", helper.Object2String(bodyMap))
	return headerMap, queryMap, bodyMap
}

// getRequestParam 获取请求参数
func (a *ChatAgentImpl) getRequestParam(ctx context.Context, arguments string,
	bs *botsession.BotSession, param *KEP_WF.ToolNodeData_RequestParam, varMap map[string]any) map[string]any {
	if param.GetInput().GetInputType() == KEP_WF.InputSourceEnum_USER_INPUT &&
		len(param.GetInput().GetUserInputValue().GetValues()) > 0 {
		varMap[param.GetParamName()] = param.GetInput().GetUserInputValue().GetValues()[0]
	}
	if param.GetInput().GetInputType() == KEP_WF.InputSourceEnum_REFERENCE_OUTPUT {
		tmp := helper.Arguments2Map(arguments)
		if value, ok := tmp[param.GetParamName()]; ok {
			varMap[param.GetParamName()] = value
		} else {
			varMap[param.GetParamName()] = bs.WorkflowInput[param.GetParamName()]
		}
	}
	if param.GetInput().GetInputType() == KEP_WF.InputSourceEnum_SYSTEM_VARIABLE {
		sysName := param.GetInput().GetSystemVariable().GetName()
		parts := strings.Split(sysName, ".")
		if len(parts) > 1 {
			sysName = parts[1]
		}
		varMap[param.GetParamName()] = model.GetFieldValue(bs.AgentSystemVar, sysName)
	}
	if param.GetInput().GetInputType() == KEP_WF.InputSourceEnum_CUSTOM_VARIABLE {
		varMap[param.GetParamName()] = a.GetVarValue(ctx, bs, param.GetInput().GetCustomVarID())
	}

	if param.GetInput().GetInputType() == KEP_WF.InputSourceEnum_NODE_INPUT_PARAM {

	}

	log.InfoContextf(ctx, "getRequestParam: %s", helper.Object2String(varMap))
	return varMap
}

// getWorkflowVariables 获取工作流变量
func (a *ChatAgentImpl) getWorkflowVariables(ctx context.Context, bs *botsession.BotSession,
	toolCall *openapipb.ToolCall, tool *KEP_WF.PDLTool) map[string]any {
	log.InfoContextf(ctx, "getWorkflowVariables toolCall is: %s", helper.Object2String(toolCall))
	log.InfoContextf(ctx, "getWorkflowVariables PDLtool is: %s", helper.Object2String(tool))
	varMap := make(map[string]any)
	for _, input := range tool.GetToolNodes()[0].GetInputs() {
		if input.GetInput().GetInputType() == KEP_WF.InputSourceEnum_USER_INPUT &&
			len(input.GetInput().GetUserInputValue().GetValues()) > 0 {
			varMap[input.GetName()] = input.GetInput().GetUserInputValue().GetValues()[0]
		}
		if input.GetInput().GetInputType() == KEP_WF.InputSourceEnum_REFERENCE_OUTPUT {
			tmp := helper.Arguments2Map(toolCall.GetFunction().GetArguments())
			if value, ok := tmp[input.GetName()]; ok {
				varMap[input.GetName()] = value
			} else {
				varMap[input.GetName()] = bs.WorkflowInput[input.GetName()]
			}
		}
		if input.GetInput().GetInputType() == KEP_WF.InputSourceEnum_SYSTEM_VARIABLE {
			sysName := input.GetInput().GetSystemVariable().GetName()
			parts := strings.Split(sysName, ".")
			if len(parts) > 1 {
				sysName = parts[1]
			}
			varMap[input.GetName()] = model.GetFieldValue(bs.AgentSystemVar, sysName)
		}
		if input.GetInput().GetInputType() == KEP_WF.InputSourceEnum_CUSTOM_VARIABLE {
			varMap[input.GetName()] = a.GetVarValue(ctx, bs, input.GetInput().GetCustomVarID())
		}

		if input.GetInput().GetInputType() == KEP_WF.InputSourceEnum_NODE_INPUT_PARAM {

		}
	}
	log.InfoContextf(ctx, "getWorkflowVariables: %s", helper.Object2String(varMap))
	return varMap
}

// parseURLAndQuery 解析基础URL并设置查询参数
func (a *ChatAgentImpl) parseURLAndQuery(baseURL string, querys map[string]interface{}) (*url.URL, error) {
	// 1. 解析基础URL
	parsedURL, err := url.Parse(baseURL)
	if err != nil {
		return nil, fmt.Errorf("parse URL failed: %v", err)
	}

	// 2. 拼装查询参数
	queryValues := url.Values{}
	for k, v := range querys {
		switch val := v.(type) {
		case int:
			queryValues.Set(k, strconv.FormatInt(int64(val), 10))
		case int64:
			queryValues.Set(k, strconv.FormatInt(val, 10))
		case float64:
			queryValues.Set(k, strconv.FormatFloat(val, 'f', -1, 64))
		default:
			queryValues.Set(k, fmt.Sprintf("%v", val))
		}
	}
	parsedURL.RawQuery = queryValues.Encode()

	return parsedURL, nil
}

// buildRequest 构造并返回 HTTP 请求
func (a *ChatAgentImpl) buildRequest(
	ctx context.Context,
	method string,
	parsedURL *url.URL,
	headers, bodys map[string]interface{},
) (*http.Request, error) {
	// 1. 将 bodys 序列化为 JSON
	bodyBytes, err := json.Marshal(bodys)
	if err != nil {
		log.WarnContextf(ctx, "json marshal body failed: %v", err)
		// 不因为序列化失败而中断
		bodyBytes = nil
	}

	// 2. 构造 HTTP 请求
	req, err := http.NewRequest(method, parsedURL.String(), bytes.NewReader(bodyBytes))
	if err != nil {
		log.ErrorContextf(ctx, "http.NewRequest error: %v", err)
		return nil, err
	}

	// 3. 设置 Header
	req.Header.Set("Content-Type", "application/json")
	for k, v := range headers {
		req.Header.Set(k, fmt.Sprintf("%v", v))
	}

	log.InfoContextf(ctx, "%s request: %s", method, parsedURL.String())
	return req, nil
}

// doHTTPRequest 执行 HTTP 请求并返回响应字符串
func (a *ChatAgentImpl) doHTTPRequest(
	ctx context.Context,
	method, baseURL string,
	headers, querys, bodys map[string]interface{},
) (string, error) {
	// 1. 解析URL并设置查询参数
	parsedURL, err := a.parseURLAndQuery(baseURL, querys)
	if err != nil {
		log.ErrorContextf(ctx, "parseURLAndQuery failed: %v", err)
		return "", err
	}

	// 2. 构造请求
	req, err := a.buildRequest(ctx, method, parsedURL, headers, bodys)
	if err != nil {
		return "", err
	}

	// 3. 执行请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		log.ErrorContextf(ctx, "request error: %v", err)
		return "", err
	}
	defer resp.Body.Close()

	// 4. 读取响应
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		log.ErrorContextf(ctx, "read response body failed: %v", err)
		return "", err
	}
	log.InfoContextf(ctx, "status: %v, response: %s", resp.Status, string(respBody))

	// 5. 状态码检查
	if resp.StatusCode < http.StatusOK || resp.StatusCode >= http.StatusMultipleChoices {
		return "", fmt.Errorf("request failed, status code: %d, response: %s",
			resp.StatusCode, string(respBody))
	}

	return string(respBody), nil
}

// sendGetRequest 发送一个带有查询参数的 HTTP GET 请求
func (a *ChatAgentImpl) sendGetRequest(
	ctx context.Context,
	baseURL string,
	headers, querys, bodys map[string]interface{},
) (string, error) {
	// 调用公共方法
	return a.doHTTPRequest(ctx, http.MethodGet, baseURL, headers, querys, bodys)
}

// sendPostRequest 发送一个带有请求体的 HTTP POST 请求
func (a *ChatAgentImpl) sendPostRequest(
	ctx context.Context,
	baseURL string,
	headers, querys, bodys map[string]interface{},
) (string, error) {
	// 调用公共方法
	return a.doHTTPRequest(ctx, http.MethodPost, baseURL, headers, querys, bodys)
}

// RunCode 代码执行
func (a *ChatAgentImpl) RunCode(ctx context.Context, sessionID, code string) (rsp string, err error) {
	log.InfoContextf(ctx, "Interpreter Run called req: %s,%s", sessionID, code)
	req := &codeRun.ExecuteReq{
		SessionID: sessionID,
		Code:      code,
		Language:  "python",
	}
	response, err := a.dao.RunCode(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "Interpreter Run http.Do err:%+v", err)
		return rsp, err
	}
	log.InfoContextf(ctx, "Interpreter Run success rsp: %+v", response)
	rsp = a.GetCodeResult(response.Outputs)
	if len(rsp) == 0 {
		rsp = "代码执行异常"
	}
	return rsp, nil
}

// GetCodeResult 获取代码执行结果
func (a *ChatAgentImpl) GetCodeResult(outputs []*codeRun.Output) (rsp string) {
	for _, output := range outputs {
		if output.Type == "text" {
			rsp = output.Content
			break
		}
	}
	return rsp
}

// GetVarValue 获取变量值
func (a *ChatAgentImpl) GetVarValue(ctx context.Context, bs *botsession.BotSession, varID string) any {
	rsp := a.dao.GetVarList(ctx, &KEP.GetVarListReq{
		AppBizId: strconv.FormatUint(bs.App.GetAppBizId(), 10),
		VarIds:   []string{varID},
		Offset:   0,
		Limit:    100,
	})
	if len(rsp) > 0 {
		value, err := a.ConvertValue(bs.CustomVariables[rsp[0].VarName], rsp[0].VarType)
		if err != nil {
			log.ErrorContextf(ctx, "GetVarValue: %s", err)
		}
		return value
	}
	return ""
}

// ConvertValue 根据类型转换值
// 0:  "STRING",
// 1:  "INT",
// 2:  "FLOAT",
// 3:  "BOOL",
// 4:  "OBJECT",
// 5:  "ARRAY_STRING",
// 6:  "ARRAY_INT",
// 7:  "ARRAY_FLOAT",
// 8:  "ARRAY_BOOL",
// 9:  "ARRAY_OBJECT",
// 10: "FILE",
// 11: "DOCUMENT",
// 12: "IMAGE",
// 13: "AUDIO",
func (a *ChatAgentImpl) ConvertValue(orgValue string, toType string) (any, error) {
	var err error
	switch toType {
	case "OBJECT":
		ret := make(map[string]any)
		err = json.Unmarshal([]byte(orgValue), &ret)
		return ret, err
	case "ARRAY_OBJECT":
		ret := make([]map[string]any, 0)
		err = json.Unmarshal([]byte(orgValue), &ret)
		return ret, err
	case "STRING", "FILE", "DOCUMENT", "IMAGE", "AUDIO":
		return orgValue, nil
	case "INT":
		return strconv.ParseInt(orgValue, 10, 64)
	case "FLOAT":
		return strconv.ParseFloat(orgValue, 64)
	case "BOOL":
		return strconv.ParseBool(orgValue)
	case "ARRAY_STRING":
		ret := make([]string, 0)
		err = json.Unmarshal([]byte(orgValue), &ret)
		return ret, err
	case "ARRAY_INT":
		ret := make([]int64, 0)
		err = json.Unmarshal([]byte(orgValue), &ret)
		return ret, err
	case "ARRAY_FLOAT":
		ret := make([]float64, 0)
		err = json.Unmarshal([]byte(orgValue), &ret)
		return ret, err
	case "ARRAY_BOOL":
		ret := make([]bool, 0)
		err = json.Unmarshal([]byte(orgValue), &ret)
		return ret, err
	default:
		return nil, fmt.Errorf("ParseOutputValue failed, invalid type:%v", toType)
	}
}

// HandleDeDefaultValue 处理默认值
func HandleDeDefaultValue(ty plugin.TypeEnum, str string) any {
	switch ty {
	case plugin.TypeEnum_INT:
		value, _ := strconv.ParseInt(str, 10, 64) // 基数为10，位大小为64
		return value
	case plugin.TypeEnum_FLOAT:
		value, _ := strconv.ParseFloat(str, 64) // 转换为 float64
		return value
	case plugin.TypeEnum_BOOL:
		value, _ := strconv.ParseBool(str) // 转换为 bool
		return value
	case plugin.TypeEnum_OBJECT:
		var obj map[string]any
		_ = json.Unmarshal([]byte(str), &obj) // 解析 JSON 字符串为对象
		return obj
	case plugin.TypeEnum_ARRAY_STRING:
		var arr []string
		_ = json.Unmarshal([]byte(str), &arr) // 解析 JSON 字符串为字符串数组
		return arr
	case plugin.TypeEnum_ARRAY_INT:
		var arr []int64
		_ = json.Unmarshal([]byte(str), &arr) // 解析 JSON 字符串为整数数组
		return arr
	case plugin.TypeEnum_ARRAY_FLOAT:
		var arr []float64
		_ = json.Unmarshal([]byte(str), &arr) // 解析 JSON 字符串为浮点数数组
		return arr
	case plugin.TypeEnum_ARRAY_BOOL:
		var arr []bool
		_ = json.Unmarshal([]byte(str), &arr) // 解析 JSON 字符串为布尔数组
		return arr
	case plugin.TypeEnum_ARRAY_OBJECT:
		var arr []map[string]any
		_ = json.Unmarshal([]byte(str), &arr) // 解析 JSON 字符串为对象数组
		return arr
	default:
		return str
	}
}
