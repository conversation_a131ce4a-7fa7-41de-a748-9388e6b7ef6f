package agent

const (
	// ToolNameKnowledge 知识库
	ToolNameKnowledge = "KnowledgeRetrievalAnswer"
	// ToolNameBoundedKnowledgeQA 指定范围知识库搜索
	ToolNameBoundedKnowledgeQA = "BoundedKnowledgeQA"
	// ToolNameHunYuanSearch 混元搜索
	ToolNameHunYuanSearch = "HunyuanSearchSummary"
	// ToolNameCode 代码解释器
	ToolNameCode = "CodeInterpreter"
	// ToolNameCalculator 计算器
	ToolNameCalculator = "Calculator"
	// ToolNameCharts 图表生成器
	ToolNameCharts = "GenerateCharts"
	// ToolNameDeepSeekR1 DeepSeekR1 搜索
	ToolNameDeepSeekR1 = "DeepSeekR1Search"
	// ToolNameDeepSeekV3 DeepSeekV3 搜索
	ToolNameDeepSeekV3 = "DeepSeekV3Search"
	// ToolNameImageQuiz 图文问答
	ToolNameImageQuiz = "ImageQuiz"
	// ToolNameImageUnderstand 图片理解
	ToolNameImageUnderstand = "ImageUnderstand"
	// ToolNameDeployHTML deploy-html
	ToolNameDeployHTML = "deploy-html"
	// ToolNameDeployFile deploy-file
	ToolNameDeployFile = "deploy_file"
	// ToolNameTextToImage 文本转图片
	ToolNameTextToImage = "TextToImage"
)

// DeepSeekModelPrefix DeepSeek模型前缀
const DeepSeekModelPrefix = "lke-deepseek-r1"

// DefaultThinkingModel 默认思考模型
const DefaultThinkingModel = "function-call-pro"
