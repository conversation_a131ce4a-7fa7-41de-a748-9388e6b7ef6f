package agent

import (
	"context"
	"strconv"
	"strings"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/common/v3/limiter"
	"git.woa.com/dialogue-platform/common/v3/sync/errgroupx"
	"git.woa.com/dialogue-platform/go-comm/clues"
	llmm "git.woa.com/dialogue-platform/proto/pb-stub/llm-manager-server"
	"git.woa.com/dialogue-platform/proto/pb-stub/openapi"
	"git.woa.com/ivy/protobuf/trpc-go/qbot/qbot/infosec"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	"git.woa.com/ivy/qbot/qbot/chat/internal/eventbus/botsession"
	"git.woa.com/ivy/qbot/qbot/chat/internal/metrics"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model/event"
	"git.woa.com/ivy/qbot/qbot/chat/pkg"
	"git.woa.com/ivy/qbot/qbot/chat/pkg/agentlogger"
	jsoniter "github.com/json-iterator/go"
)

// AgentReply 智能体回复
func (a *ChatAgentImpl) AgentReply(ctx context.Context, bs *botsession.BotSession) (err error) {
	// SystemRole 在多 agent 体系下应该对应到 agent 的 instructions
	// if len(bs.SystemRole) < 1 {
	// 	bs.SystemRole = i18n.Translate(ctx, bs.App.RoleDescription())
	// }
	// 整合用户上传的端上配置和云上配置
	agentContainer, err := a.NewContainer(ctx, bs)
	if err != nil {
		return err
	}
	a.setContainer(*agentContainer)

	a.initAgentMemory(ctx, bs)
	a.initThoughtEvent(ctx, bs)

	// 处理上一次因为本地工具调用导致的中断（如果存在）
	lastStep, err := a.dealLastInterrupt(ctx, bs)
	if err != nil {
		return err
	}

	// bs.TokenStat.UpdateProcedure(event.NewProcessingTSProcedure(event.ProcedureAgent))
	// a.SendTokenStat(ctx, bs.To.ClientID, bs.TokenStat)

	// 判断当前需要用哪个Prompt
	// 启用PDL 且传了WorkflowID，则是PDL调试，使用WorkflowPrompt
	if a.useWorkflowPrompt(ctx, bs) {
		log.DebugContextf(ctx, "useWorkflowPrompt")
		bs.AgentStatus.AgentType = model.AgentTypeWorkflow
	}

	totalStep, maxIterations := lastStep, config.AgentConfig.MaxIterations // totalStep 从上次中断处开始计算
	if config.IsQBAppBizID(bs.App.GetAppBizId()) {
		maxIterations = config.GetAgentConfig().QBConfig.MaxIterations
	}
	for i := 0; i < maxIterations && totalStep < 2*maxIterations; i++ {
		totalStep++
		bs.AgentStatus.CurrentStep = totalStep
		log.InfoContextf(ctx, "current agent status: %s, session_id: %s",
			helper.Object2String(bs.AgentStatus), bs.SessionID)
		currentPrompt := a.getMainAgentPrompt(ctx, bs)
		if bs.AgentStatus.AgentType == model.AgentTypeWorkflow {
			currentPrompt = a.getWorkflowPrompt(ctx, bs)
		}
		// 如果中途切换，在这里判断
		if bs.AgentStatus.AgentSwitch {
			i = 0 // 重置迭代次数，这里如果一直切换有可能会导致无限循环.设置一个totalStep上限
			bs.AgentStatus.AgentSwitch = false
		}
		finish, err := a.agentReply(ctx, bs, currentPrompt)
		if err != nil {
			// bs.TokenStat.UpdateProcedure(event.NewFailedTSProcedure(ctx, event.ProcedureAgent))
			// a.SendTokenStat(ctx, bs.To.ClientID, bs.TokenStat)
			return err
		}
		if finish {
			return nil
		}
	}
	// 超过最大迭代次数 强制回复
	currentPrompt := a.getReachThinkingLimitPrompt(ctx, bs)
	a.mustReply = true
	bs.AgentStatus.AgentType = model.AgentTypeMain // 超限回复，强制使用主Agent，否则KBAgent有问题
	_, err = a.agentReply(ctx, bs, currentPrompt)
	return err
}

// agentReply 智能体回复
func (a *ChatAgentImpl) agentReply(ctx context.Context,
	bs *botsession.BotSession, prompt string) (finish bool, err error) {

	// 封装请求
	req, err := a.NewAgentRequest(ctx, bs, prompt)
	if err != nil {
		log.ErrorContextf(ctx, "build NewAgentRequest error: %v", err)
		return finish, err
	}
	clues.AddTrackData(ctx, "ChatAgentImpl.NewAgentRequest", req)
	step := agentlogger.New(ctx, agentlogger.StepKeyLLM, req)
	isR1 := strings.HasPrefix(a.ThinkingModelName, DeepSeekModelPrefix)
	// 发起请求
	cfg := config.App().Bot
	ch := make(chan *llmm.Response, cfg.ResponseChannelSize)
	g, gCtx := errgroupx.WithContext(ctx)
	agentCtx, cancel := context.WithCancel(ctx)
	isTimeout, isEvil, last, signal := false, false, &llmm.Response{}, make(chan int, 10)
	placeholders := make(map[string]string)
	g.Go(func() error {
		bs.TokenStat.UpdateAgentProcedure(event.NewAgentProcessingTSProcedure(event.ProcedureThinkingModel))
		a.SendTokenStat(ctx, bs.To.ClientID, bs.TokenStat)
		// chatDecorator 对函数 chat 调用做一次装饰，处理用户自定义参数
		err := a.chatDecorator(func(ctx context.Context, req *llmm.Request, ch chan *llmm.Response, startTime time.Time,
			signal chan int) error {
			step.Input = req
			return a.dao.Chat(ctx, req, ch, startTime, signal)
		}, bs, placeholders)(agentCtx, req, ch, bs.StartTime, signal) // 调用LLM
		if err != nil {
			log.ErrorContextf(ctx, "chat error: %v", err)
		}
		return err
	})
	g.Go(func() error {
		last, finish, isTimeout, isEvil, err = a.agentStreamDisplay(gCtx, cancel, bs, ch, signal, req, placeholders)
		step.Output = last
		step.ExtraData = placeholders

		if last != nil && !last.GetFinished() {
			last.Finished = true
			extra := event.ReplyExtraInfo{EChartsInfo: a.EChartsInfo}
			re := bs.NewAgentReplyEvent(ctx, last, isEvil, model.ReplyMethodAgent, bs.StartTime, extra)
			_ = a.dao.DoEmitWsClient(ctx, bs.To.ClientID, re, cancel)
		}

		return helper.When(isTimeout, pkg.ErrLLMTimeout, err)
	})
	err = g.Wait()
	step.SetResult(err)
	if !isR1 { // 两阶段的，已经处理过上报了。
		step.UpsertStep(true)
	}
	return finish, err
}

// SendTokenStat 发送 token 统计事件到前端
func (a *ChatAgentImpl) SendTokenStat(ctx context.Context, clientID string, tokenStat *event.TokenStatEvent) {
	if tokenStat == nil {
		return
	}
	ctx, cancel := context.WithCancel(ctx)
	err := a.dao.DoEmitWsClient(ctx, clientID, tokenStat, cancel)
	tse0 := *tokenStat
	tse0.Procedures = append([]event.Procedure{}, tokenStat.Procedures...)
	clues.AddTrackDataWithError(ctx, tokenStat.EventSource+".TokenStatEvent", tse0, err)
}

// NewAgentRequest 构造请求，请求Agent
func (a *ChatAgentImpl) NewAgentRequest(ctx context.Context, bs *botsession.BotSession, prompt string) (*llmm.Request,
	error) {
	// 1. 准备模型配置
	m := a.prepareModel(bs)
	requestID := model.RequestID(ctx, bs.SessionID, bs.RecordID)
	instructions, _ := a.agentContainer.GetAgentInstructions(bs.AgentStatus.CurrentAgent)

	// 2. 初始化变量
	var (
		memory      []*model.AgentMemory
		tools       []*openapi.Tool
		reachLimit  = a.mustReply
		isPdl       bool
		taskGoal    = instructions
		finalPrompt = prompt
	)

	// 3. 根据场景选择处理流程
	switch {
	case reachLimit:
		// 超限回复，仅传递用户输入
		memory = wrapUserMemory(bs.OriginContent)

	case bs.AgentStatus.AgentType == model.AgentTypeWorkflow:
		// 工作流PDL模式
		memory, tools, finalPrompt = buildWorkflowContext(a, ctx, bs)
		reachLimit = a.mustReply
		isPdl = true

	case bs.AgentStatus.AgentType == model.AgentTypeKBAgent:
		// 知识库Agent 模式
		memory, tools, finalPrompt = buildKBAgentContext(a, ctx, bs)
		reachLimit = a.mustReply
		isPdl = true

	default:
		// 普通 Agent 模式
		a.getAllTools(ctx, bs)
		tools, _ = a.agentContainer.GetFunctionCalls(ctx, bs.AgentStatus.CurrentAgent, bs)
		memory = a.agentMemory
	}

	// 4. 构造请求并设置额外参数
	req := m.NewAgentRequest(requestID, memory, tools, finalPrompt)
	req.ExtraParams.ReachThinkingLimit = reachLimit
	req.ExtraParams.IsPdl = isPdl
	req.ExtraParams.TaskGoal = taskGoal

	return req, nil
}

// prepareModel 提取模型配置逻辑
func (a *ChatAgentImpl) prepareModel(bs *botsession.BotSession) model.AppModel {
	current := bs.AgentStatus.CurrentAgent
	m := a.agentContainer.GetAgentModel(current)
	m.BotBizID = bs.App.GetAppBizId()
	m.ModelName = config.GetAgentModelMap(m.GetModelName())
	if config.IsQBAppBizID(bs.App.GetAppBizId()) {
		m.ModelName = config.GetQBModelName()
	}
	a.ThinkingModelName = m.GetModelName()
	return m
}

// wrapUserMemory 简化用户记忆构造
func wrapUserMemory(prompt string) []*model.AgentMemory {
	return []*model.AgentMemory{{
		Message: &llmm.Message{Role: llmm.Role_USER, Content: prompt},
		From:    "USER",
	}}
}

// buildWorkflowContext 构建工作流上下文和工具列表
func buildWorkflowContext(a *ChatAgentImpl, ctx context.Context, bs *botsession.BotSession) ([]*model.AgentMemory,
	[]*openapi.Tool, string) {
	workflowCtx := a.wrapWorkflowCtx(ctx, bs)
	prompt, _ := model.Render(ctx, a.getWorkflowPrompt(ctx, bs), workflowCtx)
	mem := []*model.AgentMemory{{
		Message: &llmm.Message{Role: llmm.Role_USER, Content: prompt},
		From:    bs.AgentStatus.WorkflowName,
	}}
	tools := model.GetPDLDefaultTools(bs.WorkflowDebug)
	return mem, tools, prompt
}

// buildKBAgentContext 构建知识检索 Agent 上下文和工具列表
func buildKBAgentContext(a *ChatAgentImpl, ctx context.Context, bs *botsession.BotSession) ([]*model.AgentMemory,
	[]*openapi.Tool, string) {
	kbCtx := a.wrapKBAgentCtx(ctx, bs, bs.AgentStatus.CurrentAgent)
	prompt, _ := model.Render(ctx, a.getKBAgentPrompt(ctx, bs), kbCtx)
	mem := []*model.AgentMemory{{
		Message: &llmm.Message{Role: llmm.Role_USER, Content: prompt},
		From:    "USER",
	}}
	tools := model.GetKBAgentDefaultTools()
	return mem, tools, prompt
}

// agentStreamDisplay 智能体结果输出
func (a *ChatAgentImpl) agentStreamDisplay(ctx context.Context, cancel context.CancelFunc, bs *botsession.BotSession,
	ch chan *llmm.Response, signal chan int,
	req *llmm.Request, placeholders map[string]string,
) (last *llmm.Response, isFinish, isTimeout, isEvil bool, err error) {
	cfg := config.App().Bot
	throttles := cfg.Throttles
	ticker := time.NewTicker(time.Duration(cfg.StopGeneration.CheckInterval) * time.Millisecond)
	timeout := time.NewTimer(time.Duration(cfg.Timeout) * time.Second)
	timeout2 := time.NewTicker(time.Duration(cfg.Timeout/5) * time.Second)
	throttleCheck := helper.NewThrottle(throttles.Check)
	replyThrottleCheck := helper.NewThrottle(throttles.Check)
	throttleStreaming := helper.NewThrottle(helper.When(bs.StreamingThrottle > 0, bs.StreamingThrottle,
		throttles.Streaming))
	codeStreaming := helper.NewThrottle(helper.When(bs.StreamingThrottle > 0, bs.StreamingThrottle,
		throttles.Streaming))
	index, isFinish, isEvil, last, lastThought, preUseToken := 0, false, false, &llmm.Response{}, 0, uint32(0)
	defer func() {
		ticker.Stop()
		timeout.Stop()
		timeout2.Stop()
	}()
	for {
		select {
		case <-timeout.C:
			signal <- 1
			// bs.TokenStat.UpdateProcedure(event.NewFailedTSProcedure(ctx, event.ProcedureAgent))
			// a.SendTokenStat(ctx, bs.To.ClientID, bs.TokenStat)
			return last, isFinish, true, false, nil
		case <-timeout2.C:
			a.SendTokenStat(ctx, bs.To.ClientID, bs.TokenStat)
		case <-ticker.C:
			if ok, _ := a.dao.IsGenerationStopped(ctx, bs.To.CorpStaffID, bs.TokenStat.RecordID); ok {
				last.Finished = true
				extra := event.ReplyExtraInfo{EChartsInfo: a.EChartsInfo}
				re := bs.NewAgentReplyEvent(ctx, last, false, model.ReplyMethodAgent, bs.StartTime, extra)
				_ = a.dao.DoEmitWsClient(ctx, bs.To.ClientID, re, cancel)
				a.processAgentRecord(ctx, bs, last)
				signal <- 1
				return last, true, false, false, nil
			}
		case rsp, ok := <-ch:
			if a.needReportToken(ctx, bs, preUseToken, rsp) {
				preUseToken = rsp.GetStatisticInfo().GetTotalTokens()
			}
			if err = a.chatRspVarDecorator(ctx, rsp, bs); err != nil {
				return last, isFinish, false, false, err
			}
			chatRspShortLinkDecorator(rsp, placeholders) // 短链接转长链接
			timeout.Stop()
			last = rsp
			if !ok {
				return last, isFinish, false, false, pkg.ErrAgentRunError
			}
			if strings.TrimSpace(rsp.GetMessage().GetContent()) == "" &&
				strings.TrimSpace(rsp.GetMessage().GetReasoningContent()) == "" &&
				len(rsp.GetMessage().GetToolCalls()) == 0 {
				continue
			}
			if index == 0 && !a.mustReply { // 最后超限回复 不处理思考事件
				metrics.ReportThoughtFirstToken(ctx, time.Since(bs.StartTime).Milliseconds())
				a.addThoughtProcedure(rsp.GetMessage().GetContent())
				index++
			}
			if isEvil, isFinish, err = a.processAgentResponse(ctx, bs,
				rsp, throttleCheck, replyThrottleCheck, throttleStreaming, codeStreaming, &index,
				&lastThought, signal, req); isEvil || err != nil {
				signal <- 1
				return last, isFinish, false, isEvil, err
			}
			if isFinish || rsp.Finished {
				last = rsp // 在中间rsp被手工置为finished
				return last, isFinish, false, isEvil, nil
			}
		}
	}
}

// processAgentResponse 处理智能体的返回  1. 处理Thought事件; 2. 处理Memory; 3. tool调用; 4. 安全审核; 5. 最终结果输出
func (a *ChatAgentImpl) processAgentResponse(ctx context.Context, bs *botsession.BotSession,
	rsp *llmm.Response, throttleCheck, replyThrottleCheck, throttleStreaming, codeStreaming helper.Throttle,
	index, lastThought *int, signal chan int, req *llmm.Request) (isEvil, isFinish bool, err error) {
	if a.processAgentCheck(ctx, bs, rsp, throttleCheck) {
		return true, true, nil
	}

	if a.mustReply { // 强制回复,安全审核？
		if *index == 0 {
			metrics.ReportContentFirstToken(ctx, time.Since(bs.StartTime).Milliseconds())
			*index++
		}
		return a.processAgentMustReply(ctx, bs, rsp)
	}
	// 处理必须要回复的，都要处理思考事件
	r1ThoughtFinish := len(rsp.GetMessage().GetReasoningContent()) > 0 && len(rsp.GetMessage().GetContent()) > 0
	lastThoughtOutput := *lastThought == 0 && (r1ThoughtFinish || a.needOutputDirectly(bs, rsp) || a.needUseTool(rsp) ||
		rsp.GetFinished())
	a.processAgentThought(ctx, bs, rsp, throttleStreaming, lastThoughtOutput)
	if lastThoughtOutput {
		*lastThought++
		if strings.HasPrefix(a.ThinkingModelName, DeepSeekModelPrefix) {
			signal <- 1
			llmStep := agentlogger.New(ctx, agentlogger.StepKeyLLM, req) // 这里开始时间不对，后面想想怎么样处理更合适
			llmStep.Output = rsp
			llmStep.SetResult(nil)
			llmStep.UpsertStep(true)
			isFinish, err = a.thinkingModelCompletion(ctx, bs, req, rsp.GetMessage().GetReasoningContent())

			log.InfoContextf(ctx, "thinkingModelCompletion, isFinish: %v, err: %v", isFinish, err)
			rsp.Finished = true
			return false, isFinish, err
		}
	}
	return a.processFunctionCalls(ctx, bs, rsp, throttleStreaming, replyThrottleCheck, codeStreaming, index)
}

// processFunctionCalls 统一调度 Agent 输出、工具调用、工作流与会话交接
func (a *ChatAgentImpl) processFunctionCalls(
	ctx context.Context,
	bs *botsession.BotSession,
	rsp *llmm.Response,
	throttleStreaming, replyThrottleCheck, codeStreaming helper.Throttle,
	index *int,
) (isEvil, isFinish bool, err error) {

	// 1. 直接向前端流式输出
	if a.handleDirectOutput(ctx, bs, rsp, throttleStreaming, index) {
		return a.processAgentDirectlyReply(ctx, bs, rsp, replyThrottleCheck)
	}

	// 2. 结束时统一更新 Token 统计
	a.updateStatisticsOnFinish(ctx, bs, rsp)

	// 3. 会话交接 / 工作流分支
	if done, err := a.handleHandoffAndWorkflow(ctx, bs, rsp); done || err != nil {
		return false, false, err
	}

	// 4. Code Interpreter 工具流式输出
	if a.shouldStreamTool(rsp) {
		return a.processStreamAgentTools(ctx, bs, rsp, codeStreaming)
	}

	// 5. 结束时调用工具
	if a.shouldCallTool(rsp) {
		return a.processAgentTools(ctx, bs, rsp)
	}

	return false, false, nil
}

/* ---------- 私有辅助方法 ---------- */

// handleDirectOutput 直接输出并做首 token 计时、文件提取等副作用
func (a *ChatAgentImpl) handleDirectOutput(
	ctx context.Context, bs *botsession.BotSession, rsp *llmm.Response,
	th helper.Throttle, index *int,
) bool {
	if !(a.needOutputDirectly(bs, rsp) &&
		th.Hit(len([]rune(bs.GetAgentReply(ctx, rsp))), rsp.GetFinished())) {
		return false
	}

	if rsp.Finished {
		a.updateAgentProcedure(ctx, bs, rsp.GetMessage().GetContent(), event.ProcedureThinkingModel)

		if a.ExtractFileFromOutput(rsp.GetMessage().GetContent()) {
			a.updateThoughtFiles()
			a.ThoughtEventReply(ctx, bs)
		}
		// 如果是KBAgent，需要处理转交 自动转交给主Agent。 需要添加条件 「直接输出」的时候，说明KBAgent处理完了
		if bs.AgentStatus.AgentType == model.AgentTypeKBAgent && (a.needOutputDirectly(bs, rsp)) {
			// 处理转交
			bs.AgentStatus.AgentSwitch = true
			bs.AgentStatus.CurrentAgent = a.agentContainer.EntryAgent
			bs.AgentStatus.CurrentStep = 0
			bs.AgentStatus.AgentType = model.AgentTypeMain
			log.InfoContextf(ctx, "agent backtrace to %s", bs.AgentStatus.CurrentAgent)
		}

	}

	if *index == 1 {
		metrics.ReportContentFirstToken(ctx, time.Since(bs.StartTime).Milliseconds())
		*index++
	}
	return true
}

// updateAgentProcedure 统一封装 AgentProcedure 更新 + TokenStat 写入
func (a *ChatAgentImpl) updateAgentProcedure(
	ctx context.Context, bs *botsession.BotSession, output string, pType string,
) {
	d := event.ProcedureDebugging{CustomVariables: bs.CustomVariablesForDisplay}
	d.Agent.Output = output
	bs.TokenStat.UpdateAgentProcedure(
		event.NewAgentSuccessTSProcedure(pType, nil, d, []*event.TokenUsage{}))
	a.SendTokenStat(ctx, bs.To.ClientID, bs.TokenStat)
}

func (a *ChatAgentImpl) updateStatisticsOnFinish(
	ctx context.Context, bs *botsession.BotSession, rsp *llmm.Response,
) {
	if rsp.Finished {
		out := helper.Object2StringEscapeHTML(rsp.GetMessage().GetToolCalls())
		a.updateAgentProcedure(ctx, bs, out, event.ProcedureThinkingModel)
	}
}

// handleHandoffAndWorkflow 会话交接 / 工作流 逻辑集中处理
func (a *ChatAgentImpl) handleHandoffAndWorkflow(
	ctx context.Context, bs *botsession.BotSession, rsp *llmm.Response,
) (done bool, err error) {
	switch {
	case a.hitHandoff(ctx, bs, rsp):
		msg := a.buildAgentMemory(rsp)
		bs.AgentStatus.AddMemory([]*model.AgentMemory{msg})
		if !config.IsQBAppBizID(bs.App.AppBizId) {
			a.addAgentMemory(msg)
		}
		if workflow, ok := a.hitWorkflow(ctx, bs, rsp); ok {
			msg.CallType = "workflow"
			bs.AgentStatus.AddWorkflowMemory(msg)
			return true, a.processWorkflowThought(bs, workflow)
		}
		bs.AgentStatus.AddWorkflowMemory(msg)
		return true, nil

	case a.isHandoffToParent(bs, rsp, false):
		a.thought.IsWorkflow = false
		bs.WorkflowID = ""
		bs.AgentStatus.AgentBacktrace()
		log.InfoContextf(ctx, "agent backtrace to %s", bs.AgentStatus.CurrentAgent)
		return true, nil
	}
	return false, nil
}

func (a *ChatAgentImpl) buildAgentMemory(rsp *llmm.Response) *model.AgentMemory {
	return &model.AgentMemory{
		Message: &llmm.Message{
			Role:      llmm.Role_ASSISTANT,
			Content:   rsp.GetMessage().GetContent(),
			ToolCalls: rsp.GetMessage().GetToolCalls(),
		},
		From:     "MAIN",
		CallType: "API",
	}
}

func (a *ChatAgentImpl) shouldStreamTool(rsp *llmm.Response) bool {
	return a.needUseTool(rsp) &&
		isCodeInterpreter(rsp.GetMessage().GetToolCalls()[0].GetFunction().GetName())
}

func (a *ChatAgentImpl) shouldCallTool(rsp *llmm.Response) bool {
	return a.needUseTool(rsp) && rsp.GetFinished()
}

// needOutputDirectly 判断是否需要直接输出
func (a *ChatAgentImpl) needOutputDirectly(bs *botsession.BotSession, rsp *llmm.Response) bool {
	if config.IsQBAppBizID(bs.App.GetAppBizId()) && rsp.Finished && len(rsp.Message.ToolCalls) == 0 {
		// 浏览器的特殊处理逻辑，没有工具调用就结束循环
		return true
	}
	for _, toolCall := range rsp.GetMessage().ToolCalls {
		if model.IsDefaultTools(toolCall.GetFunction().GetName()) {
			return true
		}
	}
	return false
}

// isHandoffToParent 判断是否转交到主Agent
func (a *ChatAgentImpl) isHandoffToParent(bs *botsession.BotSession,
	rsp *llmm.Response, force bool) bool {
	if config.IsQBAppBizID(bs.App.GetAppBizId()) {
		// 浏览器不处理 task_done
		return false
	}
	if len(rsp.GetMessage().GetToolCalls()) > 0 && (force || rsp.Finished) &&
		(rsp.GetMessage().GetToolCalls()[0].GetFunction().GetName() == model.SysToolSwitch2Main ||
			rsp.GetMessage().GetToolCalls()[0].GetFunction().GetName() == model.SysToolSwitch2Parent) {
		// 处理模型幻觉，在入口 agent 回返回 task_done
		if bs.AgentStatus.AgentSwitch {
			return true
		}
		if bs.AgentStatus.CurrentAgent == a.agentContainer.EntryAgent {
			// 入口 agent 不能再往回转交了
			return false
		}
		return true
	}
	return false
}

// processAgentRecord 处理智能体的记录
func (a *ChatAgentImpl) processAgentRecord(ctx context.Context,
	bs *botsession.BotSession, rsp *llmm.Response) (isFinish bool) {
	if !rsp.Finished {
		return false
	}

	base := rsp.GetMessage().GetContent()
	if !a.mustReply {
		base = bs.GetAgentReply(ctx, rsp)
	}
	reply := helper.SanitizeReply(base)

	// 更新状态
	_ = a.dao.SetAgentStatus(ctx, strconv.FormatUint(bs.App.GetAppBizId(), 10),
		bs.SessionID, bs.AgentStatus)

	lastEvil := &infosec.CheckRsp{ResultCode: bs.Msg.ResultCode, ResultType: bs.Msg.ResultType}
	record := bs.NewBotRecord(ctx, reply,
		nil, model.ReplyMethodAgent, lastEvil, bs.StartTime)
	log.DebugContextf(ctx, "Token Stat: %v", helper.Object2String(bs.TokenStat))
	newRecord, newStat := event.GetMsgRecordAndTokenStat(ctx, record)
	newStat.AgentThought, _ = jsoniter.MarshalToString(a.thought)
	_, _ = a.dao.CreateMsgRecord(ctx, newRecord, newStat) // for answer
	return true

}

// processAgentMustReply 处理Agent超限回复
func (a *ChatAgentImpl) processAgentMustReply(ctx context.Context, bs *botsession.BotSession,
	rsp *llmm.Response) (isEvil, isFinish bool, err error) {

	m := &llmm.Message{Role: llmm.Role_ASSISTANT, Content: rsp.GetMessage().GetContent()}
	from, callType := "MAIN", "main"
	if bs.AgentStatus.AgentType == model.AgentTypeWorkflow {
		from = bs.AgentStatus.WorkflowName
		callType = "workflow"
	}
	if rsp.Finished {
		message := &model.AgentMemory{Message: m, From: from, CallType: callType}
		a.addAgentMemory(message)
		bs.AgentStatus.AddWorkflowMemory(message)
		bs.AgentStatus.AddMemory(a.agentMemory)
		d := event.ProcedureDebugging{CustomVariables: bs.CustomVariablesForDisplay}
		d.Agent.Output = helper.Object2StringEscapeHTML(rsp.GetMessage().GetToolCalls())
		bs.TokenStat.UpdateAgentProcedure(event.NewAgentSuccessTSProcedure(event.ProcedureThinkingModel,
			nil, d, []*event.TokenUsage{}))
		a.SendTokenStat(ctx, bs.To.ClientID, bs.TokenStat)
	}
	rsp.GetMessage().Content = helper.ReplaceInvalidHTML(rsp.GetMessage().GetContent())
	ctx, cancel := context.WithCancel(ctx)
	re := bs.NewReplyEvent(ctx, rsp, isEvil, model.ReplyMethodAgent, bs.StartTime, nil)
	_ = a.dao.DoEmitWsClient(ctx, bs.To.ClientID, re, cancel)
	isFinish = a.processAgentRecord(ctx, bs, rsp)
	return false, isFinish, nil
}

// processAgentDirectlyReply 处理Agent直接回复
func (a *ChatAgentImpl) processAgentDirectlyReply(
	ctx context.Context, bs *botsession.BotSession,
	rsp *llmm.Response, replyThrottleCheck helper.Throttle,
) (isEvil, isFinish bool, err error) {
	if a.processAgentReplyCheck(ctx, bs, rsp, replyThrottleCheck) {
		return true, true, nil
	}
	ctx, cancel := context.WithCancel(ctx)
	extra := event.ReplyExtraInfo{EChartsInfo: a.EChartsInfo}
	re := bs.NewAgentReplyEvent(ctx, rsp, isEvil, model.ReplyMethodAgent, bs.StartTime, extra)
	_ = a.dao.DoEmitWsClient(ctx, bs.To.ClientID, re, cancel)
	if !rsp.GetFinished() {
		return false, false, nil
	}
	toolcalls := rsp.GetMessage().GetToolCalls()
	m := &llmm.Message{
		Role:      llmm.Role_ASSISTANT,
		Content:   rsp.GetMessage().GetContent(),
		ToolCalls: toolcalls,
	}
	from, callType := "MAIN", "main"
	if len(toolcalls) > 0 && toolcalls[0].GetFunction().GetName() == "response_to_user" {
		from = bs.AgentStatus.WorkflowName
		callType = "workflow"
	}
	a.addAgentMemory(&model.AgentMemory{Message: m, From: from, CallType: callType})
	bs.AgentStatus.AddMemory(a.agentMemory)
	bs.AgentStatus.AddWorkflowMemory(&model.AgentMemory{Message: m, From: from, CallType: callType})

	isFinish = a.processAgentRecord(ctx, bs, rsp)
	return false, isFinish, nil
}

// needReportToken 上报使用token 给到统计
func (a *ChatAgentImpl) needReportToken(ctx context.Context, bs *botsession.BotSession, preToken uint32,
	last *llmm.Response) bool {
	currToken := last.GetStatisticInfo().GetTotalTokens()
	if currToken <= preToken {
		return false
	}
	need := currToken-preToken > uint32(config.App().Limiter.ReportJudgeLength)
	if !need && !last.GetFinished() { // 如果没有达到上报的阈值，并且不是最后一个包，则不上报
		return false
	}
	log.DebugContextf(ctx, "reportUseToken: preToken:%d, currToken:%d", preToken, currToken)
	uin, sin, _ := a.dao.GetUinByCorpID(ctx, bs.App.GetCorpId())
	a.dao.ReportToken(ctx, limiter.ReportTokenReq{
		Uin:       strconv.FormatUint(uin, 10),
		SID:       uint32(sin),
		AppBizID:  strconv.FormatUint(bs.App.GetAppBizId(), 10),
		ModelName: bs.App.GetAgentMainModelName(),
		Token:     uint64(currToken - preToken),
	})
	return true
}
