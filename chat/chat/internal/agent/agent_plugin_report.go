package agent

import (
	"context"
	"encoding/json"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	pluginReport "git.woa.com/dialogue-platform/go-comm/plugin"
	pluginRun "git.woa.com/dialogue-platform/lke_proto/pb-protocol/plugin_exec_server"
	"git.woa.com/dialogue-platform/proto/pb-stub/openapi"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/eventbus/botsession"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
)

// MetricsConfig 指标配置
type MetricsConfig struct {
	Uin        string
	PluginType int
	CreateType int
	ToolName   string
	PluginName string
}

// runToolWithMetrics 带指标上报的RunTool装饰器
func (a *ChatAgentImpl) runToolWithMetrics(
	ctx context.Context, req *pluginRun.RunToolReq, config MetricsConfig,
) (*pluginRun.RunToolRsp, error) {
	startTime := time.Now()

	// 执行原函数
	rsp, err := a.dao.RunTool(ctx, req)

	// 计算耗时
	cost := time.Since(startTime).Milliseconds()

	// 解析结果状态
	isSuccess := true
	errCode := 0

	if err != nil {
		isSuccess = false
		errCode = -1 // 系统错误
	} else if rsp != nil && rsp.Result != "" {
		// 尝试解析Result中的Code
		var toolResp model.ToolResponse
		if parseErr := json.Unmarshal([]byte(rsp.Result), &toolResp); parseErr == nil {
			if toolResp.Code != 0 {
				isSuccess = false
				errCode = toolResp.Code
			}
		}
	}

	// 上报指标
	data := &pluginReport.ReportData{
		Dimension: pluginReport.Dimension{
			Uin:        config.Uin,
			AppID:      req.AppId,
			PluginID:   req.PluginId,
			PluginName: config.PluginName,
			ToolID:     req.ToolId,
			ToolName:   config.ToolName,
			PluginType: config.PluginType,
			CreateType: config.CreateType,
			ErrCode:    errCode,
		},
		Cost:      cost,
		IsSuccess: isSuccess,
	}
	pluginReport.Report(data)

	return rsp, err
}

// runStreamToolWithMetrics 带指标上报的RunTool装饰器
func (a *ChatAgentImpl) runStreamToolWithMetrics(
	ctx context.Context, bs *botsession.BotSession,
	toolCall *openapi.ToolCall, req *pluginRun.RunToolReq, config MetricsConfig,
) (string, error) {
	startTime := time.Now()

	// 执行原函数
	toolResponse, err := a.executeStreamTool(ctx, bs, toolCall, req)

	// 计算耗时
	cost := time.Since(startTime).Milliseconds()

	// 解析结果状态
	isSuccess := true
	errCode := 0

	if err != nil {
		isSuccess = false
		errCode = -1 // 系统错误
	} else if toolResponse != "" {
		// 尝试解析Result中的Code
		var toolResp model.ToolResponse
		if parseErr := json.Unmarshal([]byte(toolResponse), &toolResp); parseErr == nil {
			if toolResp.Code != 0 {
				isSuccess = false
				errCode = toolResp.Code
			}
		}
	}

	// 上报指标
	data := &pluginReport.ReportData{
		Dimension: pluginReport.Dimension{
			Uin:        config.Uin,
			AppID:      req.AppId,
			PluginID:   req.PluginId,
			PluginName: config.PluginName,
			ToolID:     req.ToolId,
			ToolName:   config.ToolName,
			PluginType: config.PluginType,
			CreateType: config.CreateType,
			ErrCode:    errCode,
		},
		Cost:      cost,
		IsSuccess: isSuccess,
	}
	pluginReport.Report(data)
	log.InfoContextf(ctx, "pluginReport finish :%s", helper.Object2StringEscapeHTML(data))

	return toolResponse, err
}
