package agent

// 处理和用户端上本地工具交互的相关逻辑

import (
	"context"
	"encoding/json"
	"strconv"

	"git.code.oa.com/trpc-go/trpc-go/log"
	llmm "git.woa.com/dialogue-platform/proto/pb-stub/llm-manager-server"
	"git.woa.com/dialogue-platform/proto/pb-stub/openapi"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/eventbus/botsession"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model/event"
	"git.woa.com/ivy/qbot/qbot/chat/pkg"
	"google.golang.org/protobuf/proto"
)

// adaptLocalToolCustomVar 本地工具处理 CustomVariables
func adaptLocalToolCustomVar(tool *openapi.Tool, bs *botsession.BotSession) (newTool *openapi.Tool) {
	if tool == nil {
		return nil
	}
	// 用户本地工具，默认传给模型的 tool，参数第一层需要去掉 CustomVariables 中的参数
	// 不需要模型提取参数，使用 sdk 传入的参数
	newTool = proto.Clone(tool).(*openapi.Tool)
	// 没有参数不处理
	if newTool.Function == nil || newTool.Function.Parameters == nil {
		return newTool
	}
	newRequired := []string{}
	// 处理 Required
	for _, param := range tool.Function.Parameters.Required {
		if _, ok := bs.CustomVariables[param]; !ok {
			// 不在用户自定义参数中，保留其 required 属性
			newRequired = append(newRequired, param)
		}
	}
	newTool.Function.Parameters.Required = newRequired

	// 处理 properties，只处理第一层结构
	newProp := map[string]*openapi.Definition{}
	for paramName, define := range tool.Function.Parameters.Properties {
		if _, ok := bs.CustomVariables[paramName]; !ok {
			// 不在用户自定义参数中，保留参数
			newProp[paramName] = define
		}
	}
	newTool.Function.Parameters.Properties = newProp
	return newTool
}

// hitLocalTool 是否包含端上本地插件
func (a *ChatAgentImpl) hitLocalTool(ctx context.Context,
	bs *botsession.BotSession, rsp *llmm.Response) (hit bool) {
	if rsp == nil {
		return false
	}
	if rsp.Message == nil {
		return false
	}

	// 先获取当前的 agent 对应的本地工具列表
	dynamicTool := a.agentContainer.GetLocalTools(bs.AgentStatus.CurrentAgent)
	for _, toolCall := range rsp.Message.ToolCalls {
		if toolCall != nil {
			for _, tool := range dynamicTool.Tools {
				// 函数输出的工具列表在本地工具列表中
				if tool.Function.Name == toolCall.Function.Name {
					return true
				}
			}
		}
	}
	return false
}

// interruptRepley 中断处理的回复
func (a *ChatAgentImpl) interruptRepley(ctx context.Context,
	bs *botsession.BotSession, rsp *llmm.Response) (err error) {
	status := &model.SessionInterruptStatus{
		RequestID:   bs.RequestID,
		RecordID:    bs.RecordID,
		Interrupt:   true,
		LastLLmRsp:  rsp,
		AgentStatus: bs.AgentStatus,
		ChatAgentImplCache: &model.ChatAgentImplCache{
			AgentMemory: a.agentMemory,
			History:     a.history,
			EChartsInfo: a.EChartsInfo,
			Thought:     helper.Object2String(a.thought),
			Container:   helper.Object2String(a.agentContainer),
		},
	}
	log.InfoContextf(ctx, "dialog interrupt because of local tools call, interrupt status: %v",
		helper.Object2String(status))
	err = a.dao.SetSessionInterrupt(ctx, strconv.FormatUint(bs.App.GetAppBizId(), 10), bs.SessionID, status)
	if err != nil {
		log.InfoContextf(ctx, "interrupt session %s error: %v", bs.SessionID, err)
		return pkg.ErrInternalServerError
	}
	a.dao.SetRequestInterruptTraceID(ctx, bs.RequestID, model.TraceID(ctx))
	repleyMethod := model.ReplyMethodInterrupt

	r := &event.ReplyEvent{
		RequestID:       bs.RequestID,
		SessionID:       bs.Session.SessionID,
		Content:         "",
		FromName:        bs.App.BaseConfig.GetName(),
		FromAvatar:      bs.App.BaseConfig.GetAvatar(),
		RecordID:        bs.RecordID,
		RelatedRecordID: bs.RelatedRecordID,
		Timestamp:       bs.StartTime.Unix(),
		IsFinal:         true, // 当前 sse 请求结束
		IsFromSelf:      false,
		CanRating:       false,
		CanFeedback:     false,
		IsEvil:          false,
		IsLLMGenerated:  false,
		Knowledge:       nil,
		ReplyMethod:     repleyMethod,
		IntentCategory:  bs.IntentCate,
		OptionCards:     nil,
		CustomParams:    bs.CustomParams,
		FileInfos:       bs.FileInfos,
		QuoteInfos:      nil,
		InterruptInfo: &event.InterruptInfo{
			CurrentAgent: bs.AgentStatus.CurrentAgent,
			ToolCalls:    rsp.GetMessage().GetToolCalls(),
		},
	}
	_ = a.dao.DoEmitWsClient(ctx, bs.To.ClientID, r, nil)
	return nil
}

// dealLastInterrupt 处理上一次请求可能的本地工具调用中断
func (a *ChatAgentImpl) dealLastInterrupt(ctx context.Context, bs *botsession.BotSession) (
	lastStep int, err error) {
	appbizid := strconv.FormatUint(bs.App.GetAppBizId(), 10)
	if len(bs.ToolOuputs) == 0 {
		// 用户没有上报工具调用，按正常调用处理，忽略上次中断状态
		if bs.OriginContent != "" {
			// 用户输入了正常对话聊天，清空中断状态
			a.dao.SetSessionUnInterrupt(ctx, appbizid, bs.SessionID)
		}
		return 0, nil
	}
	// 处理用户上报了本地工具调用的情况
	status, err := a.dao.GetSessionInterruptStatus(ctx, appbizid, bs.SessionID)
	if err != nil {
		// 这里一定要获取到用户的中断状态才能后续处理，无法降级，暴露系统错误
		log.ErrorContextf(ctx, "get interrupt status error: %v", err)
		return 0, pkg.ErrInternalServerError
	}
	if status == nil {
		// 如果获取的状态为空
		log.ErrorContextf(ctx, "get interrupt status nil, may interrupt timeout clean or no interrupt")
		return 0, pkg.ErrAgentLocalToolOuputsRedundant
	}
	log.InfoContextf(ctx, "interrupt status %v", helper.Object2String(status))
	if !status.CheckVaild() {
		// redis 存的上次状态有问题，报系统错误
		log.ErrorContextf(ctx, "interrupt status data invaild, get status %v", helper.Object2String(status))
		return 0, pkg.ErrInternalServerError
	}
	bs.RelatedRecordID = status.RecordID
	// 从缓存中恢复上次的 agent_status 和 ChatAgentImpl 状态
	bs.AgentStatus = status.AgentStatus
	a.thought = &event.AgentThoughtEvent{}
	if err := json.Unmarshal([]byte(status.ChatAgentImplCache.Thought), a.thought); err != nil {
		// 这里只打 log，降级处理，不报错，历史 thought 丢失
		log.WarnContextf(ctx, "json Unmarshal error: %v, data: %v", err, status.ChatAgentImplCache.Thought)
	}
	a.agentMemory = status.ChatAgentImplCache.AgentMemory
	a.history = status.ChatAgentImplCache.History
	a.EChartsInfo = status.ChatAgentImplCache.EChartsInfo
	if err := json.Unmarshal([]byte(status.ChatAgentImplCache.Container), &a.agentContainer); err != nil {
		// 这里只打 log，降级处理
		log.WarnContextf(ctx, "json Unmarshal error: %v, data: %v", err, status.ChatAgentImplCache.Container)
	}
	if len(status.LastLLmRsp.Message.ToolCalls) == 0 {
		log.ErrorContext(ctx, "last interrupt tool calls null: %s", helper.Object2String(status.LastLLmRsp))
		return 0, pkg.ErrInternalServerError
	}
	// copy from processAgentTools 的工具调用后的处理
	toolcall := status.LastLLmRsp.Message.ToolCalls[0]
	toolFound := false
	for _, toolOutput := range bs.ToolOuputs {
		if toolOutput.ToolName == toolcall.Function.Name {
			toolFound = true
			a.updateProcedureByToolname(toolcall.Function.Name, toolOutput.Output)
			a.ThoughtEventReply(ctx, bs)
			a.updateAgentMemory(ctx, toolcall.Id, toolOutput.Output)
			a.dao.SetSessionUnInterrupt(ctx, appbizid, bs.SessionID) // 中断处理完成，删除中断状态
			return status.AgentStatus.CurrentStep, nil
		}
	}
	if !toolFound {
		log.WarnContextf(ctx, "user submit output not found tool, LocalToolOuputs: %v, lastRsp: %v",
			bs.ToolOuputs, status.LastLLmRsp)
		return 0, pkg.ErrAgentLocalToolsNotFound
	}
	return 0, nil
}
