package agent

import (
	"context"
	"encoding/json"
	"strconv"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/agent_config_server"
	kpb "git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_knowledge_config_server"
	pluginRun "git.woa.com/dialogue-platform/lke_proto/pb-protocol/plugin_exec_server"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/eventbus/botsession"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
)

// 该文件实现与知识库相关的插件处理逻辑

// adapterKnowledgeRuntoolReq 增加知识库插件的 runtool req 参数
func (a *ChatAgentImpl) adapterKnowledgeRuntoolReq(
	ctx context.Context,
	req *pluginRun.RunToolReq,
	bs *botsession.BotSession,
	tool *model.AgentTool,
) error {

	plugin := tool.Plugin
	if !a.isValidKnowledgePlugin(ctx, plugin) {
		return nil
	}

	a.initExtraInfo(req)

	if err := a.setupKnowledgeFilter(ctx, req, plugin, bs); err != nil {
		return err
	}

	a.setupModelConfig(req, plugin)
	return nil
}

// isValidKnowledgePlugin 检查插件配置是否有效
func (a *ChatAgentImpl) isValidKnowledgePlugin(
	ctx context.Context, plugin *agent_config_server.DescribeAppAgentListRsp_AgentPluginInfo,
) bool {
	if plugin == nil || plugin.GetKnowledgeQa() == nil {
		log.WarnContextf(ctx, "agent knowledge plugin config is nil")
		return false
	}
	return true
}

// initExtraInfo 初始化 ExtraInfo 结构
func (a *ChatAgentImpl) initExtraInfo(req *pluginRun.RunToolReq) {
	if req.GetExtraInfo() == nil {
		req.ExtraInfo = &pluginRun.ToolExtraInfo{}
	}
	if req.GetExtraInfo().GetAgentKnowledgeQaConfig() == nil {
		req.ExtraInfo.AgentKnowledgeQaConfig = &pluginRun.AgentKnowledgeQAConfig{}
	}
	req.ExtraInfo.Labels = nil // 清空 labels，采用新的方式传递 labels
}

// setupKnowledgeFilter 设置知识库过滤器配置
func (a *ChatAgentImpl) setupKnowledgeFilter(
	ctx context.Context, req *pluginRun.RunToolReq,
	plugin *agent_config_server.DescribeAppAgentListRsp_AgentPluginInfo, bs *botsession.BotSession,
) error {

	filter := plugin.GetKnowledgeQa().GetFilter()
	if filter == nil {
		return nil
	}

	// 基础过滤器配置
	req.ExtraInfo.AgentKnowledgeQaConfig.Filter = &pluginRun.KnowledgeFilter{
		FilterType: pluginRun.KnowledgeFilterEnum(filter.GetFilterType()),
	}

	// 设置文档和答案配置
	if err := a.setupDocAndAnswerConfig(req, filter); err != nil {
		return err
	}

	// 处理标签过滤器
	if a.isTagFilterType(filter) {
		return a.setupTagFilter(ctx, req, filter, bs)
	}

	return nil
}

// setupDocAndAnswerConfig 设置文档和答案配置
func (a *ChatAgentImpl) setupDocAndAnswerConfig(req *pluginRun.RunToolReq,
	filter *agent_config_server.KnowledgeFilter) error {

	data, err := json.Marshal(filter.GetDocAndAnswer())
	if err != nil {
		return err
	}

	docAndAnswer := &pluginRun.KnowledgeFilterDocAndAnswer{}
	if err := json.Unmarshal(data, docAndAnswer); err != nil {
		return err
	}

	req.ExtraInfo.AgentKnowledgeQaConfig.Filter.DocAndAnswer = docAndAnswer
	return nil
}

// isTagFilterType 判断是否为标签过滤器类型
func (a *ChatAgentImpl) isTagFilterType(filter *agent_config_server.KnowledgeFilter) bool {
	return filter.GetFilterType() == agent_config_server.KnowledgeFilterEnum_KNOWLEDGE_FILTER_TYPE_TAG &&
		filter.GetTag() != nil
}

// setupTagFilter 设置标签过滤器
func (a *ChatAgentImpl) setupTagFilter(ctx context.Context, req *pluginRun.RunToolReq,
	filter *agent_config_server.KnowledgeFilter, bs *botsession.BotSession) error {

	tag := filter.GetTag()
	req.ExtraInfo.AgentKnowledgeQaConfig.Filter.TagOperator =
		pluginRun.KnowledgeFilter_TagOperatorEnum(tag.GetOperator())

	labels, err := a.processLabels(ctx, tag.GetLabels(), bs)
	if err != nil {
		return err
	}

	req.ExtraInfo.Labels = labels
	return nil
}

// processLabels 处理标签数据
func (a *ChatAgentImpl) processLabels(
	ctx context.Context,
	inputLabels []*agent_config_server.KnowledgeFilterTag_KnowledgeAttrLabel, bs *botsession.BotSession,
) ([]*pluginRun.Label, error) {

	attrBizIds := a.extractAttrBizIds(inputLabels)
	resMap, err := a.dao.DescribeAttributeLabel(ctx, strconv.FormatUint(bs.App.GetAppBizId(), 10), attrBizIds)
	if err != nil {
		log.ErrorContextf(ctx, "DescribeAttributeLabel error: %v", err)
		return nil, err
	}

	log.InfoContextf(ctx, "attrBizIds: %v, resMap: %v", attrBizIds, helper.Object2String(resMap))

	var labels []*pluginRun.Label
	for _, inputLabel := range inputLabels {
		if label := a.buildLabel(ctx, inputLabel, resMap, bs); label != nil {
			labels = append(labels, label)
		}
	}

	return labels, nil
}

// extractAttrBizIds 提取属性业务ID列表
func (a *ChatAgentImpl) extractAttrBizIds(
	labels []*agent_config_server.KnowledgeFilterTag_KnowledgeAttrLabel,
) []uint64 {
	attrBizIds := make([]uint64, 0, len(labels))
	for _, label := range labels {
		attrBizIds = append(attrBizIds, label.GetAttributeBizId())
	}
	return attrBizIds
}

// buildLabel 构建单个标签
func (a *ChatAgentImpl) buildLabel(
	ctx context.Context, inputLabel *agent_config_server.KnowledgeFilterTag_KnowledgeAttrLabel,
	resMap map[uint64]*kpb.GetAttributeInfoRsp_AttrLabelInfo, bs *botsession.BotSession,
) *pluginRun.Label {

	rsp, exists := resMap[inputLabel.GetAttributeBizId()]
	if !exists || rsp == nil {
		return nil
	}

	values := a.extractLabelValues(ctx, inputLabel.GetInputs(), rsp, bs)
	if len(values) == 0 {
		return nil // 筛选的标签集不能为空
	}

	return &pluginRun.Label{
		Name:   rsp.GetAttrKey(),
		Values: values,
	}
}

// extractLabelValues 提取标签值
func (a *ChatAgentImpl) extractLabelValues(
	ctx context.Context, inputs []*agent_config_server.Input,
	rsp *kpb.GetAttributeInfoRsp_AttrLabelInfo, bs *botsession.BotSession,
) []string {

	var values []string
	for _, input := range inputs {
		vs := a.getLabelValue(ctx, bs, input)
		for _, v := range vs {
			if v == "0" {
				continue // 跳过无效值
			}

			attrValue := a.convertToLabelName(v, rsp)
			values = append(values, attrValue)
		}
	}
	return values
}

// convertToLabelName 将ID转换为标签名称
func (a *ChatAgentImpl) convertToLabelName(
	value string, rsp *kpb.GetAttributeInfoRsp_AttrLabelInfo,
) string {
	id, err := strconv.ParseUint(value, 10, 64)
	if err != nil {
		return value // 如果不是数字ID，直接返回原值
	}

	for _, tag := range rsp.GetLabels() {
		if tag.GetLabelBizId() == id {
			return tag.GetLabelName()
		}
	}
	return value // 找不到对应标签名，返回原值
}

// setupModelConfig 设置模型配置
func (a *ChatAgentImpl) setupModelConfig(
	req *pluginRun.RunToolReq, plugin *agent_config_server.DescribeAppAgentListRsp_AgentPluginInfo,
) {
	model := plugin.GetModel()
	if model == nil {
		return
	}

	req.ExtraInfo.AgentKnowledgeQaConfig.AgentModel = &pluginRun.AgentModelInfo{
		ModelName:   model.GetModelName(),
		Temperature: model.GetTemperature(),
		TopP:        model.GetTopP(),
	}
}
