package agent

import (
	"context"
	"io"
	"net/http"
	"regexp"
	"strconv"
	"strings"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/proto/pb-stub/openapi"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model/event"
	secapi "git.woa.com/sec-api/go/scurl"
	jsoniter "github.com/json-iterator/go"
)

// CodeObject 代码解释器
type CodeObject struct {
	Code string `json:"Code"`
}

// specialDisplayTools 根据工具类型，设置不同的调试展示内容
func (a *ChatAgentImpl) specialDisplayTools(
	ctx context.Context,
	procedure event.AgentProcedure,
	toolCall *openapi.ToolCall,
	toolResponse string,
) event.AgentProcedure {
	// 始终保留原始响应
	procedure.Debugging.Content = toolResponse
	toolName := toolCall.GetFunction().GetName()

	switch {
	case isSearchTool(toolName):
		if resp, err := unmarshal[model.HunYuanResponse](toolResponse); err == nil {
			procedure.Debugging.DisplayType = event.DisplayTypeSearch
			procedure.Debugging.References = model.PluginReferenceConvert(resp.Data.References)
			if isDeepSeekSearchTool(toolName) {
				c, quote := ExtractQuotes(resp.Data.Answer)
				procedure.Debugging.DisplayContent = c
				procedure.Debugging.QuoteInfos = quote
			} else {
				procedure.Debugging.DisplayContent = resp.Data.Answer
				procedure.Debugging.QuoteInfos = resp.Data.QuoteInfos
			}
		} else {
			log.WarnContextf(ctx, "unmarshal tool response error: %v, response: %s", err, toolResponse)
		}

	case toolName == ToolNameKnowledge || toolName == ToolNameBoundedKnowledgeQA:
		if resp, err := unmarshal[model.KnowledgeResponse](toolResponse); err == nil {
			procedure.Debugging.DisplayType = event.DisplayTypeKnowledge
			procedure.Debugging.DisplayThought = resp.Data.Thought
			procedure.Debugging.DisplayContent = resp.Data.Answer
			procedure.Debugging.References = model.PluginReferenceConvert(resp.Data.References)
		} else {
			log.WarnContextf(ctx, "unmarshal tool response error: %v, response: %s", err, toolResponse)
		}

	case isCodeInterpreter(toolName):
		// 将代码解释器相关展示逻辑聚合到一起
		procedure = a.handleCodeInterpDisplay(procedure, ctx, toolCall, toolResponse)

	case toolName == ToolNameCalculator:
		procedure.Debugging.Content = "已完成科学计算"

	case toolName == ToolNameTextToImage:
		a.getTextToImageURL(ctx, toolResponse)
	}

	return procedure
}

// unmarshal 通用反序列化，避免重复写 Unmarshal 逻辑
// NOCA:staticcheck(设计如此)
func unmarshal[T any](data string) (*T, error) {
	var obj T
	if err := jsoniter.Unmarshal([]byte(data), &obj); err != nil {
		return nil, err
	}
	return &obj, nil
}

// handleCodeInterpDisplay 专门处理代码解释器的展示逻辑
func (a *ChatAgentImpl) handleCodeInterpDisplay(
	procedure event.AgentProcedure,
	ctx context.Context,
	toolCall *openapi.ToolCall,
	toolResponse string,
) event.AgentProcedure {
	procedure.Debugging.Content = a.handleCodeInterpreterDisplay(toolCall.GetFunction().GetArguments())
	a.handleCodeInterpreterFiles(ctx, toolResponse)
	a.handleEChartsDisplay(ctx, toolCall, toolResponse)
	return procedure
}

// handleCodeInterpreterDisplay 处理代码解释器展示
func (a *ChatAgentImpl) handleCodeInterpreterDisplay(code string) string {
	codeObject := &CodeObject{}
	err := jsoniter.Unmarshal([]byte(code), codeObject)
	if err != nil {
		return code
	}
	result := "```python\n" + codeObject.Code + "\n```"
	return result
}

// handleEChartsDisplay 处理ECharts展示
func (a *ChatAgentImpl) handleEChartsDisplay(ctx context.Context, toolCall *openapi.ToolCall, toolResponse string) {
	toolName := toolCall.GetFunction().GetName()
	if toolName == ToolNameCharts && codeIncludePyECharts(toolCall.GetFunction().GetArguments()) {
		response := &model.EChartsResponse{}
		err := jsoniter.Unmarshal([]byte(toolResponse), response)
		if err != nil {
			return
		}

		if fileIsHTML(*response) {
			echartsJSON := fetchJSONFromURL(ctx, response.Data.Files[0].URL)
			if len(echartsJSON) > 0 {
				a.EChartsInfo = append(a.EChartsInfo, echartsJSON)
			}
		}
	}
}

// codeIncludePyECharts 代码内包含pyechart包
func codeIncludePyECharts(code string) bool {
	return strings.Contains(code, "pyecharts")
}

// fileIsHTML 文件是否是HTML
func fileIsHTML(response model.EChartsResponse) bool {
	return len(response.Data.Files) > 0 && len(response.Data.Files[0].URL) > 0 &&
		strings.HasSuffix(response.Data.Files[0].FileName, ".html")
}

// fetchJSONFromURL 从URL获取JSON
func fetchJSONFromURL(ctx context.Context, url string) string {
	// 发送HTTP请求获取HTML内容
	safeClient := secapi.NewSafeClient(secapi.WithUnsafeDomain(config.GetUnsafeDomain()),
		secapi.WithAllowPorts([]string{"80", "443"}))

	httpReq, err := http.NewRequest("GET", url, nil)
	if err != nil {
		log.ErrorContextf(ctx, "failed to create request: %v", err)
		return ""
	}

	// 基于安全请求的客户端，发起安全请求
	resp, err := safeClient.Do(httpReq)
	if err != nil {
		log.ErrorContextf(ctx, "failed to fetch URL: %v", err)
		return ""
	}

	defer resp.Body.Close()

	// 读取HTML响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		log.ErrorContextf(ctx, "failed to read body: %v", err)
		return ""
	}

	// 使用正则表达式提取JSON部分
	re := regexp.MustCompile(`(?s)option_\w+\s*=\s*(\{.*?\});`)
	matches := re.FindSubmatch(body)
	if len(matches) < 2 {
		log.ErrorContextf(ctx, "failed to parse JSON from URL: %s", url)
		return ""
	}
	return string(matches[1])
}

// getTextToImageURL 获取文本转图片的URL
func (a *ChatAgentImpl) getTextToImageURL(ctx context.Context, toolResponse string) {
	var response model.TextToImageResponse
	if err := jsoniter.Unmarshal([]byte(toolResponse), &response); err != nil {
		return
	}
	for _, result := range response.Data.Results {
		fileName, fileType := helper.GetFileNameAndType(result.URL)
		a.Files = append(a.Files, &model.FileInfo{
			FileName:  fileName,
			FileURL:   result.URL,
			FileType:  fileType,
			CreatedAt: time.Now().Unix(),
		})
	}
	log.DebugContextf(ctx, "get text to image url: %v", a.Files)
}

// handleCodeInterpreterFiles 处理代码解释器生成的代码
func (a *ChatAgentImpl) handleCodeInterpreterFiles(ctx context.Context, toolResponse string) {
	response := &model.EChartsResponse{}
	err := jsoniter.Unmarshal([]byte(toolResponse), response)
	if err != nil {
		return
	}

	for _, file := range response.Data.Files {
		if len(file.URL) > 0 {
			fileName, fileType := helper.GetFileNameAndType(file.URL)
			if file.Size < 0 {
				file.Size = 0
			}
			a.Files = append(a.Files, &model.FileInfo{
				FileName:  fileName,
				FileURL:   file.URL,
				FileType:  fileType,
				FileSize:  strconv.Itoa(file.Size),
				CreatedAt: time.Now().Unix(),
			})
		}
	}

	if len(response.Data.Files) > 0 && len(response.Data.Files[0].URL) > 0 {
		_, fileType := helper.GetFileNameAndType(response.Data.Files[0].URL)
		a.Files = append(a.Files, &model.FileInfo{
			FileName:  response.Data.Files[0].FileName,
			FileSize:  strconv.Itoa(response.Data.Files[0].Size),
			FileURL:   response.Data.Files[0].URL,
			FileType:  fileType,
			CreatedAt: time.Now().Unix(),
		})
	}
	log.DebugContextf(ctx, "handle Code InterpreterFiles: %v", a.Files)
}
