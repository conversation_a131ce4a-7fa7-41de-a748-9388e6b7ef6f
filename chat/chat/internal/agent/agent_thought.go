package agent

import (
	"context"
	"strings"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	llmm "git.woa.com/dialogue-platform/proto/pb-stub/llm-manager-server"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	"git.woa.com/ivy/qbot/qbot/chat/internal/eventbus/botsession"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model/event"
)

// ThoughtEventReply 思考事件回复
func (a *ChatAgentImpl) ThoughtEventReply(ctx context.Context, bs *botsession.BotSession) {
	ctx, cancel := context.WithCancel(ctx)
	defer cancel()
	if len(a.thought.Procedures) > 0 &&
		a.thought.Procedures[len(a.thought.Procedures)-1].Status != event.ProcedureStatusProcessing {
		displayStatus := a.thought.Procedures[len(a.thought.Procedures)-1].Debugging.DisplayStatus
		result := strings.Split(displayStatus, " ")
		if len(result) > 0 {
			a.thought.Procedures[len(a.thought.Procedures)-1].Debugging.DisplayStatus = result[0]
		}
	}
	_ = a.dao.DoEmitWsClient(ctx, bs.To.ClientID, a.thought, cancel)
}

// EvilThoughtEventReply 敏感思考事件回复
func (a *ChatAgentImpl) EvilThoughtEventReply(ctx context.Context, bs *botsession.BotSession) {
	ctx, cancel := context.WithCancel(ctx)
	defer cancel()
	if len(a.thought.Procedures) > 0 {
		a.thought.Procedures[len(a.thought.Procedures)-1].Debugging.Content = "思考内容异常"
		a.thought.Procedures[len(a.thought.Procedures)-1].Status = event.ProcedureStatusFailed
	}
	_ = a.dao.DoEmitWsClient(ctx, bs.To.ClientID, a.thought, cancel)
}

// initThoughtEvent 初始化一个思考事件
func (a *ChatAgentImpl) initThoughtEvent(ctx context.Context, bs *botsession.BotSession) {
	a.thought.SessionID = bs.SessionID
	a.thought.RequestID = bs.RequestID
	a.thought.RecordID = bs.RecordID
	a.thought.StartTime = bs.StartTime
	a.thought.IsWorkflow = bs.AgentStatus.AgentType == model.AgentTypeWorkflow
	a.thought.WorkflowName = bs.AgentStatus.WorkflowName

	a.thought.Procedures = make([]event.AgentProcedure, 0)

	log.InfoContextf(ctx, "initThoughtEvent: %s", a.thought.String())
}

// addProcedure 添加一个过程
func (a *ChatAgentImpl) addProcedure(procedure event.AgentProcedure) {
	procedure.Index = uint32(len(a.thought.Procedures))
	procedure.StartTime = time.Now()
	// a.thought.WorkflowName = procedure.WorkflowName
	a.thought.Procedures = append(a.thought.Procedures, procedure) // 可能还需要添加一些其他的逻辑
}

// addThoughtProcedure 添加一个思考过程
func (a *ChatAgentImpl) addThoughtProcedure(content string) {

	procedure := event.AgentProcedure{
		Index:     0,
		Name:      "thought",
		Title:     "思考",
		Status:    event.ProcedureStatusProcessing,
		Icon:      config.AgentConfig.ThoughtIcon,
		Debugging: event.AgentProcedureDebugging{Content: content},
	}

	procedure.Index = uint32(len(a.thought.Procedures))
	procedure.StartTime = time.Now()
	a.thought.Procedures = append(a.thought.Procedures, procedure) // 可能还需要添加一些其他的逻辑
}

// addHandoffThought 添加一个转交思考过程
func (a *ChatAgentImpl) addHandoffThought(procedure event.AgentProcedure) {
	startTime := a.thought.StartTime
	if len(a.thought.Procedures) > 1 {
		last := a.thought.Procedures[len(a.thought.Procedures)-1]
		startTime = last.StartTime.Add(time.Duration(last.Elapsed) * time.Millisecond)
	}
	procedure.Index = uint32(len(a.thought.Procedures))
	procedure.StartTime = startTime
	procedure.Elapsed = uint32(time.Since(procedure.StartTime).Milliseconds())
	a.thought.Elapsed = uint32(time.Since(a.thought.StartTime).Milliseconds())
	a.thought.Procedures = append(a.thought.Procedures, procedure) // 可能还需要添加一些其他的逻辑
}

// updateThoughtProcedure 更新一个思考过程
func (a *ChatAgentImpl) updateThoughtProcedure(procedure event.AgentProcedure) {
	if len(a.thought.Procedures) == 0 {
		return
	}

	current := a.thought.Procedures[len(a.thought.Procedures)-1]
	// 保留不变的。 其实可以换个做法，只改更新的值
	procedure.Index = current.Index
	procedure.StartTime = current.StartTime
	procedure.Elapsed = uint32(time.Since(current.StartTime).Milliseconds())
	// 计算时间
	a.thought.Elapsed = uint32(time.Since(a.thought.StartTime).Milliseconds())
	a.thought.Files = a.Files
	// a.thought.IsWorkflow = procedure.IsWorkflow
	// a.thought.WorkflowName = procedure.WorkflowName
	a.thought.Procedures[len(a.thought.Procedures)-1] = procedure
}

// addOrUpdateProcedure 根据 Title 判断是新增还是更新过程
func (a *ChatAgentImpl) addOrUpdateProcedure(proc event.AgentProcedure) {
	n := len(a.thought.Procedures)
	if n > 0 {
		last := a.thought.Procedures[n-1]
		if last.Title == proc.Title {
			// 相同 Title，更新最后一个过程
			a.updateThoughtProcedure(proc)
			return
		}
	}
	// 不存在或 Title 不同，新增过程
	a.addProcedure(proc)
}

// processAgentThought 处理智能体的思考事件的返回
func (a *ChatAgentImpl) processAgentThought(ctx context.Context, bs *botsession.BotSession,
	rsp *llmm.Response, throttleStreaming helper.Throttle, forceOutput bool) {
	var reply string
	if len(rsp.GetMessage().GetReasoningContent()) > 0 {
		reply = rsp.GetMessage().GetReasoningContent()
	} else {
		reply = rsp.GetMessage().GetContent()
	}
	if forceOutput {
		log.InfoContextf(ctx, "rsp of last thought: %s", helper.Object2StringEscapeHTML(rsp))
	}
	if throttleStreaming.Hit(len([]rune(reply)), forceOutput) {
		procedure := event.AgentProcedure{
			Index:           0,
			Name:            "thought",
			Title:           "思考",
			Status:          event.ProcedureStatusProcessing,
			Icon:            config.AgentConfig.ThoughtIcon,
			Debugging:       event.AgentProcedureDebugging{Content: reply},
			WorkflowName:    bs.AgentStatus.WorkflowName,
			TargetAgentName: bs.AgentStatus.CurrentAgent,
			AgentIcon:       a.agentContainer.GetAgentIcon(bs.AgentStatus.CurrentAgent),
		}

		if forceOutput {
			procedure.Status = event.ProcedureStatusSuccess
		}
		a.updateThoughtProcedure(procedure)
		a.ThoughtEventReply(ctx, bs)
	}
	if rsp.Finished {
		// 处理转交
		if a.hitHandoff(ctx, bs, rsp) {
			procedure := event.AgentProcedure{
				Index:           0,
				Name:            "thought",
				Title:           "Agent 转交",
				Status:          event.ProcedureStatusSuccess,
				Icon:            config.AgentConfig.ThoughtIcon,
				AgentIcon:       a.agentContainer.GetAgentIcon(bs.AgentStatus.CurrentAgent),
				Switch:          bs.AgentStatus.AgentType,
				SourceAgentName: bs.AgentStatus.GetLastParentAgent(),
				TargetAgentName: bs.AgentStatus.CurrentAgent,
			}
			// 如果转交给工作流
			if workflow, ok := a.hitWorkflow(ctx, bs, rsp); ok {
				a.thought.IsWorkflow = true
				a.thought.WorkflowName = workflow.WorkflowName
				procedure.WorkflowName = workflow.WorkflowName
			}
			a.addHandoffThought(procedure)
			log.DebugContextf(ctx, "handoff a child add procedure: %s, current agent: %s",
				helper.Object2StringEscapeHTML(procedure), helper.Object2StringEscapeHTML(bs.AgentStatus.CurrentAgent))
			a.ThoughtEventReply(ctx, bs)
		} else if a.isHandoffToParent(bs, rsp, forceOutput) {
			// 如果向上转交
			procedure := event.AgentProcedure{
				Index:           0,
				Name:            "thought",
				Title:           "Agent 转交",
				Status:          event.ProcedureStatusSuccess,
				Icon:            config.AgentConfig.ThoughtIcon,
				AgentIcon:       a.agentContainer.GetAgentIcon(bs.AgentStatus.GetLastParentAgent()),
				Switch:          model.AgentTypeMain,
				SourceAgentName: bs.AgentStatus.CurrentAgent,
				TargetAgentName: bs.AgentStatus.GetLastParentAgent(),
				WorkflowName:    a.thought.WorkflowName,
			}
			a.thought.IsWorkflow = false
			a.thought.WorkflowName = ""
			a.addHandoffThought(procedure)
			log.DebugContextf(ctx, "handoff parent add procedure: %s, current agent: %s",
				helper.Object2StringEscapeHTML(procedure), helper.Object2StringEscapeHTML(bs.AgentStatus.CurrentAgent))
			a.ThoughtEventReply(ctx, bs)
		}
	}
}

// updateProcedureByToolname 更新一个思考过程
func (a *ChatAgentImpl) updateProcedureByToolname(toolName string, content string) {
	if len(a.thought.Procedures) == 0 {
		return
	}

	// 从后往前找到第一个 tool 更新
	for i := len(a.thought.Procedures) - 1; i >= 0; i-- {
		procedure := &a.thought.Procedures[i]
		if procedure.Name == toolName {
			procedure.Elapsed = uint32(time.Since(procedure.StartTime).Milliseconds())
			procedure.Debugging.Content = content
			a.thought.Elapsed = uint32(time.Since(a.thought.StartTime).Milliseconds())
			return
		}
	}
}

// updateThoughtFiles 更新一个思考过程中的Files
func (a *ChatAgentImpl) updateThoughtFiles() {
	if len(a.thought.Procedures) == 0 {
		return
	}

	a.thought.Files = a.Files
}

// getCurrentThoughtContent 获取当前思考内容
func (a *ChatAgentImpl) getCurrentThoughtContent() string {
	for i := len(a.thought.Procedures) - 1; i >= 0; i-- {
		if procedure := a.thought.Procedures[i]; procedure.Name == "thought" {
			return procedure.Debugging.Content
		}
	}
	return ""
}
