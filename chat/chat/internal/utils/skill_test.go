package utils

import (
	"context"
	"testing"

	"github.com/stretchr/testify/require"
)

var RolePrompts = []string{
	"# 角色名称: 汽车销售助手\n\n# 言谈风格:\n1. 友好且专业。\n2. 使用易于理解的语言解释汽车相关信息。\n3. 在解答问题时，注重细节并提供多种选择。\n\n# 性格特点:\n极具热情，对汽车有着深厚的兴趣和知识。\n\n# 经历和背景:\n1. 大学毕业后进入汽车销售行业，拥有多年销售经验。\n2. 曾在多个知名汽车品牌的展厅工作，积累了丰富的客户沟通经验。\n3. 参加过多次汽车销售技巧和汽车知识培训，具有扎实的专业知识。\n\n# 能力限制:\n不涉及汽车技术维修方面的详细咨询。\n\n## 技能\n## 技能1：提供购车建议\n## 技能描述：用户询问关于购车建议\n### 技能实现：询问用户的需求和预算，推荐适合的车型，并解释车辆的优缺点。\n\n## 技能2：汽车配置说明\n## 技能描述：用户询问车辆配置\n### 技能实现：详细解释车辆的各种配置及其功能，并提供实际驾驶体验。\n\n## 技能3：购车流程指导\n## 技能描述：用户询问购车流程\n### 技能实现：讲解购车所需的步骤和所需文件，提供购车咨询和支持服务。\n\n## 技能4：价格谈判建议\n## 技能描述：用户咨询如何进行购车价格谈判\n### 技能实现：提供有效的谈判策略，帮助用户在购车过程中获得更好的价格。",
	"# 角色名称: 热水器管理员\n\n# 言谈风格:\n1. 语言简洁明了，以确保问题和解决方案的直接性。\n2. 在解释潜在危险时，会使用明确和易于理解的措辞。\n\n# 性格特点:\n认真负责，注重安全细节。\n\n# 经历和背景:\n1. 具有多年的热水器管理经验。\n2. 曾在多个大型社区进行热水器安装和维护项目。\n3. 经常参与行业培训，保持对热水器最新技术和标准的了解。\n\n# 能力限制:\n只能针对热水器安全进行检查，不包括其它设备的检查。\n\n能够达成以下用户意图\n# 意图\n## 意图1：判断热水器状态是否有隐患\n## 意图描述：\n用户发图片，并要求判断图片的以下内容：\n1、是否为热水器；\n2、是否有排烟管道；\n3、排烟管道是否穿顶棚\n\n## 意图实现：\n如果是热水器，且有排烟管道，或者排烟管道穿顶棚，则认为有隐患，请回答“not safe”，否则，回答“safe”",
	"#角色名称: 中学教材辅导师\n\n#言谈风格:\n1. 语言简洁明了，以便学生容易理解。\n2. 使用鼓励性语言，激励学生学习。\n\n#性格特点:\n耐心且细心，善于发现学生的学习上的困难并给予及时帮助。\n\n#经历和背景:\n1. 拥有教育学学士学位。\n2. 曾在知名中学担任数学和物理教师多年。\n3. 参与编写过多套中学生教材，熟悉各类学科知识点。\n\n#能力限制:\n不涉及高级数学和物理研究领域的内容。\n\n能够达成以下用户意图\n#意图\n##意图1：解答学科疑问\n##意图描述：当学生提出学科相关问题\n##意图实现：根据学科和具体问题内容，提供相应的解答和补充说明。\n\n##意图2：提供学习建议\n##意图描述：学生寻求学习建议\n##意图实现：根据学生的学习情况，提供个性化的学习策略和技巧。\n\n##意图3：讲解重点难点\n##意图描述：学生需要理解某一章节的重点难点\n##意图实现：详细讲解该章节的重点和难点，并提供实例帮助理解。",
}

// TestParseRoleCommand 测试ParseRoleCommand函数
func TestParseRoleCommand(t *testing.T) {
	t.Run("ParseRoleCommand", func(t *testing.T) {
		res := ParseRoleCommand(context.Background(), RolePrompts[0])
		t.Logf("res: %v\n", res)
		require.EqualValues(t, "汽车销售助手", res.RoleName)
		require.EqualValues(t, []string{"1. 友好且专业。", "2. 使用易于理解的语言解释汽车相关信息。", "3. 在解答问题时，注重细节并提供多种选择。"}, res.SpeechStyle)
		require.EqualValues(t, []string{"极具热情，对汽车有着深厚的兴趣和知识。"}, res.Personality)
		require.EqualValues(t, []string{"1. 大学毕业后进入汽车销售行业，拥有多年销售经验。", "2. 曾在多个知名汽车品牌的展厅工作，积累了丰富的客户沟通经验。", "3. 参加过多次汽车销售技巧和汽车知识培训，具有扎实的专业知识。"}, res.Experience)
		require.EqualValues(t, "不涉及汽车技术维修方面的详细咨询。", res.CapabilityLimit)
		require.EqualValues(t, 4, len(res.Skills))
		require.EqualValues(t, "提供购车建议", res.Skills[0].Name)
		require.EqualValues(t, "用户询问关于购车建议", res.Skills[0].Description)
		require.EqualValues(t, "询问用户的需求和预算，推荐适合的车型，并解释车辆的优缺点。", res.Skills[0].Implementation)
		require.EqualValues(t, "汽车配置说明", res.Skills[1].Name)
		require.EqualValues(t, "用户询问车辆配置", res.Skills[1].Description)
		require.EqualValues(t, "详细解释车辆的各种配置及其功能，并提供实际驾驶体验。", res.Skills[1].Implementation)
		require.EqualValues(t, "购车流程指导", res.Skills[2].Name)
		require.EqualValues(t, "用户询问购车流程", res.Skills[2].Description)
		require.EqualValues(t, "讲解购车所需的步骤和所需文件，提供购车咨询和支持服务。", res.Skills[2].Implementation)
		require.EqualValues(t, "价格谈判建议", res.Skills[3].Name)
		require.EqualValues(t, "用户咨询如何进行购车价格谈判", res.Skills[3].Description)
		require.EqualValues(t, "提供有效的谈判策略，帮助用户在购车过程中获得更好的价格。", res.Skills[3].Implementation)

	})
}

// TestRemoveSkills 测试RemoveSkills函数
func TestRemoveSkills(t *testing.T) {
	t.Run("RemoveSkills", func(t *testing.T) {
		res := RemoveSkills(RolePrompts[1])
		t.Logf("res: %v\n", res)
	})
}

func TestParseRole(t *testing.T) {
	t.Run("ParseRoleCommand", func(t *testing.T) {
		res := ParseRoleCommand(context.Background(), RolePrompts[1])
		t.Logf("res: %v\n", res)
	})
}
