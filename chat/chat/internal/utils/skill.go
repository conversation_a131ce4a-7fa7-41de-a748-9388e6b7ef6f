package utils

import (
	"context"
	"regexp"
	"strings"

	"git.woa.com/dialogue-platform/common/v3/plugins/i18n"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model/event"
)

// ParseRoleCommand 解析角色指令内容
func ParseRoleCommand(ctx context.Context, content string) event.RoleCommand {
	content = content + "\n#"
	roleNameMatch := regexp.MustCompile(i18n.Translate(ctx, config.App().Bot.RoleCommandRegx.RoleNameRegex))
	speechStyleMatch := regexp.MustCompile(i18n.Translate(ctx, config.App().Bot.RoleCommandRegx.SpeechStyleRegex))
	personalityMatch := regexp.MustCompile(i18n.Translate(ctx, config.App().Bot.RoleCommandRegx.PersonalityRegex))
	experienceMatch := regexp.MustCompile(i18n.Translate(ctx, config.App().Bot.RoleCommandRegx.ExperienceRegex))
	capabilityLimitMatch := regexp.MustCompile(i18n.Translate(ctx, config.App().Bot.RoleCommandRegx.CapabilityLimitRegex))

	roleName := roleNameMatch.FindStringSubmatch(content)
	speechStyle := speechStyleMatch.FindStringSubmatch(content)
	personality := personalityMatch.FindStringSubmatch(content)
	experience := experienceMatch.FindStringSubmatch(content)
	capabilityLimit := capabilityLimitMatch.FindStringSubmatch(content)

	roleCommand := event.RoleCommand{}
	if len(roleName) > 1 {
		roleCommand.RoleName = roleName[1]
	}
	if len(speechStyle) > 1 {
		roleCommand.SpeechStyle = strings.Split(speechStyle[1], "\n")
	}
	if len(personality) > 1 {
		roleCommand.Personality = strings.Split(personality[1], "\n")
	}
	if len(experience) > 1 {
		roleCommand.Experience = strings.Split(experience[1], "\n")
	}
	if len(capabilityLimit) > 1 {
		roleCommand.CapabilityLimit = capabilityLimit[1]
	}
	var skills []event.Skill
	skillsMatch := regexp.MustCompile(i18n.Translate(ctx, config.App().Bot.RoleCommandRegx.SkillsRegex))
	skillsMatches := skillsMatch.FindAllStringSubmatch(content, -1)
	for _, match := range skillsMatches {
		skill := event.Skill{
			Name:           match[2],
			Description:    match[4],
			Implementation: match[6],
		}
		skills = append(skills, skill)
	}
	roleCommand.Skills = skills
	return roleCommand
}

// RemoveSkills 移出技能相关的内容
func RemoveSkills(content string) string {
	// 找到 ## 技能 开头的内容
	pos := strings.Index(content, "## 意图\n## 意图1")
	if pos == -1 {
		return content
	}
	return content[:pos]
}
