package retrieval

import (
	"context"
	"sync"
	"time"
	"unicode/utf8"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/common/v3/errors"
	"git.woa.com/dialogue-platform/go-comm/clues"
	"git.woa.com/dialogue-platform/go-comm/pf"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/knowledge"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	"git.woa.com/ivy/qbot/qbot/chat/internal/eventbus/botsession"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model/event"
	"git.woa.com/ivy/qbot/qbot/chat/internal/utils"
	"git.woa.com/ivy/qbot/qbot/chat/pkg"
	jsoniter "github.com/json-iterator/go"
)

func getSearchSceneType(bs *botsession.BotSession) knowledge.SceneType {
	if bs.EventSource == event.EventExperience {
		return knowledge.SceneType_TEST
	}
	return knowledge.SceneType_PROD
}

// basicSearchCheck 检查基础检索条件
func basicSearchCheck(ctx context.Context, bs *botsession.BotSession) bool {
	if utf8.RuneCountInString(bs.OriginContent) > config.GetSearchThreshold() {
		log.InfoContextf(ctx, "basicSearch, originContent too long, return.")
		return false
	}
	if len(bs.WorkflowID) > 0 {
		log.DebugContextf(ctx, "basicSearch, workflowSearch in workflow debug return.")
		return false
	}
	return true
}

func getModelName(bs *botsession.BotSession) string {
	if bs.ModelName == "" {
		if bs.App != nil {
			return bs.App.GetModelName()
		}
	} else {
		return bs.ModelName
	}
	return ""
}

// BasicSearch 起三个协程分别检索拒答问题、全局知识、FAQ直接回复的场景，如果有结果直接返回
// todo go协程的地方，捕获panic，发送告警
func (s *Retrieval) BasicSearch(ctx context.Context, bs *botsession.BotSession) bool {
	tik := time.Now() // 耗时点统计
	defer func() {
		log.InfoContextf(ctx, "basicSearch time cost: %v", time.Since(tik).Milliseconds())
	}()
	if !basicSearchCheck(ctx, bs) {
		return false
	}
	if s.dao.GetWorkflowUnchanged(ctx, bs.App.GetAppBizId(), bs.SessionID) != "" { // 工作流白名单 不跳出
		return false
	}
	req := &knowledge.SearchKnowledgeReq_SearchReq{ // 请求体，三个协程共用
		BotBizId:       bs.App.GetAppBizId(),
		Question:       bs.OriginContent,
		UsePlaceholder: bs.App.CanUsePlaceholder(),
		ImageUrls:      bs.Images,
		ModelName:      getModelName(bs),
	}
	// 添加搜索调试节点用于拉通上报耗时链路
	addSearchNode(ctx, req)
	// 三个协程并行请求
	var rejectedRes, globalRes, qaPriorityRes []*knowledge.SearchKnowledgeRsp_SearchRsp_Doc
	var wg sync.WaitGroup
	if bs.EventSource == event.EventExperience || bs.App.IsQARelease() { // 拒答问题 对话测试 or 用户端已发布
		wg.Add(1)
		go func() {
			defer errors.PanicHandler()
			defer wg.Done()
			rejectedRes, _ = s.dao.SearchRejectedQuestion(ctx, req, getSearchSceneType(bs))
		}()
	}
	// 全局知识检索，只有部分应用才需要检索
	if config.NeedSearchGlobalKnowledge(bs.App.GetAppBizId()) {
		wg.Add(1)
		go func() {
			defer errors.PanicHandler()
			defer wg.Done()
			globalRes, _ = s.dao.SearchGlobalKnowledge(ctx, req)
		}()
	}
	// FAQ直接回复的场景: 勾选直接回复 and (对话测试 or 用户端已发布)
	if bs.App.IsQAPriority() && (bs.EventSource == event.EventExperience || bs.App.IsQARelease()) {
		wg.Add(1)
		go func() {
			defer errors.PanicHandler()
			defer wg.Done()
			qaPriorityRes = s.SearchQaPriority(ctx, bs)
		}()
	}
	// 等待三个结果
	wg.Wait()
	return s.basicSearchResponse(ctx, bs, rejectedRes, globalRes, qaPriorityRes)
}

// basicSearchResponse 返回BasicSearch的结果
func (s *Retrieval) basicSearchResponse(ctx context.Context, bs *botsession.BotSession,
	rejectedRes, globalRes, qaPriorityRes []*knowledge.SearchKnowledgeRsp_SearchRsp_Doc) bool {
	// 优先判断拒答
	if len(rejectedRes) > 0 {
		bs.Flags.IsRejectReply = true
		bs.RejectedQuestion = rejectedRes
		log.InfoContextf(ctx, "basicSearch, rejectedRes: %s", helper.Object2String(rejectedRes))
		bs.Intent = rejectedRes[0].GetQuestion()
		return true
	}
	// 然后判断全局知识
	if len(globalRes) > 0 {
		bs.Flags.IsGlobalKnowledge = true
		bs.Knowledge = globalRes
		bs.Intent = globalRes[0].GetQuestion()
		log.InfoContextf(ctx, "basicSearch, globalRes: %s", helper.Object2String(globalRes))
		return true
	}
	// 判断FAQ
	if len(qaPriorityRes) > 0 {
		log.InfoContextf(ctx, "basicSearch, qaPriorityRes: %s", helper.Object2String(qaPriorityRes))
		bs.Flags.IsPriorityQA = true
		bs.Placeholders = utils.GetPlaceHolders(qaPriorityRes)
		bs.CustomParams = utils.GetCustomParams(qaPriorityRes)
		bs.Knowledge = qaPriorityRes
		bs.Intent = qaPriorityRes[0].GetQuestion()
		bs.IntentCate = model.IntentTypeQAPriority
		return true
	}
	return false
}

// SearchQaPriority 检索QA优先回复场景
func (s *Retrieval) SearchQaPriority(ctx context.Context,
	bs *botsession.BotSession) []*knowledge.SearchKnowledgeRsp_SearchRsp_Doc {
	// 关闭优先回复场景 or 超过检索阈值
	if config.IsClosePriority(bs.App.GetAppBizId()) ||
		utf8.RuneCountInString(bs.OriginContent) > config.GetSearchThreshold() {
		return []*knowledge.SearchKnowledgeRsp_SearchRsp_Doc{}
	}

	req := &knowledge.SearchKnowledgeReq_SearchReq{
		BotBizId:        bs.App.GetAppBizId(),
		Question:        bs.OriginContent,
		Labels:          bs.Labels,
		UsePlaceholder:  bs.App.CanUsePlaceholder(),
		ImageUrls:       bs.Images,
		SearchScope:     3,
		CustomVariables: bs.CustomVariables,
		ModelName:       getModelName(bs),
	}
	// 通用大模型，检索QA优先回复场景，阈值大于0.97的场景
	priorityRsp, _ := s.dao.SearchKnowledge(ctx, req, getSearchSceneType(bs))
	if len(priorityRsp) > 0 {
		bs.Knowledge = priorityRsp
		return priorityRsp
	}
	// 金融行业大模型，检索金融FAQ
	if bs.App.CanUseFinancialKnowledge() {
		req.BotBizId = config.App().FinancialBotBizID
		priorityRsp, _ = s.dao.SearchFinancialKnowledge(ctx, req)
		if len(priorityRsp) > 0 {
			bs.Knowledge = priorityRsp
			return priorityRsp
		}
	}
	// 医疗行业大模型，检索医疗FAQ
	if bs.App.CanUseMedicalKnowledge() {
		req.BotBizId = config.App().MedicalBotBizID
		priorityRsp, _ = s.dao.SearchMedicalKnowledge(ctx, req)
		if len(priorityRsp) > 0 {
			return priorityRsp
		}
	}
	return priorityRsp
}

// KnowledgeSearch TODO
func (s *Retrieval) KnowledgeSearch(ctx context.Context, bs *botsession.BotSession) error {
	if utf8.RuneCountInString(bs.OriginContent) > config.GetSearchThreshold() {
		return nil
	}
	if bs.Flags.IsRealTimeDocument {
		return s.RealtimeKnowledgeSearch(ctx, bs)
	}
	req := &knowledge.SearchKnowledgeReq_SearchReq{
		BotBizId:        bs.App.GetAppBizId(),
		Question:        bs.PromptCtx.Question,
		SubQuestions:    bs.QueryRewrite.ReviseQueries,
		Labels:          bs.Labels,
		UsePlaceholder:  bs.App.CanUsePlaceholder(),
		ImageUrls:       bs.Images,
		CustomVariables: bs.CustomVariables,
		ModelName:       getModelName(bs),
	}
	if len(bs.QueryRewrite.QuestionForSearch) > 0 {
		req.Question = bs.QueryRewrite.QuestionForSearch
	}
	var err error
	bs.Knowledge, err = s.dao.SearchKnowledge(ctx, req, getSearchSceneType(bs))
	if err != nil {
		log.WarnContextf(ctx, "KnowledgeSearch, req: %s,err: %v", helper.Object2String(req), err)
	}
	return nil
}

// RealtimeKnowledgeSearch 实时文档检索
func (s *Retrieval) RealtimeKnowledgeSearch(ctx context.Context, bs *botsession.BotSession) error {
	fis, err := s.GetAllFileInfos(ctx, bs)
	if err != nil {
		return err
	}
	fis = append(fis, bs.FileInfos...)
	fis = getDistinctFileInfos(fis) // fis去重

	m := bs.App.GetModel(ctx, model.AppTypeKnowledgeQA, bs.ModelType)
	m.PromptLimit = s.dao.GetModelPromptLimit(ctx, m.GetModelName()) - len([]rune(bs.SystemRole))
	bs.Knowledge, _ = s.SearchRealtime(ctx, bs, fis, int64(m.PromptLimit))
	return nil
}

// SearchQa 检索问答
func (s *Retrieval) SearchQa(ctx context.Context,
	bs *botsession.BotSession) []*knowledge.SearchKnowledgeRsp_SearchRsp_Doc {
	if utf8.RuneCountInString(bs.OriginContent) > config.GetSearchThreshold() {
		return []*knowledge.SearchKnowledgeRsp_SearchRsp_Doc{}
	}

	if bs.EventSource == event.EventSend && !bs.App.IsQARelease() { // 用户端未发布 不检索
		return []*knowledge.SearchKnowledgeRsp_SearchRsp_Doc{}
	}

	req := &knowledge.SearchKnowledgeReq_SearchReq{
		BotBizId:        bs.App.GetAppBizId(),
		Question:        bs.OriginContent,
		Labels:          bs.Labels,
		UsePlaceholder:  bs.App.CanUsePlaceholder(),
		ImageUrls:       bs.Images,
		SearchScope:     1, // 问答场景
		CustomVariables: bs.CustomVariables,
		ModelName:       getModelName(bs),
	}

	rsp, _ := s.dao.SearchKnowledge(ctx, req, getSearchSceneType(bs))
	return rsp
}

// SearchRealtime 搜索实时知识
func (s *Retrieval) SearchRealtime(ctx context.Context, bs *botsession.BotSession, fileInfos []*model.FileInfo,
	modelLength int64) ([]*knowledge.SearchKnowledgeRsp_SearchRsp_Doc, error) {

	md := helper.New()
	b, p := md.ExtractLinkWithPlaceholder([]byte(bs.OriginContent))
	log.InfoContextf(ctx, "content after Exatract: %s, placeholder: %s", string(b), helper.Object2String(p))

	docs := make([]*knowledge.SearchKnowledgeRsp_SearchRsp_Doc, 0)
	useSearch := false
	if len(fileInfos) > 1 {
		useSearch = true
	}
	if len(fileInfos) == 1 { // 多个文件的情况下，直接走检索
		// queryToken := len([]rune(string(b))) + len(p)*config.App().MultiModal.OneImageTokenLength
		textRsp, err := s.dao.GetDocFullText(ctx, &knowledge.GetDocFullTextReq{
			StorageType: knowledge.StorageType_STORAGE_REALTIME,
			DocId:       helper.GetUint64FromString(fileInfos[0].DocID),
			SessionId:   bs.Session.SessionID,
			MaxSize:     modelLength,
			// 长度的截断 要扣掉图片的长度。如果返回长度不长，但是包含好几张图片也不行。需要再处理一下。
			UsePlaceholder: false,
		})
		if err != nil {
			return docs, err
		}
		if !textRsp.GetIsCharSizeExceed() { // 字数没有超
			useSearch = false
			doc := &knowledge.SearchKnowledgeRsp_SearchRsp_Doc{
				DocType: 2,                        // 2: segment
				OrgData: textRsp.GetDocFullText(), // 这里只有
			}
			docs = []*knowledge.SearchKnowledgeRsp_SearchRsp_Doc{doc}
		} else {
			useSearch = true
		}
	}
	if useSearch {
		// 超过字数限制，使用检索
		searchLabels := make([]*knowledge.VectorLabel, 0)
		sessionLabel := &knowledge.VectorLabel{
			Name:   "SessionID",
			Values: []string{bs.Session.SessionID},
		}
		searchLabels = append(searchLabels, sessionLabel)
		docIDLabel := &knowledge.VectorLabel{
			Name:   "DocID",
			Values: []string{},
		}
		docIDLabel.Values = getAllDistinctDocIDs(fileInfos)
		searchLabels = append(searchLabels, docIDLabel)

		req := &knowledge.SearchKnowledgeReq_SearchReq{
			BotBizId:       bs.App.GetAppBizId(),
			Question:       bs.PromptCtx.Question,
			Labels:         searchLabels,
			UsePlaceholder: bs.App.CanUsePlaceholder(),
			ImageUrls:      bs.Images,
			ModelName:      getModelName(bs),
		}

		docs, _ = s.dao.SearchRealtimeKnowledge(ctx, req, getSearchSceneType(bs))
	}
	log.DebugContextf(ctx, "SearchRealtime, originQuery: %s, fileInfos: %s, docs: %s",
		bs.OriginContent, helper.Object2String(fileInfos), helper.Object2String(docs))
	return docs, nil
}

// SearchMultiDoc 多个文件的检索
func (s *Retrieval) SearchMultiDoc(ctx context.Context, bs *botsession.BotSession,
	fileInfos []*model.FileInfo) ([]*knowledge.SearchKnowledgeRsp_SearchRsp_Doc, error) {
	md := helper.New()
	b, p := md.ExtractLinkWithPlaceholder([]byte(bs.OriginContent))
	log.InfoContextf(ctx, "content after Exatract: %s, placeholder: %s", string(b), helper.Object2String(p))
	docs := make([]*knowledge.SearchKnowledgeRsp_SearchRsp_Doc, 0)
	searchLabels := make([]*knowledge.VectorLabel, 0)
	sessionLabel := &knowledge.VectorLabel{
		Name:   "SessionID",
		Values: []string{bs.Session.SessionID},
	}
	searchLabels = append(searchLabels, sessionLabel)
	docIDLabel := &knowledge.VectorLabel{
		Name:   "DocID",
		Values: []string{},
	}
	docIDLabel.Values = getAllDistinctDocIDs(fileInfos)
	searchLabels = append(searchLabels, docIDLabel)

	req := &knowledge.SearchKnowledgeReq_SearchReq{
		BotBizId:       bs.App.GetAppBizId(),
		Question:       bs.PromptCtx.Question,
		Labels:         searchLabels,
		UsePlaceholder: bs.App.CanUsePlaceholder(),
		ImageUrls:      bs.Images,
		ModelName:      getModelName(bs),
	}
	docs, err := s.dao.SearchRealtimeKnowledge(ctx, req, getSearchSceneType(bs))
	log.DebugContextf(ctx, "SearchMultiDoc, originQuery: %s, fileInfos: %s, docs: %s",
		bs.OriginContent, helper.Object2String(fileInfos), helper.Object2String(docs))
	return docs, err
}

// GetDocFullText 获取全文内容
func (s *Retrieval) GetDocFullText(ctx context.Context, bs *botsession.BotSession, fileInfo model.FileInfo,
	modelLength int64) (*model.FileInfo, error) {
	var file model.FileInfo
	textRsp, err := s.dao.GetDocFullText(ctx, &knowledge.GetDocFullTextReq{
		StorageType: knowledge.StorageType_STORAGE_REALTIME,
		DocId:       helper.GetUint64FromString(fileInfo.DocID),
		SessionId:   bs.Session.SessionID,
		MaxSize:     modelLength,
		// 长度的截断 要扣掉图片的长度。如果返回长度不长，但是包含好几张图片也不行。需要再处理一下。
		UsePlaceholder: false,
	})
	if err != nil {
		return &file, err
	}
	file.FileName = fileInfo.FileName
	file.FullText = textRsp.GetDocFullText()
	file.IsTruncated = helper.When(textRsp.GetIsCharSizeExceed(), true, false)
	return &file, nil
}

// GetAllFileInfos 获取全部文件信息， 用于  实时检索流程
func (s *Retrieval) GetAllFileInfos(ctx context.Context, botSession *botsession.BotSession) (
	fileInfos []*model.FileInfo,
	err error) {
	// 查询历史记录，获取所有doc_id
	records, err := s.dao.GetLastNBotRecord(ctx, botSession.App.AppBizId, botSession.Session,
		uint(botSession.App.GetHistoryLimit()), []model.RecordType{botSession.Type})
	if err != nil {
		return fileInfos, err
	}
	if len(records) == 0 {
		return fileInfos, nil
	}

	ids := make([]string, 0, len(records))
	for _, v := range records {
		ids = append(ids, v.RelatedRecordID)
	}

	relates, err := s.dao.GetMsgRecordsByRecordID(ctx, ids, botSession.App.AppBizId)
	clues.AddTrackDataWithError(ctx, "dao.GetMsgRecordsByRecordID", map[string]any{"ids": ids, "relates": relates}, err)
	if err != nil {
		return fileInfos, err
	}
	if len(relates) == 0 {
		return fileInfos, pkg.ErrInvalidMsgRecord
	}
	fileInfos = make([]*model.FileInfo, 0)
	for _, v := range relates {
		if len(v.FileInfos) == 0 {
			continue
		}
		temp := make([]*model.FileInfo, 0)
		err := jsoniter.UnmarshalFromString(v.FileInfos, &temp)
		if err != nil {
			return fileInfos, err // 如果部分错误，上游可以忽略错误，继续进行？
		}
		fileInfos = append(fileInfos, temp...)
	}
	return fileInfos, nil
}

func getDistinctFileInfos(fis []*model.FileInfo) []*model.FileInfo {
	fileInfos := make([]*model.FileInfo, 0)
	for _, fileInfo := range fis {
		if fileInfo == nil {
			continue
		}
		// docIDs中是否包含了fileInfo.DocID
		contains := false
		for _, v := range fileInfos {
			if v.DocID == fileInfo.DocID {
				contains = true
				break
			}
		}
		if contains {
			continue
		}
		fileInfos = append(fileInfos, fileInfo)
	}
	log.DebugContextf(context.Background(), "getDistinctFileInfos, origin:%s, after: %s",
		helper.Object2String(fis), helper.Object2String(fileInfos))
	return fileInfos

}

func getAllDistinctDocIDs(fileInfos []*model.FileInfo) []string {
	docIDs := make([]string, 0)
	for _, fileInfo := range fileInfos {
		if fileInfo == nil {
			continue
		}
		// docIDs中是否包含了fileInfo.DocID
		contains := false
		for _, docID := range docIDs {
			if docID == fileInfo.DocID {
				contains = true
				break
			}
		}
		if contains {
			break
		}
		docIDs = append(docIDs, fileInfo.DocID)
	}
	return docIDs
}

// WorkflowSearch 工作流检索
func (s *Retrieval) WorkflowSearch(ctx context.Context, bs *botsession.BotSession) error {
	bs.Knowledge = make([]*knowledge.SearchKnowledgeRsp_SearchRsp_Doc, 0) // 存储结果

	req := &knowledge.SearchKnowledgeReq_SearchReq{
		BotBizId:                 bs.App.GetAppBizId(),
		Question:                 bs.OriginContent,
		Labels:                   bs.Labels,
		UsePlaceholder:           bs.App.CanUsePlaceholder(),
		ImageUrls:                bs.Images,
		WorkflowSearchExtraParam: bs.WorkflowSearchExtraParam,
		ModelName:                getModelName(bs),
	}
	rsp, _ := s.dao.SearchWorkflow(ctx, req, getSearchSceneType(bs))
	bs.Knowledge = rsp
	return nil
}

func addSearchNode(ctx context.Context, req *knowledge.SearchKnowledgeReq_SearchReq) {
	pf.AddNode(ctx, pf.PipelineNode{
		Key:   "BasicSearch.SearchKnowledge",
		Input: req,
	})
}
