package helper

import (
	"fmt"
	"strings"
	"testing"

	"git.woa.com/dialogue-platform/proto/pb-stub/openapi"
	jsoniter "github.com/json-iterator/go"
	"github.com/stretchr/testify/require"
)

// TestExtractJSON tests the ExtractJSON function
func TestExtractJSON(t *testing.T) {
	testCases := []struct {
		rawJSON  string
		expected string
	}{
		{
			rawJSON:  "#问题示例：\n##问题：您好\n##答案：您也好\n\n#检索过程示例：\n##调用工具1：tool_a\n##检索问题1：车窗怎么打开\n##预期输出1：这样开\n\n##调用工具2：ToolB\n##检索问题2：车门怎么开\n##预期输出2：找人开",
			expected: "{\"question\":\"您好\",\"answer\":\"您也好\",\"tool_steps\":[{\"tool_name\":\"tool_a\",\"tool_input\":{\"query\":\"车窗怎么打开\"},\"tool_output\":\"这样开\"},{\"tool_name\":\"ToolB\",\"tool_input\":{\"query\":\"车门怎么开\"},\"tool_output\":\"找人开\"},{\"tool_name\":\"task_completed\",\"tool_input\":{\"final_answer\":\"您也好\"},\"tool_output\":\"\"}]}",
		},
	}
	for _, testCase := range testCases {
		t.Run("extract json", func(t *testing.T) {
			res := ExtractJSON(testCase.rawJSON)
			t.Logf("extract json: %s", res)
			require.EqualValues(t, testCase.expected, res)
		})
	}
}

func TestRenderKBAgentTools(t *testing.T) {
	const inputJSON = `[
  {"type":"function","function":{"name":"task_completed","description":"The task has been completed and returns the final detailed answer to the user","parameters":{"type":"object","properties":{"final_answer":{"type":"string","description":"your complete and detailed and formatted answer to user, not a abstract"}},"required":["final_answer"]}}},
  {"type":"function","function":{"name":"ask_user","description":"Ask user for missing information","parameters":{"type":"object","properties":{"question":{"type":"string","description":"the question or missing information you want to ask the user, DO NOT ask the same question as the user's question"}},"required":["question"]}}},
  {"type":"function","function":{"name":"answer_directly","description":"Answer directly for simple objective, and wait for user's response","parameters":{"type":"object","properties":{"answer":{"type":"string","description":"your complete and detailed and formatted answer to user, not a abstract"}},"required":["answer"]}}},
  {"type":"function","function":{"name":"FileRetriever","description":"在指定范围检索，比如特定标签范围的多个文件或者特定单文件中检索","parameters":{"type":"object","properties":{"Query":{"type":"string","description":"用户的query"},"Filters":{"type":"array","description":"检索条件","properties":{"DocId":{"type":"string","description":"文档Id"}},"required":["DocId"]}},"required":["Query","Filters"]}}},
  {"type":"function","function":{"name":"KnowledgeBaseRetrieval","description":"在全部知识库范围内检索，从完整知识库库中检索相关和query相关的信息","parameters":{"type":"object","properties":{"Query":{"type":"string","description":"用户的query"}},"required":["Query"]}}},
  {"type":"function","function":{"name":"transfer_to_KBAgent自测","description":"负责Agent转发","parameters":{"type":"object"}}}
]`

	var tools []*openapi.Tool
	if err := jsoniter.UnmarshalFromString(inputJSON, &tools); err != nil {
		t.Fatalf("cannot unmarshal input JSON: %v", err)
	}

	out := RenderKBAgentTools(tools)
	fmt.Println(out)

	tests := []struct {
		index   int
		name    string
		desc    string
		argsSub string
		reqs    string
	}{
		{1, "task_completed", "The task has been completed and returns the final detailed answer to the user", `{"type":"object","properties":{"final_answer":{"type":"string","description":"your complete and detailed and formatted answer to user, not a abstract"}}}`, `["final_answer"]`},
		{2, "ask_user", "Ask user for missing information", `{"type":"object","properties":{"question":{"type":"string","description":"the question or missing information you want to ask the user, DO NOT ask the same question as the user's question"}}}`, `["question"]`},
		{3, "answer_directly", "Answer directly for simple objective, and wait for user's response", `{"type":"object","properties":{"answer":{"type":"string","description":"your complete and detailed and formatted answer to user, not a abstract"}}}`, `["answer"]`},
		{4, "FileRetriever", "在指定范围检索，比如特定标签范围的多个文件或者特定单文件中检索", `{"type":"object","properties":{"Query":{"type":"string","description":"用户的query"},"Filters":{"type":"array","description":"检索条件","properties":{"DocId":{"type":"string","description":"文档Id"}}}}}`, `["Query","Filters"]`},
		{5, "KnowledgeBaseRetrieval", "在全部知识库范围内检索，从完整知识库库中检索相关和query相关的信息", `{"type":"object","properties":{"Query":{"type":"string","description":"用户的query"}}}`, `["Query"]`},
		{6, "transfer_to_KBAgent自测", "负责Agent转发", `{"type":"object"}`, `[]`},
	}

	for _, tc := range tests {
		line := fmt.Sprintf(
			`%d. "%s": %s. Args: %s Required: %s`,
			tc.index, tc.name, tc.desc, tc.argsSub, tc.reqs,
		)
		if !strings.Contains(out, line) {
			t.Errorf("output missing expected line:\n%s\n-- actual output:\n%s", line, out)
		}
	}
}

func TestFormatExamples(t *testing.T) {
	r := Result{
		Question: "历年来，航空轮胎的起草人有谁？",
		Answer:   "- GB 9745-1995的起草人有：盛保信、长庆曾、王慧宁。\n- GB 9745-2009的起草人有：邓海燕、周碧蓉、张虹、齐立平。",
		ToolSteps: []ToolStep{
			{
				ToolName:   "KnowledgeRetrievalAnswer",
				ToolInput:  "{\"Query\": \"GB 9745-1995的起草人有谁？\"}",
				ToolOutput: "GB 9745-1995的起草人有：盛保信、长庆曾、王慧宁。",
			},
			{
				ToolName:   "BoundedKnowledgeQA",
				ToolInput:  "{\"Query\": \"GB 9745-1995的起草人有谁？\", \"Filters\": [{\"DocId\":\"doc_123\"}]}",
				ToolOutput: "GB 9745-2009的起草人有：邓海燕、周碧蓉、张虹、齐立平。",
			},
			{
				ToolName:  "task_completed",
				ToolInput: "",
			},
		},
	}

	want := `# 例1
<用户问题>
历年来，航空轮胎的起草人有谁？
</用户问题>
<检索辅助机器人>
Thought: 需要检索历年来，航空轮胎的起草人有谁？
Action: KnowledgeRetrievalAnswer
Action Input: {"Query": "GB 9745-1995的起草人有谁？"}
</检索辅助机器人>

# 例2
<用户问题>
历年来，航空轮胎的起草人有谁？
</用户问题>
<历史记录>
检索步骤1: Thought: 需要检索历年来，航空轮胎的起草人有谁？
Action: KnowledgeRetrievalAnswer
Action Input: {"Query": "GB 9745-1995的起草人有谁？"}
Action Output: GB 9745-1995的起草人有：盛保信、长庆曾、王慧宁。
</历史记录>
<检索辅助机器人>
Thought: 需要检索历年来，航空轮胎的起草人有谁？
Action: BoundedKnowledgeQA
Action Input: {"Query": "GB 9745-1995的起草人有谁？", "Filters": [{"DocId":"doc_123"}]}
</检索辅助机器人>

# 例3
<用户问题>
历年来，航空轮胎的起草人有谁？
</用户问题>
<历史记录>
检索步骤1: Thought: 需要检索历年来，航空轮胎的起草人有谁？
Action: KnowledgeRetrievalAnswer
Action Input: {"Query": "GB 9745-1995的起草人有谁？"}
Action Output: GB 9745-1995的起草人有：盛保信、长庆曾、王慧宁。
检索步骤2: Thought: 需要检索历年来，航空轮胎的起草人有谁？
Action: BoundedKnowledgeQA
Action Input: {"Query": "GB 9745-1995的起草人有谁？", "Filters": [{"DocId":"doc_123"}]}
Action Output: GB 9745-2009的起草人有：邓海燕、周碧蓉、张虹、齐立平。
</历史记录>
<检索辅助机器人>
Thought: 需要整理信息，回答用户。
Action: task_completed
Action Input: {"final_answer":"- GB 9745-1995的起草人有：盛保信、长庆曾、王慧宁。\n- GB 9745-2009的起草人有：邓海燕、周碧蓉、张虹、齐立平。"}
</检索辅助机器人>

`

	got := FormatExamples(r)
	if got != want {
		t.Errorf("FormatExamples() = \n%v\nwant\n%v", got, want)
	}
}
