package helper

import (
	"reflect"
	"testing"

	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/agent_config_server"
	"github.com/stretchr/testify/require"
)

// TestArguments2Map// TestArguments2Map 验证 Arguments2Map 在不同输入下的行为
func TestArguments2Map(t *testing.T) {
	cases := []struct {
		name    string
		input   string
		want    map[string]any
		wantNil bool
	}{
		{
			name:  "正常 JSON",
			input: `{"a":1,"b":"str"}`,
			want: map[string]any{
				"a": float64(1),
				"b": "str",
			},
		},
		{
			name: "数字后多余引号",
			input: "{\"dimension\":\"org\",\"deptNums\":[\"101\"],\"IndiCode\":\"busi_income_kpi\",\"end_mon\":202505,\"begin_mon\":202401\"}",
			want: map[string]any{
				"dimension":  "org",
				"deptNums":   []any{"101"},
				"IndiCode":   "busi_income_kpi",
				"end_mon":    float64(202505),
				"begin_mon":  float64(202401),
			},
		},
		{
			name:    "无法修复的畸形 JSON",
			input:   `{"key":"value","num":abc}`,
			want: map[string]any{
				"key": "value",
				"num": nil,
			},
		},
	}

	for _, cc := range cases {
		t.Run(cc.name, func(t *testing.T) {
			got := Arguments2Map(cc.input)
			if cc.wantNil {
				if got != nil {
					t.Fatalf("Expected nil, got %#v", got)
				}
				return
			}
			if !reflect.DeepEqual(got, cc.want) {
				t.Errorf("Arguments2Map() = %#v; want %#v", got, cc.want)
			}
		})
	}
}

// TestSanitizeTrailingQuote 验证 sanitizeTrailingQuote 清洗逻辑
func TestSanitizeTrailingQuote(t *testing.T) {
	cases := []struct {
		input string
		want  string
	}{
		{
			input: `{"x":1234","y":5678}`,
			want:  `{"x":1234,"y":5678}`,
		},
		{
			input: `{"a":111" , "b":222"}`,
			want:  `{"a":111 , "b":222}`, 
		},
	}

	for _, cc := range cases {
		got := sanitizeTrailingQuote(cc.input)
		if got != cc.want {
			t.Errorf("sanitizeTrailingQuote(%q) = %q; want %q", cc.input, got, cc.want)
		}
	}
}

func TestConvertToMap(t *testing.T) {
	t.Run("ConvertToMap", func(t *testing.T) {
		data := map[string]any{
			"Answer": "为您找到这些新闻：...",
			"QuoteInfos": []any{
				map[string]any{
					"Position": 373,
					"Index":    1,
				},
				map[string]any{
					"Position": 222,
					"Index":    1,
				},
			},
			"References": []any{
				map[string]any{
					"Index": 1,
					"Title": "唐勇中将被撤销全国政协委员资格",
					"Url":   "https://news.qq.com/rain/a/20250327A05O2400",
				},
			},
		}

		// 示例参数
		subParams := []*agent_config_server.AgentToolRspParam{
			{Name: "Answer", AgentHidden: false},
			{
				Name:        "QuoteInfos",
				AgentHidden: true,
				SubParams: []*agent_config_server.AgentToolRspParam{
					{Name: "Index", AgentHidden: true},
					{Name: "Position", AgentHidden: false},
				},
			},
			{Name: "References", AgentHidden: true},
		}
		res := HiddenSubParams(subParams, data)
		t.Logf("res: %v\n", res)
	})
}

// TestReplaceHTML
func TestReplaceHTML(t *testing.T) {
	testCases := []struct {
		input    string
		expected string
	}{
		{
			input: `根据2024年中国男女比例数据，生成的饼图如下：

![2024年中国男女比例饼图](https://lke-1251316161.cos.myqcloud.com/file/abc.html?q-sign=sha1&q=54c6e8bb5)1111`,
			expected: `根据2024年中国男女比例数据，生成的饼图如下：

[2024年中国男女比例饼图](https://lke-1251316161.cos.myqcloud.com/file/abc.html?q-sign=sha1&q=54c6e8bb5)1111`,
		},
		{
			input: `根据2024年中国男女比例数据，生成的饼图如下：

![2024年中国男女比例饼图](https://lke-1251316161.cos.myqcloud.com/file/abc.png?q-sign=sha1&q=54c6e8bb5)`,
			expected: `根据2024年中国男女比例数据，生成的饼图如下：

![2024年中国男女比例饼图](https://lke-1251316161.cos.myqcloud.com/file/abc.png?q-sign=sha1&q=54c6e8bb5)`,
		},
		{
			input: `根据2024年中国男女比例数据，生成的饼图如下：

![2024年中国男女比例饼图](https://lke-1251316161.cos.myqcloud.com/file/abc.html`,
			expected: `根据2024年中国男女比例数据，生成的饼图如下：

![2024年中国男女比例饼图](https://lke-1251316161.cos.myqcloud.com/file/abc.html`,
		},
		{
			input:    "![](https://lke-realtime-test-1251316161.cos.ap-guangzhou.myqcloud.com/code-interpreter/dev/file/1b43186e-b12a-4019-b3d9-b999a91581be_z3TF0aJR_example_chart.html)",
			expected: "[链接](https://lke-realtime-test-1251316161.cos.ap-guangzhou.myqcloud.com/code-interpreter/dev/file/1b43186e-b12a-4019-b3d9-b999a91581be_z3TF0aJR_example_chart.html)",
		},
	}
	for _, testCase := range testCases {
		t.Run("replace html", func(t *testing.T) {
			res := ReplaceInvalidHTML(testCase.input)
			t.Logf("res: %v\n", res)
			require.EqualValues(t, testCase.expected, res)
		})
	}
}

// TestReplaceURLsWithPlaceholders 测试`ReplaceURLsWithPlaceholders`函数
func TestReplaceURLsWithPlaceholders(t *testing.T) {
	// 测试数据
	testCases := []struct {
		input        string
		result       string
		placeholders map[string]string
	}{
		{
			input:  "{\"Code\":0,\"Data\":{\"ExecMsg\":\"\",\"ExecResult\":\"\",\"ExecState\":\"success\",\"Files\":[],\"Images\":[{\"Url\":\"https://lke-realtime-test-1251316161.cos.ap-guangzhou.myqcloud.com/code-interpreter/testing/image/image_894675e8-21af-11f0-a3d0-525400d5ecda_C0nUgwFH.png\"},{\"Url\":\"https://lke-realtime-test-1251316161.cos.ap-guangzhou.myqcloud.com/code-interpreter/testing/image/image_894675e8-21af-11f0-a3d0-525400d5ecda_oOFV0CZc.png\"}]},\"Msg\":\"success\"}",
			result: "{\"Code\":0,\"Data\":{\"ExecMsg\":\"\",\"ExecResult\":\"\",\"ExecState\":\"success\",\"Files\":[],\"Images\":[{\"Url\":\"https://I0.png\"},{\"Url\":\"https://I1.png\"}]},\"Msg\":\"success\"}",
			placeholders: map[string]string{
				"https://I0.png": "https://lke-realtime-test-1251316161.cos.ap-guangzhou.myqcloud.com/code-interpreter/testing/image/image_894675e8-21af-11f0-a3d0-525400d5ecda_C0nUgwFH.png",
				"https://I1.png": "https://lke-realtime-test-1251316161.cos.ap-guangzhou.myqcloud.com/code-interpreter/testing/image/image_894675e8-21af-11f0-a3d0-525400d5ecda_oOFV0CZc.png",
			},
		},
		{
			input:  "根据2024年中国男女比例数据，生成的饼图如下：\n\n[2024年中国男女比例饼图](https://lke-1251316161.cos.myqcloud.com/file/abc.html?q-sign=sha1&q=54c6e8bb5)1111",
			result: "根据2024年中国男女比例数据，生成的饼图如下：\n\n[2024年中国男女比例饼图](https://I0.html)1111",
			placeholders: map[string]string{
				"https://I0.html": "https://lke-1251316161.cos.myqcloud.com/file/abc.html?q-sign=sha1&q=54c6e8bb5",
			},
		},
		{
			input:        "实验材料（菌种、药品、仪器）及培养方法（白曲霉斜面培养、麸皮固体培养）[2,3](@ref)- 实验药品和仪器表格（含来源与生产厂商）[2,3](@ref) 。",
			result:       "实验材料（菌种、药品、仪器）及培养方法（白曲霉斜面培养、麸皮固体培养）[2,3](@ref)- 实验药品和仪器表格（含来源与生产厂商）[2,3](@ref) 。",
			placeholders: map[string]string{},
		},
		{
			input:        "{\"_meta\":{\"ret\":0},\"content\":[{\"text\":\"{\\\\\"used_engine_type\\\\\":3,\\\\\"search_list\\\\\":[{\\\\\"title\\\\\":\\\\\"2025年腾讯控股历年年报/财报/社会责任ESG报告合集（共 ...\\\\\",\\\\\"url\\\\\":\\\\\"https://www.sgpjbg.com/bggroup/1658.html\\\\\",\\\\\"snippet\\\\\":\\\\\"如图，在腾讯2023年财报-资产负债表（港股上市公司叫“綜合財務狀況表”）的基础上进行简化，将资产分为生产资产、经营资产 、投资资产、类现金这四大类。划分依据如下图所示。 注：如果只是按照资产负债表中的科目名称对资产类别进行 ...\\\\\"},{\\\\\"index\\\\\":1,\\\\\"title\\\\\":\\\\\"腾讯控股2023年财报解析及估值调整_经营_投资_公司\\\\\",\\\\\"url\\\\\":\\\\\"https://www.sohu.com/a/779345334_121353753\\\\\",\\\\\"snippet\\\\\":\\\\\"2023 年中国游戏 出海美国收入约为 31 亿美元，同比+0.7%，主要由腾讯的《使命召唤手游》、点 点互动的《Whiteout Survival》等游戏贡献。2023 年我国出海手游在欧洲市场和 韩国市场的收入规模亦有所扩大，日本及东南亚市场份额有所降低。\\\\\"},{\\\\\"index\\\\\":2,\\\\\"title\\\\\":\\\\\"2024年腾讯控股研究报告：游戏行业景气度回升，业绩增长 ...\\\\\",\\\\\"url\\\\\":\\\\\"https://www.vzkoo.com/read/2024111165191980cb23da0b1e583004.html\\\\\",\\\\\"snippet\\\\\":\\\\\"2024年3月20日，$腾讯控股(HK|00700)$ 公布了2023年全年业绩。在当周的周记中，我们简单聊了一下各业务板块的经营情况，就将其抛到脑后，专心写其他公司财报分析去了。结果，等4月8日腾讯正式发布2023年报的时候，我愣是没反应过来，因为 ...\\\\\"},{\\\\\"index\\\\\":3,\\\\\"title\\\\\":\\\\\"腾讯音乐2025Q1稳健增长：总收入73.6亿元 _光明网\\\\\",\\\\\"url\\\\\":\\\\\"https://tech.gmw.cn/xinxi/2025-05/13/content_38023336.htm\\\\\",\\\\\"snippet\\\\\":\\\\\"腾讯控股2021年全年业绩报告（63页）.pdf 腾讯控股有限公司2024年第二季度业绩报告（52页）.pdf 腾讯控股有限公司2023年第一季度业绩报告.pdf 腾讯Tencent（TCEHY）2024年第一季度业绩报告「OTC」（英文版）（23页）.pdf 腾讯社会责任报告 腾讯：2020\\\\\"},{\\\\\"index\\\\\":4,\\\\\"title\\\\\":\\\\\"腾讯控股2023年财报解析及估值调整_财富号_东方财富网\\\\\",\\\\\"url\\\\\":\\\\\"https://caifuhao.eastmoney.com/news/20240516212654994728050\\\\\",\\\\\"snippet\\\\\":\\\\\"秉持长期主义理念，腾讯音乐持续深化内容与平台“一体两翼”战略，实现了2025年的良好开篇。第一季度，腾讯音乐深化与全球唱片公司的合作、发力内容制作与共创、打造多个线下音乐活动，以持续焕发活力的内容生态满足用户的多元喜好；同时，不断创新\\\\\"}]}\",\"type\":\"text\"}]}",
			result:       "{\"_meta\":{\"ret\":0},\"content\":[{\"text\":\"{\\\\\"used_engine_type\\\\\":3,\\\\\"search_list\\\\\":[{\\\\\"title\\\\\":\\\\\"2025年腾讯控股历年年报/财报/社会责任ESG报告合集（共 ...\\\\\",\\\\\"url\\\\\":\\\\\"https://I0.html\",\\\\\"snippet\\\\\":\\\\\"如图，在腾讯2023年财报-资产负债表（港股上市公司叫“綜合財務狀況表”）的基础上进行简化，将资产分为生产资产、经营资产 、投资资产、类现金这四大类。划分依据如下图所示。 注：如果只是按照资产负债表中的科目名称对资产类别进行 ...\\\\\"},{\\\\\"index\\\\\":1,\\\\\"title\\\\\":\\\\\"腾讯控股2023年财报解析及估值调整_经营_投资_公司\\\\\",\\\\\"url\\\\\":\\\\\"https://I1\",\\\\\"snippet\\\\\":\\\\\"2023 年中国游戏 出海美国收入约为 31 亿美元，同比+0.7%，主要由腾讯的《使命召唤手游》、点 点互动的《Whiteout Survival》等游戏贡献。2023 年我国出海手游在欧洲市场和 韩国市场的收入规模亦有所扩大，日本及东南亚市场份额有所降低。\\\\\"},{\\\\\"index\\\\\":2,\\\\\"title\\\\\":\\\\\"2024年腾讯控股研究报告：游戏行业景气度回升，业绩增长 ...\\\\\",\\\\\"url\\\\\":\\\\\"https://I2.html\",\\\\\"snippet\\\\\":\\\\\"2024年3月20日，$腾讯控股(HK|00700)$ 公布了2023年全年业绩。在当周的周记中，我们简单聊了一下各业务板块的经营情况，就将其抛到脑后，专心写其他公司财报分析去了。结果，等4月8日腾讯正式发布2023年报的时候，我愣是没反应过来，因为 ...\\\\\"},{\\\\\"index\\\\\":3,\\\\\"title\\\\\":\\\\\"腾讯音乐2025Q1稳健增长：总收入73.6亿元 _光明网\\\\\",\\\\\"url\\\\\":\\\\\"https://I3.htm\",\\\\\"snippet\\\\\":\\\\\"腾讯控股2021年全年业绩报告（63页）.pdf 腾讯控股有限公司2024年第二季度业绩报告（52页）.pdf 腾讯控股有限公司2023年第一季度业绩报告.pdf 腾讯Tencent（TCEHY）2024年第一季度业绩报告「OTC」（英文版）（23页）.pdf 腾讯社会责任报告 腾讯：2020\\\\\"},{\\\\\"index\\\\\":4,\\\\\"title\\\\\":\\\\\"腾讯控股2023年财报解析及估值调整_财富号_东方财富网\\\\\",\\\\\"url\\\\\":\\\\\"https://I4\",\\\\\"snippet\\\\\":\\\\\"秉持长期主义理念，腾讯音乐持续深化内容与平台“一体两翼”战略，实现了2025年的良好开篇。第一季度，腾讯音乐深化与全球唱片公司的合作、发力内容制作与共创、打造多个线下音乐活动，以持续焕发活力的内容生态满足用户的多元喜好；同时，不断创新\\\\\"}]}\",\"type\":\"text\"}]}",
			placeholders: map[string]string{"https://I2.html": "https://www.vzkoo.com/read/2024111165191980cb23da0b1e583004.html", "https://I3.htm": "https://tech.gmw.cn/xinxi/2025-05/13/content_38023336.htm", "https://I4": "https://caifuhao.eastmoney.com/news/20240516212654994728050", "https://I0.html": "https://www.sgpjbg.com/bggroup/1658.html", "https://I1": "https://www.sohu.com/a/779345334_121353753"},
		},
		{
			input:        "[https://www.qq.com](https://www.qq.com)![https://www.baidu.com](https://www.baidu.com)",
			result:       "[https://I0](https://I0)![https://I1](https://I1)",
			placeholders: map[string]string{"https://I0": "https://www.qq.com", "https://I1": "https://www.baidu.com"},
		},
		{
			input:        "https://www.baidu.com\\\nanything",
			result:       "https://I0\nanything",
			placeholders: map[string]string{"https://I0": "https://www.baidu.com"},
		},
	}

	for _, testCase := range testCases {
		t.Run("replace urls with placeholders", func(t *testing.T) {
			// 调用函数
			placeholders := make(map[string]string)
			result := ReplaceURLsWithPlaceholders(testCase.input, placeholders, nil)

			// 输出结果
			t.Logf("替换后的文本: %s\n", result)
			t.Logf("占位符映射: %v\n", Object2StringEscapeHTML(placeholders))

			// 断言结果
			require.Equal(t, testCase.result, result)
			require.Equal(t, testCase.placeholders, placeholders)
		})
	}
}

// TestFixEmptyMarkdownLinks 测试修复空的Markdown链接
func TestFixEmptyMarkdownLinks(t *testing.T) {
	testCases := []struct {
		input    string
		expected string
	}{
		{
			input:    "这是一个错误链接：[](sss)![](https://www.baidu.com/abc.png)",
			expected: "这是一个错误链接：[链接](sss)![](https://www.baidu.com/abc.png)",
		},
		{
			input:    "这是一个正常的链接：[百度](https://www.baidu.com)还有这个![](https://www.baidu.com/abc.png)",
			expected: "这是一个正常的链接：[百度](https://www.baidu.com)还有这个![](https://www.baidu.com/abc.png)",
		},
		{
			input:    "这啥也不是",
			expected: "这啥也不是",
		},
		{
			input:    "这是图片链接：![](https://www.baidu.com/abc.png)",
			expected: "这是图片链接：![](https://www.baidu.com/abc.png)",
		},
		{
			input:    "这是一个错误链接：[4](@ref)![](https://www.baidu.com/abc.png)",
			expected: "这是一个错误链接：[4](@ref)![](https://www.baidu.com/abc.png)",
		},
	}

	for _, testCase := range testCases {
		t.Run("fix empty markdown links", func(t *testing.T) {
			res := FillEmptyLinkText(testCase.input)
			t.Logf("res: %v\n", res)
			require.EqualValues(t, testCase.expected, res)
		})
	}
}

// TestGetVNCURL 测试获取VNC链接
func TestGetVNCURL(t *testing.T) {
	testCases := []struct {
		input    string
		expected string
	}{
		{
			input:    "{\"content\":[{\"type\":\"text\",\"text\":\"运行环境实时查看链接：https://novnc.testsite.woa.com/vnc_lite.html?token=1b212be9-e397-4765-b4f2-6b5097f3e1d3cdfc8ef5-8937-45ef-8e1f-8fd40740e0c83b0362d8-8d4f-49b4-9790-31d8c3bef19fd7b64307-970b-425e-8137-de91dd4e854c\"},{\"type\":\"text\",\"text\":\"action_result: Action result: Success! Navigated to www.qq.com >>>>> Page Content State of current webpage. NOTE that the following is one-time \"}]}",
			expected: "https://novnc.testsite.woa.com/vnc_lite.html?token=1b212be9-e397-4765-b4f2-6b5097f3e1d3cdfc8ef5-8937-45ef-8e1f-8fd40740e0c83b0362d8-8d4f-49b4-9790-31d8c3bef19fd7b64307-970b-425e-8137-de91dd4e854c",
		},
	}

	for _, testCase := range testCases {
		t.Run("get VNC URL", func(t *testing.T) {
			res := GetVNCURL(testCase.input)
			t.Logf("res: %v\n", res)
			require.EqualValues(t, testCase.expected, res)
		})
	}
}

// TestGetFileNameAndType 测试获取文件名和类型
func TestGetFileNameAndType(t *testing.T) {
	testCases := []struct {
		rawURL       string
		expectedName string
		expectedType string
	}{
		{
			rawURL:       "https://lke-1251316161.cos.myqcloud.com/file/abc.txt",
			expectedName: "abc.txt",
			expectedType: "txt",
		},
		{
			rawURL:       "https://lke-1251316161.cos.myqcloud.com/file/abc",
			expectedName: "abc",
			expectedType: "",
		},
		{
			rawURL:       "https://lke-realtime-test-1251316161.cos.ap-guangzhou.myqcloud.com/%2Fsandbox/dev/1918575630663024640/ab578b74-1f16-42ca-8188-c4dcf8703baa/abc.txt?user=11110",
			expectedName: "abc.txt",
			expectedType: "txt",
		},
	}

	for _, testCase := range testCases {
		t.Run("get file name and type", func(t *testing.T) {
			fileName, fileType := GetFileNameAndType(testCase.rawURL)
			t.Logf("fileName: %s, fileType: %s", fileName, fileType)
			require.EqualValues(t, testCase.expectedName, fileName)
			require.EqualValues(t, testCase.expectedType, fileType)
		})
	}
}

// TestTrimPageContentSection 测试修剪页面内容部分
func TestTrimPageContentSection(t *testing.T) {
	testCases := []struct {
		rawJSON   string
		expected  string
		expectErr bool
	}{
		{
			rawJSON:  "{\"content\":[{\"type\":\"text\",\"text\":\"Hello, world!\"},{\"type\":\"text\",\"text\":\"\\n\\n>>>>> Page Content\"}]}",
			expected: "{\"content\":[{\"type\":\"text\",\"text\":\"Hello, world!\"},{\"type\":\"text\",\"text\":\"\"}]}",
		},
		{
			rawJSON:  "{\"content\":[{\"type\":\"text\",\"text\":\"Hello, world!\"}]}",
			expected: "{\"content\":[{\"type\":\"text\",\"text\":\"Hello, world!\"}]}",
		},
		{
			rawJSON:  "{\"content\":[{\"text\":\"运行环境实时查看链接：https://devwss.testsite.woa.com/novnc/vnc_lite.html?token=4cb2a49a-c80a-4657-8cac-66337fa3bb642b1f494d-da02-4e44-be6e-c3cc0adaab486f0f0522-d603-47a5-8e44-cc0ea12f76bbc968eb47-bd6f-435d-839b-43ebb72bc15a\\u0026traceId=c21c918a-53ea-45d6-9c66-d2d6990c0b00\\u0026env=dev\",\"type\":\"text\"},{\"text\":\"action_result: Action result: Success! Navigated to https://www.tencent.com\\n\\n\\u003e\\u003e\\u003e\\u003e\\u003e Page Content\\nState of current webpage. NOTE that the following is one-time information!\\n[Start of state]\\nPrevious page: (about:blank)\\nAction: go_to_url ({\\\"url\\\":\\\"https://www.tencent.com\\\"})\\nAction Result: Your current page information has changed to Tencent 腾讯 (https://www.tencent.com/zh-cn/)\\nInteractive elements from top layer of current page inside the viewport: [Start of page]\\n[0]\\u003ca /\\u003e\\n[1]\\u003ch1 Tencent腾讯/\\u003e\\n[2]\\u003cul 简体\\n|\\n繁体\\n|\\nEnglish/\\u003e\\n[3]\\u003cli 公司简介\\n愿景及使命\\n发展历程\\n业务架构\\n管理团队\\n董事会成员\\n企业文化\\n办公地点/\\u003e\\n[4]\\u003ca 简介/\\u003e\\n[5]\\u003cli 面向用户\\n面向企业\\n创新科技/\\u003e\\n[6]\\u003ca 业务/\\u003e\\n[7]\\u003cli 人才发展\\n腾讯学堂\\n工作环境\\n员工活动/\\u003e\\n[8]\\u003ca 员工/\\u003e\\n[9]\\u003cli 环境\\n社会\\n治理\\nESG评级\\n报告/\\u003e\\n[10]\\u003ca ESG/\\u003e\\n[11]\\u003cli 季度业绩及投资者新闻\\n公告及财务报告\\n业绩电话会及投资者日历\\n投资者工具包\\n证券及债券信息\\n环境、社会及管治\\n股东资讯/\\u003e\\n[12]\\u003ca 投资者/\\u003e\\n[13]\\u003cli 企业动态\\n财务新闻\\n腾讯视角\\n媒体资料库/\\u003e\\n[14]\\u003ca 媒体/\\u003e\\n[15]\\u003ca 简/\\u003e\\n|\\n[16]\\u003ca 繁/\\u003e\\n|\\n[17]\\u003ca EN/\\u003e\\n[18]\\u003cdiv /\\u003e\\n[19]\\u003ca /\\u003e\\n[20]\\u003cimg /\\u003e\\n[21]\\u003cdiv /\\u003e\\n[22]\\u003cdiv /\\u003e\\n[23]\\u003cdiv /\\u003e\\n[24]\\u003ch2 有朋自远方来，欢迎使用微信支付!/\\u003e\\n[25]\\u003ch5 腾讯正全面提升外籍人士在华支付体验，以促进国际旅游进一步蓬勃发展和国际人文交流。/\\u003e\\n[26]\\u003cdiv /\\u003e\\n[27]\\u003cdiv /\\u003e\\n[28]\\u003cdiv /\\u003e\\n[29]\\u003ch2 电子游戏为日本老年人带来欢乐与社交联结/\\u003e\\n[30]\\u003cdiv /\\u003e\\n[31]\\u003ch2 腾讯公布二零二四年年度及第四季业绩/\\u003e\\n[32]\\u003cdiv /\\u003e\\n[33]\\u003ca /\\u003e\\n[34]\\u003cimg /\\u003e\\n[35]\\u003cdiv /\\u003e\\n[36]\\u003cdiv /\\u003e\\n[37]\\u003cdiv /\\u003e\\n[38]\\u003ch2 腾讯启动2025全球实习生招聘/\\u003e\\n[39]\\u003cdiv /\\u003e\\n[40]\\u003ca /\\u003e\\n[41]\\u003cimg /\\u003e\\n[42]\\u003cdiv /\\u003e\\n[43]\\u003cdiv /\\u003e\\n[44]\\u003cdiv /\\u003e\\n[45]\\u003ch2 有朋自远方来，欢迎使用微信支付!/\\u003e\\n[46]\\u003ch5 腾讯正全面提升外籍人士在华支付体验，以促进国际旅游进一步蓬勃发展和国际人文交流。/\\u003e\\n[47]\\u003cdiv /\\u003e\\n[48]\\u003ca /\\u003e\\n[49]\\u003cimg /\\u003e\\n[50]\\u003cdiv /\\u003e\\n[51]\\u003cdiv /\\u003e\\n[52]\\u003cdiv /\\u003e\\n[53]\\u003ch2 电子游戏为日本老年人带来欢乐与社交联结/\\u003e\\n[54]\\u003cspan Previous slide;button/\\u003e\\n[55]\\u003cspan Next slide;button/\\u003e\\n[56]\\u003cspan Go to slide 1;button/\\u003e\\n[57]\\u003cspan Go to slide 2;button/\\u003e\\n[58]\\u003cspan Go to slide 3;button/\\u003e\\n[59]\\u003cspan Go to slide 4;button/\\u003e\\n最新动态\\n[60]\\u003cimg /\\u003e\\n2025.04.17\\n游戏玩家迎来新队友：AI\\n腾讯游戏魔方工作室群首席工程师兼AI技术负责人廖诗飏为我们介绍F.A.C.U.L.的愿景、创新与潜在影响。\\n[61]\\u003cimg /\\u003e\\n2025.04.09\\n[62]\\u003ca /\\u003e\\n[63]\\u003ch3 PayPal旗下Xoom与Tenpay Global合作实现跨境汇款直达微信/\\u003e\\n[64]\\u003cp 腾讯跨境支付平台Tenpay Global与PayPal旗下服务与数字汇款先驱Xoom宣布合作。/\\u003e\\n[65]\\u003cimg /\\u003e\\n2025.03.20\\n[66]\\u003ca /\\u003e\\n[67]\\u003ch3 好游戏的基石：连接、创意与创新/\\u003e\\n[68]\\u003cp 最新一届游戏开发者大会刚刚在旧金山落下帷幕，腾讯游戏再次亮相，与全球同行分享游戏的魅力。/\\u003e\\n[69]\\u003ca /\\u003e\\n[70]\\u003ch3 当技术遇见社会创新：用户、企业与社会价值的共生/\\u003e\\n[71]\\u003cp 通过将社会价值融入产品、服务与企业文化，我们持续探索用技术与商业能力弥合鸿沟、消除壁垒，提升人类福祉并守护地球生态。/\\u003e\\n[72]\\u003cimg /\\u003e\\n2025.05.09\\n一次午后散步引发的防鸟撞救援行动\\n鸟类是城市生物多样性最直观的指标之一，保护它们至关重要。\\n[73]\\u003cimg /\\u003e\\n2025.03.15\\n[74]\\u003ca /\\u003e\\n[75]\\u003ch3 腾讯在香港成立Tencent WeTech Academy 推动科创教育与青年科技向善/\\u003e\\n[76]\\u003cp 腾讯宣布在香港成立Tencent WeTech Academy，助力香港建设成为国际科创中心。/\\u003e\\n[77]\\u003ca /\\u003e\\n[78]\\u003cimg /\\u003e\\n[79]\\u003cdiv /\\u003e\\n[80]\\u003ch2 公司简介/\\u003e\\n[81]\\u003ca /\\u003e\\n[82]\\u003cimg /\\u003e\\n[83]\\u003cdiv /\\u003e\\n[84]\\u003ch2 企业文化/\\u003e\\n[85]\\u003ca /\\u003e\\n[86]\\u003cimg /\\u003e\\n[87]\\u003cdiv /\\u003e\\n[88]\\u003ch2 办公地点/\\u003e\\n[89]\\u003ca /\\u003e\\n[90]\\u003ch2 连接用户与生活/\\u003e\\n[91]\\u003cp 让生活更便捷更多彩/\\u003e\\n[92]\\u003ca /\\u003e\\n[93]\\u003ch2 连接企业与科技/\\u003e\\n[94]\\u003cp 数字化助手，助力产业升级/\\u003e\\n[95]\\u003ca /\\u003e\\n[96]\\u003ch2 连接现在与未来/\\u003e\\n[97]\\u003cp 探索面向未来的创新科技/\\u003e\\n[98]\\u003cimg /\\u003e\\n[99]\\u003ca 连接人与人，提供功能丰富的即时通信和社交平台，让沟通更便捷。/\\u003e\\n[100]\\u003ch3 通信与社交/\\u003e\\n[101]\\u003cimg /\\u003e\\n[102]\\u003ca 基于优质内容，以技术为驱动引擎，探索社交和内容融合的下一代形态。/\\u003e\\n[103]\\u003ch3 数字内容/\\u003e\\n[104]\\u003cimg /\\u003e\\n[105]\\u003ca 连接用户、商户和金融机构，提供安全、专业、便捷的金融产品与服务。/\\u003e\\n[106]\\u003ch3 金融科技服务/\\u003e\\n[107]\\u003cimg /\\u003e\\n[108]\\u003ca 提供多种工具性软件，帮助用户快速直接解决各项具体需求。/\\u003e\\n[109]\\u003ch3 工具/\\u003e\\n[110]\\u003cimg /\\u003e\\n[111]\\u003ca 腾讯长城保护项目\\n腾讯公益慈善基金会与中国文物保护基金会共同合作，吸引公众关注和参与长城保护的文化遗产类公益项目。/\\u003e\\n[112]\\u003cimg /\\u003e\\n[113]\\u003ca /\\u003e\\n[114]\\u003ch3 碳中和/\\u003e\\n[115]\\u003cp 腾讯宣布2030年实现碳中和/\\u003e\\n[116]\\u003cimg /\\u003e\\n[117]\\u003ca 99公益日\\n每年一度于9月举行的全民公益活动，透过网上平台将大众在活动期间网上作出的捐款进行匹配。/\\u003e\\n[118]\\u003cimg /\\u003e\\n[119]\\u003ca 腾讯长城保护项目\\n腾讯公益慈善基金会与中国文物保护基金会共同合作，吸引公众关注和参与长城保护的文化遗产类公益项目。/\\u003e\\n[120]\\u003cimg /\\u003e\\n[121]\\u003ca /\\u003e\\n[122]\\u003ch3 碳中和/\\u003e\\n[123]\\u003cp 腾讯宣布2030年实现碳中和/\\u003e\\n[124]\\u003ca Previous slide;button/\\u003e\\n[125]\\u003ca Next slide;button/\\u003e\\n[126]\\u003ca /\\u003e\\n[127]\\u003ch2 连接\\n责任与信任/\\u003e\\n[128]\\u003ch5 聚合微小善行，以科技让世界更美好/\\u003e\\n[129]\\u003cp /\\u003e\\n[130]\\u003cspan /\\u003e\\n[131]\\u003ca /\\u003e\\n[132]\\u003ch2 连接\\n人才与发展/\\u003e\\n[133]\\u003ch5 激发活力，助力成长/\\u003e\\n[134]\\u003cp /\\u003e\\n[135]\\u003cspan /\\u003e\\n[136]\\u003cimg /\\u003e\\n[137]\\u003cimg /\\u003e\\n[138]\\u003cimg /\\u003e\\n[139]\\u003cimg /\\u003e\\n[140]\\u003cimg /\\u003e\\n[141]\\u003cspan Previous slide;button/\\u003e\\n[142]\\u003cspan Next slide;button/\\u003e\\n[143]\\u003cspan Go to slide 1;button/\\u003e\\n[144]\\u003cspan Go to slide 2;button/\\u003e\\n[145]\\u003cspan Go to slide 3;button/\\u003e\\n[146]\\u003ca /\\u003e\\n[147]\\u003ci /\\u003e\\n[148]\\u003cimg /\\u003e\\n[149]\\u003ca /\\u003e\\n[150]\\u003ci /\\u003e\\n[151]\\u003ca /\\u003e\\n[152]\\u003ci /\\u003e\\n[153]\\u003ca /\\u003e\\n[154]\\u003ci /\\u003e\\n[155]\\u003ca /\\u003e\\n[156]\\u003ci /\\u003e\\n[157]\\u003ca 社会招聘/\\u003e\\n[158]\\u003ca 校园招聘/\\u003e\\n[159]\\u003ca 国际招聘/\\u003e\\n[160]\\u003ca 客户服务/\\u003e\\n[161]\\u003ca 合作洽谈/\\u003e\\n[162]\\u003ca 腾讯采购/\\u003e\\n[163]\\u003ca 诚信合规/\\u003e\\n[164]\\u003ca 媒体及投资者/\\u003e\\n[165]\\u003ca 服务协议/\\u003e\\n[166]\\u003ca 隐私政策/\\u003e\\n[167]\\u003ca 知识产权/\\u003e\\n[168]\\u003cimg /\\u003e\\n[169]\\u003ca 法律声明/\\u003e\\n[170]\\u003ca 阳光准则/\\u003e\\n[171]\\u003ca 网站地图/\\u003e\\n[172]\\u003ca 粤网文[2023]2882-203号/\\u003e\\n[173]\\u003ca 粤公网安备 44030502008569号/\\u003e\\n您的Cookies偏好\\n[175]\\u003cdiv /\\u003e\\n欢迎来到Tencent.com！\\n我们希望使用分析型Cookies和类似技术 (“Cookies”) 来改善我们的网站。\\n Cookies收集的信息不会识别您个人。有关我们使用的Cookies的类型以及您的偏好选项（包括如何更改您的偏好设置）的更多信息，请查看此处的\\n[176]\\u003ca Cookies政策/\\u003e\\n。\\n[177]\\u003cdiv 接受所有分析型Cookies/\\u003e\\n[178]\\u003cdiv 拒绝所有分析型Cookies/\\u003e\\n... 4277 pixels below - scroll or extract content to see more ...\\n[End of state], vnc_stream_url: http://9.139.226.113:8080/vnc_lite.html\",\"type\":\"text\"}]}",
			expected: "{\"content\":[{\"type\":\"text\",\"text\":\"action_result: Action result: Success! Navigated to https://www.tencent.com\"}]}",
		},
		{
			rawJSON:  "{\"content\":[{\"type\":\"text\",\"text\":\"运行环境实时查看链接：https://devwss.testsite.woa.com/novnc/vnc_lite.html?token=7617bd36-9441-4e23-bba4-195dbe46487b69ee046c-3db5-4f54-a812-5651fc3a6d6edbb5829f-507e-4ea4-80b1-87a4dde7affb5720054d-9877-4588-a44e-c35ca85094b8&traceId=1ed91a6f-7465-473c-88f8-ff6a79e5ef85&env=dev\"},{\"type\":\"text\",\"text\":\"action_result: Action result: You have clicked the element 上海 (22)\\n\\n\\u003e\\u003e\\u003e\\u003e\\u003e Page Content\\nState of current webpage. NOTE that the following is one-time information!\\n[Start of state]\\nPrevious page: 搜索 | 腾讯招聘 (https://careers.tencent.com/search.html?query=at_1)\\nAction: click_element ({\\\"index\\\":22})\\nAction Result: Your current page information has changed to 搜索 | 腾讯招聘 (https://careers.tencent.com/search.html?query=at_1,ci_3)\\nInteractive elements from top layer of current page inside the viewport: [Start of page]\\n[0]<a />\\n[1]<div 登录/>\\n[2]<a 社会招聘/>\\n[3]<a 校园招聘/>\\n[4]<a 生活在腾讯/>\\n[5]<a 产品和服务/>\\n[6]<a 工作地点/>\\n[7]<div />\\n[8]<input 搜索工作岗位;text/>\\n[9]<span 查看工作岗位/>\\n过滤条件\\n[10]<div />\\n[11]<a 国家/地区/>\\n[12]<div />\\n[13]<div />\\n[14]<div />\\n[15]<div />\\n[16]<a 城市/>\\n[17]<div />\\n[18]<div />\\n[19]<input 请输入城市搜索;text/>\\n[20]<i />\\n[21]<div 北京/>\\n[22]<div 上海/>\\n[23]<div 广州/>\\n[24]<div 深圳/>\\n[25]<div 成都/>\\n[26]<div 杭州/>\\n[27]<div 中国香港/>\\n[28]<div 中国台北/>\\n中国澳门\\n武汉\\n长沙\\n重庆\\n长春\\n天津\\n大连\\n福州\\n贵阳\\n哈尔滨\\n合肥\\n呼和浩特\\n[29]<div 济南/>\\n[30]<div 昆明/>\\n[31]<div 兰州/>\\n[32]<div 南宁/>\\n[33]<div 南昌/>\\n[34]<div 南京/>\\n[35]<div 沈阳/>\\n[36]<div 石家庄/>\\n[37]<div 太原/>\\n[38]<div 乌鲁木齐/>\\n[39]<div 西安/>\\n[40]<div 西宁/>\\n[41]<div 厦门/>\\n[42]<div 郑州/>\\n[43]<div 青岛/>\\n[44]<div 无锡/>\\n[45]<div 烟台/>\\n[46]<div 苏州/>\\n[47]<div 海口/>\\n[48]<div 淄博/>\\n[49]<div 宁波/>\\n[50]<div 银川/>\\n[51]<div 扬州/>\\n[52]<div 汕尾/>\\n[53]<div 顺德/>\\n[54]<div 桂林/>\\n[55]<div 珠海/>\\n[56]<div 贵安/>\\n[57]<div 佛山/>\\n[58]<div 保定/>\\n[59]<div 南通/>\\n[60]<div 拉萨/>\\n[61]<div 清远/>\\n[62]<div 三亚/>\\n[63]<div 东莞/>\\n[64]<div 张家口/>\\n[65]<div 雄安新区/>\\n[66]<div 澄迈县/>\\n[67]<div 霍尔果斯/>\\n[68]<div 贝尔维尤/>\\n[69]<div 华盛顿/>\\n[70]<div 加利福尼亚州/>\\n[71]<div 弗罗里达州/>\\n[72]<div 乔治亚州/>\\n[73]<div 爱达荷州/>\\n[74]<div 伊利诺伊州/>\\n[75]<div 马里兰州/>\\n[76]<div 马萨诸塞州/>\\n[77]<div 密歇根州/>\\n[78]<div 明尼苏达州/>\\n[79]<div 新罕布什尔州/>\\n[80]<div 新泽西州/>\\n[81]<div 纽约州/>\\n[82]<div 俄亥俄州/>\\n[83]<div 俄勒冈州/>\\n[84]<div 宾夕法尼亚州/>\\n[85]<div 得克萨斯州/>\\n[86]<div 弗吉尼亚州/>\\n[87]<div 亚利桑那州/>\\n[88]<div 华盛顿州/>\\n[89]<div 帕罗奥多/>\\n[90]<div 纽约/>\\n[91]<div 西雅图/>\\n[92]<div 洛杉矶/>\\n[93]<div 尔湾/>\\n[94]<div 新加坡/>\\n[95]<div 大阪/>\\n[96]<div 东京/>\\n[97]<div 首尔/>\\n[98]<div 吉隆坡/>\\n[99]<div 利物浦/>\\n[100]<div 英国-远程/>\\n[101]<div 伦敦/>\\n[102]<div 荷兰-远程/>\\n[103]<div 阿姆斯特丹/>\\n[104]<div 班加罗尔/>\\n[105]<div 古尔冈/>\\n[106]<div 印度-远程/>\\n[107]<div 泰国-远程/>\\n[108]<div 曼谷/>\\n[109]<div 印尼-远程/>\\n[110]<div 雅加达/>\\n[111]<div 马尼拉/>\\n[112]<div 越南-远程/>\\n[113]<div 河内/>\\n[114]<div 迪拜/>\\n[115]<div 土耳其-远程/>\\n[116]<div 伊斯坦布尔/>\\n[117]<div 巴基斯坦-远程/>\\n[118]<div 伊斯兰堡/>\\n[119]<div 沙特-远程/>\\n[120]<div 利雅得/>\\n[121]<div 孟加拉-远程/>\\n[122]<div 慕尼黑/>\\n[123]<div 柏林/>\\n[124]<div 德国-远程/>\\n[125]<div 法兰克福/>\\n[126]<div 意大利-远程/>\\n[127]<div 米兰/>\\n[128]<div 法国-远程/>\\n[129]<div 巴黎/>\\n[130]<div 波兰-远程/>\\n[131]<div 华沙/>\\n[132]<div 奥地利-远程/>\\n[133]<div 维也纳/>\\n[134]<div 俄罗斯-远程/>\\n[135]<div 莫斯科/>\\n[136]<div 瑞典-远程/>\\n[137]<div 捷克-远程/>\\n[138]<div 魁北克省/>\\n[139]<div 阿尔伯塔/>\\n[140]<div 不列颠哥伦比亚省/>\\n[141]<div 英属哥伦比亚省-远程/>\\n[142]<div 安大略省/>\\n[143]<div 温哥华/>\\n[144]<div 多伦多/>\\n[145]<div 蒙特利尔/>\\n[146]<div 墨西哥-远程/>\\n[147]<div 墨西哥城/>\\n[148]<div 巴西-远程/>\\n[149]<div 巴西利亚/>\\n[150]<div 圣保罗/>\\n[151]<div 阿根廷-远程/>\\n[152]<div 智利-远程/>\\n[153]<div 澳大利亚首都领地/>\\n[154]<div 新南威尔士州/>\\n[155]<div 堪培拉/>\\n[156]<div 悉尼/>\\n[157]<div 新西兰-远程/>\\n[158]<div 奥克兰/>\\n[159]<div 南非-远程/>\\n[160]<div 茨瓦内/>\\n[161]<div 埃及-远程/>\\n[162]<div 开罗/>\\n[163]<div />\\n[164]<a 职业类别/>\\n[165]<div />\\n[166]<div />\\n[167]<div />\\n[168]<div />\\n[169]<a 事业群/>\\n[170]<div />\\n[171]<div />\\n[172]<div />\\n[173]<div />\\n[174]<a 招聘类型/>\\n[175]<div />\\n[176]<div />\\n[177]<div 社招/>\\n[178]<div 校招应届生/>\\n[179]<div 校招实习生/>\\n[180]<div 校招青云计划/>\\n最新发布\\n显示1-10,共255职位\\n[181]<div 清除全部/>\\n[182]<a 社招/>\\n[183]<span />\\n[184]<a 上海/>\\n[185]<span />\\n[186]<div />\\n[187]<a />\\n[188]<div />\\n[189]<span 《王者荣耀世界》资深2D角色设计/>\\n[190]<span 上海/>\\n[191]<span />\\n[192]<p />\\n[193]<span IEG/>\\n[194]<span />\\n[195]<span 设计/>\\n[196]<span />\\n[197]<span 三年以上工作经验/>\\n[198]<span />\\n[199]<span 更新于2025年05月14日/>\\n[200]<p 1.负责商业化主角皮肤及武器皮肤设计把控，进行品质提升与指导，并提升产出效率；\\r\\n2.负责现代风格与东方美学概念探索；\\r\\n3.根据研发计划，安排相关美术内容研发计划并负责品质管理；\\r\\n4.需要对新型技术有了解研究，例如使用AI软件进行美术探索。/>\\n[201]<div />\\n[202]<div 分享岗位\\n方式1:复制岗位链接\\n点击复制\\n方式2:分享岗位海报\\n手机扫描二维码分享/>\\n[203]<span />\\n[204]<span 分享/>\\n[205]<div />\\n[206]<span />\\n[207]<span 收藏/>\\n[208]<div />\\n[209]<a />\\n[210]<div />\\n[211]<span 腾讯云--- 音频语音识别/理解算法高级工程师/>\\n[212]<span 上海/>\\n[213]<span />\\n[214]<p />\\n[215]<span CSIG/>\\n[216]<span />\\n[217]<span 技术/>\\n[218]<span />\\n[219]<span 三年以上工作经验/>\\n[220]<span />\\n[221]<span 更新于2025年05月14日/>\\n[222]<p 1.负责语音识别ASR应用涉及的效果调优与实现，比如多语种语音识别与翻译（中/英/法/日/韩/东南亚/中东等）、端侧超级轻量级高效ASR的落地实现、解决目标说话人增强的ASR相关技术的落地、声学/语意vad等；\\r\\n2.负责语音识别asr-llm大模型与前沿技术调研与落地，比如更合理的语音与文本对齐、适用于多语种的模型方案、更高效的弱监督数据筛选流程等；\\r\\n3.负责跟踪并复现业界最前沿的音频处理方案，并能融合优化当前效果。/>\\n[223]<div />\\n[224]<div 分享岗位\\n方式1:复制岗位链接\\n点击复制\\n方式2:分享岗位海报\\n手机扫描二维码分享/>\\n[225]<span />\\n[226]<span 分享/>\\n[227]<div />\\n[228]<span />\\n[229]<span 收藏/>\\n[230]<div 分享\\n分享岗位\\n方式1:复制岗位链接\\n点击复制\\n方式2:分享岗位海报\\n手机扫描二维码分享\\n收藏/>\\n[231]<a />\\n[232]<div />\\n[233]<span SI工程师/>\\n[234]<span 上海/>\\n[235]<span />\\n[236]<p />\\n[237]<span TEG/>\\n[238]<span />\\n[239]<span 技术/>\\n[240]<span />\\n[241]<span 五年以上工作经验/>\\n[242]<span />\\n[243]<span 更新于2025年05月14日/>\\n[244]<p 1.负责设计、开发和维护系统信号完整性方案，确保设计的有效性和可靠性；\\r\\n2.参与产品开发过程中的设计评审，提出关于信号完整性的技术建议和改进方案；\\r\\n3.开发和维护信号完整性测试方法和流程，制定测试计划并进行测试，提供测试报告和数据分析；\\r\\n4.分析系统集成中的问题，并提供解决方案，确保系统集成的高效运行；\\r\\n5.持续跟踪和研究最新的信号完整性技术和工具，提出改进和创新的建议。/>\\n[245]<div />\\n[246]<a />\\n[247]<div />\\n[248]<span 《王者荣耀》IP在研项目-海外运营-宣发运营方向/>\\n[249]<span 上海/>\\n[250]<span />\\n[251]<p />\\n[252]<span IEG/>\\n[253]<span />\\n[254]<span 产品/>\\n[255]<span />\\n[256]<span 三年以上工作经验/>\\n[257]<span />\\n[258]<span 更新于2025年05月14日/>\\n[259]<p 1.负责海外多地区的游戏筹备、上线及运营期的宣发工作，为项目手游、PC等多端版本的整合营销制定行之有效的策略，助力产品目标达成；\\r\\n2.协调统筹品牌、媒介、内容、公关等各多模块工作，完成宣发的落地执行，对营销结果负责，并保证与版本内容、运营节点的紧密配合，辅助活跃及商业化目标达成；\\r\\n3.熟练掌握海外社区、内容以及KOL宣发运营手段，持续监控及指导优化区域本地化推广效果；\\r\\n4.对接不同团队沟通协调（包括但不限于研发团队、各地区发行团队以及BG各支持团队），推动跨团队合作，推进项目发展。/>\\n[260]<div />\\n[261]<div 分享岗位\\n方式1:复制岗位链接\\n点击复制\\n方式2:分享岗位海报\\n手机扫描二维码分享/>\\n[262]<span />\\n[263]<span 分享/>\\n[264]<div />\\n[265]<span />\\n[266]<span 收藏/>\\n[267]<div />\\n[268]<a />\\n[269]<div />\\n[270]<span 体育游戏-数据科学工程师-新星引力计划/>\\n[271]<span 上海/>\\n[272]<span />\\n[273]<p />\\n[274]<span IEG/>\\n[275]<span />\\n[276]<span 技术/>\\n[277]<span />\\n[278]<span 一年以上工作经验/>\\n[279]<span />\\n[280]<span 更新于2025年05月14日/>\\n[281]<p 1.负责游戏产品在研发和运营中的数据埋点/建模/分析/实验设计等工作，为业务提供数据支持和方向判断；\\n2.能够深刻理解游戏中的各项因素指标，负责数据分析体系搭建，包括TLOG埋点、日志上报、分析系统规划等工作；\\n3.负责游戏运营效果分析，包括留存及流失分析，玩法模式分析等，并能依据数据表现提出合理建议，为后续优化提供依据；\\n4.为游戏中数据相关特性的研发与迭代提供算法支持，包括但不限于：对战匹配算法、推荐算法等；\\n5.构建可复用的数据科学模型，为游戏数据表现以及内容体验提供结果分析和预测，包括但不限于：聚类分析、目标预测、异常检测、高维数据可视化等；\\n6.持续进行数据分析工具的使用以及开发，优化数据分析流程以及效率，在团队内建立高效敏捷的数据共享机制；\\n7.能够结合实际业务需求和数据来优化现有算法，从而得到前沿可靠的解决方案，能够对所做工作进行归纳整理，确保所提出算法和思维的领先。/>\\n[282]<div />\\n[283]<div 分享岗位\\n方式1:复制岗位链接\\n方式2:分享岗位海报\\n手机扫描二维码分享/>\\n[284]<span />\\n[285]<span 分享/>\\n[286]<a />\\n[287]<img />\\n[288]<button 点击复制/>\\n[289]<div />\\n[290]<span />\\n[291]<span 收藏/>\\n[292]<div />\\n[293]<a />\\n[294]<div />\\n[295]<span 腾讯云-芯片半导体行业大客户销售经理/>\\n[296]<span 上海/>\\n[297]<span />\\n[298]<p />\\n[299]<span CSIG/>\\n[300]<span />\\n[301]<span 销售、服务与支持/>\\n[302]<span />\\n[303]<span 八年以上工作经验/>\\n[304]<span />\\n[305]<span 更新于2025年05月14日/>\\n[306]<p 1.深入理解半导体行业业务场景及技术需求，基于腾讯云全线产品并结合腾讯综合能力，推动打造行业解决方案，推动内部产品持续迭代；\\r\\n2.拓展和维护半导体企业客户关系，帮助客户上云、进行数字化转型，跟进从拓客到落地，跟进方案的设计和输出、和持续优化；\\r\\n3.和半导体产业链上下游厂商及各合作伙伴共同建设行业生态，助力行业发展。/>\\n[307]<div />\\n[308]<div 分享岗位\\n方式1:复制岗位链接\\n方式2:分享岗位海报\\n手机扫描二维码分享/>\\n[309]<span />\\n[310]<span 分享/>\\n[311]<a />\\n[312]<img />\\n[313]<button 点击复制/>\\n[314]<div />\\n[315]<span />\\n[316]<span 收藏/>\\n[317]<div />\\n[318]<a />\\n[319]<div />\\n[320]<span 腾讯游戏-资深匹配算法工程师（上海/杭州）-新星引力计划/>\\n[321]<span 上海/>\\n[322]<span />\\n[323]<p />\\n[324]<span IEG/>\\n[325]<span />\\n[326]<span 技术/>\\n[327]<span />\\n[328]<span 两年以上工作经验/>\\n[329]<span />\\n[330]<span 更新于2025年05月14日/>\\n[331]<p 1.负责腾讯游戏新一代匹配系统的算法设计和业务落地工作；\\r\\n2.分析游戏数据，实现实时胜率预测、玩家状态判定、角色平衡性分析及⽤户体验分析算法；\\r\\n3.参与腾讯游戏项目，推动匹配系统的创新。/>\\n[332]<div />\\n[333]<div 分享岗位\\n方式1:复制岗位链接\\n方式2:分享岗位海报\\n手机扫描二维码分享/>\\n[334]<span />\\n[335]<span 分享/>\\n[336]<a />\\n[337]<img />\\n[338]<button 点击复制/>\\n[339]<div />\\n[340]<span />\\n[341]<span 收藏/>\\n[342]<div />\\n[343]<a />\\n[344]<div />\\n[345]<span 《王者荣耀世界》战斗策划-新星引力计划/>\\n[346]<span 上海/>\\n[347]<span />\\n[348]<p />\\n[349]<span IEG/>\\n[350]<span />\\n[351]<span 产品/>\\n[352]<span />\\n[353]<span 三年以上工作经验/>\\n[354]<span />\\n[355]<span 更新于2025年05月14日/>\\n[356]<p 1.参与游戏核心战斗的设计及规划, 包括战斗系统设计和战斗数值模型的搭建；\\n2.参与游戏的战斗效果表现，打造优秀的战斗体验；\\n3.参与设计和制作英雄技能、怪物和关卡的设计及规划，体验并调优技能设计；\\n4.包括但不限于地图设计、英雄设计及英雄管线管理、新玩法开发等。/>\\n[357]<div />\\n[358]<div 分享岗位\\n方式1:复制岗位链接\\n方式2:分享岗位海报\\n手机扫描二维码分享/>\\n[359]<span />\\n[360]<span 分享/>\\n[361]<a />\\n[362]<img />\\n[363]<button 点击复制/>\\n[364]<div />\\n[365]<span />\\n[366]<span 收藏/>\\n[367]<div />\\n[368]<a />\\n[369]<div />\\n[370]<span 《王者荣耀世界》数据分析-新星引力计划/>\\n[371]<span 上海/>\\n[372]<span />\\n[373]<p />\\n[374]<span IEG/>\\n[375]<span />\\n[376]<span 产品/>\\n[377]<span />\\n[378]<span 五年以上工作经验/>\\n[379]<span />\\n[380]<span 更新于2025年05月14日/>\\n[381]<p 1.参与《王者荣耀世界》数据分析，数据框架的搭建和测试/运营期数据监测分析，包括数据打点上报、数据系统接入、关键指标监控预警、产品和运营数据的深度分析挖掘，并推动产品和运营调优；\\n2.与中台团队合作，推进通用数据能力建设，包括 IP用户画像、精细化运营能力接入、通用系统工具接入等；\\n3.参与重点竞品和市场新品的数据洞察，包括活跃和付费分析、效果对比分析、波动原因挖掘等；\\n4.根据项目版本和运营节点，与支持部门合作组织用户调研，基于用研结论推动产品和运营调优。/>\\n[382]<div />\\n[383]<div 分享岗位\\n方式1:复制岗位链接\\n方式2:分享岗位海报\\n手机扫描二维码分享/>\\n[384]<span />\\n[385]<span 分享/>\\n[386]<a />\\n[387]<img />\\n[388]<button 点击复制/>\\n[389]<div />\\n[390]<span />\\n[391]<span 收藏/>\\n[392]<div />\\n[393]<a />\\n[394]<div />\\n[395]<span 《王者荣耀世界》系统策划-新星引力计划/>\\n[396]<span 上海/>\\n[397]<span />\\n[398]<p />\\n[399]<span IEG/>\\n[400]<span />\\n[401]<span 产品/>\\n[402]<span />\\n[403]<span 三年以上工作经验/>\\n[404]<span />\\n[405]<span 更新于2025年05月14日/>\\n[406]<p 1.参与游戏内核心玩法的系统设计、以及各系统的功能设计、界面及操作设计；\\n2.参与游戏模式及其玩法研究，确保产品具备优良的游戏性和竞争优势；\\n3.了解游戏研发步骤，可独立策划游戏中大型系统，整理和书写需求文档，供设计、开发、测试人员明确功能需求；\\n4.与程序、美术密切合作，负责游戏系统设计及推进执行。/>\\n[407]<div />\\n[408]<div 分享岗位\\n方式1:复制岗位链接\\n方式2:分享岗位海报\\n手机扫描二维码分享/>\\n[409]<span />\\n[410]<span 分享/>\\n[411]<a />\\n[412]<img />\\n[413]<button 点击复制/>\\n[414]<div />\\n[",
			expected: "{\"content\":[{\"type\":\"text\",\"text\":\"action_result: Action result: You have clicked the element 上海 (22)\"}]}",
		},
	}

	for _, testCase := range testCases {
		t.Run("trim page content section", func(t *testing.T) {
			res := TrimPageContentSection(testCase.rawJSON)
			t.Logf("trimpage content section: %s", res)
			require.EqualValues(t, testCase.expected, res)

		})
	}
}
