package helper

import (
	"bytes"
	"fmt"
	"strings"
	"text/template"

	"git.woa.com/dialogue-platform/proto/pb-stub/openapi"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
)

// ToolStep 表示一次工具调用及其输入输出
type ToolStep struct {
	ToolName   string `json:"tool_name"`
	ToolInput  string `json:"tool_input"`
	ToolOutput string `json:"tool_output"`
}

// Result 为最终的 JSON 结构
type Result struct {
	Question  string     `json:"question"`
	Answer    string     `json:"answer"`
	ToolSteps []ToolStep `json:"tool_steps"`
}

// ExtractJSON 接收 raw 文本，返回结构化的 JSON bytes
func ExtractJSON(raw string) string {
	var (
		res    Result
		steps  []ToolStep
		active *ToolStep
	)
	// 按行拆分
	for _, line := range strings.Split(raw, "\n") {
		line = strings.TrimSpace(line)
		switch {
		case strings.HasPrefix(line, "##问题："):
			res.Question = parseLineValue(line, "：")
		case strings.HasPrefix(line, "##答案："):
			res.Answer = parseLineValue(line, "：")
		case strings.HasPrefix(line, "##调用工具"):
			parts := strings.SplitN(line, "：", 2)
			if len(parts) == 2 {
				// 新增一个步骤并切换上下文
				steps = append(steps, ToolStep{ToolName: strings.TrimSpace(parts[1])})
				active = &steps[len(steps)-1]
				active.ToolInput = ""
			}
		case strings.HasPrefix(line, "##检索问题"):
			if active != nil {
				active.ToolInput = parseLineValue(line, "：")
			}
		case strings.HasPrefix(line, "##预期输出"):
			if active != nil {
				active.ToolOutput = parseLineValue(line, "：")
			}
		}
	}
	if len(res.Question) == 0 || len(res.Answer) == 0 { // 没有问题或答案,返回空。上游会用默认的Examples
		return ""
	}
	steps = append(steps, ToolStep{ToolName: "task_completed", ToolInput: res.Answer})
	res.ToolSteps = steps
	return FormatExamples(res)
	// // 序列化为缩进 JSON
	// result, err := jsoniter.MarshalToString(res)
	// if err != nil {
	//	return ""
	// }
	// return result
}

// parseLineValue 提取以分隔符分割的第二部分内容，如果不存在则返回空字符串
func parseLineValue(line, sep string) string {
	parts := strings.SplitN(line, sep, 2)
	if len(parts) == 2 {
		return strings.TrimSpace(parts[1])
	}
	return ""
}

// FormatExamples 生成示例文本
func FormatExamples(r Result) string {
	var b strings.Builder
	for i := range r.ToolSteps {
		// 标题和用户问题
		b.WriteString(fmt.Sprintf("# 例%d\n", i+1))
		b.WriteString(formatUserQuestion(r.Question))

		// 历史记录（前 i 步）
		if i > 0 {
			b.WriteString(formatHistory(r.ToolSteps[:i], r.Question))
		}

		// 当前检索辅助机器人
		b.WriteString(formatRobotBlock(r.ToolSteps[i], r.Question, r.Answer))
		b.WriteString("\n")
	}
	return b.String()
}

// formatUserQuestion 渲染 <用户问题> 区块
func formatUserQuestion(q string) string {
	return fmt.Sprintf("<用户问题>\n%s\n</用户问题>\n", q)
}

// formatHistory 渲染 <历史记录> 区块
func formatHistory(prev []ToolStep, question string) string {
	var sb strings.Builder
	sb.WriteString("<历史记录>\n")
	for idx, step := range prev {
		sb.WriteString(fmt.Sprintf(
			"检索步骤%d: Thought: 需要检索%s\n", idx+1, question))
		sb.WriteString(fmt.Sprintf("Action: %s\n", step.ToolName))
		sb.WriteString("Action Input: " + step.ToolInput + "\n")
		sb.WriteString(fmt.Sprintf("Action Output: %s\n", step.ToolOutput))
	}
	sb.WriteString("</历史记录>\n")
	return sb.String()
}

// formatRobotBlock 渲染 <检索辅助机器人> 区块
func formatRobotBlock(step ToolStep, question, answer string) string {
	var sb strings.Builder
	sb.WriteString("<检索辅助机器人>\n")

	// 如果是汇总调用，改换 Thought 并聚合输出
	if step.ToolName == "task_completed" {
		sb.WriteString("Thought: 需要整理信息，回答用户。\n")
		sb.WriteString(fmt.Sprintf("Action: %s\n", step.ToolName))
		finalAnswer := map[string]string{"final_answer": answer}
		sb.WriteString("Action Input: " + Object2String(finalAnswer) + "\n")
	} else {
		// 普通检索步骤
		sb.WriteString(fmt.Sprintf("Thought: 需要检索%s\n", question))
		sb.WriteString(fmt.Sprintf("Action: %s\n", step.ToolName))
		sb.WriteString("Action Input: " + step.ToolInput + "\n")
	}

	sb.WriteString("</检索辅助机器人>\n")
	return sb.String()
}

// indentJSON 在逗号后增加空格，美化单行 JSON
func indentJSON(line string) string {
	return strings.ReplaceAll(line, ",\"", ", \"")
}

// view 模型：增加 Req 字段
type view struct {
	Idx  int
	Name string
	Desc string
	Args string // parameters JSON
}

// RenderKBAgentTools 渲染工具列表
func RenderKBAgentTools(tools []*openapi.Tool) string {
	vms := make([]view, 0, len(tools))

	for i, tool := range tools {
		vms = append(vms, view{
			Idx:  i + 1,
			Name: tool.Function.Name,
			Desc: strings.TrimSuffix(tool.Function.Description, "."),
			Args: Object2StringEscapeHTML(tool.Function.Parameters),
		})
	}

	// 4. 模板渲染
	// const tmpl = `{{- range .}}{{.Idx }}. \"{{ .Name }}\": {{ .Desc }}. Args: {{ .Args }}{{- end }}`
	tmpl := config.GetAgentConfig().KBAgentConfig.ToolTmpl
	t, err := template.New("list").Parse(tmpl)
	if err != nil {
		return ""
	}

	// 执行到缓冲区
	var buf bytes.Buffer
	if err := t.Execute(&buf, vms); err != nil {
		return ""
	}

	return buf.String()
}

// extractRequired 从 params 中取出 "required" 并删除该字段
func extractRequired(params map[string]interface{}) []string {
	var req []string
	if raw, ok := params["required"]; ok {
		if arr, ok := raw.([]interface{}); ok {
			for _, v := range arr {
				if s, ok := v.(string); ok {
					req = append(req, s)
				}
			}
		}
		delete(params, "required")
	}
	return req
}
